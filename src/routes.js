import { AuditTrailRoute } from '#src/modules/audit-trail/index.js';
import { MediaRoute } from '#src/modules/media/index.js';
import { OrganisationRoute } from '#src/modules/organisation/index.js';
import {
  AccessControlRoute,
  AppRoute,
  DeveloperHubRoute,
  LocalisationRoute,
  SettingRoute,
} from '#src/modules/setting/index.js';
import {
  AuthRoute,
  DepartmentRoute,
  DepartmentTemplateRoute,
  RoleRoute,
  UserRoute,
} from '#src/modules/user/index.js';
import { getPackageJson } from '#src/utils/file.util.js';

// Get the package.json content
const packageJson = getPackageJson();

// Extract the major version number
const majorVersion = packageJson.version.split('.')[0];

// Function to generate prefix
const API_PREFIX = '/api';
const generatePrefix = (module) => `${API_PREFIX}/v${majorVersion}/${module}`;
const routeGroups = [
  { routes: AccessControlRoute, opts: { prefix: generatePrefix('access-controls') } },
  { routes: AppRoute, opts: { prefix: generatePrefix('apps') } },
  { routes: AuditTrailRoute, opts: { prefix: generatePrefix('audit-trails') } },
  { routes: DepartmentRoute, opts: { prefix: generatePrefix('departments') } },
  { routes: DepartmentTemplateRoute, opts: { prefix: generatePrefix('department-templates') } },
  { routes: DeveloperHubRoute, opts: { prefix: generatePrefix('developer-hubs') } },
  { routes: LocalisationRoute, opts: { prefix: generatePrefix('localisations') } },
  { routes: MediaRoute, opts: { prefix: generatePrefix('media') } },
  { routes: OrganisationRoute, opts: { prefix: generatePrefix('organisations') } },
  { routes: RoleRoute, opts: { prefix: generatePrefix('roles') } },
  { routes: SettingRoute, opts: { prefix: generatePrefix('settings') } },
  { routes: UserRoute, opts: { prefix: generatePrefix('users') } },
  { routes: AuthRoute, opts: { prefix: generatePrefix('auth') } },
];

/**
 * Registers a set of route groups with the Fastify instance.
 *
 * @param {Object} fastify - The Fastify instance to register routes with.
 * @param {Object} options - Additional options for route registration.
 * @returns {Promise<void>} A promise that resolves when all routes are registered.
 */
export default async (fastify, options) => {
  await Promise.all(
    routeGroups.map(async ({ routes, opts }) => {
      await fastify.register(routes, opts);
    }),
  );
};
