import { swaggerUiConfig } from '#config/swagger/swagger.config.js';
import { ACCESS_LEVEL_KEYS } from '#src/modules/core/constants/core.constant.js';

/**
 * Checks if a user has access to a specific route based on their authentication access level and route access configuration.
 *
 * @param {Object} request - The HTTP request object.
 * @param {string} [authAccess] - The authentication access level of the user.
 * @param {Object} [routeAccess={}] - An object containing access configuration for the route.
 *
 * @returns {boolean} - True if the user has access to the route, false otherwise.
 */
export const checkAccess = (request, authAccess, routeAccess = {}) => {
  if (!authAccess) {
    return routeAccess.public === true;
  }
  const accessKey = ACCESS_LEVEL_KEYS[authAccess];

  if (!accessKey) {
    return false;
  }
  return routeAccess[accessKey] === true;
};

/**
 * Enforces IP whitelist for incoming requests.
 *
 * This function checks if the IP address of the incoming request is included in the provided whitelist.
 * If the IP address is not found in the whitelist, the function returns false, indicating that the request should be denied access.
 * If the IP address is found in the whitelist, the function returns true, indicating that the request should be allowed access.
 *
 * @param {Object} request - The HTTP request object.
 * @param {string[]} [whitelist=[]] - An array of IP addresses that are allowed to access the API.
 *
 * @returns {boolean} - True if the request's IP address is in the whitelist, false otherwise.
 */
export const enforceIPWhitelist = (request, whitelist = []) => {
  const ip = request.headers['x-forwarded-for'] || request.ip;
  return whitelist.includes(ip);
};

/**
 * Checks if a request should bypass access control checks based on the URL.
 *
 * This function is used to allow access to specific routes without applying the access control rules.
 * It checks if the URL of the incoming request starts with the specified route prefix.
 * If the condition is met, the function returns true, indicating that the request should bypass access control checks.
 * If the condition is not met, the function returns false, indicating that the request should be subject to access control rules.
 *
 * @param {Object} request - The HTTP request object.
 * @param {Object} [swaggerUiConfig={}] - An object containing configuration settings for Swagger UI.
 * @param {string} [swaggerUiConfig.routePrefix='/swagger-ui'] - The route prefix for Swagger UI.
 *
 * @returns {boolean} - True if the request's URL starts with the specified route prefix, false otherwise.
 */
export const bypassAccessCheck = (request) => {
  return request.raw.url.startsWith(swaggerUiConfig.routePrefix);
};
