import { createHash } from 'crypto';

import { pinoLogger } from '#config/pino-logger.config.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';

const {
  CACHE_KEY_SEGMENTS: { ENTITY, AUTH, AUDIT_TRAIL },
  CACHE_SECOND,
} = CoreConstant;

/**
 * Generates a cache key based on the provided prefix, request, and options.
 *
 * @param {string} prefix - The prefix to use for the cache key. Must be a non-empty string.
 * @param {Object} request - The request object containing information used to generate the cache key.
 * @param {Object} [options={}] - Optional settings for generating the cache key.
 * @param {string} [options.keyType=ENTITY] - The type of key to generate, which determines the entityId used.
 * @param {boolean} [options.excludeKey=false] - Whether to exclude the entityId from the cache key.
 * @param {string} [options.hashAlgo='sha256'] - The hashing algorithm to use for generating the hash.
 * @returns {string} The generated cache key, which includes the prefix, entityId, and a hash of the request URL and entityId.
 * @throws {Error} If the prefix is not a non-empty string.
 *
 *  *
 * @example
 * // Default (keyType = ENTITY, excludeKey = false)
 * generateCacheKey("user", req);
 *
 * @example
 * // Only excludeKey
 * generateCacheKey("user", req, { excludeKey: true });
 *
 * @example
 * // Custom keyType
 * generateCacheKey("user", req, { keyType: AUTH });
 *
 * @example
 * // Both options
 * generateCacheKey("user", req, { excludeKey: true, keyType: AUDIT_TRAIL });
 */
export const generateCacheKey = (prefix, request, options = {}) => {
  const { keyType = ENTITY, excludeKey = false, hashAlgo = 'sha256' } = options;

  if (!prefix || typeof prefix !== 'string' || prefix.trim() === '') {
    throw new Error('Cache key prefix must be a non-empty string');
  }

  let entityId;
  switch (keyType) {
    case AUTH:
      entityId = request.authInfo?.id ?? 'no-auth';
      break;
    case AUDIT_TRAIL:
      entityId = `${request.entityAccessId}:${request.authInfo?.id ?? 'no-auth'}`;
      break;
    case ENTITY:
    default:
      entityId = request.entity?.id;
      break;
  }

  // fallback if no entityId found
  if (!entityId) {
    entityId = 'no-entity';
  }

  const raw = JSON.stringify({
    url: request?.raw?.url ?? 'no-url',
    entityId,
  });

  const hash = createHash(hashAlgo).update(raw).digest('hex');

  return excludeKey ? `${prefix}:${hash}` : `${prefix}:${entityId}:${hash}`;
};

/**
 * Fetches data from the cache using the provided key. If the data is not found in the cache,
 * it will be fetched from the resolver function (if provided) and then cached for future use.
 *
 * @param {Object} redis - The Redis client instance used to interact with the cache.
 * @param {string} key - The cache key to use for fetching data. Must be a non-empty string.
 * @param {Function|null} [resolver=null] - An optional function that fetches fresh data when the cache is empty.
 *   - If `resolver` is a function, it will be called to fetch fresh data.
 *   - If `resolver` is a direct value, that value will be used.
 * @param {number} [expiration=CACHE_SECOND.DAILY] - The expiration time for the cache entry in seconds.
 * @returns {Promise<any>} The fetched or cached data.
 * @throws {Error} If the cache key is not a non-empty string.
 */
export const fetchFromCache = async (
  redis,
  key,
  resolver = null,
  expiration = CACHE_SECOND.DAILY,
) => {
  if (!key || typeof key !== 'string' || key.trim() === '') {
    throw new Error('Cache key must be a non-empty string');
  }

  try {
    const cachedData = await redis.get(key);
    if (cachedData) {
      const parsed = JSON.parse(cachedData);
      pinoLogger.debug(`Cache hit for key: ${key}`);
      return parsed;
    }

    const setCache = async (data) => {
      if (data !== undefined) {
        await redis.setex(key, expiration, JSON.stringify(data));
        pinoLogger.debug({ key, expiration }, 'Cache set');
      }
      return data;
    };

    // If no callback, act like "set/get"
    if (typeof resolver !== 'function') {
      return setCache(resolver);
    }

    // Otherwise, fetch fresh data and cache it
    const freshData = await resolver();
    return setCache(freshData);
  } catch (err) {
    pinoLogger.error({ key, err }, 'Redis error in fetchFromCache');
    throw err;
  }
};

/**
 * Clears cache entries that match a specific prefix and optional entityId.
 *
 * @param {Object} redis - The Redis client instance used to interact with the cache.
 * @param {string} prefix - The prefix to match cache keys against. Must be a non-empty string.
 * @param {string|null} [entityId=null] - An optional entityId to further filter cache keys.
 * @param {number} [count=1000] - The number of keys to scan per iteration.
 * @param {number} [batchSize=500] - The number of keys to delete in each batch operation.
 * @returns {Promise<void>} Resolves when the cache clearing operation is complete.
 * @throws {Error} If there is a Redis error during the operation.
 */
export const clearCacheWithPrefix = async (
  redis,
  prefix,
  entityId = null,
  count = 1000,
  batchSize = 500,
) => {
  const matchPattern = entityId ? `${prefix}*:${entityId}:*` : `${prefix}*:*`;

  let cursor = '0';
  const keysToDelete = [];

  try {
    do {
      const [nextCursor, keys] = await redis.scan(cursor, 'MATCH', matchPattern, 'COUNT', count);
      cursor = nextCursor;
      if (keys.length > 0) {
        keysToDelete.push(...keys);
      }
    } while (cursor !== '0');

    if (keysToDelete.length > 0) {
      pinoLogger.info(
        { prefix, entityId, totalKeys: keysToDelete.length, batchSize },
        'Cache keys found for deletion',
      );

      for (let i = 0; i < keysToDelete.length; i += batchSize) {
        const batch = keysToDelete.slice(i, i + batchSize);
        await redis.del(batch);
        pinoLogger.debug(
          { prefix, entityId, batchDeleted: batch.length, from: i, to: i + batch.length - 1 },
          'Deleted cache keys batch',
        );
      }

      pinoLogger.info(
        { prefix, entityId, deletedCount: keysToDelete.length },
        'Cache cleared with prefix',
      );
    } else {
      pinoLogger.debug({ prefix, entityId }, 'No cache keys found to clear');
    }
  } catch (err) {
    pinoLogger.error({ prefix, entityId, err }, 'Error clearing cache with prefix');
    throw err;
  }
};

/**
 * Retrieves all cache keys that match a specific prefix.
 *
 * @param {Object} redis - The Redis client instance used to interact with the cache.
 * @param {string} prefix - The prefix to match cache keys against. Must be a non-empty string.
 * @returns {Promise<string[]>} An array of cache keys that match the specified prefix.
 * @throws {Error} If there is a Redis error during the operation.
 */
export const getCacheKeysWithPrefix = async (redis, prefix) => {
  const keys = [];
  let cursor = '0';
  try {
    do {
      const [nextCursor, results] = await redis.scan(cursor, 'MATCH', `${prefix}*`, 'COUNT', 1000);
      cursor = nextCursor;
      if (results?.length) {
        keys.push(...results);
      }
    } while (cursor !== '0');
    pinoLogger.debug({ prefix, count: keys.length }, 'Retrieved cache keys with prefix');
    return keys;
  } catch (err) {
    pinoLogger.error({ prefix, err }, 'Redis error in getCacheKeysWithPrefix');
    throw err;
  }
};

/**
 * Clears a specific cache entry identified by the given key.
 *
 * @param {Object} redis - The Redis client instance used to interact with the cache.
 * @param {string} key - The cache key to be cleared. Must be a non-empty string.
 * @returns {Promise<void>} Resolves when the cache entry is successfully cleared.
 * @throws {Error} If there is a Redis error during the operation.
 */
export const clearCache = async (redis, key) => {
  try {
    const result = await redis.del(key);
    if (result === 0) {
      pinoLogger.debug({ key }, 'No cache key found to clear');
    } else {
      pinoLogger.info({ key }, 'Cache key cleared');
    }
  } catch (err) {
    pinoLogger.error({ key, err }, 'Redis error in clearCache');
    throw err;
  }
};

/**
 * Clears all entries in the cache.
 *
 * @param {Object} redis - The Redis client instance used to interact with the cache.
 * @returns {Promise<void>} Resolves when all cache entries are successfully cleared.
 * @throws {Error} If there is a Redis error during the operation.
 */
export const clearAllCache = async (redis) => {
  try {
    await redis.flushall();
    pinoLogger.warn('All cache cleared');
  } catch (err) {
    pinoLogger.error({ err }, 'Redis error in clearAllCache');
    throw err;
  }
};

/**
 * Sets cache data with a specified time-to-live (TTL) expiration.
 *
 * @param {Object} redis - The Redis client instance used to interact with the cache.
 * @param {string} key - The cache key to use for storing data. Must be a non-empty string.
 * @param {any} data - The data to be cached. Will be stored as-is without JSON serialization.
 * @param {number} ttlSeconds - The time-to-live in seconds for the cache entry.
 * @returns {Promise<string>} The cache key that was used to store the data.
 * @throws {Error} If there is a Redis error during the operation.
 */
export const setCacheWithTTL = async (redis, key, data, ttlSeconds) => {
  if (!key || typeof key !== 'string' || key.trim() === '') {
    throw new Error('Cache key must be a non-empty string');
  }
  if (!Number.isInteger(ttlSeconds) || ttlSeconds <= 0) {
    throw new Error('TTL must be a positive integer');
  }

  try {
    const value = JSON.stringify(data);
    await redis.setex(key, ttlSeconds, value);

    return key;
  } catch (err) {
    pinoLogger.error({ key, err }, 'Failed to cache data');
    throw err;
  }
};

/**
 * Retrieves data from the cache using the provided key without setting new data.
 *
 * @param {Object} redis - The Redis client instance used to interact with the cache.
 * @param {string} key - The cache key to use for fetching data. Must be a non-empty string.
 * @returns {Promise<any|null>} The cached data if found, null if not found.
 * @throws {Error} If the cache key is not a non-empty string or if there's a Redis error.
 */
export const getCache = async (redis, key) => {
  if (!key || typeof key !== 'string' || key.trim() === '') {
    throw new Error('Cache key must be a non-empty string');
  }

  try {
    const cachedData = await redis.get(key);
    if (cachedData) {
      const parsed = JSON.parse(cachedData);
      pinoLogger.debug(`Cache hit for key: ${key}`);
      return parsed;
    }

    pinoLogger.debug(`Cache miss for key: ${key}`);
    return null;
  } catch (err) {
    pinoLogger.error({ key, err }, 'Redis error in getCache');
    throw err;
  }
};
