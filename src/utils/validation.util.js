import fastifyMultipart from '@fastify/multipart';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';

// Ajv for GET query filters
const coerceAjv = new Ajv({
  removeAdditional: true,
  useDefaults: true,
  coerceTypes: true,
  allErrors: true,
});
addFormats(coerceAjv);

// Initialize Ajv instance
const ajv = new Ajv({
  removeAdditional: true,
  useDefaults: true,
  coerceTypes: false,
  allErrors: true,
});

// Add standard formats and custom file format
addFormats(ajv);
ajv.addFormat('file', {
  type: 'object',
  validate: (file) => file && typeof file.toBuffer === 'function',
});

// Integrate Ajv with Fastify Multipart
fastifyMultipart.ajvFilePlugin(ajv);

export default ajv; // Export the configured Ajv instance

export { coerceAjv };
