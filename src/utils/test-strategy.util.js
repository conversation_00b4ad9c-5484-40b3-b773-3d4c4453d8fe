import { decrypt } from '#src/utils/aes.util.js';
import { sendDiscordMessage } from '#src/utils/discord.util.js';
import { createValidationError } from '#src/utils/error.util.js';
import { getExchangeRates } from '#src/utils/exchange-rate.util.js';
import { paymentSystem } from '#src/utils/payment-system.util.js';
import { sendGrid } from '#src/utils/sendgrid.util.js';
import { sendSlackMessage } from '#src/utils/slack.util.js';
import { sendEmail } from '#src/utils/smtp.util.js';
import { sendTelegramMessage } from '#src/utils/telegram.util.js';
import { uploadToS3 } from '#src/utils/upload.util.js';
/**
 * Validates that all required fields are present and not falsy.
 * Throws a validation error if any field is missing or invalid.
 *
 * @param {Object} fields - An object where keys are field names and values are the field values to validate.
 * @param {Function} createValidationError - A function to create a validation error, which is thrown if validation fails.
 * @throws Will throw a validation error if any field is missing or invalid.
 */

export const validateRequiredFields = (fields) => {
  const errors = Object.entries(fields)
    .filter(([, value]) => !value)
    .map(([key]) => ({
      message: `Missing or invalid field: ${key}`,
      key,
    }));

  if (errors.length > 0) {
    throw createValidationError(
      ...errors.map((err) => ({
        message: err.message,
        instancePath: `/${err.key}`,
        schemaPath: '',
        keyword: 'required',
        params: {},
      })),
    );
  }
};

/**
 * Decrypts a configuration value by its key from a list of configurations.
 *
 * @param {string} key - The key of the configuration to decrypt.
 * @param {Array<Object>} configs - The list of configuration objects, each containing a `configKey` and `configValue`.
 * @returns {string|null} - The decrypted configuration value if found, otherwise null.
 */
export const decryptConfigByKey = (key, configs) => {
  const config = configs.find((c) => c.configKey === key);
  if (!config) {
    return null;
  }
  return decrypt(config.configValue);
};

export const testStrategy = {
  // currency exchange rate
  'Exchangerate Host': async (configs, request) => {
    const token = decryptConfigByKey('apiAccessKey', configs);

    validateRequiredFields({ apiAccessKey: token });

    try {
      const result = await getExchangeRates({ base: 'USD', token });

      return createTestResultResponse({
        success: true,
        data: result.data,
        requestHeaders: result.requestHeaders,
      });
    } catch (err) {
      return createTestResultResponse({
        success: false,
        data: null,
        error: err.message,
        requestHeaders: err.requestHeaders,
      });
    }
  },
  // communication
  Discord: async (configs, request) => {
    const webhook = request.webhook;
    const content = '✅ This is a test message';

    validateRequiredFields({ webhook });

    try {
      const result = await sendDiscordMessage({ webhook, content });

      return createTestResultResponse({
        success: true,
        data: result.message,
        requestHeaders: result.requestHeaders,
      });
    } catch (err) {
      return createTestResultResponse({
        success: false,
        data: null,
        error: err.message,
        requestHeaders: err.requestHeaders,
      });
    }
  },
  Slack: async (configs, request) => {
    const webhook = request.webhook;
    const content = '✅ This is a test message';

    validateRequiredFields({ webhook });

    try {
      const result = await sendSlackMessage({ webhook, content });

      return createTestResultResponse({
        success: true,
        data: result.message,
        requestHeaders: result.requestHeaders,
      });
    } catch (err) {
      return createTestResultResponse({
        success: false,
        data: null,
        error: err.message,
        requestHeaders: err.requestHeaders,
      });
    }
  },
  Telegram: async (configs, request) => {
    const token = decryptConfigByKey('telegramBotToken', configs);
    const chatId = request.chatId;
    const content = '✅ This is a test message';

    validateRequiredFields({ telegramBotToken: token, chatID: chatId });

    try {
      const result = await sendTelegramMessage({ token, chatId, content });

      return createTestResultResponse({
        success: true,
        data: result.message,
        requestHeaders: result.requestHeaders,
      });
    } catch (err) {
      return createTestResultResponse({
        success: false,
        data: null,
        error: err.message,
        requestHeaders: err.requestHeaders,
      });
    }
  },
  // email
  SMTP: async (configs, request) => {
    const smtpHost = decryptConfigByKey('smtpHost', configs);
    const smtpPort = decryptConfigByKey('smtpPort', configs);
    const smtpUser = decryptConfigByKey('smtpUser', configs);
    const smtpPassword = decryptConfigByKey('smtpPassword', configs);
    const email = request.email;

    validateRequiredFields({ smtpHost, smtpPort, smtpUser, smtpPassword });

    try {
      const result = await sendEmail({
        host: smtpHost,
        port: parseInt(smtpPort) || 587,
        secure: false,
        user: smtpUser,
        pass: smtpPassword,
        from: `"Notifier" <${smtpUser}>`,
        to: email,
        subject: '🎯 SMTP Email Test',
        text: 'Hello from your SMTP!',
        html: '<strong>Hello from your SMTP!</strong>',
      });

      return createTestResultResponse({
        success: true,
        data: result,
      });
    } catch (err) {
      return createTestResultResponse({
        success: false,
        data: null,
        error: err.message,
      });
    }
  },
  SendGrid: async (configs, request) => {
    const apiKey = decryptConfigByKey('sendgridApiKey', configs);
    const sender = decryptConfigByKey('sendgridSender', configs);
    const email = request.email;

    validateRequiredFields({
      sendgridApiKey: apiKey,
      sendgridSender: sender,
    });

    try {
      const result = await sendGrid({
        apiKey,
        to: email,
        from: sender,
        subject: '🎯 Send Grid Email Test',
        text: 'Hello from your Send Grid!',
        html: '<strong>Hello from your Send Grid!</strong>',
      });

      return createTestResultResponse({
        success: true,
        data: result,
      });
    } catch (err) {
      return createTestResultResponse({
        success: false,
        data: null,
        error: err.message,
      });
    }
  },
  // cloud storage
  'AWS S3': async (configs, request) => {
    const awsRegion = decryptConfigByKey('awsRegion', configs);
    const awsAccessKeyID = decryptConfigByKey('awsAccessKeyID', configs);
    const awsSecretKey = decryptConfigByKey('awsSecretKey', configs);
    const awsBucketName = decryptConfigByKey('awsBucketName', configs);
    const baseUrl = decryptConfigByKey('baseUrl', configs);

    const credentials = {
      awsRegion,
      awsAccessKeyID,
      awsSecretKey,
      awsBucketName,
      baseUrl,
    };

    const fileContent = await request.upload.toBuffer();

    const file = {
      fileContent,
      originalname: request.upload.filename,
      mimeType: request.upload.mimetype,
    };

    validateRequiredFields({
      awsRegion,
      awsAccessKeyID,
      awsSecretKey,
      awsBucketName,
      baseUrl,
    });
    try {
      const res = await uploadToS3({ credentials, file });
      return createTestResultResponse({
        success: true,
        data: res,
      });
    } catch (err) {
      return createTestResultResponse({
        success: false,
        data: null,
        error: err.message,
      });
    }
  },
  // Payment System
  'Payment System': async (configs, request) => {
    const secretCode = decryptConfigByKey('secretCode', configs);
    const secretPassword = decryptConfigByKey('secretPassword', configs);
    const accountId = request.accountId;
    try {
      const result = await paymentSystem.getMaintenance({
        secretCode,
        secretPassword,
        accountId,
      });
      return createTestResultResponse({
        success: true,
        data: result?.data,
        requestHeaders: result.requestHeaders,
      });
    } catch (err) {
      return createTestResultResponse({
        success: false,
        data: null,
        error: err.message,
        requestHeaders: err.requestHeaders,
      });
    }
  },
};

/**
 * Creates a standardized response object for test results.
 *
 * @param {Object} params - The parameters for creating the test result response.
 * @param {boolean} params.success - Indicates whether the test was successful.
 * @param {*} [params.data=null] - The data returned from the test, if any.
 * @param {string} [params.message=''] - A message describing the result of the test.
 * @param {string|null} [params.error=null] - An error message if the test failed.
 * @returns {Object} - The test result response object.
 * @returns {boolean} return.success - Indicates the success status of the test.
 * @returns {*} return.data - The data from the test if successful, otherwise null.
 * @returns {string} return.message - A message describing the test result.
 * @returns {string|null} return.error - An error message if the test failed, otherwise null.
 */
export const createTestResultResponse = ({
  success,
  data = null,
  error = null,
  requestHeaders = null,
}) => {
  return {
    success,
    data: success ? data : null,
    error: success ? null : error,
    requestHeaders,
  };
};
