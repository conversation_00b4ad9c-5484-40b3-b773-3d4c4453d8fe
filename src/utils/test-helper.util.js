/**
 * Adds a custom `toJSON` method to an object that removes the `toJSON` method itself
 * from the object when it's stringified.
 *
 * @param {Object} obj - The object to which the `toJSON` method will be added.
 * @returns {Object} - The input object with the `toJSON` method added, or the original object if it wasn't valid.
 */
export const populateToJSON = (obj) => {
  if (obj && typeof obj === 'object') {
    if (obj.toJSON) {
      return obj;
    }

    obj.toJSON = function () {
      const { toJSON, ...self } = this;
      return self;
    };
  }
  return obj;
};
