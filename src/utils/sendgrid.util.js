import sgMail from '@sendgrid/mail';

/**
 * Sends an email using SendGrid
 *
 * @param {Object} options
 * @param {string} options.to - Recipient email address
 * @param {string} options.from - Sender email (must be verified in SendGrid)
 * @param {string} options.subject - Email subject
 * @param {string} [options.text] - Plain text body
 * @param {string} [options.html] - HTML body
 */
export const sendGrid = async ({ apiKey, to, from, subject, text, html }) => {
  sgMail.setApiKey(apiKey);
  const msg = {
    to,
    from,
    subject,
    text,
    html,
  };

  try {
    const [response] = await sgMail.send(msg);
    return response;
  } catch (error) {
    throw new Error(error.response.body.errors[0].message);
  }
};
