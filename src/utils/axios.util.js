import axios from 'axios';

import { pinoLogger } from '#config/pino-logger.config.js';

const axiosInstance = () => {
  const instance = axios.create({
    timeout: 10000,
    headers: { 'Content-Type': 'application/json' },
  });

  // Request interceptor
  instance.interceptors.request.use(
    (config) => config,
    (error) => Promise.reject(error instanceof Error ? error : new Error(String(error))),
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      const errorMsg = error.response?.data || error.message || 'Unknown Axios error';
      pinoLogger.error('Axios Error:', errorMsg);

      return Promise.reject(
        new Error(typeof errorMsg === 'string' ? errorMsg : JSON.stringify(errorMsg)),
      );
    },
  );

  return instance;
};

export default axiosInstance;
