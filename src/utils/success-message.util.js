import { CoreConstant } from '#src/modules/core/constants/index.js';
import { translateMessage } from '#src/utils/i18next.util.js';

const { MODULE_METHODS } = CoreConstant;
const {
  ASSIGN_USER,
  CHECK_AVAILABILITY,
  CREATE,
  DELETE,
  EXPORT,
  INDEX,
  LOGIN,
  REGISTER_SSO,
  REQUEST_2FA_SETUP,
  RESET_2FA,
  REVOKE_TRUSTED_DEVICE,
  INFO,
  NAVIGATION,
  ONBOARD_USER,
  OPTION,
  TEST,
  UPDATE_ACCESS_CONTROL,
  UPDATE_BASIC_INFORMATION,
  UPDATE_LOGIN_ACCESS,
  UPDATE_POLICY,
  UPDATE_ORGANISATION,
  UPDATE_PERMISSION,
  UPDATE_PERSONAL,
  UPDATE_SAFETY,
  UPDATE_STATUS,
  UPDATE_THEMES,
  VIEW,
  REQUEST_2FA_LOGIN,
  UPDATE_MAINTENANCE_STATUS,
  UPDATE,
  VIEW_THEMES,
} = MODULE_METHODS;
/**
 * Represents a singleton class for generating success messages.
 */
class SuccessMessage {
  /**
   * Creates a new SuccessMessage instance or returns the existing one.
   * Implements the singleton pattern.
   */
  constructor() {
    this.templates = {
      [ASSIGN_USER]: 'common.sentence.assignUserSuccess',
      [CHECK_AVAILABILITY]: 'common.sentence.checkAvailabilitySuccess',
      [CREATE]: 'common.sentence.createSuccess',
      [CREATE]: 'common.sentence.createSuccess',
      [DELETE]: 'common.sentence.deleteSuccess',
      [EXPORT]: `common.sentence.exportSuccess`,
      [INDEX]: 'common.sentence.indexSuccess',
      [NAVIGATION]: 'common.sentence.navigationSuccess',
      [ONBOARD_USER]: 'common.sentence.onboardUserSuccess',
      [OPTION]: 'common.sentence.optionSuccess',
      [REQUEST_2FA_SETUP]: 'common.sentence.request2faSetupSuccess',
      [RESET_2FA]: 'common.sentence.reset2faSuccess',
      [REVOKE_TRUSTED_DEVICE]: 'common.sentence.revokeTrustedDeviceSuccess',
      [TEST]: 'common.sentence.testSuccess',
      [UPDATE_ACCESS_CONTROL]: 'common.sentence.updateAccessControlsSuccess',
      [UPDATE_BASIC_INFORMATION]: 'common.sentence.updateBasicInformationSuccess',
      [UPDATE_LOGIN_ACCESS]: 'common.sentence.updateLoginAccessSuccess',
      [UPDATE_POLICY]: 'common.sentence.updatePolicySuccess',
      [UPDATE_ORGANISATION]: 'common.sentence.updateOrganisationSuccess',
      [UPDATE_PERMISSION]: 'common.sentence.updatePermissionsSuccess',
      [UPDATE_PERSONAL]: 'common.sentence.updatePersonalSuccess',
      [UPDATE_SAFETY]: 'common.sentence.updateSafetySuccess',
      [UPDATE_STATUS]: 'common.sentence.updateStatusSuccess',
      [UPDATE_THEMES]: 'common.sentence.updateThemesSuccess',
      [UPDATE_PERMISSION]: 'common.sentence.updatePermissionsSuccess',
      [UPDATE_ACCESS_CONTROL]: 'common.sentence.updateAccessControlsSuccess',
      [UPDATE_POLICY]: 'common.sentence.updatePolicySuccess',
      [UPDATE_MAINTENANCE_STATUS]: 'common.sentence.updateMaintenanceStatusSuccess',
      [DELETE]: 'common.sentence.deleteSuccess',
      [LOGIN]: 'common.sentence.loginSuccess',
      [REGISTER_SSO]: 'common.sentence.registerSSOSuccess',
      [UPDATE]: 'common.sentence.updateSuccess',
      [REQUEST_2FA_LOGIN]: 'common.sentence.request2faLoginSuccess',
      [VIEW_THEMES]: 'common.sentence.viewThemesSuccess',
      [VIEW]: 'common.sentence.viewSuccess',
    };
  }
  static getInstance() {
    if (!SuccessMessage._instance) {
      SuccessMessage._instance = new SuccessMessage();
    }
    return SuccessMessage._instance;
  }

  /**
   * Retrieves a success message template based on the module and operation type.
   * @param {string} module - The name of the module (e.g., 'user', 'product').
   * @param {string} type - The type of operation (e.g., 'view', 'create', 'update', 'updateStatus').
   * @param {...*} opts - Additional optional parameters that might be used in future template expansions.
   * @returns {string} The formatted success message.
   */
  getTemplate(fastify, module, type, ...opts) {
    // Use defined template if available, otherwise use default template.
    const template = this.templates[type];
    if (template) {
      return translateMessage(template, { module: `common.label.${module}` }, fastify);
    }
    return translateMessage('common.sentence.actionSuccess', undefined, fastify); // Fallback on undefined template
  }
}
const instance = SuccessMessage.getInstance();
export default instance;
