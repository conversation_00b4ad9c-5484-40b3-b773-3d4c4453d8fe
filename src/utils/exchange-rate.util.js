import axiosInstance from '#src/utils/axios.util.js';

/**
 * Fetch exchange rates from exchangerate.host
 *
 * @param {Object} options
 * @param {string} options.base Base currency (default 'USD')
 * @param {string} [options.token] Optional token
 * @param {Object} [axios] Optional Axios instance (for testing)
 * @returns {Promise<Object>} Exchange rate data
 */
export const getExchangeRates = async ({ base = 'USD', token } = {}, axios = axiosInstance()) => {
  const apiUrl = 'https://api.exchangerate.host/live';
  const params = { base };

  if (token) {
    params.access_key = token;
  }

  let requestHeaders;
  axios.interceptors.request.use((config) => {
    requestHeaders = config.headers;
    return config;
  });

  try {
    const { data } = await axios.get(apiUrl, { params });

    if (!data || data.success === false) {
      const error = new Error(data?.error?.info);
      error.success = false;
      error.requestHeaders = requestHeaders;
      throw error;
    }
    return { data, requestHeaders };
  } catch (err) {
    const error = new Error(err.message);
    error.success = false;
    error.requestHeaders = requestHeaders;
    throw error;
  }
};
