/**
 * Decodes a JWT token using the provided Fastify instance.
 *
 * @param {string} token - The JWT token to decode.
 * @param {FastifyInstance} fastify - The Fastify instance for logging.
 * @returns {Promise<object|null>} - The decoded payload of the JWT token, or null if invalid or missing.
 */
export const decodeJWT = async (token, fastify) => {
  if (!token) {
    fastify.log.debug('No JWT token provided');
    return null;
  }

  try {
    return await fastify.jwt.verify(token);
  } catch (err) {
    fastify.log.debug(err, 'Failed to verify JWT token');
    return null;
  }
};

/**
 * Signs a JWT token using the provided Fastify instance.
 *
 * @param {object} payload - The payload to be encoded in the JWT token.
 * @param {FastifyInstance} fastify - The Fastify instance for JWT signing.
 * @param {object} [options={}] - Optional signing options (e.g., expiresIn, audience, issuer).
 * @returns {Promise<string|null>} - The signed JWT token, or null if signing fails.
 */
export const signJWT = (fastify, payload, options = {}) => {
  if (!payload || typeof payload !== 'object') {
    fastify.log.debug('Invalid payload provided for JWT signing');
    return null;
  }

  try {
    return fastify.jwt.sign(payload, options);
  } catch (err) {
    fastify.log.debug(err, 'Failed to sign JWT token');
    return null;
  }
};
