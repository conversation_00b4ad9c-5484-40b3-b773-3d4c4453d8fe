import nodemailer from 'nodemailer';

/**
 * Send email using any SMTP configuration
 * @param {Object} config
 * @param {string} config.host SMTP host (e.g. smtp.gmail.com)
 * @param {number} config.port SMTP port (465 for SSL, 587 for TLS)
 * @param {boolean} config.secure true for SSL (port 465), false for TLS (587)
 * @param {string} config.user SMTP username (usually your email address)
 * @param {string} config.pass SMTP password or app password
 * @param {string} config.from Email sender (e.g. 'App <<EMAIL>>')
 * @param {string} config.to Recipient(s) (string or array)
 * @param {string} config.subject Email subject
 * @param {string} config.text Plain text message
 * @param {string} [config.html] Optional HTML message
 */
export const sendEmail = async ({
  host,
  port = 587,
  secure = false,
  user,
  pass,
  from,
  to,
  subject,
  text,
  html,
}) => {
  const transporter = nodemailer.createTransport({
    host,
    port,
    secure,
    auth: { user, pass },
  });

  const mailOptions = {
    from,
    to,
    subject,
    text,
    ...(html && { html }),
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    return info;
  } catch (err) {
    throw new Error(err.message);
  }
};
