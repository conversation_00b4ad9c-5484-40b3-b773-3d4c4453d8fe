import axiosInstance from '#src/utils/axios.util.js';

const BASE_URL = 'https://psapi-qa.dt818w.com/api'; // Replace with your actual base URL

/**
 * Constructs standard headers for Payment System API requests.
 *
 * @param {Object} config
 * @param {string} config.secretCode
 * @param {string} config.secretPassword
 * @param {string} config.accountId
 * @returns {Object} Headers object
 */
const buildHeaders = ({ secretCode, secretPassword, accountId }) => ({
  'secret-code': secretCode,
  'secret-password': secretPassword,
  'account-id': accountId,
});

/**
 * Makes a GET request to the specified endpoint with the given headers.
 *
 * @param {string} path - API endpoint path (e.g., "/maintenances")
 * @param {Object} headers - HTTP headers
 * @param {Object} [axios] - Optional Axios instance (for testing)
 * @returns {Promise<any>} - The parsed response data
 */
const getRequest = async (path, headers, axios = axiosInstance()) => {
  const url = `${BASE_URL}${path}`;

  try {
    const { data: res, config } = await axios.get(url, { headers });
    const requestHeaders = config.headers;

    if (!res || res.success === false) {
      const error = new Error(`Payment System API - GET ${path}: ${err.message}`);
      error.success = false;
      error.requestHeaders = requestHeaders;
      throw error;
    }

    return {
      success: true,
      data: res.data,
      requestHeaders,
    };
  } catch (err) {
    const requestHeaders = err.config?.headers || headers;
    const error = new Error(`Payment System API - GET ${path}: ${err.message}`);
    error.success = false;
    error.requestHeaders = requestHeaders;
    throw error;
  }
};

/**
 * Fetches maintenance status from the Payment System.
 *
 * @param {Object} config
 * @param {Object} [axios] - Optional Axios instance (for testing)
 * @returns {Promise<any>}
 */
const getMaintenance = (config, axios) => {
  const headers = buildHeaders(config);
  return getRequest('/maintenances', headers, axios);
};

// Export as a grouped functional module
export const paymentSystem = {
  getMaintenance,
  // Add other API methods here
};
