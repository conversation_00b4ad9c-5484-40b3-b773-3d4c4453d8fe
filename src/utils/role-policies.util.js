import { CoreConstant } from '#src/modules/core/constants/index.js';
import { RoleError } from '#src/modules/user/errors/index.js';
import { findByIdWithModulePolicies } from '#src/modules/user/repository/role.repository.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';

const {
  CACHE_SECOND: { DAILY },
} = CoreConstant;

/**
 * Checks if the user has the required policies to access a route
 *
 * @async
 * @param {Object} request - The Fastify request object
 * @param {Object} fastify - The Fastify instance
 * @param {Object} routeConfig - Configuration for the route
 * @throws {Error} Throws an error if the user doesn't have the required policy
 * @returns {Promise<void>} Returns nothing if the user has access
 */
export const checkPolicies = async (request, fastify, routeConfig = {}) => {
  // Extract the required policy (format: "<module>.<action>") from the route config
  const requiredPolicy = routeConfig.policy;

  // If no policy is defined, allow access without checks
  if (!requiredPolicy) {
    return;
  }

  // Ensure the request has valid entity access
  if (!hasEntityAccess(request)) {
    throw RoleError.noEntityAccess();
  }

  // Split required policy into module and action (e.g. "user.create" -> ["user", "create"])
  const [module, action] = requiredPolicy.split('.');

  // Extract hierarchy and role details from the request context
  const hierarchy = request.entity?.hierarchy;
  const roleId = request.authInfo?.roleId;
  const userEntityId = request.userEntity?.id;

  // Fetch policies assigned to this role + user entity
  const roleWithPolicies = await fetchRolePolicies(fastify, request, roleId, userEntityId);

  // If no policies are found for the role, deny access
  if (!roleWithPolicies) {
    throw RoleError.noRolePolicyAccess();
  }

  // Get module policies for the current hierarchy level
  const hierarchyModules = roleWithPolicies.modules[hierarchy];

  // If no policies exist for this hierarchy, deny access
  if (!hierarchyModules) {
    throw RoleError.noHierarchyModuleAccess({ hierarchy });
  }

  // Find the policy that matches the requested module name
  const modulePolicy = hierarchyModules.find(
    (policy) => policy.name.toLowerCase() === module.toLowerCase(),
  );

  // If the module is not allowed, deny access
  if (!modulePolicy) {
    throw RoleError.moduleAccessDenied({ module });
  }

  // Check if the specific action is permitted in the module’s policy
  if (!modulePolicy?.policies?.[action]) {
    throw RoleError.actionNotAllowed({ action, module });
  }
};

/**
 * Determines if the user has access to the requested entity based on hierarchy
 *
 * @param {Object} request - The Fastify request object
 * @returns {boolean} True if the user has access to the entity, false otherwise
 */
const hasEntityAccess = (request) => {
  const { userEntity, entity, parentEntity } = request;
  if (userEntity.hierarchy === 'root') {
    return true;
  }

  if (userEntity.hierarchy === 'organisation') {
    return (
      (entity.hierarchy === 'organisation' && entity.id === userEntity.id) ||
      (entity.hierarchy === 'merchant' && parentEntity.id === userEntity.id)
    );
  }

  if (userEntity.hierarchy === 'merchant') {
    return entity.id === userEntity.id;
  }

  return false;
};

/**
 * Generates a cache key for role policies
 *
 * @param {Object} request - The Fastify request object
 * @param {string} roleId - The ID of the role
 * @returns {string} The generated cache key
 */
export const generateRolePoliciesCacheKey = (request, roleId) => {
  const modifiedRequest = {
    ...request,
    raw: {
      ...request.raw,
      url: `/role-policies/${roleId}`,
    },
  };

  return generateCacheKey('role_policies', modifiedRequest);
};

/**
 * Fetches role policies from cache or database
 *
 * @async
 * @param {Object} fastify - The Fastify instance
 * @param {Object} request - The Fastify request object
 * @param {string} roleId - The ID of the role to fetch policies for
 * @param {string} userEntityId - The ID of the user's entity
 * @returns {Promise<Object|null>} The role with its policies, or null if not found
 */
export const fetchRolePolicies = async (fastify, request, roleId, userEntityId) => {
  const cacheKey = generateRolePoliciesCacheKey(request, roleId);

  return await fetchFromCache(
    fastify.redis,
    cacheKey,
    async () => await findByIdWithModulePolicies(fastify, userEntityId, roleId, true),
    DAILY,
  );
};
