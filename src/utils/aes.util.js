import { createCipher<PERSON>, createDecipheriv, randomBytes } from 'crypto';

import { CoreError } from '#src/modules/core/errors/index.js';

const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 12;
const AUTH_TAG_LENGTH = 16;

/**
 * Retrieves the secret key for AES encryption/decryption.
 * The key is expected to be set as an environment variable named 'AES_SECRET_KEY'.
 *
 * @throws {Error} If 'AES_SECRET_KEY' is not set or if it is not a 32-byte hex string.
 * @returns {Buffer} The secret key as a Buffer.
 */
const getSecretKey = () => {
  const keyHex = process.env.AES_SECRET_KEY;
  if (!keyHex) {
    throw CoreError.missingEnv({ varName: 'AES_SECRET_KEY' });
  }

  const key = Buffer.from(keyHex, 'hex');
  if (key.length !== 32) {
    throw CoreError.invalidEnv({ varName: 'AES_SECRET_KEY', reason: 'must be 32 bytes in hex' });
  }

  return key;
};

/**
 * Encrypts a plaintext string using AES-256-GCM.
 *
 * @param {string} plaintext - The plaintext to encrypt.
 * @returns {string} The encrypted data, base64 encoded. Format: IV + ciphertext + auth tag.
 */
export const encrypt = (plaintext) => {
  const iv = randomBytes(IV_LENGTH);
  const cipher = createCipheriv(ALGORITHM, getSecretKey(), iv);

  const encrypted = Buffer.concat([cipher.update(plaintext, 'utf-8'), cipher.final()]);
  const authTag = cipher.getAuthTag();

  return Buffer.concat([iv, encrypted, authTag]).toString('base64');
};

/**
 * Decrypts an AES-256-GCM encrypted base64 string.
 *
 * @param {string} encryptedBase64 - The base64 encoded encrypted data (IV + ciphertext + auth tag).
 * @returns {string} The decrypted plaintext string.
 * @throws {Error} If decryption fails (e.g., wrong key, tampered data).
 */
export const decrypt = (encryptedBase64) => {
  const input = Buffer.from(encryptedBase64, 'base64');

  const iv = input.subarray(0, IV_LENGTH);
  const authTag = input.subarray(-AUTH_TAG_LENGTH);
  const encryptedText = input.subarray(IV_LENGTH, -AUTH_TAG_LENGTH);

  const decipher = createDecipheriv(ALGORITHM, getSecretKey(), iv);
  decipher.setAuthTag(authTag);

  const decrypted = Buffer.concat([decipher.update(encryptedText), decipher.final()]);
  return decrypted.toString('utf-8');
};
