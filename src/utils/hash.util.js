import bcrypt from 'bcrypt';

const SALT_ROUNDS = 10;

/**
 * Hash a plain password
 * @param {string} password - Plain password
 * @returns {Promise<string>} - Hashed password
 */
export const hashPassword = async (password) => {
  return await bcrypt.hash(password, SALT_ROUNDS);
};

/**
 * Compare a plain password with a hashed password
 * @param {string} password - Plain password
 * @param {string} hashedPassword - Password from database
 * @returns {Promise<boolean>}
 */
export const comparePassword = async (password, hashedPassword) => {
  return await bcrypt.compare(password, hashedPassword);
};
