import crypto from 'crypto';

import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';

import { CoreError } from '#src/modules/core/errors/index.js';

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'application/pdf',
  'audio/mpeg',
  'audio/ogg',
  'audio/wav',
  'text/csv',
];

/**
 * Validates file properties before upload.
 * @param {Buffer} fileContent - The file data as a buffer.
 * @param {string} mimeType - The MIME type of the file.
 */
function validateFile(fileContent, mimeType) {
  if (fileContent.length > MAX_FILE_SIZE) {
    throw CoreError.format({
      attribute: 'upload',
      format: `size exceeds ${MAX_FILE_SIZE / (1024 * 1024)} MB limit`,
    });
  }

  if (!ALLOWED_MIME_TYPES.includes(mimeType)) {
    throw CoreError.format({
      attribute: 'upload',
      format: 'JPEG, JPG, PNG, PDF, GIF, PDF, CSV, MPEG, OGG, and WAV',
    });
  }
}

/**
 * Generates a unique filename to avoid collisions in S3.
 * @param {string} originalFilename - The original filename of the uploaded file.
 * @returns {string} The unique filename.
 */
function generateUniqueFilename(originalFilename) {
  const timestamp = Date.now().toString(36);
  const randomString = crypto.randomBytes(8).toString('hex');
  const extension = originalFilename.split('.').pop();
  return `${timestamp}-${randomString}.${extension}`;
}

/**
 * Creates an S3 client using provided credentials.
 */
function createS3Client({ awsRegion, awsAccessKeyID, awsSecretKey }) {
  return new S3Client({
    region: awsRegion,
    credentials: {
      accessKeyId: awsAccessKeyID,
      secretAccessKey: awsSecretKey,
    },
  });
}
/**
 * Uploads a validated file to AWS S3.
 */
export const uploadToS3 = async ({ credentials, file }) => {
  const { awsRegion, awsAccessKeyID, awsSecretKey, awsBucketName, baseUrl } = credentials;
  const { fileContent, originalname, mimeType } = file;
  const s3 = createS3Client({ awsRegion, awsAccessKeyID, awsSecretKey });

  validateFile(fileContent, mimeType);

  const fileName = generateUniqueFilename(originalname);

  const params = {
    Bucket: awsBucketName,
    Key: fileName,
    Body: fileContent,
    ContentType: mimeType,
    ACL: 'public-read',
  };

  try {
    await s3.send(new PutObjectCommand(params));
    const fileUrl = `${baseUrl}/${fileName}`;
    return fileUrl;
  } catch (error) {
    throw new Error(error.message);
  }
};
