import axiosInstance from '#src/utils/axios.util.js';

/**
 * Sends a message to a specified Telegram chat using the Telegram Bot API.
 *
 * @param {Object} params
 * @param {string} params.token - Telegram bot token
 * @param {string} params.chatId - Target chat ID
 * @param {string} [params.content='No content provided.'] - Message content
 * @param {Object} [axios] - Optional Axios instance (for testing)
 * @returns {Promise<Object>} Telegram API response
 * @throws {Error} If sending fails
 */
export const sendTelegramMessage = async (
  { token, chatId, content = 'No content provided.' },
  axios = axiosInstance(),
) => {
  let requestHeaders;
  axios.interceptors.request.use((config) => {
    requestHeaders = config.headers;
    return config;
  });
  try {
    const response = await axios.post(`https://api.telegram.org/bot${token}/sendMessage`, {
      chat_id: chatId,
      text: content,
    });

    const data = response.data;

    if (!data.ok) {
      const error = new Error(data.description);
      error.success = false;
      error.requestHeaders = requestHeaders;
      throw error;
    }
    return { data, requestHeaders };
  } catch (err) {
    const error = new Error(err.message);
    error.success = false;
    error.requestHeaders = requestHeaders;

    throw error;
  }
};
