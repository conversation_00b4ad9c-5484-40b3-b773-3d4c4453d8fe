import { toDataURL } from 'qrcode';
import speakeasy from 'speakeasy';

import { AuthConstant } from '#src/modules/user/constants/index.js';
import { AuthError } from '#src/modules/user/errors/index.js';

/**
 * Generate a new 2FA secret for a user.
 * @param {string} label - A label (e.g., "YourApp (<EMAIL>)")
 * @returns {Promise<{ base32: string }>}
 */
export const generate2FASecret = (label = 'app') => {
  const secret = speakeasy.generateSecret({ name: label });

  return {
    base32secret: secret.base32,
  };
};

/**
 * Handles 2FA setup during login process
 * @param {Object} user - The user object
 * @param {Object|null} existing2FA - Existing 2FA settings if any
 * @returns {Promise<Object>} Setup response with QR code
 */
export const handle2FASetup = async (user, existing2FA) => {
  // Generate QR code from the secret key
  const otpauthUrl = speakeasy.otpauthURL({
    secret: existing2FA.secretKey,
    label: user.username,
    issuer: `QPLY2.0 ${user.ua[0].entity.name}`,
    encoding: 'base32',
  });

  const qrCodeUrl = await toDataURL(otpauthUrl);

  return qrCodeUrl;
};

/**
 * Verifies a TOTP token against the user's secret key and throws an error if invalid
 * @param {string} secretKey - The user's 2FA secret key
 * @param {string} token - The TOTP token to verify
 * @throws {AuthError} If the token is invalid
 */
export const validateTOTPToken = (secretKey, token) => {
  const isValid = speakeasy.totp.verify({
    secret: secretKey,
    encoding: 'base32',
    token,
    window: AuthConstant.SECURITY.TWO_FACTOR_AUTHENTICATION_WINDOW_DURATION,
  });

  if (!isValid) {
    throw AuthError.invalid2FAToken();
  }
};
