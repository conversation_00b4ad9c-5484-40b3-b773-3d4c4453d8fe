import axiosInstance from '#src/utils/axios.util.js';

export const sendDiscordMessage = async (
  { webhook, content = 'No content provided.' },
  axios = axiosInstance(),
) => {
  let requestHeaders;
  axios.interceptors.request.use((config) => {
    requestHeaders = config.headers;
    return config;
  });

  try {
    const response = await axios.post(webhook, { content });
    if (response.status === 204) {
      return { success: true, requestHeaders };
    }
  } catch (err) {
    const error = new Error(err.response?.data?.message || err.message);
    error.success = false;
    error.requestHeaders = requestHeaders;
    throw error;
  }
};
