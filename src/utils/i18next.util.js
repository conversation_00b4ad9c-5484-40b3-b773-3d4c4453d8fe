import i18next from 'i18next';

import * as v from '#src/utils/validation-message.util.js';

/**
 * Maps validation keywords to their corresponding message handler functions
 *
 * @example
 * // If validation-message.util.js exports m_required, m_enum, m_format
 * // validationKeywordHandlers will contain:
 * {
 *   required: [Function: m_required],
 *   enum: [Function: m_enum],
 *   format: [Function: m_format]
 * }
 */
const validationKeywordHandlers = Object.entries(v).reduce((acc, [key, fn]) => {
  if (key.startsWith('m_') && typeof fn === 'function') {
    const keyword = key.slice(2); // strip 'm_' prefix
    acc[keyword] = fn;
  }
  return acc;
}, {});

/**
 * Exports internal utility functions / objects for unit testing purposes.
 */
export const exportForUnitTest = { validationKeywordHandlers };

/**
 * Translates messages or arrays of messages using i18next
 *
 * @param {string|Array} message - The message key(s) to translate
 * @param {Object} [interpolationParams={}] - Parameters to interpolate into the translation
 * @param {Object} [i18nextInstance=i18next] - The i18next instance to use for translation
 * @returns {string} The translated message or array of translated messages
 */
export const translateMessage = (message, interpolationParams = {}, i18nextInstance = i18next) => {
  if (typeof message === 'string') {
    const params = interpolationParams.message || interpolationParams;
    const translatedParams = translateParams(params);
    const translation = i18nextInstance.t(message, translatedParams);

    return translation !== message ? translation : message;
  }

  if (Array.isArray(message)) {
    return message.map((item, index) => {
      return translateMessage(item, interpolationParams.details[index], i18nextInstance);
    });
  }

  return message;
};

/**
 * Recursively translates the values of an object using i18next.
 *
 * @param {Object} params - The object containing values to translate.
 * @param {Object} [i18nextInstance=i18next] - The i18next instance to use for translation.
 * @returns {Object} A new object with translated string values.
 */

export const translateParams = (params, i18nextInstance = i18next) => {
  if (typeof params === 'number' || typeof params === 'boolean') {
    return params;
  }
  if (typeof params === 'string') {
    return i18nextInstance.t(params) ?? params;
  }
  if (Array.isArray(params)) {
    return params.map((item) => translateParams(item, i18nextInstance));
  }
  if (typeof params === 'object' && params !== null) {
    return Object.fromEntries(
      Object.entries(params).map(([key, value]) => [
        key,
        typeof value === 'string'
          ? (i18nextInstance.t(value) ?? value)
          : translateParams(value, i18nextInstance),
      ]),
    );
  }
  return params;
};

/**
 * Translates the name property of a dropdown item using i18next
 *
 * @param {Array} data - The array contains the dropdown item to translate
 * @param {Object} i18nextInstance - The i18next instance to use for translation
 * @returns {Array} The translated dropdown item array with localized name property
 */
export const translateDropdownItem = (data, i18nextInstance = i18next) => {
  return data.map((item) => {
    const dataToTranslate = item?.dataValues || item;
    if (typeof dataToTranslate?.name === 'string') {
      return {
        ...dataToTranslate,
        name: i18nextInstance.t(dataToTranslate.name),
      };
    }
    return item;
  });
};

/**
 * Translates validation errors from Ajv/JSON Schema format into user-friendly messages
 * using i18next for internationalization.
 *
 * @param {Array<Object>} validationErrors - Array of validation error objects from Ajv
 * @param {Object} validationErrors[].instancePath - JSON path to the invalid property
 * @param {string} validationErrors[].keyword - Validation keyword that failed (e.g., 'required', 'format')
 * @param {string} validationErrors[].message - Default error message
 * @param {Object} validationErrors[].params - Additional parameters specific to the validation rule
 * @param {Object} [i18nextInstance=i18next] - i18next instance to use for translation
 *
 * @returns {Array<Object>} Array of objects mapping attribute names to translated error messages
 *                          Example: [{ email: "Email is required" }, { password: "Password is too short" }]
 *
 * @example
 * // With Ajv validation errors
 * const errors = [
 *   { instancePath: '/id', keyword: 'format', message: 'must match format "uuid"', params: { format: 'uuid' } }
 * ];
 * const translatedErrors = translateValidationErrors(errors);
 * // Returns: [{ id: "id must be format of uuid." }]
 */
export const translateValidationErrors = (validationErrors, i18nextInstance = i18next) => {
  return validationErrors.map(({ instancePath, message: rawMessage, params, keyword }) => {
    // Retrieve failed validation attribute(field)
    const attribute = instancePath?.slice(1) || params.missingProperty;

    const handler = validationKeywordHandlers[keyword];

    const translatedMessage =
      typeof handler === 'function'
        ? handler({ i18next: i18nextInstance, attribute, params })
        : `VE(${keyword}): ${rawMessage}`; // fallback rawMessage for unsupported keyword

    return { [attribute]: translatedMessage };
  });
};
