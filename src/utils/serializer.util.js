/**
 * Takes either a Fastify Request or a serialized Request object and returns
 * a standardized object with the following properties:
 *
 * - `headers`: The headers of the request.
 * - `ipAddress`: The IP address of the client.
 * - `method`: The request method.
 * - `parameters`: The URL parameters of the request.
 * - `path`: The URL path of the request.
 * - `query`: The query string of the request.
 * - `body`: The request body.
 * - `requestId`: The request ID.
 * - `userId`: The ID of the user associated with the request or 'anonymous'
 *   if no user is associated.
 *
 * @param {import('fastify').FastifyRequest} input - The input request.
 * @returns {object} A standardized object with the above properties.
 */
export const serializeRequest = (input) => {
  if (!input || typeof input !== 'object') {
    throw new Error('Invalid input: Expected an object');
  }

  const {
    headers = {},
    ip: ipAddress = '',
    method = '',
    params = {},
    url: path = '',
    id: requestId = '',
    user = {},
    query = {},
    body = {},
  } = input;

  return {
    headers,
    ipAddress,
    method,
    parameters: Object.keys(params).length > 0 ? params : undefined,
    path,
    requestId,
    userId: user?.id || 'anonymous',
    query: Object.keys(query).length > 0 ? query : undefined,
    body: Object.keys(body).length > 0 ? body : undefined,
  };
};

/**
 * Serializes a Fastify Response or a serialized Response object into a standardized object.
 *
 * - `payload`: The response payload.
 * - `statusCode`: The HTTP status code of the response or error.
 * - `responseTimeMs`: The time taken to process the request in milliseconds.
 *
 * @param {object} input - The input response object.
 * @returns {object} A standardized object with the above properties.
 */
export const serializeReply = (input) => {
  if (!input || typeof input !== 'object') {
    throw new Error('Invalid input: Expected an object');
  }

  const { request = {}, statusCode = 0 } = input;

  const { responsePayload, startTime } = request;

  return {
    payload: responsePayload,
    statusCode,
    responseTimeMs: startTime ? `${Date.now() - startTime}ms` : '0ms',
  };
};

/**
 * Takes an error object and serializes it into a standardized object with the
 * following properties:
 *
 * - `type`: The type of the error.
 * - `name`: The name of the error.
 * - `message`: The error message.
 * - `stack`: The error stack.
 * - `code`: The error code.
 * - `statusCode`: The HTTP status code associated with the error.
 *
 * @param {Error|object} error - The error object.
 * @returns {object} A standardized object with the above properties.
 */
export const serializeError = (error) => {
  if (!(error instanceof Error) && (typeof error !== 'object' || error === null)) {
    throw new Error('Invalid input: Expected an Error object or a non-null object');
  }

  const {
    type = error.constructor.name || 'Error',
    name = 'Error',
    message = 'Unknown error',
    stack = '',
    code = null,
    statusCode = 500,
  } = error;

  return {
    ...error,
    type,
    name,
    message: typeof message === 'string' ? message : JSON.stringify(message),
    stack: typeof stack === 'string' ? stack : JSON.stringify(stack),
    code: code || error.original?.code || null,
    statusCode,
  };
};
