import {
  prepareMongoFilters,
  prepareMongoSorting,
  prepareSortOrder,
} from '#src/utils/query.util.js';

const DEFAULT_CURSOR_PAGINATION_LIMIT = 50;
const DEFAULT_PAGE = 1;
const DEFAULT_LIMIT = 25;
const DEFAULT_SORT = [];

/**
 * Applies offset-based pagination to a database query.
 *
 * @param {Object} fastify - The Fastify instance.
 * @param {Object} model - The Sequelize model to query.
 * @param {Object} rawQuery - The raw query object containing pagination, sorting, and filtering parameters.
 * @param {Object} whereFilter - The where clause filter object for the query.
 * @param {Array} [includeFilter=[]] - An array of Sequelize include options for eager loading related models.
 * @returns {Promise<Object>} A promise that resolves to an object containing:
 *   - rows: The array of query results.
 *   - pagination: An object with pagination details (totalCount, totalPages, currentPage, limit).
 */
export const applyOffsetPagination = async (
  fastify,
  model,
  rawQuery,
  whereFilter,
  includeFilter = [],
) => {
  const { page = DEFAULT_PAGE, limit = DEFAULT_LIMIT, sortBy = DEFAULT_SORT } = rawQuery;

  const offset = (+page - 1) * +limit;

  const order = prepareSortOrder(fastify, sortBy);

  const queryOptions = {
    limit,
    offset,
    order,
    where: whereFilter,
  };

  // Only add includeFilter when includes is defined
  if (includeFilter.length > 0) {
    queryOptions.include = includeFilter;
  }

  const { count, rows } = await model.findAndCountAll(queryOptions);

  const totalPages = Math.ceil(count / limit);

  return {
    rows,
    pagination: {
      totalCount: count,
      totalPages,
      currentPage: +page,
      limit: +limit,
    },
  };
};

/**
 * Applies MongoDB filters and cursor-based pagination to a given model.
 *
 * @param {mongoose.Model} model - The Mongoose model to apply the query to.
 * @param {Object} rawQuery - The raw query parameters.
 * @param {number} rawQuery.limit - The maximum number of result to return.
 * @param {string} rawQuery.sortBy - The field to sort by.
 * @param {string} rawQuery.cursor - The cursor for pagination.
 * @param {boolean} isExport - Indicates whether the query is for exporting data.
 * @returns {Promise<{result: Array, meta: {limit: number, nextCursor: string | null}}>}
 * - result: The array of result matching the query.
 * - meta: Metadata about the pagination.
 *   - limit: The maximum number of result returned.
 *   - nextCursor: The cursor for the next page of result.
 */
export const applyMongoFiltersCursorPagination = async (model, rawQuery, isExport) => {
  let { limit, sortBy, cursor, ...filters } = rawQuery;
  const query = prepareMongoFilters(filters);
  const { sortField, sortOrder, cursorOperator } = prepareMongoSorting(sortBy);

  const numericLimit = isExport ? null : Number(limit) || DEFAULT_CURSOR_PAGINATION_LIMIT;

  if (cursor) {
    query._id = { ...(query._id || {}), [cursorOperator]: cursor };
  }

  const resultQuery = model
    .find(query)
    .sort({ [sortField]: sortOrder, _id: sortOrder })
    .lean();

  if (numericLimit !== null) {
    resultQuery.limit(numericLimit);
  }
  const rows = await resultQuery.exec();

  const nextCursor = rows.length > 0 ? rows[rows.length - 1]._id : null;

  return {
    rows,
    pagination: {
      limit: numericLimit,
      nextCursor,
    },
  };
};
