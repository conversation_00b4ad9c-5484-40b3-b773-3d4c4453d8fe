/**
 * A utility function to execute a callback within a database transaction.
 * If an external transaction is provided, it will be used instead of creating a new one.
 *
 * @param {Object} server - The server object containing the database connection.
 * @param {Object} options - Additional options for the transaction.
 * @param {Object} [options.transaction] - An optional external transaction to be used.
 * @param {Function} callback - The callback function to be executed within the transaction.
 * @returns {Promise<any>} - The result of the callback function.
 */
export const withTransaction = async (server, options, callback) => {
  const { transaction: externalTransaction } = options || {};

  if (externalTransaction) {
    return await callback(externalTransaction);
  }

  return server.psql.connection.transaction(async (transaction) => {
    return await callback(transaction);
  });
};
