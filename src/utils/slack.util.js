import axiosInstance from '#src/utils/axios.util.js';

/**
 * Sends a message to a Slack channel using a webhook.
 *
 * @param {Object} options - Options for sending the Slack message.
 * @param {string} options.webhook - Slack webhook URL.
 * @param {string} [options.content='No content provided.'] - Message content.
 * @param {Object} [axios] - Optional Axios instance (for testing)
 * @returns {Promise<Object>} - { success: true } on success.
 * @throws {Error} - Throws an error if the webhook responds with an error.
 */
export const sendSlackMessage = async (
  { webhook, content = 'No content provided.' },
  axios = axiosInstance(),
) => {
  let requestHeaders;
  axios.interceptors.request.use((config) => {
    requestHeaders = config.headers;
    return config;
  });
  try {
    const response = await axios.post(webhook, { text: content });

    if (response.status === 200) {
      return { success: true, requestHeaders };
    }
    const data = response.data || {};
    const error = new Error(data.message);
    error.success = false;
    error.requestHeaders = requestHeaders;
  } catch (err) {
    if (err.response?.data) {
      const error = new Error(err.response.data);
      error.success = false;
      error.requestHeaders = requestHeaders;

      throw error;
    }
    const error = new Error(err.message);
    error.success = false;
    error.requestHeaders = requestHeaders;

    throw error;
  }
};
