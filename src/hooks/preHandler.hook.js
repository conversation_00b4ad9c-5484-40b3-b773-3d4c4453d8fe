import fp from 'fastify-plugin';

import { CoreError } from '#src/modules/core/errors/index.js';
import { AccessControlError } from '#src/modules/setting/errors/index.js';
import { bypassAccessCheck, checkAccess, enforceIPWhitelist } from '#src/utils/access.util.js';
import { checkPolicies } from '#src/utils/role-policies.util.js';

/**
 * preHandler hook
 *
 * This hook is executed just before the handler is executed.
 * It's useful for performing operations that should happen right before
 * the route handler, such as input validation or authentication checks.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const preHandlerHook = (fastify, options) => {
  fastify.addHook('preHandler', async (request, reply) => {
    fastify.log.debug('Executing preHandler hook');

    const routeConfig = request.routeOptions?.config;
    const routeAccess = request.routeOptions?.config?.access || {};
    const authAccess = request.authInfo?.authAccess;

    // Check if the route is marked as public or if the user has access to it
    if (!bypassAccessCheck(request)) {
      if (!checkAccess(request, authAccess, routeAccess)) {
        throw CoreError.forbidden({ resource: 'common.label.resource' });
      }

      if (authAccess === 'webhook' && routeAccess.ipWhitelist) {
        const ipAllowed = enforceIPWhitelist(request, routeAccess.ipWhitelist);
        if (!ipAllowed) {
          throw AccessControlError.ipBlocked();
        }
      }
    }

    await checkPolicies(request, fastify, routeConfig);

    if (
      request.body &&
      ['POST', 'PUT', 'PATCH'].includes(request.method) &&
      request.headers['content-type']?.includes('application/json')
    ) {
      const ignoreFields = routeConfig?.sanitiseHtml?.ignoreFields || [];
      const customPurifyOpts = routeConfig?.sanitiseHtml?.options || {};
      request.body = fastify.sanitiseData(request.body, ignoreFields, customPurifyOpts);
    }
  });
};

export default fp(preHandlerHook, {
  name: 'preHandlerHook',
});
