import fp from 'fastify-plugin';

/**
 * preParsing hook
 *
 * This hook is executed before the request body is parsed.
 * It can be used to modify the stream or add headers before parsing.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const preParsingHook = (fastify, options) => {
  fastify.addHook('preParsing', async (request, reply, payload) => {
    fastify.log.debug('Executing preParsing hook');
    // Add your preParsing logic here
    return payload;
  });
};

export default fp(preParsingHook, {
  name: 'preParsingHook',
});
