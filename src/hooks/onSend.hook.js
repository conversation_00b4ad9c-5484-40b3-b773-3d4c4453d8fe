import fp from 'fastify-plugin';

import { modifyPayload } from '#src/utils/response-modifier.util.js';

/**
 * onSend hook
 *
 * This hook is executed just before sending the response payload back to the client.
 * It can be used for modifying the response payload or adding debug information.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 * @returns {void}
 */
const onSendHook = (fastify, options) => {
  fastify.addHook('onSend', async (request, reply, payload) => {
    fastify.log.debug('Executing onSend hook');

    const modifiedPayload = modifyPayload(fastify, request, reply, payload);

    if (modifiedPayload !== payload) {
      // The payload was modified, so we update the request.responsePayload for logging purposes in JSON object format
      try {
        request.responsePayload = JSON.parse(modifiedPayload);
      } catch (error) {
        fastify.log.error(error, 'Failed to parse modified payload for logging');
      }
    }

    return modifiedPayload;
  });
};

export default fp(onSendHook, {
  name: 'onS<PERSON><PERSON><PERSON>',
});
