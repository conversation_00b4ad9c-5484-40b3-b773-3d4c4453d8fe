import fp from 'fastify-plugin';

/**
 * onListen hook
 *
 * This hook is executed when the server starts listening for incoming connections.
 * It can be used for logging or performing operations after the server starts.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onListenHook = (fastify, options) => {
  fastify.addHook('onListen', async () => {
    fastify.log.debug('Executing onListen hook');
    // Add your onListen logic here
  });
};

export default fp(onListenHook, {
  name: 'onListenHook',
});
