import fp from 'fastify-plugin';

/**
 * onClose hook
 *
 * This hook is executed when the server is closing.
 * It can be used for cleanup operations or closing database connections.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onCloseHook = (fastify, options) => {
  fastify.addHook('onClose', async (instance) => {
    try {
      // Perform any cleanup tasks here
      // Example: Close database connections, clear queues, etc.

      // For Unit Test only
      if (typeof fastify.mockErrorForUnitTest === 'function') {
        await fastify.mockErrorForUnitTest();
      }
    } catch (error) {
      fastify.log.error(error, 'Error during shutdown');
      throw error;
    }
  });
};

export default fp(onCloseHook, {
  name: 'onCloseHook',
});
