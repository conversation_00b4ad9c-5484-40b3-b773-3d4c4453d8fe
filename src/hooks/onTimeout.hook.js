import fp from 'fastify-plugin';

/**
 * onTimeout hook
 *
 * This hook is executed when a request times out.
 * It can be used for logging or performing cleanup operations.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onTimeoutHook = (fastify, options) => {
  fastify.addHook('onTimeout', async (request, reply) => {
    fastify.log.debug('Executing onTimeout hook');
    // Add your onTimeout logic here
  });
};

export default fp(onTimeoutHook, {
  name: 'onTimeoutHook',
});
