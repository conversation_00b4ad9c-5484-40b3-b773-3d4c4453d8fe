import fp from 'fastify-plugin';

/**
 * onRequestAbort hook
 *
 * This hook is executed when a client closes the connection before the response is sent.
 * It can be used for logging or performing cleanup operations.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onRequestAbortHook = (fastify, options) => {
  fastify.addHook('onRequestAbort', async (request) => {
    fastify.log.debug('Executing onRequestAbort hook');
    // Add your onRequestAbort logic here
  });
};

export default fp(onRequestAbortHook, {
  name: 'onRequestAbortHook',
});
