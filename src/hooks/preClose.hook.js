import fp from 'fastify-plugin';

/**
 * preClose hook
 *
 * This hook is executed before the server starts its closing sequence.
 * It can be used for cleanup operations that need to be done before the server closes.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const preCloseHook = (fastify, options) => {
  fastify.addHook('preClose', async () => {
    fastify.log.debug('Executing preClose hook');
    // Add your preClose logic here
  });
};

export default fp(preCloseHook, {
  name: 'preCloseHook',
});
