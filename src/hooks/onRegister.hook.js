import fp from 'fastify-plugin';

/**
 * onRegister hook
 *
 * This hook is executed when a new plugin is registered.
 * It can be used for logging or modifying plugin options.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onRegisterHook = (fastify, options) => {
  fastify.addHook('onRegister', async (instance, opts) => {
    fastify.log.debug('Executing onRegister hook');
    // Add your onRegister logic here
  });
};

export default fp(onRegisterHook, {
  name: 'onRegisterHook',
});
