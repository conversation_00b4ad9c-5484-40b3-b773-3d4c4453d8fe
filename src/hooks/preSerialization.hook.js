import fp from 'fastify-plugin';

/**
 * preSerialization hook
 *
 * This hook is executed just before the serialization of the response payload.
 * It can be used to modify the payload before it's serialized.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const preSerializationHook = (fastify, options) => {
  fastify.addHook('preSerialization', async (request, reply, payload) => {
    fastify.log.debug('Executing preSerialization hook');
    // Add your preSerialization logic here
    return payload;
  });
};

export default fp(preSerializationHook, {
  name: 'preSerializationHook',
});
