/**
 * Applies audit fields to a Sequelize model by adding hooks for create and update operations.
 * This function sets 'createdBy' and 'updatedBy' fields based on the userId provided in options.
 *
 * @param {Object} Model - The Sequelize model to which audit fields will be applied.
 * @returns {void} This function does not return a value, it modifies the provided Model in-place.
 */
export const applyAuditFields = (Model) => {
  Model.beforeCreate((instance, options) => {
    if (options.authInfoId) {
      instance.set('createdBy', options.authInfoId);
      instance.set('updatedBy', options.authInfoId);
    }
  });
  Model.beforeBulkCreate((instances, options) => {
    if (options.authInfoId) {
      for (const instance of instances) {
        instance.set('createdBy', options.authInfoId);
        instance.set('updatedBy', options.authInfoId);
      }
    }
  });
  Model.beforeUpdate((instance, options) => {
    const changedFields = instance.changed();
    instance['isDirty'] = changedFields && changedFields.length > 0;
    if (options.authInfoId) {
      if (changedFields && changedFields.length > 0) {
        instance.set('updatedBy', options.authInfoId);
      }
    }
  });
  Model.beforeDestroy((instance, options) => {
    if (options.authInfoId) {
      instance.set('deletedBy', options.authInfoId);
    }
  });
};
