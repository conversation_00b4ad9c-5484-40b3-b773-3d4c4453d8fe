import { CoreError } from '#src/modules/core/errors/index.js';

/**
 * Applies versioning to a Sequelize model by adding a hook to handle version conflicts during updates.
 *
 * @param {Object} Model - The Sequelize model to which versioning will be applied.
 * @returns {void} This function does not return a value, but modifies the provided Model.
 *
 * @description
 * This function adds a 'beforeUpdate' hook to the provided Model. The hook performs the following actions:
 * 1. Retrieves the current version from the instance's previous state.
 * 2. Retrieves the requested version from the instance's current state.
 * 3. Checks if the requested version matches the current version.
 * 4. If there's a version mismatch or no requested version, it throws a version conflict error.
 * 5. If versions match, it increments the version number for the update.
 */
export const applyVersioning = (Model) => {
  Model.beforeUpdate((instance, options) => {
    const currentVersion = instance.previous('version');
    const requestVersion = instance.get('version');

    if (!requestVersion || +currentVersion !== +requestVersion) {
      throw CoreError.versionConflict({ version: currentVersion });
    }

    instance.set('version', +currentVersion + 1);
  });
};
