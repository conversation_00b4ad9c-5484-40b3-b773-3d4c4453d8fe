import AutoLoad from '@fastify/autoload';

import routes from '#src/routes.js';
import { getHooksPath, getPluginsPath } from '#src/utils/file.util.js';

// Pass --options via CLI arguments in command to enable these options.
export const options = {
  trustProxy: true,
};

export default async function (fastify, opts) {
  // Register auto-loaded plugins for your app
  fastify.register(AutoLoad, {
    dir: getPluginsPath(),
    options: { ...opts },
  });

  //Register auto-loaded hooks
  fastify.register(AutoLoad, {
    dir: getHooksPath(),
    options: { ...opts },
  });

  // Register routes
  fastify.register(routes);
}
