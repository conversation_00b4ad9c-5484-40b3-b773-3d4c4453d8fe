import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import {
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';

const { COMMON_PROPERTIES, ERROR_RESPONSE, REQ_PARAM_UUID, CREATE_RESPONSE, UPDATE_RESPONSE } =
  CoreSchema;
const { USER_INVITATION_STATUSES } = UserConstant;

const { USER } = MODULE_NAMES;

const TAGS = ['BO / User Management / Account / Invitation '];

/**
 * Response schema properties for user invitation.
 */
const USER_COMMON_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  entityId: { type: 'string', format: 'uuid' },
  metadata: { type: 'object', additionalProperties: true },
};

const USER_RES_PROPERTIES = {
  type: 'object',
  properties: {
    ...USER_COMMON_RES_PROPERTIES,
    userAssociationId: { type: 'string' },
    invitedBy: { type: 'string' },
    invitedDate: { type: 'string' },
    acceptedDate: { type: 'string' },
    cancelledDate: { type: 'string' },
    status: { type: 'string', enum: Object.values(USER_INVITATION_STATUSES) },
  },
};

/**
 * List endpoint schema for user invitation.
 */
export const index = {
  tags: TAGS,
  summary: `Get a list of ${USER} invitation`,
  querystring: {
    type: 'object',
    properties: {
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      filter_status_eq: { type: 'string', enum: Object.values(USER_INVITATION_STATUSES) },
      filter_id_eq: { type: 'string', format: 'uuid' },
      sortBy: {
        oneOf: [{ type: 'string' }, { type: 'array' }],
        description: 'Sorting field in the format (field:order)',
        default: 'invited_date:desc',
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: USER_RES_PROPERTIES,
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * Create endpoint schema for user invitation.
 */

const usernamePattern = '^[A-Za-z0-9]+$';

export const create = {
  tags: TAGS,
  summary: `Create a ${USER} invitation`,
  body: {
    type: 'object',
    properties: {
      username: {
        type: 'string',
        minLength: 3,
        maxLength: 255,
        pattern: usernamePattern,
        default: 'user',
      },
      currencyIds: {
        type: 'array',
        items: { type: 'string', format: 'uuid' },
        uniqueItems: true,
      },
      roleId: { type: 'string', format: 'uuid' },
    },
    required: ['username', 'roleId', 'currencyIds'],
  },
  response: CREATE_RESPONSE(USER_RES_PROPERTIES),
};

/**
 * Update status schema for user.
 */
export const updateStatus = {
  tags: TAGS,
  summary: `Update ${USER} invitation`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      status: { enum: Object.values(USER_INVITATION_STATUSES) },
    },
  },
  response: UPDATE_RESPONSE,
};
