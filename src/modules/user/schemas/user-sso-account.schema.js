import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';

const { COMMON_PROPERTIES, ERROR_RESPONSE, REQ_PARAM_UUID, UPDATE_RESPONSE } = CoreSchema;
const { USER_SSO_STATUSES, USER_MFA_STATUSES } = UserConstant;
const { ORGANISATION, MERCHANT } = CoreConstant.HIERARCHY;
const { USER } = MODULE_NAMES;

const TAGS = ['BO / User Management / Account / SSO Onboarding'];

/**
 * Response schema properties for user invitation.
 */
const USER_COMMON_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  entityId: { type: 'string', format: 'uuid' },
  metadata: { type: 'object', additionalProperties: true },
};

const USER_RES_PROPERTIES = {
  type: 'object',
  properties: {
    ...USER_COMMON_RES_PROPERTIES,
    userId: { type: 'string', format: 'uuid' },
    email: { type: 'string' },
    registeredDate: { type: 'string' },
    completedDate: { type: 'string' },
    cancelledDate: { type: 'string' },
    status: { type: 'string', enum: Object.values(USER_SSO_STATUSES) },
    user: {
      type: 'object',
      properties: {
        username: { type: 'string' },
        name: { type: 'string' },
      },
    },
  },
};

/**
 * List endpoint schema for user invitation.
 */
export const index = {
  tags: TAGS,
  summary: `Get a list of ${USER} SSO Onboarding`,
  querystring: {
    type: 'object',
    properties: {
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      filter_status_eq: { type: 'string', enum: Object.values(USER_SSO_STATUSES) },
      filter_id_eq: { type: 'string', format: 'uuid' },
      sortBy: {
        oneOf: [{ type: 'string' }, { type: 'array' }],
        description: 'Sorting field in the format (field:order)',
        default: 'registeredDate:desc',
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: USER_RES_PROPERTIES,
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * Update status schema for user SSO.
 */
export const updateStatus = {
  tags: TAGS,
  summary: `Update ${USER} user SSO`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      status: { enum: Object.values(USER_SSO_STATUSES) },
    },
  },
  response: UPDATE_RESPONSE,
};

/**
 * assign schema for user SSO.
 */
export const assign = {
  tags: TAGS,
  summary: `Assign ${USER} SSO`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      username: { type: 'string' },
    },
  },
  response: UPDATE_RESPONSE,
};

/**
 * onboard schema for user SSO.
 */
const namePattern = '^[A-Za-z ]+$';
const usernamePattern = '^[A-Za-z0-9]+$';

export const onboard = {
  tags: TAGS,
  summary: `Onboard ${USER} SSO`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 3,
        maxLength: 100,
        pattern: namePattern,
      },
      username: {
        type: 'string',
        minLength: 3,
        maxLength: 100,
        pattern: usernamePattern,
      },
      password: {
        type: 'string',
        minLength: 6,
        maxLength: 100,
      },
      hierarchy: { enum: [ORGANISATION, MERCHANT] },
      roleId: { type: 'string', format: 'uuid' },
      entityId: { type: 'string', format: 'uuid' },
      currencyIds: {
        type: 'array',
        items: { type: 'string', format: 'uuid' },
        uniqueItems: true,
      },
      twoFactorAuth: {
        enum: Object.values(USER_MFA_STATUSES),
      },
      validityDate: { type: 'string', format: 'date-time' },
    },
    required: ['name', 'username', 'password', 'hierarchy', 'roleId', 'entityId', 'twoFactorAuth'],
  },
  response: UPDATE_RESPONSE,
};
