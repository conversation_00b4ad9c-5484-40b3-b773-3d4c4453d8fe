import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
const {
  COMMON_PROPERTIES,
  CREATE_RESPONSE,
  REMOVE_RESPONSE,
  ERROR_RESPONSE,
  REQ_PARAM_UUID,
  UPDATE_RESPONSE,
  VIEW_RESPONSE,
} = CoreSchema;
const { HIERARCHY, COMMON_STATUSES } = CoreConstant;
const POLICIES = [
  'canView',
  'canEdit',
  'canCreate',
  'canImport',
  'canExport',
  'canManage',
  'canMasking',
  'canOverwrite',
  'canVerify',
];

const DEPARTMENT_BASIC_PROPERTIES = {
  name: { type: 'string' },
  description: { type: 'string', nullable: true },
  hierarchy: { type: 'string', enum: Object.values(HIERARCHY) },
  status: { type: 'string', enum: Object.values(COMMON_STATUSES) },
};

const { hierarchy, status, ...UPDATE_REQ_BASIC_INFORMATION } = DEPARTMENT_BASIC_PROPERTIES;

// Shared base properties for all department levels
const BASE_DEPARTMENT_PROPERTIES = {
  id: { type: 'string', format: 'uuid' },
  name: { type: 'string' },
  hierarchy: { type: 'string' },
  translationKey: { type: 'string' },
  level: { type: 'integer' },
  parentId: { type: 'string', format: 'uuid', nullable: true },
  policies: {
    type: 'array',
    nullable: true,
    items: {
      canView: { type: 'boolean' },
      canCreate: { type: 'boolean' },
      canEdit: { type: 'boolean' },
      canImport: { type: 'boolean' },
      canExport: { type: 'boolean' },
      canManage: { type: 'boolean' },
      canMasking: { type: 'boolean' },
      canOverwrite: { type: 'boolean' },
      canVerify: { type: 'boolean' },
    },
    additionalProperties: false,
  },
};

// Leaf node (Level 3)
const DEPARTMENT_ITEM_L3 = {
  type: 'object',
  properties: {
    ...BASE_DEPARTMENT_PROPERTIES,
    children: {
      type: 'array',
      items: { type: 'object', additionalProperties: true },
    },
  },
  required: ['id', 'name', 'hierarchy', 'translationKey', 'level'],
  additionalProperties: false,
};

// Level 2
const DEPARTMENT_ITEM_L2 = {
  type: 'object',
  properties: {
    ...BASE_DEPARTMENT_PROPERTIES,
    children: {
      type: 'array',
      items: DEPARTMENT_ITEM_L3,
    },
  },
  required: ['id', 'name', 'hierarchy', 'translationKey', 'level'],
  additionalProperties: false,
};

// Level 1 (top-level)
const DEPARTMENT_ITEM_L1 = {
  type: 'object',
  properties: {
    ...BASE_DEPARTMENT_PROPERTIES,
    children: {
      type: 'array',
      items: DEPARTMENT_ITEM_L2,
    },
  },
  required: ['id', 'name', 'hierarchy', 'translationKey', 'level'],
  additionalProperties: false,
};

const BODY_REQ_MODULE_POLICY = {
  type: 'array',
  items: {
    type: 'object',
    properties: {
      moduleId: { type: 'string', format: 'uuid' },
      policies: {
        type: 'array',
        items: { type: 'string', enum: POLICIES },
      },
    },
    required: ['moduleId', 'policies'],
  },
};

const VIEW_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  ...DEPARTMENT_BASIC_PROPERTIES,
  modules: {
    type: 'object',
    properties: {
      root: { type: 'array', items: DEPARTMENT_ITEM_L1 },
      organisation: { type: 'array', items: DEPARTMENT_ITEM_L1 },
      merchant: { type: 'array', items: DEPARTMENT_ITEM_L1 },
    },
  },
};

const TAGS = ['BO / User Management / Access Control / Department Templates'];

export const index = {
  tags: TAGS,
  summary: 'List departments',
  querystring: {
    type: 'object',
    properties: {
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      filter_name_iLike: { type: 'string' },
      filter_hierarchy_in: { type: 'string' },
      filter_status_in: { type: 'string' },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              ...COMMON_PROPERTIES,
              ...DEPARTMENT_BASIC_PROPERTIES,
            },
          },
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

export const view = {
  tags: TAGS,
  summary: 'Get department by ID',
  params: REQ_PARAM_UUID,
  response: VIEW_RESPONSE({
    type: 'object',
    properties: VIEW_RES_PROPERTIES,
    additionalProperties: false,
  }),
};

export const create = {
  tags: TAGS,
  summary: 'Create a new department',
  body: {
    type: 'object',
    properties: {
      ...DEPARTMENT_BASIC_PROPERTIES,
      modules: BODY_REQ_MODULE_POLICY,
    },
    required: ['name', 'hierarchy', 'status', 'modules'],
  },
  response: CREATE_RESPONSE({
    type: 'object',
    properties: VIEW_RES_PROPERTIES,
    additionalProperties: false,
  }),
};

export const updateBasicInformation = {
  tags: TAGS,
  summary: 'Update a department basic information',
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: UPDATE_REQ_BASIC_INFORMATION,
  },
  response: UPDATE_RESPONSE,
};

export const updatePolicy = {
  tags: TAGS,
  summary: 'Update a department policy',
  params: REQ_PARAM_UUID,
  body: BODY_REQ_MODULE_POLICY,
  response: UPDATE_RESPONSE,
};

export const updateStatus = {
  tags: TAGS,
  summary: 'Update department status',
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      status: { type: 'string', enum: Object.values(COMMON_STATUSES) },
    },
    required: ['status'],
  },
  response: UPDATE_RESPONSE,
};

export const remove = {
  tags: TAGS,
  summary: 'Remove a department',
  params: REQ_PARAM_UUID,
  response: REMOVE_RESPONSE,
};

export const options = {
  tags: TAGS,
  summary: 'Get available department options',
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            root: { type: 'array', items: DEPARTMENT_ITEM_L1 },
            organisation: { type: 'array', items: DEPARTMENT_ITEM_L1 },
            merchant: { type: 'array', items: DEPARTMENT_ITEM_L1 },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};
