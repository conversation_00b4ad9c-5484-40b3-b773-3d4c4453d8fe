import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';

const { ERROR_RESPONSE, UPDATE_RESPONSE } = CoreSchema;

const TAGS = ['BO / Authentication'];

/**
 * Common user object schema used in authentication responses
 */
const USER_OBJECT_SCHEMA = {
  type: 'object',
  properties: {
    id: { type: 'string', format: 'uuid' },
    email: { type: 'string', format: 'email' },
    username: { type: 'string' },
    status: { type: 'string', enum: Object.values(UserConstant.USER_STATUSES) },
    entityId: { type: 'string', format: 'uuid' },
  },
};

/**
 * Common success response schema for authentication endpoints
 */
const AUTH_SUCCESS_RESPONSE = {
  description: 'Success response',
  type: 'object',
  properties: {
    message: { type: 'string', description: 'Success message' },
    data: {
      type: 'object',
      properties: {
        token: { type: 'string', description: 'JWT authentication token' },
        user: USER_OBJECT_SCHEMA,
        accessId: { type: 'string' },
        qrCode: { type: 'string' },
        preAuthToken: { type: 'string' },
        requiresTwoFactor: { type: 'boolean' },
      },
    },
  },
};

/**
 * Login endpoint schema.
 */
export const login = {
  tags: TAGS,
  summary: 'User authentication',
  description: 'Authenticate a user and return a JWT token',
  body: {
    type: 'object',
    properties: {
      username: { type: 'string' },
      password: { type: 'string' },
      accessId: { type: 'string' },
      'h-captcha-response': { type: 'string' },
    },
    required: ['username', 'password', 'h-captcha-response'],
  },
  response: {
    200: AUTH_SUCCESS_RESPONSE,
    ...ERROR_RESPONSE,
  },
};

/**
 * Google OAuth authentication schema
 */
export const google = {
  tags: TAGS,
  summary: 'Authenticate user with Google credentials',
  description: 'Google OAuth login',
  body: {
    type: 'object',
    properties: {
      googleToken: { type: 'string' },
      accessId: { type: 'string' },
    },
    required: ['googleToken'],
  },
  response: {
    200: AUTH_SUCCESS_RESPONSE,
    ...ERROR_RESPONSE,
  },
};

/**
 * Two-factor authentication setup endpoint schema.
 * Generates QR code and secret for TOTP setup.
 */
export const setup2fa = {
  tags: TAGS,
  summary: 'Initialize two-factor authentication setup',
  description: 'Generate and return the necessary data to begin 2FA setup',
  response: {
    200: {
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            qrCode: { type: 'string' },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * Two-factor authentication verification endpoint schema.
 * Completes login process using pre-auth token and TOTP code.
 */
export const verify2faLogin = {
  tags: TAGS,
  summary: 'Verify two-factor authentication',
  description: 'Complete login using pre-auth token and TOTP',
  body: {
    type: 'object',
    required: ['preAuthToken', 'token'],
    properties: {
      token: {
        type: 'string',
        pattern: '^[0-9]{6}$',
        description: '6-digit TOTP token',
      },
      preAuthToken: {
        type: 'string',
        description: 'Short-lived pre-authentication token issued after password verification',
      },
      trustDevice: { type: 'boolean' },
    },
  },
  response: {
    200: AUTH_SUCCESS_RESPONSE,
    ...ERROR_RESPONSE,
  },
};

/**
 * Revoke trusted devices endpoint schema.
 * Removes all trusted device cookies for the authenticated user,
 */
export const revokeTrustedDevices = {
  tags: TAGS,
  summary: 'Revoke all trusted devices',
  description:
    'Revokes all trusted devices for the authenticated user, requiring 2FA on next login from any device',
  response: UPDATE_RESPONSE,
};

/**
 * Reset two-factor authentication endpoint schema.
 * Resets 2FA settings for a user, generating a new secret key and setting status to inactive.
 */
export const reset2fa = {
  tags: TAGS,
  summary: 'Reset two-factor authentication',
  description: 'Reset 2FA for the authenticated user after token verification',
  body: {
    type: 'object',
    required: ['userId'],
    properties: {
      userId: { type: 'string', format: 'uuid' },
    },
  },
  response: {
    200: {
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid', description: 'MFA setting ID' },
            userId: { type: 'string', format: 'uuid', description: 'User ID' },
            setupStatus: { type: 'string', description: 'MFA setup status' },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

export default {
  login,
  google,
  setup2fa,
  verify2faLogin,
  reset2fa,
};
