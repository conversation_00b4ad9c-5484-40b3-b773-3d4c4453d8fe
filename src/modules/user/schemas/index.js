import * as AuthSchema from '#src/modules/user/schemas/auth.schema.js';
import * as DepartmentTemplateSchema from '#src/modules/user/schemas/department-template.schema.js';
import * as DepartmentSchema from '#src/modules/user/schemas/department.schema.js';
import * as RoleSchema from '#src/modules/user/schemas/role.schema.js';
import * as UserInvitationSchema from '#src/modules/user/schemas/user-invitation.schema.js';
import * as UserSSOAccountSchema from '#src/modules/user/schemas/user-sso-account.schema.js';
import * as UserSchema from '#src/modules/user/schemas/user.schema.js';

export {
  AuthSchema,
  DepartmentSchema,
  DepartmentTemplateSchema,
  UserInvitationSchema,
  UserSSOAccountSchema,
  UserSchema,
  RoleSchema,
};
