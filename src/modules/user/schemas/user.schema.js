import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { RegexConstant } from '#src/modules/core/constants/index.js';
import {
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';

const {
  COMMON_PROPERTIES,
  ERROR_RESPONSE,
  REQ_PARAM_UUID,
  CREATE_RESPONSE,
  UPDATE_RESPONSE,
  VIEW_RESPONSE,
} = CoreSchema;
const { USER_ORIGINS, USER_TYPES, USER_STATUSES, USER_MFA_STATUSES, USER_INVITATION_STATUSES } =
  UserConstant;

const { USER } = MODULE_NAMES;

const TAGS = ['BO / User Management / Account / User '];

/**
 * Response schema properties for user entity.
 */
const USER_COMMON_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  entityId: { type: 'string', format: 'uuid' },
  metadata: { type: 'object', additionalProperties: true },
};

const USER_RES_PROPERTIES = {
  type: 'object',
  properties: {
    ...USER_COMMON_RES_PROPERTIES,
    name: { type: 'string' },
    username: { type: 'string' },
    type: { type: 'string', enum: Object.values(USER_TYPES) },
    parentId: { type: 'string', format: 'uuid' },
    validityDate: { type: 'string', format: 'date-time' },
    status: { type: 'string', enum: Object.values(USER_STATUSES) },
    userInvitationStatus: { type: 'string', enum: Object.values(USER_INVITATION_STATUSES) },
    mfaStatus: { type: 'string', enum: Object.values(USER_MFA_STATUSES) },
    currencyCodes: { type: 'array', items: { type: 'string' } },
    origin: { type: 'string', enum: Object.values(USER_ORIGINS) },
    hierarchy: { type: 'string' },
    entityId: { type: 'string', format: 'uuid' },
    roleId: { type: 'string', format: 'uuid' },
    roleName: { type: 'string' },
    departmentName: { type: 'string' },
  },
};

const USER_VIEW_PROPERTIES = {
  ...USER_RES_PROPERTIES,
  properties: {
    ...USER_RES_PROPERTIES.properties,
    email: { type: 'string', format: 'email' },
  },
};

/**
 * List endpoint schema for user.
 */
export const index = {
  tags: TAGS,
  summary: `Get a list of ${USER}`,
  querystring: {
    type: 'object',
    properties: {
      filter_id_eq: { type: 'string', format: 'uuid' },
      filter_name_iLike: { type: 'string' },
      filter_username_iLike: { type: 'string' },
      filter_status_in: { type: 'string' },
      'filter_ua.uac.currencyId_in': {
        type: 'string',
      },
      //role and department can be added here after the module is done
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      sortBy: {
        oneOf: [{ type: 'string' }, { type: 'array' }],
        description: 'Sorting field in the format (field:order)',
        default: 'name:asc',
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: USER_RES_PROPERTIES,
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * List endpoint schema for user.
 */
export const subAccountIndex = {
  tags: TAGS,
  summary: `Get a list of ${USER} sub account`,
  querystring: {
    type: 'object',
    properties: {
      filter_id_eq: { type: 'string', format: 'uuid' },
      filter_name_iLike: { type: 'string' },
      filter_username_iLike: { type: 'string' },
      filter_status_in: { type: 'string' },
      //role and department can be added here after the module is done
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      sortBy: {
        oneOf: [{ type: 'string' }, { type: 'array' }],
        description: 'Sorting field in the format (field:order)',
        default: 'name:asc',
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: USER_RES_PROPERTIES,
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * View endpoint schema for user.
 */
export const view = {
  tags: TAGS,
  summary: `View a ${USER}`,
  params: REQ_PARAM_UUID,
  response: VIEW_RESPONSE(USER_VIEW_PROPERTIES),
};

/**
 * Create endpoint schema for user.
 */
const namePattern = RegexConstant.NAME_REGEX;
const usernamePattern = RegexConstant.USERNAME_REGEX.NORMAL;

export const create = {
  tags: TAGS,
  summary: `Create a ${USER}`,
  body: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 3,
        maxLength: 100,
        pattern: namePattern,
      },
      username: {
        type: 'string',
        minLength: 3,
        maxLength: 100,
        pattern: usernamePattern,
      },
      password: {
        type: 'string',
        minLength: 6,
        maxLength: 100,
      },
      type: { enum: Object.values(USER_TYPES) },
      email: { type: 'string', maxLength: 100, format: 'email' },
      roleId: { type: 'string', format: 'uuid' },
      currencyIds: {
        type: 'array',
        items: { type: 'string', format: 'uuid' },
        uniqueItems: true,
      },
      parentId: { type: 'string', format: 'uuid' },
      twoFactorAuth: {
        enum: Object.values(USER_MFA_STATUSES),
      },
      validityDate: { type: 'string', format: 'date-time' },
    },
    required: ['username', 'password', 'type', 'roleId'],
  },
  response: CREATE_RESPONSE(USER_RES_PROPERTIES),
};

/**
 * Update basic information schema for user.
 */
export const updateBasicInformation = {
  tags: TAGS,
  summary: `Update ${USER} basic information`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        minLength: 3,
        maxLength: 100,
        pattern: namePattern,
      },
      email: { type: 'string', maxLength: 100, format: 'email' },
    },
  },
  response: UPDATE_RESPONSE,
};

/**
 * Update organisation schema for user.
 */
export const updateOrganisation = {
  tags: TAGS,
  summary: `Update ${USER} organisation`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      roleId: { type: 'string', format: 'uuid' },
      currencyIds: {
        type: 'array',
        items: { type: 'string', format: 'uuid' },
        uniqueItems: true,
      },
    },
  },
  response: UPDATE_RESPONSE,
};

/**
 * Update login access schema for user.
 */
export const updateLoginAccess = {
  tags: TAGS,
  summary: `Update ${USER} login access`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      twoFactorAuth: {
        enum: Object.values(USER_MFA_STATUSES),
      },
      validityDate: { type: 'string', format: 'date-time' },
    },
  },
  response: UPDATE_RESPONSE,
};

/**
 * Update status schema for user.
 */
export const updateStatus = {
  tags: TAGS,
  summary: `Update ${USER} status`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      status: { enum: Object.values(USER_STATUSES) },
    },
  },
  response: UPDATE_RESPONSE,
};

/**
 * Get options endpoint for user.
 */
export const options = {
  tags: TAGS,
  summary: 'Get available user options',
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
          additionalProperties: true,
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * check username availability endpoint for user.
 */
export const checkAvailability = {
  tags: TAGS,
  summary: 'Check username availability',
  querystring: {
    type: 'object',
    properties: {
      username: { type: 'string' },
    },
    required: ['username'],
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};
