import {
  create as templateCreate,
  index as templateIndex,
  options as templateOptions,
  remove as templateRemove,
  updateBasicInformation as templateUpdateBasicInformation,
  updatePolicy as templateUpdatePolicy,
  updateStatus as templateUpdateStatus,
  view as templateView,
} from '#src/modules/user/schemas/department-template.schema.js';

const TAGS = ['BO / User Management / Access Control / Departments'];

export const index = {
  ...templateIndex,
  tags: TAGS,
};

export const view = {
  ...templateView,
  tags: TAGS,
};

export const create = {
  ...templateCreate,
  tags: TAGS,
};

export const updateBasicInformation = {
  ...templateUpdateBasicInformation,
  tags: TAGS,
};

export const updatePolicy = {
  ...templateUpdatePolicy,
  tags: TAGS,
};

export const updateStatus = {
  ...templateUpdateStatus,
  tags: TAGS,
};

export const remove = {
  ...templateRemove,
  tags: TAGS,
};

export const options = {
  ...templateOptions,
  tags: TAGS,
};
