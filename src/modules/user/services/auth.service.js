import { OAuth2Client } from 'google-auth-library';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { SettingRepository } from '#src/modules/setting/repository/index.js';
import { AuthConstant, UserConstant } from '#src/modules/user/constants/index.js';
import { AuthError } from '#src/modules/user/errors/index.js';
import {
  UserMfaSettingRepository,
  UserRepository,
  UserSSOAccountRepository,
} from '#src/modules/user/repository/index.js';
import { AuthValidations } from '#src/modules/user/validations/index.js';
import {
  fetchFromCache,
  generateCacheKey,
  clearCacheWithPrefix,
  setCacheWithTTL,
} from '#src/utils/cache.util.js';
import { signJWT } from '#src/utils/jwt.util.js';
import { handle2FASetup, validateTOTPToken, generate2FASecret } from '#src/utils/twofa.util.js';

const { EXPIRY_TIMES, KEYS, SECURITY, SECONDS } = AuthConstant;
const { USER_SSO_STATUSES, USER_MFA_SETUP_STATUSES, USER_ORIGINS } = UserConstant;

/**
 * Authenticates a user and handles 2FA setup/verification flow.
 * @param {Object} request - The request object containing login credentials.
 * @returns {Promise<Object>} A promise that resolves to the authentication result or 2FA setup/verification requirement.
 * @throws {AuthError} If authentication fails due to invalid credentials, inactive account, or other security checks.
 */
export const login = async (request) => {
  const { body, server, ip, headers } = request;
  const { username, password, accessId } = body;

  // 1) Protect endpoint from brute-force attacks: IP-based rate limit
  await AuthValidations.checkIpRateLimiting(server, ip);

  // 2) Risk pre-check — block immediately if risk is too high (e.g., suspicious bot)
  if (request.riskLevel === CoreConstant.HCAPTCHA_RISK_LEVELS.HIGH) {
    throw AuthError.forbidden();
  }

  // 3) Look up the user record by username
  const user = await findUserByCredentials(server, { username });

  // 4) Validate system availability
  await AuthValidations.checkSystemStatus(server, user);

  // 5) Validate the user's password and log IP for auditing
  await AuthValidations.validatePassword(server, user, password, ip);

  // 6) Make sure the account status is valid (active / not suspended)
  AuthValidations.validateUserStatus(user);

  // 7) Check 2FA status and determine required action with trusted device support
  const twoFactorResponse = await AuthValidations.validate2FA(server, user, request, headers);
  if (twoFactorResponse) {
    const preAuthToken = await generatePreAuthToken(user.id, ip, accessId, server);
    return { ...twoFactorResponse, preAuthToken };
  }

  // 8) Verify accessId permissions and retrieve entity details
  const entityData = await AuthValidations.validateAccessAndGetEntityData(server, user, accessId);

  // 9) Build and return the final authentication response (e.g., token, profile)
  return buildAuthResponse(server, user, entityData, headers?.fingerprint);
};

/**
 * Authenticates a user using Google OAuth and handles 2FA setup/verification flow
 * @param {Object} request - The request object containing Google token
 * @returns {Promise<Object>} A promise that resolves to the authentication result or 2FA setup/verification requirement
 * @throws {AuthError} If authentication fails due to invalid credentials, inactive account, or other security checks
 */
export const google = async (request) => {
  const { body, server, ip, headers } = request;
  const { googleToken, accessId } = body;

  // Protect endpoint from brute-force: check IP-based rate limit
  await AuthValidations.checkIpRateLimiting(server, ip);

  // Verify the Google ID token and extract user info (email, sub, domain, etc.)
  const googleUserInfo = await verifyGoogleToken(server, googleToken);

  // Make sure the email's domain is allowed for SSO login
  await AuthValidations.validateEmailDomain(server, googleUserInfo.emailDomain);

  // Look up an existing user and their SSO account by email / Google sub
  const user = await findUserByCredentials(server, { email: googleUserInfo.email });
  const ssoAccount = await UserSSOAccountRepository.findOne(server, { sub: googleUserInfo.sub });

  // If no user exists yet, handle self-registration or account creation
  if (!user) {
    return handleNewUserRegistration(server, ssoAccount, googleUserInfo);
  }

  // Validate system availability
  await AuthValidations.checkSystemStatus(server, user);

  // Validate the user's status (e.g., active, not suspended)
  AuthValidations.validateUserStatus(user);

  // Ensure SSO account is correctly linked with the local user
  await linkUserAccount(server, ssoAccount, user, googleUserInfo);

  // Confirm the user has access to the given accessId and get entity details
  const entityData = await AuthValidations.validateAccessAndGetEntityData(server, user, accessId);

  // Build and return the final authentication response (JWT, profile, etc.)
  return buildAuthResponse(server, user, entityData, headers?.fingerprint);
};

/**
 * Initializes two-factor authentication setup for the authenticated user.
 * Generates and returns the necessary secret key and QR code for setting up
 * an authenticator app.
 *
 * @param {Object} request - The request object containing user authentication data.
 * @returns {Promise<Object>} A promise that resolves to the 2FA setup data with secret and QR code.
 * @throws {CoreError} If user is not found or already has 2FA enabled.
 */
export const setup2fa = async (request) => {
  const { server } = request;
  const userId = request.authInfo.id;

  // Find the user by ID
  const user = await UserRepository.findById(server, userId);
  if (!user) {
    throw CoreError.dataNotFound({
      data: 'common.label.user',
      attribute: 'ID',
      value: userId,
    });
  }

  // If user already setup 2FA, throw an error
  if (user.mfa?.setupStatus === USER_MFA_SETUP_STATUSES.ACTIVE) {
    throw AuthError.twoFactorAlreadySetup();
  }

  // Generate QR code from the secret key
  return { qrCode: await handle2FASetup(user, user.mfa) };
};

/**
 * Verifies 2FA token during login process and completes authentication
 * Handles both verification of existing 2FA and completion of 2FA setup
 * @param {Object} request - The request object containing verification data
 * @returns {Promise<Object>} A promise that resolves to the authentication result with JWT token
 * @throws {AuthError} If verification fails or user is not found
 */
export const verify2faLogin = async (request) => {
  const { body, server, headers, ip } = request;
  const { preAuthToken, token, trustDevice = false } = body;
  const { userId, accessId } = await AuthValidations.validatePreAuthToken(preAuthToken, ip, server);

  const user = await UserRepository.findById(server, userId);
  if (!user) {
    throw CoreError.dataNotFound({
      data: 'common.label.user',
      attribute: 'ID',
      value: userId,
    });
  }

  const userMFA = user.mfa;
  validateTOTPToken(userMFA.secretKey, token);

  // If 2FA is not yet enabled, this is a setup completion
  if (!userMFA.isEnabled) {
    // Enable 2FA
    await UserMfaSettingRepository.update(userMFA, {
      setupStatus: USER_MFA_SETUP_STATUSES.ACTIVE,
    });
  }

  // Complete authentication flow
  const entityData = await AuthValidations.validateAccessAndGetEntityData(server, user, accessId);
  const authResponse = buildAuthResponse(server, user, entityData, headers?.fingerprint);

  // Handle device trust via cookie if requested
  if (trustDevice && headers?.fingerprint) {
    authResponse.setCookie = await createTrustedDeviceCookie(server, user.id, headers.fingerprint);
  }

  return authResponse;
};

/**
 * Reset two-factor authentication for the authenticated user.
 *
 * @param {Object} request - The request object containing user authentication data.
 * @returns {Promise<Object>} A promise that resolves to a success response.
 * @throws {CoreError} If user is not found.
 * @throws {AuthError} If user doesn't have 2FA enabled or verification fails.
 */
export const reset2fa = async (request) => {
  const { body, server, authInfo, entity } = request;
  const { userId } = body;

  // Find the user with their associations to check entity access
  const user = await UserRepository.findById(server, userId);

  // Check if user belongs to the requested entity
  const userEntityIds = user?.ua?.map((association) => association.entityId);
  if (!user || !userEntityIds.includes(entity.id)) {
    throw CoreError.dataNotFound({
      data: 'common.label.user',
      attribute: 'User ID',
      value: userId,
    });
  }

  // Use the already-loaded MFA settings from the user record
  const userMfa = user?.mfa;
  if (!userMfa) {
    throw CoreError.dataNotFound({
      data: 'common.label.userMfaSetting',
      attribute: 'User ID',
      value: userId,
    });
  }

  // Check if user already setup 2FA
  if (userMfa?.setupStatus !== USER_MFA_SETUP_STATUSES.ACTIVE) {
    throw AuthError.twoFactorNotSetup();
  }

  // Disable 2FA and generate new secret key
  const { base32secret } = await generate2FASecret();
  await UserMfaSettingRepository.update(
    userMfa,
    {
      setupStatus: USER_MFA_SETUP_STATUSES.INACTIVE,
      secretKey: base32secret,
    },
    { authInfoId: authInfo.id },
  );

  // Revoke trusted devices and capture any cookie-setting result
  const revokeResult = await revokeTrustedDevices(request);

  // Return both updated MFA record and cookie instructions to the handler
  return {
    userMfa,
    ...(revokeResult?.setCookie && { setCookie: revokeResult.setCookie }),
  };
};

/**
 * Finds a user by identifier (username or email) with appropriate access constraints
 * @param {Object} server - The server instance
 * @param {Object} options - Search options
 * @returns {Promise<Object>} The user object if found
 * @throws {AuthError} If no user is found with the specified criteria
 */
const findUserByCredentials = async (server, { username, email }) => {
  let user = null;
  const query = {
    'filter_ua.origin_eq': USER_ORIGINS.INTERNAL,
  };

  if (username) {
    query.filter_username_eq = username;
  } else if (email) {
    query.filter_email_eq = email;
  }

  user = await UserRepository.findUser(server, query, true);

  if (!user && username) {
    throw AuthError.invalidCredentials();
  }

  return user;
};

/**
 * Generates a JWT authentication token
 * @param {Object} server - The server instance
 * @param {Object} user - The user object
 * @param {Object} assoc - The user association object
 * @param {Object} targetEntity - The target entity object
 * @param {string} fingerprintId - The fingerprint ID from request headers
 * @returns {string} The JWT token
 */
const generateAuthToken = (server, user, assoc, targetEntity, fingerprintId) => {
  const now = Math.floor(Date.now() / 1000);

  const parentEntity = targetEntity?.parent;
  const userEntity = assoc.entity;

  const payload = {
    iat: now,
    exp: now + EXPIRY_TIMES.TOKEN,
    sub: user.id,

    basicInformation: {
      authInfo: {
        id: user.id,
        authAccess: 'user',
        username: user.username,
        roleId: assoc.roleId,
        fingerprintId,
      },
      userEntity: userEntity
        ? {
            id: userEntity.id,
            hierarchy: userEntity.hierarchy,
            code: userEntity.code,
            prefix: userEntity.prefix,
            name: userEntity.name,
          }
        : null,
      parentEntity: parentEntity
        ? {
            id: parentEntity.id,
            hierarchy: parentEntity.hierarchy,
            code: parentEntity.code,
            prefix: parentEntity.prefix,
            name: parentEntity.name,
          }
        : null,
      entity: targetEntity
        ? {
            id: targetEntity.id,
            hierarchy: targetEntity.hierarchy,
            code: targetEntity.code,
            prefix: targetEntity.prefix,
            name: targetEntity.name,
          }
        : null,
    },
  };

  return signJWT(server, payload);
};

/**
 * Verifies a Google ID token and extracts user information
 * @param {Object} server - The server instance
 * @param {string} googleToken - The Google ID token to verify
 * @returns {Promise<Object>} A promise that resolves to the user information from the token
 * @throws {AuthError} If token verification fails or required fields are missing
 */
const verifyGoogleToken = async (server, googleToken) => {
  const CLIENT_ID = '407408718192.apps.googleusercontent.com'; // Get from app center
  const client = new OAuth2Client(CLIENT_ID);
  let ticket;
  let payload;

  try {
    ticket = await client.verifyIdToken({
      idToken: googleToken,
      audience: CLIENT_ID,
    });

    payload = ticket.getPayload();
  } catch (error) {
    throw AuthError.googleAuthError({ message: error.message });
  }

  // Validate required fields
  if (!payload?.email || !payload?.sub) {
    throw AuthError.googleAuthError({ message: 'error.auth.sentence.payloadTokenMissing' });
  }

  // Issuer validation - ensure token is from Google
  if (!SECURITY.VALID_ISSUERS.includes(payload.iss)) {
    throw AuthError.googleAuthError({ message: 'error.auth.sentence.invalidTokenIssuer' });
  }

  // Email verification check
  if (payload.email_verified !== true) {
    throw AuthError.googleAuthError({ message: 'error.auth.sentence.emailNotVerified' });
  }

  return {
    email: payload.email,
    sub: payload.sub,
    emailDomain: payload.hd,
  };
};

/**
 * Handles registration for new users authenticating with Google
 * @param {Object} server - The server instance
 * @param {Object|null} ssoAccount - The existing SSO account if found
 * @param {Object} googleUserInfo - The Google user information
 * @returns {Promise<Object>} A promise that resolves to the registration status
 * @throws {AuthError} If an SSO account exists but is pending
 */
const handleNewUserRegistration = async (server, ssoAccount, googleUserInfo) => {
  if (ssoAccount) {
    throw AuthError.ssoAccountPending();
  }

  const newSSOAccount = await UserSSOAccountRepository.create(server, {
    sub: googleUserInfo.sub,
    email: googleUserInfo.email,
    status: USER_SSO_STATUSES.PENDING,
    registeredDate: new Date(),
  });

  return {
    status: USER_SSO_STATUSES.PENDING,
    ssoAccountId: newSSOAccount.id,
  };
};

/**
 * Links a user account with their SSO account information
 * @param {Object} server - The server instance
 * @param {Object|null} ssoAccount - The existing SSO account if found
 * @param {Object} user - The user object to link
 * @param {Object} googleUserInfo - The Google user information
 * @returns {Promise<void>} A promise that resolves when the account is linked
 */
const linkUserAccount = async (server, ssoAccount, user, googleUserInfo) => {
  const now = new Date();

  if (ssoAccount) {
    if (!ssoAccount.user_id) {
      await UserSSOAccountRepository.update(ssoAccount, {
        userId: user.id,
        status: USER_SSO_STATUSES.COMPLETED,
        completedDate: now,
      });
    }
  } else {
    await UserSSOAccountRepository.create(server, {
      userId: user.id,
      sub: googleUserInfo.sub,
      email: googleUserInfo.email,
      status: USER_SSO_STATUSES.COMPLETED,
      registeredDate: now,
      completedDate: now,
    });
  }
};

/**
 * Creates a short-lived pre-auth token for a user.
 * The token is valid for 5 minutes and contains the user ID, access ID, issued at time, expiration time, type, and IP address.
 * @param {string} userId - The user ID
 * @param {string} ip - The IP address of the user
 * @param {string} accessId - The access ID for the user
 * @param {Object} server - The server instance
 * @returns {Promise<string>} A promise that resolves to the pre-auth token
 */
export const generatePreAuthToken = async (userId, ip, accessId, server) => {
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    sub: userId,
    accessId: accessId,
    iat: now,
    exp: now + EXPIRY_TIMES.PRE_AUTH_TOKEN, // 5 minutes
    type: KEYS.PRE_AUTH_TOKEN_NAME,
    ip,
  };

  return server.jwt.sign(payload);
};

/**
 * Builds a consistent authentication response for all login flows
 * @param {Object} server - The server instance
 * @param {Object} user - The authenticated user
 * @param {Object} entityData - The entity data object
 * @param {string} fingerprint - The fingerprint ID from request headers
 * @returns {Object} Standardized authentication response with token and user data
 */
const buildAuthResponse = (server, user, entityData = null, fingerprint = null) => {
  const token = generateAuthToken(
    server,
    user,
    entityData.userAssociation,
    entityData.targetEntity,
    fingerprint,
  );

  return {
    token,
    user: user.toJSON(),
    accessId: entityData.targetEntity?.accessId,
  };
};

/**
 * Revokes all trusted devices for the authenticated user
 * @param {Object} request - The request object
 * @returns {Promise<Object>} Success response
 */
export const revokeTrustedDevices = async (request) => {
  const { server } = request;
  const userId = request.authInfo.id;

  // Clear all cached tokens for this user using the prefix
  const cachePrefix = `${KEYS.TRUSTED_DEVICE_COOKIE_NAME}:${userId}`;
  await clearCacheWithPrefix(server.redis.db3, cachePrefix);

  return {
    setCookie: {
      name: KEYS.TRUSTED_DEVICE_COOKIE_NAME,
      value: '',
      options: {
        httpOnly: true,
        secure: true,
        sameSite: 'none',
        maxAge: 0, // Expire immediately
        expires: new Date(0), // Set to epoch time
      },
    },
  };
};

/**
 * Creates an encrypted cookie for trusted device with complete cookie configuration
 * @param {Object} server - The server instance
 * @param {string} userId - The user ID
 * @param {string} fingerprint - Device fingerprint
 * @returns {Object} Object with cookie configuration ready for response
 */
export const createTrustedDeviceCookie = async (server, userId, fingerprint) => {
  const now = Math.floor(Date.now() / 1000);

  const cacheKey = generateCacheKey(
    'trusted_device_settings',
    {
      raw: { url: '/settings/safety/twoFactorSessionTimeoutDays' },
      query: {},
      entity: { id: 'global' },
    },
    { excludeKey: true },
  );

  const trustDeviceSettings = await fetchFromCache(server.redis, cacheKey, async () => {
    return await SettingRepository.getSingleSetting(server, {
      category: 'safety',
      field: 'twoFactorSessionTimeoutDays',
    });
  });

  const trustDays = trustDeviceSettings?.customSettings?.value || EXPIRY_TIMES.DEFAULT_TRUST_DAYS;
  const trustDaysInSecs = trustDays * SECONDS.DAY;
  const expiresAt = now + trustDaysInSecs;

  const payload = {
    userId,
    fingerprint,
    createdAt: now,
    expiresAt,
  };

  const token = signJWT(server, payload);

  // Store the JWT token in cache with the specified key pattern
  const trustedDeviceCacheKey = `${KEYS.TRUSTED_DEVICE_COOKIE_NAME}:${userId}:${fingerprint}`;
  const ttlSeconds = trustDaysInSecs;

  await setCacheWithTTL(server.redis.db3, trustedDeviceCacheKey, token, ttlSeconds);

  return {
    name: KEYS.TRUSTED_DEVICE_COOKIE_NAME,
    value: token,
    options: {
      httpOnly: true,
      secure: true,
      sameSite: 'none',
      maxAge: trustDaysInSecs,
    },
  };
};
