import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { UserValidation } from '#src/modules/core/validations/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import {
  UserAssociationCurrencyRepository,
  UserAssociationRepository,
  UserMfaSettingRepository,
  UserRepository,
  UserSSOAccountRepository,
} from '#src/modules/user/repository/index.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';
import { hashPassword } from '#src/utils/hash.util.js';
import { generate2FASecret } from '#src/utils/twofa.util.js';

// Destructuring constants for easier reference
const { USER_ORIGINS, USER_TYPES, USER_STATUSES, USER_SSO_STATUSES, USER_MFA_STATUSES } =
  UserConstant;
const { ROOT, ORGANISATION, MERCHANT } = CoreConstant.HIERARCHY;

/**
 * Retrieves a list of user SSO records based on the provided query parameters.
 *
 * @param {Object} request - The request object containing necessary data.
 * @param {Object} [options={}] - Additional options for the database operation.
 *
 * @returns {Promise<Object>} A promise that resolves to the list of user SSO records.
 */
export const index = async (request) => {
  const { query, server } = request;
  const res = await UserSSOAccountRepository.findAll(server, query);
  return {
    ...res,
  };
};

/**
 * Updates the status of a user SSO record based on the provided request data.
 *
 * @param {Object} request - The request object containing necessary data.
 * @param {Object} request.body - The body of the request.
 * @param {string} request.body.status - The new status to be set for the user SSO.
 * @param {Object} request.params - The parameters of the request.
 * @param {string} request.params.id - The ID of the user SSO to be updated.
 * @param {Object} request.server - The server instance for database operations.
 * @param {Object} request.authInfo - The authentication information of the requester.
 * @param {string} request.authInfo.id - The ID of the authenticated user making the request.
 * @param {Object} [options={}] - Additional options for the database operation.
 *
 * @returns {Promise<void>} A promise that resolves when the user SSO status is successfully updated.
 * @throws {CoreError} Throws a not found error if the user SSO record does not exist.
 */
export const updateStatus = async (request, options = {}) => {
  const {
    body: { status },
    params: { id: ssoId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  // Query for the SSO record by ID
  const query = {
    filter_id_eq: ssoId,
  };
  const usersso = await UserSSOAccountRepository.findById(server, query);

  // If no matching SSO record, throw not found error
  if (!usersso) {
    throw CoreError.dataNotFound({
      data: 'common.label.userSSO',
      attribute: 'ID',
      value: ssoId,
    });
  }

  // Allowed status transitions map
  const allowed = {
    pending: new Set(['completed', 'cancelled']),
  };

  // Normalise both current and requested statuses to lowercase
  const current = usersso.status.toLowerCase();
  const next = status.toLowerCase();

  // Prevent updating to the same status or an invalid transition
  if (current === next || !allowed[current]?.has(next)) {
    throw CoreError.unprocessable();
  }

  // Prepare update payload
  const data = { status };
  const now = new Date();

  // Attach completion/cancellation timestamps based on new status
  switch (status) {
    case USER_SSO_STATUSES.COMPLETED:
      data.completedDate = now;
      break;
    case USER_SSO_STATUSES.CANCELLED:
      data.cancelledDate = now;
      break;
    default:
      throw CoreError.unprocessable();
  }

  // Perform update (audit with authInfoId)
  await UserSSOAccountRepository.update(usersso, data, { authInfoId });
};

/**
 * Assigns a user to a User SSO record based on the provided request data.
 *
 * @param {Object} request - The request object containing necessary data.
 * @param {Object} request.body - The body of the request.
 * @param {string} request.body.username - The username of the user to be assigned.
 * @param {Object} request.params - The parameters of the request.
 * @param {string} request.params.id - The ID of the user SSO to be assigned.
 * @param {Object} request.server - The server instance for database operations.
 * @param {Object} request.authInfo - The authentication information of the requester.
 * @param {string} request.authInfo.id - The ID of the authenticated user making the request.
 * @param {Object} [options={}] - Additional options for the database operation.
 *
 * @returns {Promise<void>} A promise that resolves when the user is successfully assigned to the User SSO record.
 * @throws {CoreError} Throws a not found error if the user or user SSO record does not exist.
 */

export const assign = async (request, options = {}) => {
  const {
    body: { username },
    params: { id: ssoId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  // Find the user by username
  const queryUser = {
    filter_username_eq: username,
  };
  const user = await UserRepository.findUser(server, queryUser);
  if (!user) {
    throw CoreError.dataNotFound({
      data: 'common.label.user',
      attribute: 'common.label.username',
      value: username,
    });
  }

  // Find the pending SSO record by ID
  const querySSO = {
    filter_id_eq: ssoId,
    filter_status_eq: USER_SSO_STATUSES.PENDING,
  };

  const usersso = await UserSSOAccountRepository.findById(server, querySSO);
  if (!usersso) {
    throw CoreError.dataNotFound({
      data: 'common.label.userSSO',
      attribute: 'ID',
      value: ssoId,
    });
  }

  return withTransaction(server, options, async (t) => {
    // Update SSO to link the user and mark it completed
    const ssoData = {
      userId: user.id,
      completedDate: new Date(),
      status: USER_SSO_STATUSES.COMPLETED,
    };

    await UserSSOAccountRepository.update(usersso, ssoData, { transaction: t, authInfoId });

    // Validate that the email is unique (excluding this user)
    await UserValidation.validateEmail(server, usersso.email, user.id);

    // Update the user’s email to match SSO record
    const userData = {
      email: usersso.email,
    };

    await UserRepository.update(user, userData, { transaction: t, authInfoId });
  });
};

/**
 * Onboards a new user based on the provided request data and associates them with a User SSO record.
 *
 * @param {Object} request - The request object containing necessary data.
 * @param {Object} request.body - The body of the request containing user details.
 * @param {string} request.body.name - The name of the user to be onboarded.
 * @param {string} request.body.username - The desired username for the new user.
 * @param {string} request.body.password - The password for the new user.
 * @param {Array<string>} request.body.currencyIds - The list of currency IDs associated with the user.
 * @param {string} request.body.hierarchy - The hierarchy level of the user (e.g., MERCHANT).
 * @param {string} request.body.roleId - The role ID to be assigned to the user.
 * @param {string} request.body.entityId - The entity ID associated with the user.
 * @param {string} [request.body.type=USER_TYPES.NORMAL] - The type of user being onboarded.
 * @param {string} [request.body.twoFactorAuth=USER__STATUSES.DISABLED] - The 2FA status for the user.
 * @param {Date} [request.body.validityDate] - The validity date for the user's account.
 * @param {Object} request.params - The parameters of the request.
 * @param {string} request.params.id - The ID of the user SSO to be associated with the new user.
 * @param {Object} request.server - The server instance for database operations.
 * @param {Object} request.authInfo - The authentication information of the requester.
 * @param {string} request.authInfo.id - The ID of the authenticated user making the request.
 * @param {Object} [options={}] - Additional options for the database operation.
 *
 * @returns {Promise<void>} A promise that resolves when the user is successfully onboarded and associated with the User SSO record.
 * @throws {CoreError} Throws a not found error if the user SSO record does not exist or if required data is missing.
 */
export const onboard = async (request, options = {}) => {
  // Extract request data
  const {
    body,
    params: { id: ssoId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  // Destructure and set defaults from request body
  const {
    name,
    username,
    password,
    currencyIds,
    hierarchy,
    roleId,
    entityId,
    type = USER_TYPES.NORMAL,
    twoFactorAuth = USER_MFA_STATUSES.DISABLED,
    validityDate,
  } = body;

  // Ensure the SSO record exists and is pending
  const querySSO = {
    filter_id_eq: ssoId,
    filter_status_eq: USER_SSO_STATUSES.PENDING,
  };
  const usersso = await UserSSOAccountRepository.findById(server, querySSO);
  if (!usersso) {
    throw CoreError.dataNotFound({
      data: 'common.label.userSSO',
      attribute: 'ID',
      value: ssoId,
    });
  }

  // Merchant accounts must have currencies assigned
  if (
    hierarchy === MERCHANT &&
    (!currencyIds || !Array.isArray(currencyIds) || currencyIds.length === 0)
  ) {
    throw CoreError.requiredData({ attribute: 'common.label.currency' });
  }

  // Validate email uniqueness
  await UserValidation.validateEmail(server, usersso.email);

  // Validate and normalise username
  const finalUsername = await UserValidation.validateUsername(server, username, type, null);

  // Securely hash the password
  const hashedPassword = await hashPassword(password);

  // Prepare user creation payload
  const userData = {
    name,
    username: finalUsername,
    password: hashedPassword,
    email: usersso.email,
    type,
    validityDate: validityDate ?? null,
    status: USER_STATUSES.ACTIVE,
  };

  return withTransaction(server, options, async (t) => {
    // Create the user record
    const createdUser = await UserRepository.create(server, userData, {
      transaction: t,
      authInfoId,
    });

    // Create the MFA setting for the user with a generated secret
    const { base32secret } = await generate2FASecret();
    await UserMfaSettingRepository.create(
      server,
      {
        userId: createdUser.id,
        secretKey: base32secret,
        status: twoFactorAuth,
      },
      { transaction: t, authInfoId },
    );

    // Create the user-entity-role association
    const userAssoc = await UserAssociationRepository.create(
      server,
      {
        userId: createdUser.id,
        entityId: entityId,
        origin: USER_ORIGINS.INTERNAL,
        roleId: roleId,
      },
      { transaction: t, authInfoId },
    );

    // Prepare bulk insert for currency associations
    const bulkCurrencyRecords = currencyIds.map((currencyId) => ({
      userAssociationId: userAssoc.id,
      currencyId: currencyId,
    }));

    await UserAssociationCurrencyRepository.create(server, bulkCurrencyRecords, {
      transaction: t,
      authInfoId,
    });

    // Update SSO record to link created user and mark completion
    const ssoData = {
      userId: createdUser.id,
      completedDate: new Date(),
      status: USER_SSO_STATUSES.COMPLETED,
    };
    await UserSSOAccountRepository.update(usersso, ssoData, { transaction: t, authInfoId });
  });
};
