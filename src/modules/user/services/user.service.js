import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { UserValidation } from '#src/modules/core/validations/index.js';
import { LocalisationConstant } from '#src/modules/setting/constants/index.js';
import { LocalisationService } from '#src/modules/setting/services/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import { UserError } from '#src/modules/user/errors/index.js';
import {
  UserAssociationCurrencyRepository,
  UserAssociationRepository,
  UserMfaSettingRepository,
  UserRepository,
} from '#src/modules/user/repository/index.js';
import { withTransaction } from '#src/utils/db-transaction.util.js'; // Utilities for MFA, password hashing, and transaction handling
import { hashPassword } from '#src/utils/hash.util.js';
import { generate2FASecret } from '#src/utils/twofa.util.js';

const { ROOT, ORGANISATION, MERCHANT } = CoreConstant.HIERARCHY;
const { USER_STATUSES, USER_TYPES, USER_ORIGINS, USER_MFA_STATUSES } = UserConstant;
const { CURRENCY } = LocalisationConstant.LOCALISATION_CATEGORIES;

/**
 * Retrieves all user entries for a given entity.
 * @param {Object} request - The request object containing entity information.
 * @returns {Promise<Object>} A promise that resolves to the list of user entries.
 */
export const index = async (request) => {
  const { entity, server } = request;

  // Build a query that forces: only NORMAL users, within this entity.
  const query = {
    ...request.query,
    filter_type_eq: USER_TYPES.NORMAL,
    'filter_ua.entityId_eq': entity.id,
  };

  // Fetch with eager joins as defined in repository
  const res = await UserRepository.findAll(server, query);

  // Return rows and original pagination metadata from repository
  return {
    rows: res.rows.map(mapUserToDto),
    pagination: res.pagination,
  };
};

/**
 * List sub-accounts belonging to the authenticated user within the entity.
 */
export const subAccountIndex = async (request) => {
  const {
    server,
    entity,
    authInfo: { id: authInfoId }, // the parent (owner of sub-accounts)
  } = request;

  // Force sub-account type, current user as parent, and current entity
  const query = {
    ...request.query,
    filter_type_eq: USER_TYPES.SUB_ACCOUNT,
    filter_parentId_eq: authInfoId,
    'filter_ua.entityId_eq': entity.id,
  };

  // Delegate to repository (likely includes joins specific to sub-accounts)
  const res = await UserRepository.findAllSubAccount(server, query);

  // Return rows and original pagination metadata from repository
  return {
    rows: res.rows.map(mapUserToDto),
    pagination: res.pagination,
  };
};
/**
 * Retrieves a specific user entry by ID.
 * @param {Object} request - The request object containing params, body, and entity information.
 * @returns {Promise<Object>} A promise that resolves to the user entry.
 * @throws {CoreError} If the user is not found or there's a version conflict.
 */
export const view = async (request) => {
  const { id } = request.params;
  const { server } = request;

  const user = await UserRepository.findById(server, id);
  if (!user) {
    throw CoreError.dataNotFound({ data: 'common.label.user', attribute: 'ID', value: id });
  }

  return mapUserToDto(user);
};

/**
 * Creates a new user entry in the system.
 *
 * @param {Object} request - The request object containing user and entity information.
 * @param {Object} request.body - The body of the request containing user details.
 * @returns {Promise<Object>} A promise that resolves to the created user object.
 * @throws {CoreError} If the data is already taken or required data is missing.
 */
export const create = async (request, options = {}) => {
  const {
    body,
    entity: { id: entityId, hierarchy }, // which tenant/entity we’re creating under
    server,
    authInfo: { id: authInfoId }, // who is performing the action
  } = request;

  const {
    name,
    username,
    password,
    email,
    currencyIds,
    roleId,
    type = USER_TYPES.NORMAL,
    parentId,
    twoFactorAuth = USER_MFA_STATUSES.DISABLED,
    validityDate,
  } = body;

  // If email is provided, ensure it’s unique (repo should enforce case-insensitive uniqueness)
  if (email) {
    await UserValidation.validateEmail(server, email);
  }

  const isSubAccount = type === USER_TYPES.SUB_ACCOUNT;

  // For main accounts: a role is required; merchants also require currencies
  if (!isSubAccount) {
    if (!roleId) {
      throw CoreError.requiredData({ attribute: 'common.label.role' });
    }
    if (
      hierarchy === MERCHANT && // only enforce at merchant level
      (!currencyIds || !Array.isArray(currencyIds) || currencyIds.length === 0)
    ) {
      throw CoreError.requiredData({ attribute: 'common.label.currency' });
    }
  }

  // For sub-accounts: a parent is mandatory
  if (isSubAccount && !parentId) {
    throw CoreError.requiredData({ attribute: 'common.label.parentUser' });
  }

  // If sub-account, ensure parent exists (and could also check same entity if needed)
  let parentUser = null;
  if (isSubAccount) {
    parentUser = await UserRepository.findById(server, parentId);
    if (!parentUser) {
      throw CoreError.dataNotFound({
        data: 'common.label.parentUser',
        attribute: 'ID',
        value: parentId,
      });
    }
  }

  // Validate username (enforces format + uniqueness; may consider parent/type rules)
  const finalUsername = await UserValidation.validateUsername(server, username, type, parentUser);

  // Hash password before persisting (never store plaintext)
  const hashedPassword = await hashPassword(password);

  // Shape the user record to create
  const userData = {
    name: name,
    username: finalUsername,
    password: hashedPassword,
    email,
    type,
    validityDate,
    parentId: isSubAccount ? parentId : null, // only set for sub-accounts
    currencyIds,
    status: USER_STATUSES.ACTIVE, // new users start active
  };

  return withTransaction(server, options, async (t) => {
    // Create the user first
    const createdUser = await UserRepository.create(server, userData, {
      transaction: t,
      authInfoId, // for auditing/createdBy
    });

    // Generate TOTP secret and create MFA settings row for the user
    const { base32secret } = await generate2FASecret();
    await UserMfaSettingRepository.create(
      server,
      {
        userId: createdUser.id,
        secretKey: base32secret, // SECURITY: consider encrypting this at rest
        status: twoFactorAuth, // enable/disable MFA per request
      },
      { transaction: t, authInfoId },
    );

    // If this is a main account, also create the association & currency links
    if (!isSubAccount) {
      // Create user ↔ entity/role association (origin INTERNAL as created by the system)
      const userAssoc = await UserAssociationRepository.create(
        server,
        {
          userId: createdUser.id,
          entityId: entityId,
          origin: USER_ORIGINS.INTERNAL,
          roleId: roleId,
        },
        { transaction: t, authInfoId },
      );

      if (hierarchy === MERCHANT) {
        // Prepare currency link rows from provided currency IDs
        const bulkCurrencyRecords = currencyIds.map((currencyId) => ({
          userAssociationId: userAssoc.id,
          currencyId: currencyId,
        }));

        // Persist currency links in bulk
        await UserAssociationCurrencyRepository.create(server, bulkCurrencyRecords, {
          transaction: t,
          authInfoId,
        });
      }
    }

    // Return the created user record to caller
    return createdUser;
  });
};

/**
 * Updates the basic information of a user.
 *
 * @param {Object} request - The request object containing user information.
 * @param {Object} request.body - The body of the request containing updated user details.
 * @param {string} request.body.name - The new name of the user.
 * @param {string} request.body.email - The new email of the user.
 * @param {Object} request.params - The parameters of the request.
 * @param {string} request.params.id - The ID of the user to update.
 * @param {Object} request.server - The server instance used for database operations.
 * @param {Object} request.authInfo - The authentication information of the requester.
 * @param {string} request.authInfo.id - The ID of the authenticated user making the request.
 * @param {Object} [options={}] - Additional options for the transaction.
 * @returns {Promise<void>} A promise that resolves when the user's basic information is updated.
 * @throws {CoreError} If the user is not found or the email is already taken.
 */
export const updateBasicInformation = async (request, options = {}) => {
  const {
    body,
    params: { id: userId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  // Fetch current user record for update base
  const user = await UserRepository.findById(server, userId);
  if (!user) {
    throw CoreError.dataNotFound({ data: 'common.label.user', attribute: 'ID', value: userId });
  }

  // If email is changing, ensure it’s unique (excluding current user)
  if (body.email) {
    await UserValidation.validateEmail(server, body.email, userId);
  }

  // Persist changes; repository returns an object with change flags
  const userRes = await UserRepository.update(user, body, { authInfoId });
  if (!userRes.isDirty) {
    throw CoreError.unprocessable();
  }
};

/**
 * Updates the organisation-related information of a user.
 *
 * @param {Object} request - The request object containing user and organisation information.
 * @param {Object} request.body - The body of the request containing updated organisation details.
 * @param {Array<string>} request.body.currencyIds - The list of currency IDs associated with the user.
 * @param {string} request.body.roleId - The role ID to be assigned to the user.
 * @param {Object} request.params - The parameters of the request.
 * @param {string} request.params.id - The ID of the user to update.
 * @param {Object} request.server - The server instance used for database operations.
 * @param {Object} request.entity - The entity information related to the user.
 * @param {Object} request.authInfo - The authentication information of the requester.
 * @param {string} request.authInfo.id - The ID of the authenticated user making the request.
 * @param {Object} [options={}] - Additional options for the transaction.
 * @returns {Promise<void>} A promise that resolves when the user's organisation information is updated.
 * @throws {CoreError} If the user is not found.
 */
export const updateOrganisation = async (request, options = {}) => {
  const {
    body: { currencyIds, roleId },
    params: { id: userId },
    server,
    entity,
    authInfo: { id: authInfoId },
  } = request;

  // Ensure target user exists
  const user = await UserRepository.findById(server, userId);
  if (!user) {
    throw CoreError.dataNotFound({ data: 'common.label.user', attribute: 'ID', value: userId });
  }

  // Load the user’s association within this entity (role + currencies live here)
  const userAssoc = await UserAssociationRepository.findByUserId(server, entity, userId);

  return withTransaction(server, options, async (t) => {
    const dirtyFlags = []; // track whether any section actually changed

    if (user.type === USER_TYPES.NORMAL) {
      // only main accounts carry role/currencies
      if (roleId) {
        const uaRes = await UserAssociationRepository.update(
          userAssoc,
          { roleId },
          { transaction: t, authInfoId },
        );

        if (uaRes?.isDirty) {
          dirtyFlags.push('role'); // mark section changed
        }
      }

      // Update currencies if provided
      if (currencyIds?.length > 0) {
        // de‑dupe + sort desired
        const desired = [...new Set(currencyIds ?? [])].sort((a, b) => a.localeCompare(b));

        // read existing from association, sort
        const existing = (userAssoc.uac ?? [])
          .map((rec) => rec.currencyId)
          .sort((a, b) => a.localeCompare(b));

        // Compare as sets by position (both arrays sorted)
        const isSame =
          desired.length === existing.length && desired.every((id, i) => id === existing[i]);

        if (!isSame) {
          // Not identical: rewrite the association set
          await UserAssociationCurrencyRepository.deleteByUserAssociationId(server, userAssoc.id, {
            transaction: t,
          });

          const bulkCurrencyRecords = currencyIds.map((currencyId) => ({
            userAssociationId: userAssoc.id,
            currencyId: currencyId,
          }));

          await UserAssociationCurrencyRepository.create(server, bulkCurrencyRecords, {
            transaction: t,
            authInfoId,
          });

          dirtyFlags.push('currency');
        }
      }

      // If neither role nor currencies changed, treat as no-op
      if (!dirtyFlags.length) {
        throw CoreError.unprocessable();
      }
    }
  });
};

/**
 * Updates the login access details of a user, including validity date and two-factor authentication status.
 *
 * @param {Object} request - The request object containing user information.
 * @param {Object} request.body - The body of the request containing updated login access details.
 * @param {string} request.body.validityDate - The new validity date for the user's login access.
 * @param {string} request.body.twoFactorAuth - The new two-factor authentication status for the user.
 * @param {Object} request.params - The parameters of the request.
 * @param {string} request.params.id - The ID of the user whose login access is to be updated.
 * @param {Object} request.server - The server instance used for database operations.
 * @param {Object} request.authInfo - The authentication information of the requester.
 * @param {string} request.authInfo.id - The ID of the authenticated user making the request.
 * @param {Object} [options={}] - Additional options for the transaction.
 * @returns {Promise<void>} A promise that resolves when the user's login access details are updated.
 * @throws {CoreError} If the user is not found.
 */
export const updateLoginAccess = async (request, options = {}) => {
  const {
    body, // expected fields: validityDate, twoFactorAuth
    params: { id: userId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  // Ensure target user exists
  const user = await UserRepository.findById(server, userId);
  if (!user) {
    throw CoreError.dataNotFound({ data: 'common.label.user', attribute: 'ID', value: userId });
  }

  return withTransaction(server, options, async (t) => {
    const dirtyFlags = [];

    // Load the user’s MFA settings row
    const userMfaSet = await UserMfaSettingRepository.findByUserId(server, userId);

    // Update MFA status (enable/disable)
    const mfaRes = await UserMfaSettingRepository.update(
      userMfaSet,
      { status: body.twoFactorAuth },
      { transaction: t, authInfoId },
    );
    if (mfaRes?.isDirty) {
      dirtyFlags.push('mfa');
    }

    // Update user’s basic login access fields (e.g., validityDate)
    const userRes = await UserRepository.update(user, body, { transaction: t, authInfoId });
    if (userRes?.isDirty) {
      dirtyFlags.push('user');
    }

    // If neither MFA nor user fields changed, reject as no-op
    if (!dirtyFlags.length) {
      throw CoreError.unprocessable();
    }
  });
};

/**
 * Updates the status of a user and, if deactivating a main account,
 * also deactivates all sub-accounts (atomically).
 * - Sub-accounts cannot be activated if their parent is inactive.
 *
 * @param {Object} request - Fastify/HTTP request object
 * @throws {CoreError.dataNotFound} if user is not found
 * @throws {CoreError.unprocessable} if no changes were made
 * @throws {UserError.parentInactive} if activating sub-account while parent is inactive
 */
export const updateStatus = async (request) => {
  const {
    body,
    params: { id: userId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  return withTransaction(server, {}, async (t) => {
    // 1. Find user inside transaction
    const user = await UserRepository.findById(server, userId, { transaction: t });
    if (!user) {
      throw CoreError.dataNotFound({ data: 'common.label.user', attribute: 'ID', value: userId });
    }

    // 2. Prevent activating sub-account if parent inactive
    if (body.status === USER_STATUSES.ACTIVE && user.type === USER_TYPES.SUB_ACCOUNT) {
      if (!user.parentId) {
        throw CoreError.unprocessable();
      }

      const parentUser = await UserRepository.findById(server, user.parentId, { transaction: t });
      if (!parentUser) {
        throw CoreError.dataNotFound({
          data: 'common.label.parentUser',
          attribute: 'ID',
          value: user.parentId,
        });
      }

      if (parentUser.status !== USER_STATUSES.ACTIVE) {
        throw UserError.parentInactive();
      }
    }

    // 3. No-op check: if status is unchanged, do nothing
    if (user.status === body.status) {
      throw CoreError.unprocessable();
    }

    // 4. Update user status
    const userRes = await UserRepository.update(user, body, { transaction: t, authInfoId });
    if (!userRes?.isDirty) {
      throw CoreError.unprocessable();
    }

    // 5. If deactivating a main account, also deactivate all sub-accounts
    if (body.status === USER_STATUSES.INACTIVE && user.type === USER_TYPES.NORMAL) {
      const subAccounts = await UserRepository.getSubAccounts(
        server,
        { parentId: userId, status: USER_STATUSES.ACTIVE },
        { transaction: t },
      );

      // Promise.all for concurrent sub-account updates
      if (subAccounts.length) {
        await Promise.all(
          subAccounts.map((sub) =>
            UserRepository.update(
              sub,
              { status: USER_STATUSES.INACTIVE },
              { transaction: t, authInfoId },
            ),
          ),
        );
      }
    }
  });
};

// need entity table to proceed with update hierarchy

/**
 * Retrieves a option of available currencies, roles, and departments.
 *
 * @param {Object} request - The request object containing query and server information.
 * @param {Object} request.query - The query parameters for the request.
 * @param {Object} request.server - The server instance used for database operations.
 * @returns {Promise<Object>} A promise that resolves to an object containing lists of currencies, roles, and departments.
 */
export const option = async (request, options = {}) => {
  const localisationRequest = {
    ...request,
    query: { ...request.query, 'filter_localisation.category_eq': CURRENCY },
    server: request.server,
  };
  const currency = await LocalisationService.generateDropdown(localisationRequest);

  // temporary hardcoded role
  const role = [
    {
      id: '3a991024-313e-11f0-93de-e7665ffe77de',
      name: 'Admin',
    },
    {
      id: '3a991024-313e-11f0-93de-e7665ffe77df',
      name: 'Manager',
    },
    {
      id: '3a991024-313e-11f0-93de-e7665ffe77dg',
      name: 'User',
    },
  ];

  // temporary hardcoded department
  const department = [
    {
      id: '3a991024-313e-11f0-93de-e7665ffe77de',
      name: 'Dev Team',
    },
    {
      id: '3a991024-313e-11f0-93de-e7665ffe77df',
      name: 'Finance',
    },
    {
      id: '3a991024-313e-11f0-93de-e7665ffe77dg',
      name: 'Operation',
    },
  ];

  return { currency, role, department };
};

/**
 * Checks the availability of a username within the system.
 *
 * @param {Object} request - The request object containing query and server information.
 * @param {Object} request.query - The query parameters for the request.
 * @param {string} request.query.username - The username to check for availability.
 * @param {Object} request.server - The server instance used for database operations.
 * @param {Object} [options={}] - Additional options for the operation.
 * @returns {Promise<string>} A promise that resolves to the validated username if it is available.
 * @throws {CoreError} If the username is already taken by another user.
 */
export const checkAvailability = async (request, options = {}) => {
  const {
    query: { username }, // username to check
    server,
  } = request;

  const type = USER_TYPES.NORMAL; // default context for validation (main account rules)

  // Delegate to validation service (should enforce format + uniqueness)
  return await UserValidation.validateUsername(server, username, type);
};

/**
 * Maps a User entity (with associations) into a DTO for API responses.
 * Keeps consistency and avoids duplication across service methods.
 * @param {Object} user
 * @returns {Object}
 */
const mapUserToDto = (user) => {
  const ua = user.userAssociation?.[0] || {};
  return {
    id: user.id,
    name: user.name,
    username: user.username,
    type: user.type,
    email: user.email,
    status: user.status,
    parentId: user.parentId || '',
    validityDate: user.validityDate || '',
    mfaStatus: user.mfa?.status,
    currencyCodes: ua.currencyCodes || [],
    roleId: ua.roleId || '',
    roleName: ua.role?.name || '',
    departmentName: ua.role?.department?.name || '',
    entityId: ua.entityId || '',
    origin: ua.origin,
    userInvitationStatus: ua.ui?.status || '',
    hierarchy: ua.entity?.hierarchy,
    createdAt: user.createdAt,
    createdBy: user.createdBy,
    updatedAt: user.updatedAt,
    updatedBy: user.updatedBy,
  };
};
