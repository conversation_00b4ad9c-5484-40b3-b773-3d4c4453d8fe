import { CoreError } from '#src/modules/core/errors/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import { UserError } from '#src/modules/user/errors/index.js';
import {
  UserAssociationCurrencyRepository,
  UserAssociationRepository,
  UserInvitationRepository,
  UserRepository,
} from '#src/modules/user/repository/index.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

const { USER_ORIGINS, USER_INVITATION_STATUSES } = UserConstant;

/**
 * Retrieves a list of user invitations based on the provided request and options.
 *
 * @param {Object} request - The request object containing necessary data.
 * @param {Object} request.entity - The entity related to the request.
 * @param {Object} request.server - The server instance handling the request.
 * @param {Object} request.authInfo - Authentication information of the user.
 * @param {string} request.authInfo.id - The ID of the authenticated user.
 * @param {Object} [options={}] - Additional options for the request.
 *
 * @returns {Promise<Object>} A promise that resolves to an object containing the list of user invitations.
 */
export const index = async (request, options = {}) => {
  const {
    server,
    authInfo: { id: authInfoId },
  } = request;

  // Build a query object including any provided query params
  // Force filter so results are only for this user's associations
  const query = {
    ...request.query,
    'filter_ua.userId_eq': authInfoId,
  };

  const res = await UserInvitationRepository.findAll(server, query);
  return {
    ...res,
  };
};

/**
 * Creates a new user invitation record in the database.
 *
 * @param {Object} request - The request object containing necessary data.
 * @param {Object} data - The data to be inserted into the user invitation table.
 * @param {Object} [options={}] - Additional options for the database operation.
 *
 * @returns {Promise<Object>} The newly created user invitation record.
 */
export const create = async (request, options = {}) => {
  const {
    authInfo: { id: authInfoId },
    body: { username, currencyIds, roleId },
    entity: { id: entityId },
    server,
  } = request;

  // Prepare query to find the target user by username
  const query = {
    filter_username_eq: username,
  };

  // Attempt to find the target user
  const user = await UserRepository.findUser(server, query);
  if (!user) {
    throw CoreError.dataNotFound({
      data: 'common.label.user',
      attribute: 'common.label.username',
      value: username,
    });
  }

  // Check if user is already associated with the entity
  const existsInEntity = user.userAssociation?.some((a) => a.entityId === entityId);
  if (existsInEntity) {
    throw UserError.userEntityConflict({ username });
  }

  return withTransaction(server, options, async (t) => {
    // 1. Create a user–entity association with EXTERNAL origin
    const userAssoc = await UserAssociationRepository.create(
      server,
      {
        userId: user.id,
        entityId: entityId,
        origin: USER_ORIGINS.EXTERNAL,
        roleId: roleId,
      },
      { transaction: t, authInfoId },
    );

    // 2. Prepare currency association records for the new association
    const bulkCurrencyRecords = currencyIds.map((currencyId) => ({
      userAssociationId: userAssoc.id,
      currencyId: currencyId,
    }));

    // Insert currency association records
    await UserAssociationCurrencyRepository.create(server, bulkCurrencyRecords, {
      transaction: t,
      authInfoId,
    });

    // 3. Create the actual invitation record for this association
    const createdUserInvitation = await UserInvitationRepository.create(
      server,
      {
        userAssociationId: userAssoc.id,
        invitedBy: authInfoId,
        status: USER_INVITATION_STATUSES.PENDING,
      },
      {
        transaction: t,
        authInfoId,
      },
    );

    // Return the newly created invitation
    return createdUserInvitation;
  });
};

/** Updates the status of a user invitation in the database.
 *
 * @param {Object} server - The server object containing the database connection.
 * @param {number|string} id - The ID of the invitation status is to be updated.
 * @param {Object} data - The data containing the new status to be updated in the user invitation.
 * @param {Object} [options={}] - Additional options for the database operation.
 *
 * @returns {Promise<Object>} A promise that resolves to the updated user invitation record.
 */
export const updateStatus = async (request) => {
  const {
    body: { status },
    params: { id: invId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  // Retrieve the invitation record by its ID
  const userInv = await UserInvitationRepository.findById(server, invId);
  if (!userInv) {
    throw CoreError.dataNotFound({
      data: 'common.label.userInvitation',
      attribute: 'ID',
      value: invId,
    });
  }

  // Prevent updating if the status is already the same
  if (userInv.status === status) {
    throw CoreError.unprocessable();
  }

  // Prepare update payload
  const data = { status };
  const now = new Date();

  // If status is APPROVED, set acceptedDate
  if (status === USER_INVITATION_STATUSES.APPROVED) {
    data.acceptedDate = now;
  } else if (
    [
      USER_INVITATION_STATUSES.REJECTED,
      USER_INVITATION_STATUSES.REVOKED,
      USER_INVITATION_STATUSES.CANCELLED,
    ].includes(status)
  ) {
    data.cancelledDate = now;
  }

  // Save the updated record
  await UserInvitationRepository.update(userInv, data, { authInfoId });
};
