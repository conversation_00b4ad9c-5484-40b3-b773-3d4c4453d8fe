import { CoreConstant } from '#src/modules/core/constants/index.js';
import { initializeAuditMeta } from '#src/utils/audit-trail.util.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  EVENT_ACTIONS,
  MODULE_METHODS: {
    CREATE,
    DELETE,
    INDEX,
    OPTION,
    UPDATE_POLICY,
    UPDATE_STATUS,
    UPDATE_BASIC_INFORMATION,
    VIEW,
  },
} = CoreConstant;

/**
 * Factory function to create department handlers with configurable options.
 *
 * @param {Object} config - Configuration object for the handler factory
 * @param {Object} config.service - The service object containing business logic methods
 * @param {string} config.moduleName - The name of the module (e.g., DEPARTMENT, DEPARTMENT_TEMPLATE)
 * @param {string} config.eventName - The event name for audit logging
 * @param {boolean} config.isTemplate - Flag to indicate if this is for templates
 * @returns {Object} An object containing all handler functions
 */
export const createDepartmentHandlers = ({ service, moduleName, eventName, isTemplate }) => {
  /**
   * Handles the retrieval of department data with pagination.
   */
  const index = (request, reply) => {
    initializeAuditMeta(request, {
      module: moduleName,
      event: eventName,
      action: EVENT_ACTIONS.SEARCHED,
    });

    const cacheKey = generateCacheKey(`${moduleName}_${INDEX}`, request);
    const cachedServiceFn = () =>
      fetchFromCache(
        request.server.redis,
        cacheKey,
        () => service.index(request, isTemplate),
        SHORT,
      );

    return handleServiceResponse({
      request,
      reply,
      serviceFn: cachedServiceFn,
      module: moduleName,
      method: INDEX,
    });
  };

  /**
   * Handles the retrieval of a specific department view.
   */
  const view = async (request, reply) => {
    const { id } = request.params;

    initializeAuditMeta(
      request,
      {
        module: moduleName,
        event: eventName,
        action: EVENT_ACTIONS.DETAILS_VIEWED,
      },
      id,
    );

    const cacheKey = generateCacheKey(`${moduleName}_${VIEW}`, request);

    const result = await fetchFromCache(
      request.server.redis,
      cacheKey,
      () => service.view(request),
      SHORT,
    );

    const precomputedServiceFn = async () => result;

    return handleServiceResponse({
      request,
      reply,
      serviceFn: precomputedServiceFn,
      module: moduleName,
      method: VIEW,
    });
  };

  /**
   * Handles the creation of a new department.
   */
  const create = async (request, reply) => {
    initializeAuditMeta(request, {
      module: moduleName,
      event: eventName,
      action: EVENT_ACTIONS.CREATED,
    });

    const { result, auditModelMapping } = await service.create(request, isTemplate);

    const precomputedServiceFn = async () => result;

    return handleServiceResponse({
      request,
      reply,
      serviceFn: precomputedServiceFn,
      module: moduleName,
      method: CREATE,
      audit: {
        modelMapping: auditModelMapping,
      },
    });
  };

  /**
   * Handles the update of basic information for a department.
   */
  const updateBasicInformation = async (request, reply) => {
    const { id } = request.params;

    initializeAuditMeta(
      request,
      {
        module: moduleName,
        event: eventName,
        action: EVENT_ACTIONS.BASIC_INFORMATION_UPDATED,
      },
      id,
    );

    const { result, auditModelMapping } = await service.updateBasicInformation(request, id);

    const precomputedServiceFn = async () => result;

    return handleServiceResponse({
      request,
      reply,
      serviceFn: precomputedServiceFn,
      module: moduleName,
      method: UPDATE_BASIC_INFORMATION,
      audit: {
        modelMapping: auditModelMapping,
      },
    });
  };

  /**
   * Handles the update of module policy for a department.
   */
  const updatePolicy = async (request, reply) => {
    const { id } = request.params;

    initializeAuditMeta(
      request,
      {
        module: moduleName,
        event: eventName,
        action: EVENT_ACTIONS.POLICY_UPDATED,
      },
      id,
    );

    const { result, auditModelMapping } = await service.updatePolicy(request);

    const precomputedServiceFn = async () => result;

    return handleServiceResponse({
      request,
      reply,
      serviceFn: precomputedServiceFn,
      module: moduleName,
      method: UPDATE_POLICY,
      audit: {
        modelMapping: auditModelMapping,
      },
    });
  };

  /**
   * Handles the update of status for a department.
   */
  const updateStatus = async (request, reply) => {
    const { id } = request.params;

    initializeAuditMeta(
      request,
      {
        module: moduleName,
        event: eventName,
        action: EVENT_ACTIONS.STATUS_UPDATED,
      },
      id,
    );

    const { result, auditModelMapping } = await service.updateStatus(request);

    const precomputedServiceFn = async () => result;

    return handleServiceResponse({
      request,
      reply,
      serviceFn: precomputedServiceFn,
      module: moduleName,
      method: UPDATE_STATUS,
      audit: {
        modelMapping: auditModelMapping,
      },
    });
  };

  /**
   * Handles the removal of a department.
   */
  const remove = async (request, reply) => {
    const { id } = request.params;

    initializeAuditMeta(
      request,
      {
        module: moduleName,
        event: eventName,
        action: EVENT_ACTIONS.DELETED,
      },
      id,
    );

    const { result, auditModelMapping } = await service.remove(request);

    const precomputedServiceFn = async () => result;

    return handleServiceResponse({
      request,
      reply,
      serviceFn: precomputedServiceFn,
      module: moduleName,
      method: DELETE,
      audit: {
        modelMapping: auditModelMapping,
      },
    });
  };

  /**
   * Handles the options request for departments.
   */
  const options = (request, reply) => {
    initializeAuditMeta(request, {
      module: CoreConstant.MODULE_NAMES.MODULE_POLICY,
      event: eventName,
      action: EVENT_ACTIONS.VIEWED,
    });

    return handleServiceResponse({
      request,
      reply,
      serviceFn: () => service.options(request),
      module: moduleName,
      method: OPTION,
    });
  };

  return {
    index,
    view,
    create,
    updateBasicInformation,
    updatePolicy,
    updateStatus,
    remove,
    options,
  };
};
