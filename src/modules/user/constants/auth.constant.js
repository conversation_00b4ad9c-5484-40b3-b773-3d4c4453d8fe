export const FAILED_LOGIN_EXPIRY = 24 * 60 * 60; // 24 hours in seconds
export const FAILED_LOGIN_KEY_PREFIX = 'failed_login:';
export const IP_ATTEMPTS = 5;
export const LOGIN_ATTEMPTS = 5;
export const PRE_AUTH_TOKEN_EXPIRY_TIME = 5 * 60; // 5 minutes
export const PRE_AUTH_TOKEN_NAME = 'pre_auth_token';
export const TOKEN_EXPIRY_TIME = 24 * 60 * 60; // 24 hours in seconds
export const TWO_FACTOR_AUTHENTICATION_WINDOW_DURATION = 1;
export const VALID_ISSUERS = ['accounts.google.com', 'https://accounts.google.com'];

export const SECONDS = {
  MINUTE: 60,
  HOUR: 60 * 60,
  DAY: 24 * 60 * 60,
};

// Expiry times
export const EXPIRY_TIMES = {
  DEFAULT_TRUST_DAYS: 7, // 7 days
  FAILED_LOGIN: SECONDS.DAY, // 24 hours
  TOKEN: SECONDS.DAY, // 24 hours
  PRE_AUTH_TOKEN: 5 * SECONDS.MINUTE, // 5 minutes
};

// Attempt limits
export const LIMITS = {
  IP_ATTEMPTS: 5,
  LOGIN_ATTEMPTS: 5,
};

// Cache keys and identifiers
export const KEYS = {
  FAILED_LOGIN_PREFIX: 'failed_login:',
  PRE_AUTH_TOKEN_NAME: 'pre_auth_token',
  TRUSTED_DEVICE_COOKIE_NAME: 'trusted_device',
};

// Security and authentication settings
export const SECURITY = {
  VALID_ISSUERS: ['accounts.google.com', 'https://accounts.google.com'],
  TWO_FACTOR_AUTHENTICATION_WINDOW_DURATION: 1,
  DEFAULT_TRUST_DAYS: 7,
};
