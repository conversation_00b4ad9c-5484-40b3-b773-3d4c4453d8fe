import { <PERSON><PERSON><PERSON><PERSON> } from '#src/modules/user/handlers/index.js';
import { RoleSchema } from '#src/modules/user/schemas/index.js';

/**
 * Role API routes.
 *
 * @param {import('fastify').FastifyInstance} fastify - Fastify instance
 * @param {Object} opts - Optional Fastify plugin options
 */
const RoleRoute = async (fastify, opts) => {
  const PREFIX = '/:id';

  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: false,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  // List all roles
  fastify.get('/', {
    schema: RoleSchema.index,
    config: { name: 'role.list', policy: 'role.canView', access: commonAccessConfig },
    handler: RoleHandler.index,
  });

  // View a specific role
  fastify.get(PREFIX, {
    schema: RoleSchema.view,
    config: { name: 'role.view', policy: 'role.canView', access: commonAccessConfig },
    handler: RoleHandler.view,
  });

  // Create a new role
  fastify.post('/', {
    schema: RoleSchema.create,
    config: { name: 'role.create', policy: 'role.canCreate', access: commonAccessConfig },
    handler: RoleHandler.create,
  });

  // Update a role entry
  fastify.put(PREFIX, {
    schema: RoleSchema.update,
    config: { name: 'role.update', policy: 'role.canEdit', access: commonAccessConfig },
    handler: RoleHandler.update,
  });

  // Update status of a role entry
  fastify.patch(`${PREFIX}/status`, {
    schema: RoleSchema.updateStatus,
    config: { name: 'role.updateStatus', policy: 'role.canManage', access: commonAccessConfig },
    handler: RoleHandler.updateStatus,
  });

  // Get options for role settings
  fastify.get('/options', {
    schema: RoleSchema.options,
    config: { name: 'role.options', policy: 'role.canView', access: commonAccessConfig },
    handler: RoleHandler.options,
  });

  // Get navigations
  fastify.get('/navigations', {
    schema: RoleSchema.navigations,
    config: { name: 'role.navigations', access: commonAccessConfig },
    handler: RoleHandler.navigations,
  });
};

export default RoleRoute;
