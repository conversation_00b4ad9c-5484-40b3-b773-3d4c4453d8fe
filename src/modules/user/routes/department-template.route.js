import { DepartmentTemplateHandler } from '#src/modules/user/handlers/index.js';
import { DepartmentTemplateSchema } from '#src/modules/user/schemas/index.js';

const DepartmentTemplateRoute = async (fastify, opts) => {
  const PREFIX = '/:id';

  // Define the common access configuration for all routes
  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: false,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  fastify.get('/', {
    schema: DepartmentTemplateSchema.index,
    config: { name: 'department-template.index', access: commonAccessConfig },
    handler: DepartmentTemplateHandler.index,
  });

  fastify.get(PREFIX, {
    schema: DepartmentTemplateSchema.view,
    config: { name: 'department-template.view', access: commonAccessConfig },
    handler: DepartmentTemplateHandler.view,
  });

  fastify.post('/', {
    schema: DepartmentTemplateSchema.create,
    config: { name: 'department-template.create', access: commonAccessConfig },
    handler: DepartmentTemplateHandler.create,
  });

  fastify.patch(`${PREFIX}/basic-information`, {
    schema: DepartmentTemplateSchema.updateBasicInformation,
    config: { name: 'department-template.update', access: commonAccessConfig },
    handler: DepartmentTemplateHandler.updateBasicInformation,
  });

  fastify.patch(`${PREFIX}/policy`, {
    schema: DepartmentTemplateSchema.updatePolicy,
    config: { name: 'department-template.update', access: commonAccessConfig },
    handler: DepartmentTemplateHandler.updatePolicy,
  });

  fastify.patch(`${PREFIX}/status`, {
    schema: DepartmentTemplateSchema.updateStatus,
    config: { name: 'department-template.updateStatus', access: commonAccessConfig },
    handler: DepartmentTemplateHandler.updateStatus,
  });

  fastify.delete(PREFIX, {
    schema: DepartmentTemplateSchema.remove,
    config: { name: 'department-template.remove', access: commonAccessConfig },
    handler: DepartmentTemplateHandler.remove,
  });

  fastify.get(`/options`, {
    schema: DepartmentTemplateSchema.options,
    config: { name: 'department-template.options', access: commonAccessConfig },
    handler: DepartmentTemplateHandler.options,
  });
};

export default DepartmentTemplateRoute;
