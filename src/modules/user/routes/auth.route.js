import { Auth<PERSON>and<PERSON> } from '#src/modules/user/handlers/index.js';
import { AuthSchema } from '#src/modules/user/schemas/index.js';

/**
 * User API routes.
 *
 * @param {import('fastify').FastifyInstance} fastify - Fastify instance
 * @param {Object} opts - Optional Fastify plugin options
 */
const AuthRoute = async (fastify, opts) => {
  const commonAccessConfig = {
    user: false,
    member: false,
    webhook: false,
    public: true,
    ipWhitelist: [],
  };

  const userAccessConfig = {
    user: true,
    member: false,
    webhook: false,
    public: false,
    ipWhitelist: [],
  };

  fastify.post('/login', {
    preHandler: fastify.hcaptcha,
    schema: AuthSchema.login,
    config: { name: 'user.login', access: commonAccessConfig },
    handler: AuthHandler.login,
  });

  fastify.post('/google', {
    schema: AuthSchema.google,
    config: { name: 'user.google', access: commonAccessConfig },
    handler: AuthHandler.google,
  });

  fastify.get('/setup-2fa', {
    schema: AuthSchema.setup2fa,
    config: { name: 'user.setup2fa', access: userAccessConfig },
    handler: AuthHandler.setup2fa,
  });

  fastify.post('/verify-2fa-login', {
    schema: AuthSchema.verify2faLogin,
    config: { name: 'user.verify2faLogin', access: commonAccessConfig },
    handler: AuthHandler.verify2faLogin,
  });

  fastify.post('/revoke-trusted-devices', {
    schema: AuthSchema.revokeTrustedDevices,
    config: { name: 'user.revokeTrustedDevices', access: userAccessConfig },
    handler: AuthHandler.revokeTrustedDevices,
  });

  fastify.post('/reset-2fa', {
    schema: AuthSchema.reset2fa,
    config: {
      name: 'user.reset2fa',
      policy: 'user.canManage',
      access: { ...commonAccessConfig, user: true, public: false },
    },
    handler: AuthHandler.reset2fa,
  });
};

export default AuthRoute;
