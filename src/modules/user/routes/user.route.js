import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>r<PERSON>n<PERSON><PERSON><PERSON><PERSON>,
  UserSSOAccountHandler,
} from '#src/modules/user/handlers/index.js';
import {
  UserInvitationSchema,
  UserSSOAccountSchema,
  UserSchema,
} from '#src/modules/user/schemas/index.js';

/**
 * User API routes.
 *
 * @param {import('fastify').FastifyInstance} fastify - Fastify instance
 * @param {Object} opts - Optional Fastify plugin options
 */
const UserRoute = async (fastify, opts) => {
  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: true,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  /** USER CRUD */
  // List all normal users
  fastify.get('/', {
    schema: UserSchema.index,
    config: { name: 'user.list', access: commonAccessConfig },
    handler: UserHandler.index,
  });

  // List all sub account users
  fastify.get('/sub-account', {
    schema: UserSchema.subAccountIndex,
    config: { name: 'user.subaccount.list', access: commonAccessConfig },
    handler: UserHandler.subAccountIndex,
  });

  // Create a new user
  fastify.post('/', {
    schema: UserSchema.create,
    config: { name: 'user.create', access: commonAccessConfig },
    handler: UserHandler.create,
  });

  // View a specific user
  fastify.get('/:id', {
    schema: UserSchema.view,
    config: { name: 'user.view', access: commonAccessConfig },
    handler: UserHandler.view,
  });

  // Update basic information of a user
  fastify.patch(`/:id/basic-information`, {
    schema: UserSchema.updateBasicInformation,
    config: { name: 'user.updateBasicInformation', access: commonAccessConfig },
    handler: UserHandler.updateBasicInformation,
  });

  // Update organisation of a user
  fastify.patch(`/:id/organisation`, {
    schema: UserSchema.updateOrganisation,
    config: { name: 'user.updateOrganisation', access: commonAccessConfig },
    handler: UserHandler.updateOrganisation,
  });

  // Update login and access of a user
  fastify.patch(`/:id/login-access`, {
    schema: UserSchema.updateLoginAccess,
    config: { name: 'user.updateLoginAccess', access: commonAccessConfig },
    handler: UserHandler.updateLoginAccess,
  });

  // Update status of a user
  fastify.patch(`/:id/status`, {
    schema: UserSchema.updateStatus,
    config: { name: 'user.updateStatus', access: commonAccessConfig },
    handler: UserHandler.updateStatus,
  });

  // Get additional info options for user attribute (e.g., role, currency)
  fastify.get('/options', {
    schema: UserSchema.options,
    config: { name: 'user.options', access: commonAccessConfig },
    handler: UserHandler.options,
  });

  //Check user name availability
  fastify.get('/check-username', {
    schema: UserSchema.checkAvailability,
    config: { name: 'user.checkAvailability', access: commonAccessConfig },
    handler: UserHandler.checkAvailability,
  });

  /** USER INVITATION */
  // List all user invitations
  fastify.get('/invitations', {
    schema: UserInvitationSchema.index,
    config: { name: 'user.invitation.list', access: commonAccessConfig },
    handler: UserInvitationHandler.index,
  });

  // Create a new user invitation
  fastify.post('/invitations', {
    schema: UserInvitationSchema.create,
    config: { name: 'user.invitation.create', access: commonAccessConfig },
    handler: UserInvitationHandler.create,
  });

  // Create a new user invitation
  fastify.patch('/invitations/:id/status', {
    schema: UserInvitationSchema.updateStatus,
    config: { name: 'user.invitation.updateStatus', access: commonAccessConfig },
    handler: UserInvitationHandler.updateStatus,
  });

  /** USER SSO */
  // List all user sso
  fastify.get('/sso', {
    schema: UserSSOAccountSchema.index,
    config: { name: 'user.sso.list', access: commonAccessConfig },
    handler: UserSSOAccountHandler.index,
  });

  // Update a user sso status
  fastify.patch('/sso/:id/status', {
    schema: UserSSOAccountSchema.updateStatus,
    config: { name: 'user.sso.updateStatus', access: commonAccessConfig },
    handler: UserSSOAccountHandler.updateStatus,
  });

  // Assign an existing user
  fastify.put('/sso/:id/assign', {
    schema: UserSSOAccountSchema.assign,
    config: { name: 'user.sso.assign', access: commonAccessConfig },
    handler: UserSSOAccountHandler.assign,
  });

  // Onboard a new user
  fastify.put('/sso/:id/onboard', {
    schema: UserSSOAccountSchema.onboard,
    config: { name: 'user.sso.onboard', access: commonAccessConfig },
    handler: UserSSOAccountHandler.onboard,
  });
};

export default UserRoute;
