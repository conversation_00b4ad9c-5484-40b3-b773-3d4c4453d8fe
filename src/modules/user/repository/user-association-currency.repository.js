/**
 * Creates new UserAssociationCurrency entries.
 * @param {Object} server - The server object containing the PostgreSQL models.
 * @param {Object} data - The data for creating the new user entry.
 * @param {Object} [options={}] - Additional options for the create operation.
 * @returns {Promise<Object>} A promise that resolves to the newly created user entry.
 */
export const create = async (server, data = [], options = {}) => {
  return await server.psql.UserAssociationCurrency.bulkCreate(data, {
    ...options,
    individualHooks: true, // Ensures beforeCreate hooks run on each instance to set audit fields
  });
};

/**
 * Deletes user association currency entries by user association ID.
 *
 * @param {Object} server - The server object containing the PostgreSQL models.
 * @param {number|string} userAssociationId - The ID of the user association to delete entries for.
 * @param {Object} [options={}] - Additional options for the delete operation.
 * @returns {Promise<number>} A promise that resolves to the number of rows deleted.
 */
export const deleteByUserAssociationId = async (server, userAssociationId, options = {}) => {
  return await server.psql.UserAssociationCurrency.destroy({
    where: {
      userAssociationId,
    },
    ...options,
  });
};
