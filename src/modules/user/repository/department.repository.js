import { computeUpdateDiff } from '#src/utils/audit-trail.util.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

/**
 * Retrieves all departments based on the provided query parameters with pagination.
 *
 * @async
 * @param {Object} fastify - The Fastify instance containing the database models.
 * @param {Object} query - The query parameters for filtering and pagination.
 * @returns {Promise<Object>} A promise that resolves to an object containing the paginated departments and metadata.
 */
export const findAll = async (fastify, query) => {
  const { Department } = fastify.psql;
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(query, Department);
  return await applyOffsetPagination(fastify, Department, query, whereFilter, includeFilter);
};

/**
 * Retrieves a department by its ID with associated modules and policy.
 *
 * @async
 * @param {Object} server - The server object containing the database models.
 * @param {number|string} id - The unique identifier of the department to retrieve.
 * @param {Object} [options={}] - Additional options for the database query.
 * @returns {Promise<Object|null>} A promise that resolves to the Department object if found, or null if not found.
 */
export const findById = async (server, id, withAssoc = true) =>
  await server.psql.Department.findByPk(id, {
    ...(withAssoc && {
      include: [
        {
          association: 'modulePolicies',
          include: [{ association: 'module' }, { association: 'policy' }],
        },
      ],
    }),
  });

/**
 * Retrieves all module policies associated with a specific department.
 *
 * @async
 * @param {Object} server - The server object containing the database models.
 * @param {number|string} departmentId - The unique identifier of the department to retrieve module policies for.
 * @param {Object} [options={}] - Additional options for the database query.
 * @param {Object} [options.transaction] - The database transaction to use for the operation.
 * @returns {Promise<Array>} A promise that resolves to an array of DepartmentModule objects with their associated policy.
 */
export const findAllModulePolicies = async (server, departmentId, options = {}) => {
  const { transaction } = options;

  return await server.psql.DepartmentModule.findAll({
    where: { departmentId },
    include: [{ model: server.psql.Policy, as: 'policy' }],
    transaction,
  });
};

/**
 * Finds a specific department module based on the department ID and module ID.
 *
 * @async
 * @param {Object} server - The server object containing the database models.
 * @param {number|string} departmentId - The ID of the department.
 * @param {number|string} moduleId - The ID of the module.
 * @param {Object} [options={}] - Additional options for the database query.
 * @returns {Promise<Object|null>} A promise that resolves to the DepartmentModule object if found, or null if not found.
 */
export const findDepartmentModule = async (server, departmentId, moduleId, options = {}) =>
  await server.psql.DepartmentModule.findOne({
    where: {
      departmentId,
      moduleId,
    },
    ...options,
  });

/**
 * Creates a new department in the database.
 *
 * @async
 * @param {Object} server - The server object containing the database models.
 * @param {Object} data - The data for the new department.
 * @param {Object} [options={}] - Additional options for the database operation.
 * @returns {Promise<Object>} A promise that resolves to the newly created Department object.
 */
export const createDepartment = async (server, data, options = {}) =>
  await server.psql.Department.create(data, options);

/**
 * Creates a new department module association in the database.
 *
 * @async
 * @param {Object} server - The server object containing the database models.
 * @param {Object} data - The data for the new department module association.
 * @param {Object} [options={}] - Additional options for the database operation.
 * @returns {Promise<Object>} A promise that resolves to the newly created DepartmentModule object.
 */
export const createDepartmentModule = async (server, data, options = {}) =>
  await server.psql.DepartmentModule.create(data, options);

/**
 * Updates an existing model instance in the database.
 *
 * @async
 * @param {Object} modelData - The model instance to update.
 * @param {Object} updateData - The data to update the model with.
 * @param {Object} [options={}] - Additional options for the database operation.
 * @returns {Promise<Object>} A promise that resolves to the updated model instance.
 */
export const update = async (modelData, updateData, options = {}) =>
  await modelData.update(updateData, options);

/**
 * Removes (soft deletes) a model instance from the database.
 *
 * @async
 * @param {Object} modelData - The model instance to remove.
 * @param {Object} [options={}] - Additional options for the database operation.
 * @returns {Promise<Object>} A promise that resolves to the removed model instance.
 */
export const remove = async (modelData, options = {}) =>
  await modelData.destroy({
    ...options,
  });

/**
 * Creates a new policy in the database.
 *
 * @async
 * @param {Object} server - The server object containing the database models.
 * @param {Object} data - The data for the new policy.
 * @param {Object} [options={}] - Additional options for the database operation.
 * @returns {Promise<Object>} A promise that resolves to the newly created Policy object.
 */
export const createPolicy = async (server, data, options = {}) =>
  await server.psql.Policy.create(data, options);

/**
 * Upserts (inserts or updates) a policy for a module.
 *
 * @async
 * @param {Object} server - The server object containing the database models.
 * @param {Object} policyData - The policy data to be upserted.
 * @param {string} policyData.parentId - The ID of the parent department module.
 * @param {boolean} policyData.view - Whether viewing is allowed.
 * @param {boolean} policyData.edit - Whether editing is allowed.
 * @param {boolean} policyData.create - Whether creation is allowed.
 * @param {boolean} policyData.delete - Whether deletion is allowed.
 * @param {boolean} policyData.import - Whether importing is allowed.
 * @param {boolean} policyData.export - Whether exporting is allowed.
 * @param {Object} options - Additional options for the database operation.
 * @param {Object} options.transaction - The database transaction to use.
 * @param {string} options.authInfoId - The ID of the user performing the operation.
 * @returns {Promise<Object>} A promise that resolves to the upserted Policy object.
 */
export const upsertPolicy = async (server, policyData, options) => {
  const { Policy } = server.psql;
  const { parentId, ...policies } = policyData;
  let isDirty = true;

  const [policy, created] = await Policy.findOrCreate({
    where: { parentId },
    defaults: {
      ...policies,
    },
    ...options,
  });
  const originalPolicy = policy.toJSON();

  // Assume policy was newly created
  let afterState = policy.toJSON();

  if (!created) {
    const updatedPolicy = await policy.update(
      {
        ...policies,
      },
      options,
    );
    isDirty = updatedPolicy.isDirty;

    // Overwrite diff as policySetting is updated
    afterState = updatedPolicy.toJSON();
    const { fieldsChanged } = computeUpdateDiff(originalPolicy, updatedPolicy.toJSON());

    // if no field changed, clear diff to avoid unnecessary audit records
    if (fieldsChanged.length === 0) {
      afterState = null;
    }
  }

  return { policy, isDirty, created, afterState };
};

/**
 * Removes a department module and its associated policy from the database.
 *
 * @async
 * @param {Object} server - The server object containing the database models.
 * @param {string|number} departmentModuleId - The ID of the department module to remove.
 * @param {Object} [options={}] - Additional options for the database operation.
 * @param {Object} [options.transaction] - The database transaction to use.
 * @param {string} [options.authInfoId] - The ID of the user performing the operation.
 * @returns {Promise<number>} A promise that resolves to the number of deleted records.
 */
export const removeDepartmentModule = async (server, departmentModuleId, options = {}) => {
  const { transaction } = options;

  await server.psql.Policy.destroy({
    where: { parentId: departmentModuleId },
    transaction,
  });

  const deletedCount = await server.psql.DepartmentModule.destroy({
    where: { id: departmentModuleId },
    transaction,
  });

  return deletedCount;
};
