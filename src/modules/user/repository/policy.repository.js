import { Op } from 'sequelize';
/**
 * Find a policy by parent ID
 * @param {Object} fastify - The fastify instance
 * @param {string} parentId - The parent ID
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Object>} - The policy
 */
export const findByParentId = async (fastify, parentId, options = {}) => {
  const { Policy } = fastify.psql;
  return await Policy.findOne({
    where: { parentId },
    ...options,
  });
};

/**
 * Find policy by multiple parent IDs
 * @param {Object} fastify - The fastify instance
 * @param {string[]} parentIds - Array of parent IDs to search for
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Object[]>} - Array of policy matching the parent IDs
 */
export const findByParentIds = async (fastify, parentIds, options = {}) => {
  const { Policy } = fastify.psql;

  if (!parentIds || parentIds.length === 0) {
    return [];
  }

  return await Policy.findAll({
    where: {
      parentId: {
        [Op.in]: parentIds,
      },
    },
    ...options,
  });
};

/**
 * Bulk creates multiple policy records in the database
 * @param {Object} fastify - The fastify instance containing database connection
 * @param {Array} data - Array of policy objects to be created
 * @param {Object} options - Additional options for the bulk create operation
 * @param {Object} options.transaction - The database transaction to use
 * @returns {Promise<Array>} - Array of created policy instances
 */
export const bulkCreate = async (fastify, data, options = {}) => {
  return await fastify.psql.Policy.bulkCreate(data, {
    ...options,
    returning: true,
  });
};

/**
 * Bulk upserts policy
 * @param {Object} fastify - The fastify instance
 * @param {Array} data - Array of policy objects to upsert
 * @param {Object} options - Additional options for the operation
 * @param {Array} options.conflictFields - Fields to check for conflicts
 * @param {Object} options.transaction - The database transaction
 * @returns {Promise<Array>} - The upserted policy
 */
export const bulkUpsert = async (fastify, data, conflictFields, options = {}) => {
  const { Policy } = fastify.psql;

  return await Policy.bulkCreate(data, {
    updateOnDuplicate: [
      ...Object.keys(data[0]).filter((key) => !conflictFields.includes(key)),
      'updatedAt',
    ],
    ...options,
    returning: true,
  });
};

/**
 * Upsert a policy
 * @param {Object} fastify - The fastify instance
 * @param {Object} values - The values to upsert
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Object>} - The upserted policy
 */
export const upsert = async (fastify, values, options = {}) => {
  const { Policy } = fastify.psql;
  const { parentId, ...updateData } = values;

  const [instance, created] = await Policy.findOrCreate({
    where: { parentId },
    defaults: { ...values },
    ...options,
  });

  if (!created) {
    await instance.update(updateData, options);
  }

  return [instance, created];
};
