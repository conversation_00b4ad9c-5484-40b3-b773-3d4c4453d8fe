/**
 * Find all role module policies with their associated policy and modules for a specific role
 * @param {Object} server - The server instance
 * @param {string} roleId - The ID of the role
 * @returns {Promise<Array>} - Array of role module policies with their policy and modules
 */
export const findAllByRoleIdWithPolicyAndModules = async (fastify, roleId) => {
  const { Module, Policy, RoleModule } = fastify.psql;
  return await RoleModule.findAll({
    where: { roleId },
    include: [
      {
        model: Policy,
        as: 'policy',
      },
      {
        model: Module,
        as: 'module',
      },
    ],
  });
};
/**
 * Bulk creates role modules
 * @param {Object} fastify - The fastify instance
 * @param {Array} data - Array of role module objects to create
 * @param {Object} options - Additional options for the operation
 * @returns {Promise<Array>} - The created role modules
 */
export const bulkCreate = async (fastify, data, options = {}) => {
  return await fastify.psql.RoleModule.bulkCreate(data, {
    ...options,
    returning: true,
  });
};

/**
 * Find or create a role module policy
 * @param {Object} fastify - The fastify instance
 * @param {Object} where - The where clause
 * @param {Object} defaults - The default values
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Array>} - [RoleModule, created]
 */
export const findOrCreate = async (fastify, where, defaults, options = {}) => {
  const { RoleModule } = fastify.psql;
  return await RoleModule.findOrCreate({
    where,
    defaults,
    ...options,
  });
};

/**
 * Find all role module policies by role ID
 * @param {Object} fastify - The fastify instance
 * @param {string} roleId - The role ID
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Array>} - The role module policies
 */
export const findAllByRoleId = async (fastify, roleId, options = {}) => {
  const { RoleModule } = fastify.psql;
  return await RoleModule.findAll({
    where: { roleId },
    ...options,
  });
};

/**
 * Update a role module policy
 * @param {Object} RoleModule - The role module policy to update
 * @param {Object} data - The data to update
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Object>} - The updated role module policy
 */
export const update = async (roleModule, data, options = {}) => {
  return await roleModule.update(data, options);
};

/**
 * Find all role module policies by role ID with policy
 * @param {Object} server - The server instance
 * @param {string} roleId - The role ID
 * @returns {Promise<Array>} - The role module policies with policy
 */
export const findAllByRoleIdWithPolicy = async (server, roleId) => {
  return await server.psql.connection.models.RoleModule.findAll({
    where: { roleId },
    include: [
      {
        model: server.psql.connection.models.Policy,
        as: 'policy',
      },
    ],
  });
};

/**
 * Bulk upsert role modules
 * @param {Object} fastify - The fastify instance
 * @param {Array} data - Array of role module data with roleId and moduleId
 * @param {Array<string>} conflictFields - Fields to use as unique constraint for identifying existing records
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Array>} - The upserted role modules
 */
export const bulkUpsert = async (fastify, data, conflictFields, options = {}) => {
  const { RoleModule } = fastify.psql;

  const allKeys = [...new Set(data.flatMap((obj) => Object.keys(obj)))];
  const updateOnDuplicate = allKeys.filter((field) => !conflictFields.includes(field));

  return await RoleModule.bulkCreate(data, {
    updateOnDuplicate,
    returning: true,
    ...options,
  });
};

/**
 * Find all role modules by multiple role IDs and a specific module ID
 * @param {Object} fastify - The fastify instance
 * @param {Array<string>} roleIds - Array of role IDs
 * @param {string} moduleId - The module ID
 * @param {Object} options - Additional options including transaction
 * @returns {Promise<Array>} - The role modules
 */
export const findAllByRoleIdsAndModuleId = async (fastify, roleIds, moduleId, options = {}) => {
  const { RoleModule } = fastify.psql;
  return await RoleModule.findAll({
    where: {
      roleId: roleIds,
      moduleId,
    },
    ...options,
  });
};
