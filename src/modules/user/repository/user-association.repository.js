/**
 * Creates a new UserAssociation entry.
 * @param {Object} server - The server object containing the PostgreSQL models.
 * @param {Object} data - The data for creating the new user entry.
 * @param {Object} [options={}] - Additional options for the create operation.
 * @returns {Promise<Object>} A promise that resolves to the newly created user entry.
 */
export const create = async (server, data, options = {}) => {
  return await server.psql.UserAssociation.create(data, options);
};

/**
 * Finds a user association by user ID.
 * @param {Object} server - The server object containing the PostgreSQL models.
 * @param {number|string} id - The ID of the user to find the association for.
 * @returns {Promise<Object|null>} A promise that resolves to the user association object if found, or null if not found.
 */
export const findByUserId = async (server, entity, id) => {
  return await server.psql.UserAssociation.findOne({
    where: {
      userId: id,
      entityId: entity.id,
    },
    include: [
      {
        association: 'uac',
        required: false,
        include: [
          {
            association: 'cl',
            required: false,
          },
        ],
      },
    ],
  });
};

/**
 * Updates a record in the database using the provided model.
 * @param {Object} model - The model object used to perform the update operation.
 * @param {Object} data - The data to update the record with.
 * @param {Object} [options={}] - Additional options for the update operation.
 * @returns {Promise<Object>} A promise that resolves to the result of the update operation.
 */
export const update = async (model, data, options = {}) => {
  const res = await model.update(data, options);
  return res;
};
