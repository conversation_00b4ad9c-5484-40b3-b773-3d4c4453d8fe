import * as DepartmentRepository from '#src/modules/user/repository/department.repository.js';
import * as PolicyRepository from '#src/modules/user/repository/policy.repository.js';
import * as RoleModuleRepository from '#src/modules/user/repository/role-module.repository.js';
import * as RoleRepository from '#src/modules/user/repository/role.repository.js';
import * as UserAssociationCurrencyRepository from '#src/modules/user/repository/user-association-currency.repository.js';
import * as UserAssociationRepository from '#src/modules/user/repository/user-association.repository.js';
import * as UserInvitationRepository from '#src/modules/user/repository/user-invitation.repository.js';
import * as UserMfaSettingRepository from '#src/modules/user/repository/user-mfa-setting.repository.js';
import * as UserSSOAccountRepository from '#src/modules/user/repository/user-sso-account.repository.js';
import * as UserRepository from '#src/modules/user/repository/user.repository.js';

export {
  DepartmentRepository,
  UserAssociationCurrencyRepository,
  UserAssociationRepository,
  UserInvitationRepository,
  UserMfaSettingRepository,
  UserRepository,
  UserSSOAccountRepository,
  PolicyRepository,
  RoleModuleRepository,
  RoleRepository,
};
