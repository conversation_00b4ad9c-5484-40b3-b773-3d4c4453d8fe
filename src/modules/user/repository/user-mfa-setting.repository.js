/**
 * Creates a new MFA (Multi-Factor Authentication) entry in the database.
 *
 * @param {Object} server - The server object containing the database connection.
 * @param {Object} data - The data object containing MFA details to be stored.
 * @param {Object} [options={}] - Optional settings for the database operation.
 * @returns {Promise<Object>} A promise that resolves to the created MFA entry.
 */
export const create = async (server, data, options = {}) => {
  return await server.psql.UserMfaSetting.create(data, options);
};

/**
 * Updates a record in the database using the provided model.
 * @param {Object} model - The model object used to perform the update operation.
 * @param {Object} data - The data to update the record with.
 * @param {Object} [options={}] - Additional options for the update operation.
 * @returns {Promise<Object>} A promise that resolves to the result of the update operation.
 */
export const update = async (model, data, options = {}) => {
  const res = await model.update(data, options);
  return res;
};

/**
 * Retrieves a user's MFA (Multi-Factor Authentication) setting by their user ID.
 *
 * @param {Object} fastify - The fastify instance containing the database connection.
 * @param {number|string} userId - The ID of the user whose MFA setting is to be retrieved.
 * @returns {Promise<Object|null>} A promise that resolves to the user's MFA setting object if found, or null if not found.
 */
export const findByUserId = async (fastify, userId) =>
  await fastify.psql.UserMfaSetting.findOne({
    where: { userId },
  });
