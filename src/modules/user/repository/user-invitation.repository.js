import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

export const findAll = async (fastify, query) => {
  const { UserInvitation, UserAssociation } = fastify.psql;

  // need to include entity to retrieve entity name
  const includes = [
    {
      model: UserAssociation,
      as: 'ua',
      required: true,
    },
  ];

  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    UserInvitation,
    includes,
  );

  return applyOffsetPagination(fastify, UserInvitation, query, whereFilter, includeFilter);
};

/**
 * Retrieves a user invitation record by its ID from the database.
 *
 * @param {Object} fastify - The Fastify instance containing the database connection.
 * @param {number|string} id - The ID of the user invitation to retrieve.
 *
 * @returns {Promise<Object>} A promise that resolves to the user invitation record with the specified ID.
 * If no record is found, the promise resolves to null.
 */
export const findById = async (fastify, id) => {
  const { UserInvitation } = fastify.psql;
  const inv = await UserInvitation.findByPk(id);
  return inv;
};

/**
 * Creates a new user invitation record in the database.
 *
 * @param {Object} server - The server object containing the database connection.
 * @param {Object} data - The data to be inserted into the user invitation table.
 * @param {Object} [options={}] - Additional options for the database operation.
 *
 * @returns {Promise<Object>} The newly created user invitation record.
 */
export const create = async (server, data, options = {}) => {
  return await server.psql.UserInvitation.create(data, options);
};

/**
 * Updates an existing record in the database using the provided model.
 *
 * @param {Object} model - The sequelize model representing the database table to update.
 * @param {Object} data - The data to be updated in the record.
 * @param {Object} [options={}] - Additional options for the database operation.
 *
 * @returns {Promise<Array>} A promise that resolves to an array containing the number of affected rows and the affected rows themselves.
 * The affected rows are instances of the model.
 */
export const update = async (model, data, options = {}) => {
  const res = await model.update(data, options);
  return res;
};
