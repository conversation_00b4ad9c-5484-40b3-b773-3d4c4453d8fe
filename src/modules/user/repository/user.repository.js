import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

const baseCurrencyInclude = {
  association: 'uac',
  required: false,
  include: [
    {
      association: 'cl',
      required: false,
      include: [
        {
          association: 'localisation',
          required: true,
        },
      ],
    },
  ],
};

const baseIncludes = {
  mfa: { association: 'mfa', required: true },
  ua: {
    association: 'ua',
    required: false,
    include: [
      baseCurrencyInclude,
      {
        association: 'entity',
        required: false,
      },
      {
        association: 'role',
        required: false,
        include: [
          {
            association: 'department',
            required: false,
          },
        ],
      },
      {
        association: 'ui',
        required: false,
      },
    ],
  },
  parent: {
    association: 'parent',
    required: false,
    include: [
      {
        association: 'ua',
        required: false,
        include: [
          baseCurrencyInclude,
          {
            association: 'entity',
            required: false,
          },
          {
            association: 'role',
            required: false,
            include: [
              {
                association: 'department',
                required: false,
              },
            ],
          },
        ],
      },
    ],
  },
};

const getUserIncludes = (...keys) => keys.map((key) => baseIncludes[key]).filter(Boolean);

/**
 * Retrieves all user entries based on the provided query filters.
 *
 * @param {Object} fastify - The Fastify instance containing the PostgreSQL models.
 * @param {Object} query - The query object containing filters and pagination options.
 * @returns {Promise<Array>} A promise that resolves to an array of user entries matching the query filters.
 */
export const findAll = async (fastify, query) => {
  const { User } = fastify.psql;
  const includes = getUserIncludes('mfa', 'ua');
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    User,
    includes,
  );

  const users = await applyOffsetPagination(fastify, User, query, whereFilter, includeFilter);

  return users;
};

/**
 * Retrieves all sub-account user entries based on the provided query filters.
 *
 * @param {Object} fastify - The Fastify instance containing the PostgreSQL models.
 * @param {Object} query - The query object containing filters and pagination options.
 * @returns {Promise<Array>} A promise that resolves to an array of user entries matching the query filters.
 */
export const findAllSubAccount = async (fastify, query) => {
  const { User } = fastify.psql;
  const includes = getUserIncludes('mfa', 'parent');

  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    User,
    includes,
  );

  const users = await applyOffsetPagination(fastify, User, query, whereFilter, includeFilter);

  return users;
};

/**
 * Finds an user control entry by its ID.
 * @param {Object} fastify - The Fastify instance.
 * @param {number|string} id - The ID of the user entry.
 * @returns {Promise<Object|null>} A promise that resolves to the user entry or null if not found.
 */
export const findById = async (fastify, id) => {
  const user = await fastify.psql.User.findByPk(id, {
    include: [
      {
        association: 'mfa',
        required: true,
      },
      {
        association: 'parent',
        required: false,
        include: [
          {
            association: 'ua',
            required: false,
            include: [
              {
                association: 'uac',
                required: false,
                include: [
                  {
                    association: 'cl',
                    required: false,
                    include: [
                      {
                        association: 'localisation',
                        required: true,
                      },
                    ],
                  },
                ],
              },
              {
                association: 'entity',
                required: false,
              },
              {
                association: 'role',
                required: false,
                include: [
                  {
                    association: 'department',
                    required: false,
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        association: 'ua',
        required: false,
        include: [
          {
            association: 'uac',
            required: false,
            include: [
              {
                association: 'cl',
                required: false,
                include: [
                  {
                    association: 'localisation',
                    required: true,
                  },
                ],
              },
            ],
          },
          {
            association: 'entity',
            required: false,
          },
          {
            association: 'role',
            required: false,
            include: [
              {
                association: 'department',
                required: false,
              },
            ],
          },
          {
            association: 'ui',
            required: false,
          },
        ],
      },
    ],
  });

  return user;
};

/**
 * Finds a user entry by their username.
 *
 * @param {Object} fastify - The Fastify instance containing the PostgreSQL models.
 * @param {string} username - The username of the user to find.
 * @returns {Promise<Object|null>} A promise that resolves to the user entry if found, or null if not found.
 */
export const findUser = async (fastify, query, withEntity = false) => {
  const includes = [
    {
      association: 'ua',
      required: true,
      include: withEntity
        ? [
            {
              association: 'entity',
              required: true,
              include: [
                {
                  association: 'parent',
                },
              ],
            },
          ]
        : [],
    },
  ];

  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    fastify.psql.User,
    includes,
  );

  return fastify.psql.User.findOne({
    where: whereFilter,
    include: includeFilter,
    subQuery: false,
  });
};

/**
 * Creates a new User entry.
 * @param {Object} server - The server object containing the PostgreSQL models.
 * @param {Object} data - The data for creating the new user entry.
 * @param {Object} [options={}] - Additional options for the create operation.
 * @returns {Promise<Object>} A promise that resolves to the newly created user entry.
 */
export const create = async (server, data, options = {}) =>
  await server.psql.User.create(data, options);

/**
 * Updates a record in the database using the provided model.
 * @param {Object} model - The model object used to perform the update operation.
 * @param {Object} data - The data to update the record with.
 * @param {Object} [options={}] - Additional options for the update operation.
 * @returns {Promise<Object>} A promise that resolves to the result of the update operation.
 */
export const update = async (model, data, options = {}) => {
  const res = await model.update(data, options);
  return res;
};

/**
 * Retrieves sub-accounts based on the provided parent ID and status.
 *
 * @param {Object} server - The server object containing the PostgreSQL models.
 * @param {Object} data - The data containing filter criteria for sub-accounts.
 * @param {number|string} data.parentId - The ID of the parent account to filter sub-accounts.
 * @param {string} data.status - The status to filter sub-accounts.
 * @param {Object} [options={}] - Additional options for the find operation.
 * @returns {Promise<Array>} A promise that resolves to an array of sub-account entries matching the criteria.
 */
export const getSubAccounts = async (server, data, options = {}) => {
  const { parentId, status } = data;

  return await server.psql.User.findAll({
    where: {
      parentId,
      status,
    },
    ...options,
  });
};
