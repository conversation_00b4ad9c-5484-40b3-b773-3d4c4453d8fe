import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

/**
 * Retrieves all user entries with pagination based on the given request.
 *
 * @param {Object} request - The request object containing query parameters for pagination.
 * @param {Object} request.query - The query parameters from the request.
 * @param {Object} request.server - The server instance for applying pagination.
 * @param {string|number} entityId - The ID of the entity to filter the user entries.
 * @returns {Promise<Object>} A promise that resolves to the paginated result of user entries.
 */
export const findAll = async (fastify, query) => {
  const { UserSSOAccount } = fastify.psql;
  const includes = [
    {
      association: 'user',
      required: false,
    },
  ];

  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    UserSSOAccount,
    includes,
  );

  const usersso = await applyOffsetPagination(
    fastify,
    UserSSOAccount,
    query,
    whereFilter,
    includeFilter,
  );

  return usersso;
};

/**
 * Retrieves a single user entry by its ID.
 *
 * @param {Object} fastify - The Fastify instance containing the database connection.
 * @param {Object} query - The query parameters used to filter the user entry.
 * @returns {Promise<Object|null>} A promise that resolves to the user entry if found, or null if not found.
 */
export const findById = async (fastify, query) => {
  const { UserSSOAccount } = fastify.psql;

  const includes = [];
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    UserSSOAccount,
    includes,
  );
  const usersso = await UserSSOAccount.findOne({
    where: whereFilter,
    include: includeFilter,
  });

  return usersso;
};

/**
 * Updates an existing record in the database using the provided model.
 *
 * @param {Object} model - The sequelize model representing the database table to update.
 * @param {Object} data - The data to be updated in the record.
 * @param {Object} [options={}] - Additional options for the database operation.
 *
 * @returns {Promise<Array>} A promise that resolves to an array containing the number of affected rows and the affected rows themselves.
 * The affected rows are instances of the model.
 */
export const update = async (model, data, options = {}) => {
  const res = await model.update(data, options);
  return res;
};

/**
 * Finds a single user SSO account based on the provided criteria.
 * @param {Object} fastify - The Fastify instance.
 * @param {Object} where - The criteria to search for.
 * @param {Object} [options={}] - Additional options for the query.
 * @returns {Promise<Object|null>} A promise that resolves to the user SSO account or null if not found.
 */
export const findOne = async (fastify, where, options = {}) =>
  await fastify.psql.UserSSOAccount.findOne({
    where,
    ...options,
  });

/**
 * Creates a new user SSO account.
 * @param {Object} fastify - The Fastify instance.
 * @param {Object} data - The data for creating the new user SSO account.
 * @param {Object} [options={}] - Additional options for the create operation.
 * @returns {Promise<Object>} A promise that resolves to the newly created user SSO account.
 */
export const create = async (fastify, data, options = {}) =>
  await fastify.psql.UserSSOAccount.create(data, options);
