import bcrypt from 'bcrypt';

import { CoreConstant, LinkConstant } from '#src/modules/core/constants/index.js';
import { EntityRepository, LinkRepository } from '#src/modules/core/repository/index.js';
import { SettingRepository } from '#src/modules/setting/repository/index.js';
import { AuthConstant, UserConstant } from '#src/modules/user/constants/index.js';
import { AuthError } from '#src/modules/user/errors/index.js';
import { UserRepository, UserMfaSettingRepository } from '#src/modules/user/repository/index.js';
import { clearCache, fetchFromCache, generateCacheKey, getCache } from '#src/utils/cache.util.js';
import { decodeJWT } from '#src/utils/jwt.util.js';
import { handle2FASetup } from '#src/utils/twofa.util.js';

const { USER_STATUSES, USER_MFA_STATUSES, USER_MFA_SETUP_STATUSES } = UserConstant;

const { EXPIRY_TIMES, KEYS, LIMITS } = AuthConstant;
/**
 * Checks if the IP has exceeded the maximum allowed login attempts
 * @param {Object} server - The server instance
 * @param {string} ip - The IP address
 * @throws {AuthError} If too many requests from the IP
 */
export const checkIpRateLimiting = async (server, ip) => {
  const cacheKey = generateIpFailedLoginCacheKey(ip);

  const ipAttempts = await getCache(server.redis.db3, cacheKey);

  if (ipAttempts && parseInt(ipAttempts, 10) >= LIMITS.IP_ATTEMPTS) {
    throw AuthError.tooManyRequests();
  }
};

/**
 * Checks system status for maintenance mode
 * @param {Object} server - The server instance
 * @param {string} hierarchy - The access id
 * @throws {AuthError} If system is in maintenance mode and user is not ROOT
 */
export const checkSystemStatus = async (server, user) => {
  // Bypass system availability check if user is ROOT
  if (user.ua[0]?.entity.hierarchy == CoreConstant.HIERARCHY.ROOT) {
    return;
  }

  const cacheKey = generateCacheKey(
    'maintenance_mode_settings',
    {
      raw: { url: '/settings/safety/maintenanceStatus' },
      query: {},
      entity: { id: 'global' },
    },
    { excludeKey: true },
  );

  const maintenanceStatus = await fetchFromCache(server.redis, cacheKey, async () => {
    return await SettingRepository.getSingleSetting(server, {
      category: 'safety',
      field: 'maintenanceStatus',
    });
  });

  if (maintenanceStatus?.customSettings?.value == 'on') {
    throw AuthError.maintenanceMode();
  }
};

/**
 * Validates the user account status
 * @param {Object} user - The user object
 * @throws {AuthError} If account status is not active
 */
export const validateUserStatus = (user) => {
  switch (user.status) {
    case USER_STATUSES.ACTIVE:
      return;
    case USER_STATUSES.INACTIVE:
      throw AuthError.accountInactive();
    case USER_STATUSES.SUSPENDED:
      throw AuthError.accountSuspended();
    case USER_STATUSES.PENDING:
      throw AuthError.accountPending();
    default:
      throw AuthError.accountInactive();
  }
};

/**
 * Validates the user's password
 * @param {Object} server - The server instance
 * @param {Object} user - The user object
 * @param {string} password - The password to validate
 * @param {string} ip - The IP address
 * @throws {AuthError} If password is invalid
 */
export const validatePassword = async (server, user, password, ip) => {
  const isPasswordValid = await bcrypt.compare(password, user.password);

  if (!isPasswordValid) {
    const cacheKey = generateCacheKey(
      'login_attempts_settings',
      {
        raw: { url: '/settings/safety/passwordMaximumAttempts' },
        query: {},
        entity: { id: 'global' },
      },
      { excludeKey: true },
    );

    const loginAttemptsSettings = await fetchFromCache(server.redis, cacheKey, async () => {
      return await SettingRepository.getSingleSetting(server, {
        category: 'safety',
        field: 'passwordMaximumAttempts',
      });
    });

    const maxAttempts = loginAttemptsSettings?.customSettings?.value || LIMITS.LOGIN_ATTEMPTS;
    const attempts = await incrementFailedLoginAttempts(server, user.id, ip);

    if (attempts >= maxAttempts) {
      await UserRepository.update(user, { status: USER_STATUSES.SUSPENDED });
      await resetFailedLoginAttempts(server, user.id);
    }

    throw AuthError.invalidCredentials();
  }

  await resetFailedLoginAttempts(server, user.id, ip);
};

/**
 * Validates user access to an entity and retrieves entity data
 *
 * @param {Object} server - The server instance with database connections
 * @param {string} user - The user requesting access
 * @param {string|null} accessId - The access ID of the target entity (optional)
 * @returns {Promise<Object>} Object containing access validation result and entity data
 * @throws {AuthError} If access is invalid or entity not found
 */
export const validateAccessAndGetEntityData = async (server, user, accessId = null) => {
  let userAssociation = null;
  let targetEntity = null;
  let parentEntity = null;

  if (!accessId) {
    // No accessId provided, use user entity as target
    userAssociation = user.ua[0];
    targetEntity = user.ua[0].entity;
    parentEntity = targetEntity.parent;

    return {
      hasAccess: true,
      userAssociation,
      targetEntity,
      parentEntity,
    };
  }

  // Find the target entity by accessId
  targetEntity = await EntityRepository.getEntity(server, { accessId });

  if (!targetEntity) {
    throw AuthError.invalidAccessId();
  }

  // Collect all entity IDs in the hierarchy chain
  const entityIds = await collectEntityHierarchyIds(server, targetEntity);

  // Verify user has access to the entity hierarchy by re-querying with entity filter
  const query = {
    filter_username_eq: user.username,
    'filter_ua.entity.id_in': entityIds,
  };

  const userWithAccess = await UserRepository.findUser(server, query, true);

  if (!userWithAccess) {
    throw AuthError.invalidAccessId();
  }

  return {
    hasAccess: true,
    userAssociation: userWithAccess.ua[0],
    targetEntity,
    parentEntity: targetEntity.parent,
  };
};

/**
 * Collects all entity IDs in the hierarchy chain starting from the given entity
 * @param {Object} server - The server instance
 * @param {Object} entity - The entity to start from
 * @returns {Promise<Array<string>>} Array of entity IDs in the hierarchy
 */
const collectEntityHierarchyIds = async (server, targetEntity) => {
  let entityIds = '';
  let currentEntity = targetEntity;

  while (currentEntity) {
    entityIds = entityIds ? `${entityIds},${currentEntity.id}` : `${currentEntity.id}`;
    currentEntity = currentEntity.parentId
      ? await EntityRepository.getEntity(server, { id: currentEntity.parentId })
      : null;
  }

  return entityIds;
};

/**
 * Determines if a user entity has access to a target entity based on hierarchy rules
 *
 * @param {Object} userEntity - The entity associated with the user
 * @param {Object} targetEntity - The target entity being accessed
 */
export const determineEntityAccess = (userEntity, targetEntity) => {
  if (!userEntity || !targetEntity) {
    return false;
  }

  // 1. Check if user has root access
  const isRootUser = userEntity.hierarchy === CoreConstant.HIERARCHY.ROOT;

  // 2. Check if accessing same entity
  const isSameEntity = targetEntity.id === userEntity.id;

  // 3. Check if the target entity is a child of the user's entity (hierarchy-based)
  const isChildEntity = targetEntity.parentId === userEntity.id;

  return isRootUser || isSameEntity || isChildEntity;
};

/**
 * Increments failed login attempts in Redis
 * @param {Object} server - The server instance
 * @param {string} userId - The user ID
 * @param {string} ip - The IP address of the login attempt
 * @returns {Promise<number>} The new count of failed attempts
 */
export const incrementFailedLoginAttempts = async (server, userId, ip) => {
  const redis = server.redis.db3;

  const userCacheKey = generateUserFailedLoginCacheKey(userId);
  const attempts = await redis.incr(userCacheKey);

  if (attempts === 1) {
    await redis.expire(userCacheKey, EXPIRY_TIMES.FAILED_LOGIN);
  }

  const ipCacheKey = generateIpFailedLoginCacheKey(ip);
  await redis.incr(ipCacheKey);
  await redis.expire(ipCacheKey, EXPIRY_TIMES.FAILED_LOGIN);

  return attempts;
};

/**
 * Resets failed login attempts in Redis
 * @param {Object} server - The server instance
 * @param {string} userId - The user ID
 * @returns {Promise<void>}
 */
export const resetFailedLoginAttempts = async (server, userId, ip = null) => {
  const redis = server.redis.db3;

  const userCacheKey = generateUserFailedLoginCacheKey(userId);
  await clearCache(redis, userCacheKey);

  if (ip) {
    const ipCacheKey = generateIpFailedLoginCacheKey(ip);
    await clearCache(redis, ipCacheKey);
  }
};

/**
 * Validates that the email domain is allowed for SSO authentication
 * @param {Object} server - The server instance
 * @param {string} emailDomain - The email domain
 * @throws {AuthError} If the email domain is not authorized
 */
export const validateEmailDomain = async (server, emailDomain) => {
  const query = {
    filter_type_eq: LinkConstant.TYPE.EMAIL_DOMAIN,
    filter_url_eq: emailDomain,
  };

  const allowedDomain = await LinkRepository.findOne(server, query);

  if (!allowedDomain) {
    throw AuthError.unauthorisedDomain();
  }
};

/**
 * Checks if two-factor authentication is required for a user based on system settings
 * and the user's current 2FA status.
 *
 * @param {Object} server - The server instance with database and cache connections
 * @param {Object} user - The user object attempting to authenticate
 * @param {Object} userMFA - The user's two-factor authentication settings
 * @returns {Promise<Object|null>} Returns null if 2FA is not required, or an object with 2FA requirements:
 */
export const check2FARequirement = async (server, user, userMFA) => {
  const cacheKey = generateCacheKey('twoFactorAuthentication', {
    raw: { url: '/settings/safety/twoFactorAuthentication' },
    query: {},
    entity: { id: user.ua[0].entity.id },
  });

  const twoFactorAuthentication = await fetchFromCache(server.redis, cacheKey, async () => {
    return await SettingRepository.getSingleSetting(
      server,
      {
        category: 'safety',
        field: 'twoFactorAuthentication',
      },
      { 'filter_customSettings.entityId_eq': user.ua[0].entity.id },
    );
  });

  // Check if 2FA is required by settings or 2fa is enforced for the user or required if user is root user
  // 2FA Requirement Logic:
  // - When entity setting is "ON": All users under the entity must use 2FA regardless of individual user setting
  // - When entity setting is "OFF": Individual user setting takes precedence (user can still enable 2FA)
  // - Root users: Always require 2FA for security
  // Entity "ON" > USER_MFA_STATUS "OFF" (entity overrides user)
  // ENTITY "OFF" < USER_MFA_STATUS "ON" (user setting takes precedence)
  const is2FARequired =
    twoFactorAuthentication?.customSettings?.value === 'on' ||
    userMFA?.status === USER_MFA_STATUSES.ENABLED ||
    user.ua[0]?.entity.hierarchy === CoreConstant.HIERARCHY.ROOT;

  // If 2FA is not required by settings, return null to continue normal login
  return is2FARequired;
};

/**
 * Enhanced 2FA validation with trusted device revocation check
 * @param {Object} server - The server instance
 * @param {Object} user - The user object
 * @param {Object} request - The request object
 * @param {Object} headers - Request headers
 * @returns {Promise<Object|null>} 2FA response if required, null if 2FA not needed
 */
export const validate2FA = async (server, user, request, headers) => {
  const userMFA = await UserMfaSettingRepository.findByUserId(server, user.id);

  const twoFactorResponse = await check2FARequirement(server, user, userMFA);
  if (!twoFactorResponse) {
    return null; // 2FA not required, continue with normal flow
  }

  const response = { requiresTwoFactor: true, userId: user.id };
  // If user has 2FA enabled, check for trusted device
  if (userMFA?.setupStatus === USER_MFA_SETUP_STATUSES.ACTIVE) {
    // Check for trusted device cookie
    const cookies = request.cookies || {};
    const trustedDeviceCookie = cookies[KEYS.TRUSTED_DEVICE_COOKIE_NAME] || null;

    const isTrustedDevice = await validateTrustedDeviceCookie(
      server,
      trustedDeviceCookie,
      user.id,
      headers?.fingerprint,
    );

    return isTrustedDevice ? null : response;
  } else {
    // Handle 2FA setup if needed (for users without 2FA setup before)
    const qrCode = await handle2FASetup(user, userMFA);
    return qrCode ? { ...response, qrCode } : null;
  }
};

/**
 * Validates trusted device cookie by checking against cached JWT token
 * @param {Object} server - The server instance
 * @param {string} cookieValue - The cookie value to validate
 * @param {string} userId - Expected user ID
 * @param {string} fingerprint - Current device fingerprint
 * @returns {Promise<boolean>} Whether the device is trusted and token matches cache
 */
export const validateTrustedDeviceCookie = async (server, cookieValue, userId, fingerprint) => {
  if (!cookieValue || !fingerprint || !userId) {
    return false;
  }

  try {
    // Verify and decode the JWT using the util function
    const payload = await decodeJWT(cookieValue, server);

    // Validate payload contents
    if (!payload.userId || !payload.fingerprint || !payload.expiresAt) {
      return false;
    }

    // Check if user and device fingerprint matches
    if (payload.userId !== userId || payload.fingerprint !== fingerprint) {
      return false;
    }

    // Check if cookie has expired
    const now = Math.floor(Date.now() / 1000);
    if (now > payload.expiresAt) {
      return false;
    }

    // Check if the token exists in cache (not revoked)
    const trustedDeviceCacheKey = `${KEYS.TRUSTED_DEVICE_COOKIE_NAME}:${userId}:${fingerprint}`;
    const cachedToken = await getCache(server.redis.db3, trustedDeviceCacheKey);

    if (!cachedToken) {
      // Token not found in cache (likely revoked)
      return false;
    }

    return cachedToken === cookieValue;
  } catch (error) {
    server.log.debug(error, 'Failed to verify JWT token');
    return false;
  }
};

/**
 * Generates a cache key for tracking failed login attempts by user ID
 * @param {string} userId - The user ID to generate the cache key for
 * @returns {string} The generated cache key for user failed login attempts
 */
const generateUserFailedLoginCacheKey = (userId) => {
  return generateCacheKey(
    `${KEYS.FAILED_LOGIN_KEY_PREFIX}user`,
    {
      raw: { url: `/auth/failed-login/${userId}` },
      query: {},
      entity: { id: 'global' },
    },
    { excludeKey: true },
  );
};

/**
 * Generates a cache key for tracking failed login attempts by IP address
 * @param {string} ip - The IP address to generate the cache key for
 * @returns {string} The generated cache key for IP-based failed login attempts
 */
const generateIpFailedLoginCacheKey = (ip) => {
  return generateCacheKey(
    `${KEYS.FAILED_LOGIN_KEY_PREFIX}ip`,
    {
      raw: { url: `/auth/ip-rate-limit/${ip}` },
      query: {},
      entity: { id: 'global' },
    },
    { excludeKey: true },
  );
};

/**
 * Validates a pre-authentication token
 * @param {string} tempToken - The pre-authentication token to validate
 * @param {string} ip - The IP address that the token was generated for
 * @param {Object} server - The server object
 * @returns {Promise<Object>} A promise that resolves to an object containing the userId and accessId
 * @throws {AuthError} If the token is invalid or not generated for the given IP address
 */
export const validatePreAuthToken = async (tempToken, ip, server) => {
  const tokenInfo = await decodeJWT(tempToken, server);

  // Check token type and ip address
  if (tokenInfo?.type !== KEYS.PRE_AUTH_TOKEN_NAME || tokenInfo?.ip !== ip) {
    throw AuthError.invalidPreAuthToken();
  }

  return { userId: tokenInfo.sub, accessId: tokenInfo.accessId };
};
