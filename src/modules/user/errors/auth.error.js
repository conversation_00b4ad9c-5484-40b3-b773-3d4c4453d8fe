import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const AUTH_ERROR_DEF = {
  accountInactive: ['20002', 'error.auth.sentence.accountInactive', 403],
  accountPending: ['20004', 'error.auth.sentence.accountPending', 403],
  accountSuspended: ['20003', 'error.auth.sentence.accountSuspended', 423],
  forbidden: ['20005', 'error.auth.sentence.forbidden', 403],
  googleAuthError: ['20009', 'error.auth.sentence.googleAuthError', 401],
  invalid2FAToken: ['20017', 'error.auth.sentence.invalid2FAToken', 403],
  invalidAccessId: ['20008', 'error.auth.sentence.invalidAccessId', 403],
  invalidCredentials: ['20001', 'error.auth.sentence.invalidCredentials', 401],
  invalidPreAuthToken: ['20020', 'error.auth.sentence.invalidPreAuthToken', 403],
  maintenanceMode: ['20007', 'error.auth.sentence.maintenanceMode', 503],
  ssoAccountPending: ['20011', 'error.auth.sentence.ssoAccountPending', 403],
  tooManyRequests: ['20006', 'error.auth.sentence.tooManyRequests', 429],
  twoFactorAlreadyEnabled: ['20018', 'error.auth.sentence.twoFactorAlreadyEnabled', 403],
  twoFactorAlreadySetup: ['20018', 'error.auth.sentence.twoFactorAlreadySetup', 403],
  twoFactorNotSetup: ['20019', 'error.auth.sentence.twoFactorNotSetup', 403],
  unauthorisedDomain: ['20010', 'error.auth.sentence.unauthorisedDomain', 403],
};

export const authError = createModuleErrors(MODULE_NAMES.USER, AUTH_ERROR_DEF);
