// errors/userErrors.js
import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const USER_ERROR_DEF = {
  userEntityConflict: ['30001', 'error.users.sentence.userAlreadyAssociated', 409],
  parentInactive: ['30002', 'error.users.sentence.parentInactive', 400],
};

export const userError = createModuleErrors(MODULE_NAMES.USER, USER_ERROR_DEF);
