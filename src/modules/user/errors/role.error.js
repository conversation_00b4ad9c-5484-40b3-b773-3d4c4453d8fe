import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const ROLE_ERROR_DEF = {
  invalidHierarchy: ['80001', 'error.roles.sentence.invalidHierarchy', 400],
  updateNotAllowedForHierarchy: ['80002', 'error.roles.sentence.updateNotAllowedForHierarchy', 403],
  parentHasNoPoliciesAccess: ['80003', 'error.roles.sentence.parentHasNoPoliciesAccess', 400],
  exceedsParentPolicies: ['80004', 'error.roles.sentence.exceedsParentPolicies', 400],
  unsupportedModulePolicies: ['80005', 'error.roles.sentence.unsupportedModulePolicies', 400],
  noEntityAccess: ['80006', 'error.roles.sentence.noEntityAccess', 403],
  noRolePolicyAccess: ['80007', 'error.roles.sentence.noRolePolicyAccess', 403],
  noHierarchyModuleAccess: ['80008', 'error.roles.sentence.noHierarchyModuleAccess', 403],
  moduleAccessDenied: ['80009', 'error.roles.sentence.moduleAccessDenied', 403],
  actionNotAllowed: ['80010', 'error.roles.sentence.actionNotAllowed', 403],
};

export const roleError = createModuleErrors(MODULE_NAMES.ROLE, ROLE_ERROR_DEF);
