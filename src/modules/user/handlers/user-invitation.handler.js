import { CoreConstant } from '#src/modules/core/constants/index.js';
import { UserInvitationService } from '#src/modules/user/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { USER },
  MODULE_METHODS: { CREATE, INDEX, UPDATE_STATUS },
} = CoreConstant;

const MODULE = USER;

/**
 * Handles the retrieval of user invitation data with pagination.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the user data and pagination details.
 */
export const index = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_invitation_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(
      request.server.redis,
      cacheKey,
      () => UserInvitationService.index(request),
      SHORT,
    );

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Creates a new user invitation.
 *
 * @param {Object} request - The request object containing the user invitation to be created.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the newly created user data.
 */
export const create = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserInvitationService.create,
    module: MODULE,
    method: CREATE,
  });

/**
 * Updates a user invitation entry.
 *
 * @param {Object} request - The request object containing the ID parameter and the updated data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated user entry.
 */
export const updateStatus = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserInvitationService.updateStatus,
    module: MODULE,
    method: UPDATE_STATUS,
  });
