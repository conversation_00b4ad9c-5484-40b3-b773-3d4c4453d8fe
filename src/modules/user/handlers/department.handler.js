import { createDepartmentHandlers } from '../factories/department-handler.factory.js';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { DepartmentService } from '#src/modules/user/services/index.js';

const {
  MODULE_NAMES: { DEPARTMENT },
  EVENTS: { DEPARTMENT: DEPARTMENT_EVENT },
} = CoreConstant;

// Create all handlers using the factory
const handlers = createDepartmentHandlers({
  service: DepartmentService,
  moduleName: DEPARTMENT,
  eventName: DEPARTMENT_EVENT,
  isTemplate: false,
});

// Export individual handlers
export const {
  index,
  view,
  create,
  updateBasicInformation,
  updatePolicy,
  updateStatus,
  remove,
  options,
} = handlers;
