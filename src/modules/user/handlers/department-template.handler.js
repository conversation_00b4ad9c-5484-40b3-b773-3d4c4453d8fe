import { createDepartmentHandlers } from '../factories/department-handler.factory.js';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { DepartmentService } from '#src/modules/user/services/index.js';

const {
  MODULE_NAMES: { DEPARTMENT_TEMPLATE },
  EVENTS: { DEPARTMENT_TEMPLATE: DEPARTMENT_TEMPLATE_EVENT },
} = CoreConstant;

// Create all handlers using the factory
const handlers = createDepartmentHandlers({
  service: DepartmentService,
  moduleName: DEPARTMENT_TEMPLATE,
  eventName: DEPARTMENT_TEMPLATE_EVENT,
  isTemplate: true,
});

// Export individual handlers
export const {
  index,
  view,
  create,
  updateBasicInformation,
  updatePolicy,
  updateStatus,
  remove,
  options,
} = handlers;
