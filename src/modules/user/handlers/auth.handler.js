import { CoreConstant } from '#src/modules/core/constants/index.js';
import { AuthService } from '#src/modules/user/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  MODULE_NAMES: { USER },
  MODULE_METHODS: {
    LOGIN,
    REGISTER_SSO,
    REQUEST_2FA_LOGIN,
    REQUEST_2FA_SETUP,
    REVOKE_TRUSTED_DEVICE,
    RESET_2FA,
  },
} = CoreConstant;

const MODULE = USER;

/**
 * Handles user authentication and login.
 * Now supports 2FA setup/verification flow during login.
 *
 * @param {Object} request - The request object containing login credentials.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the authentication token and user data,
 *                           or 2FA setup/verification requirements.
 */
export const login = async (request, reply) => {
  return handleServiceResponse({
    request,
    reply,
    serviceFn: AuthService.login,
    module: MODULE,
    method: (result) => {
      if (result?.requiresTwoFactor) {
        return REQUEST_2FA_LOGIN;
      }
      return LOGIN;
    },
  });
};

/**
 * Handles Google SSO authentication.
 *
 * @param {Object} request - The request object containing Google authentication data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing authentication token and user data,
 *                           2FA setup/verification requirements, or pending registration status.
 */
export const google = async (request, reply) => {
  return handleServiceResponse({
    request,
    reply,
    serviceFn: AuthService.google,
    module: MODULE,
    method: (result) => {
      if (result?.status === 'pending') {
        return REGISTER_SSO;
      }
      return LOGIN;
    },
  });
};

/**
 * Initializes two-factor authentication setup for the authenticated user.
 * Generates and returns the necessary secret key and QR code for setting up
 * an authenticator app.
 *
 * @param {Object} request - The request object containing user authentication data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the secret key and QR code.
 */
export const setup2fa = async (request, reply) => {
  return handleServiceResponse({
    request,
    reply,
    serviceFn: AuthService.setup2fa,
    module: MODULE,
    method: REQUEST_2FA_SETUP,
  });
};

/**
 * Verifies 2FA token during login process and completes authentication
 * Handles both 2FA verification and setup completion
 * @param {Object} request - The request object containing verification data
 * @param {Object} reply - The reply object used to send the response back to the client
 * @returns {Promise<Object>} The response object containing JWT token and user data
 */
export const verify2faLogin = async (request, reply) => {
  const result = await AuthService.verify2faLogin(request);

  // If the result contains cookie information, set it on the reply
  if (result?.setCookie) {
    reply.setCookie(result.setCookie.name, result.setCookie.value, result.setCookie.options);
  }

  return handleServiceResponse({
    request,
    reply,
    serviceFn: async () => result,
    module: MODULE,
    method: LOGIN,
  });
};

/**
 * Revokes all trusted devices for the authenticated user
 * Forces the user to perform 2FA on their next login from any device
 *
 * @param {Object} request - The request object containing user authentication data
 * @param {Object} reply - The reply object used to send the response back to the client
 * @returns {Promise<Object>} The response object containing revocation confirmation
 */
export const revokeTrustedDevices = async (request, reply) => {
  const result = await AuthService.revokeTrustedDevices(request);

  // Clear the trusted device cookie if setCookie info is provided
  if (result?.setCookie) {
    reply.setCookie(result.setCookie.name, result.setCookie.value, result.setCookie.options);
  }

  return handleServiceResponse({
    request,
    reply,
    serviceFn: async () => result,
    module: MODULE,
    method: REVOKE_TRUSTED_DEVICE,
  });
};

/**
 * Handler for resetting/disabling two-factor authentication
 * @param {Object} request - The request object
 * @param {Object} reply - The reply object
 * @returns {Promise} Promise that resolves to the service response
 */
export const reset2fa = async (request, reply) => {
  const result = await AuthService.reset2fa(request);

  // Now remove setCookie from the result before sending it back
  const { setCookie, userMfa } = result;

  // Clear the trusted device cookie if setCookie info is provided
  if (setCookie) {
    reply.setCookie(setCookie.name, setCookie.value, setCookie.options);
  }

  return handleServiceResponse({
    request,
    reply,
    serviceFn: async () => userMfa,
    module: MODULE,
    method: RESET_2FA,
  });
};
