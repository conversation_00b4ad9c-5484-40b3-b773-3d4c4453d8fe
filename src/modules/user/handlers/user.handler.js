import { CoreConstant } from '#src/modules/core/constants/index.js';
import { UserService } from '#src/modules/user/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { USER },
  MODULE_METHODS: {
    CREATE,
    CHECK_AVAILABILITY,
    INDEX,
    VIEW,
    OPTION,
    UPDATE_BASIC_INFORMATION,
    UPDATE_ORGANISATION,
    UPDATE_LOGIN_ACCESS,
    UPDATE_STATUS,
  },
} = CoreConstant;

const MODULE = USER;

/** * Handles the retrieval of user data with pagination.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the user data and pagination details.
 */
export const index = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => UserService.index(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Handles the retrieval of sub-account user data with pagination.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the sub-account user data and pagination details.
 */
export const subAccountIndex = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_subaccount_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(
      request.server.redis,
      cacheKey,
      () => UserService.subAccountIndex(request),
      SHORT,
    );

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Creates a new user.
 *
 * @param {Object} request - The request object containing the user data to be created.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the newly created user data.
 */
export const create = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserService.create,
    module: MODULE,
    method: CREATE,
  });

/**
 * Retrieves a specific user entry by ID.
 *
 * @param {Object} request - The request object containing the ID parameter.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the user entry.
 */
export const view = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => UserService.view(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Updates the basic information of a user.
 *
 * @param {Object} request - The request object containing the user data to be updated.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated user data.
 */
export const updateBasicInformation = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserService.updateBasicInformation,
    module: MODULE,
    method: UPDATE_BASIC_INFORMATION,
  });

/**
 * Updates the organisation details of a user.
 *
 * @param {Object} request - The request object containing the user data and organisation details to be updated.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated organisation details of the user.
 */
export const updateOrganisation = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserService.updateOrganisation,
    module: MODULE,
    method: UPDATE_ORGANISATION,
  });

/**
 * Updates the login access details of a user.
 *
 * @param {Object} request - The request object containing the user data and login access details to be updated.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated login access details of the user.
 */
export const updateLoginAccess = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserService.updateLoginAccess,
    module: MODULE,
    method: UPDATE_LOGIN_ACCESS,
  });

/**
 * Updates the status of a user.
 *
 * @param {Object} request - The request object containing the user data and status details to be updated.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated status details of the user.
 */
export const updateStatus = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserService.updateStatus,
    module: MODULE,
    method: UPDATE_STATUS,
  });

/**
 * Handles the retrieval of user options.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the list of user options.
 */
export const options = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserService.option,
    module: MODULE,
    method: OPTION,
  });

/**
 * Checks the availability of a user resource.
 *
 * @param {Object} request - The request object containing the necessary data to check availability.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object indicating the availability status of the user resource.
 */
export const checkAvailability = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserService.checkAvailability,
    module: MODULE,
    method: CHECK_AVAILABILITY,
  });
