import { CoreConstant } from '#src/modules/core/constants/index.js';
import { UserSSOAccountService } from '#src/modules/user/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { USER },
  MODULE_METHODS: { INDEX, UPDATE_STATUS, ASSIGN_USER, ONBOARD_USER },
} = CoreConstant;

const MODULE = USER;

/**
 * Handles the retrieval of a list of users via SSO, utilizing caching for efficiency.
 *
 * @param {Object} request - The request object containing necessary data for processing.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the list of users.
 */
export const index = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_sso_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(
      request.server.redis,
      cacheKey,
      () => UserSSOAccountService.index(request),
      SHORT,
    );

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Updates the status of a user via SSO.
 *
 * @param {Object} request - The request object containing necessary data for processing.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object indicating the result of the update operation.
 */
export const updateStatus = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserSSOAccountService.updateStatus,
    module: MODULE,
    method: UPDATE_STATUS,
  });

/**
 * Assigns a user via SSO.
 *
 * @param {Object} request - The request object containing necessary data for processing.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object indicating the result of the assign operation.
 */
export const assign = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserSSOAccountService.assign,
    module: MODULE,
    method: ASSIGN_USER,
  });

/**
 * Onboards a user via SSO.
 *
 * @param {Object} request - The request object containing necessary data for processing.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object indicating the result of the onboard operation.
 */
export const onboard = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: UserSSOAccountService.onboard,
    module: MODULE,
    method: ONBOARD_USER,
  });
