import * as Auth<PERSON>andler from '#src/modules/user/handlers/auth.handler.js';
import * as DepartmentTemplateHandler from '#src/modules/user/handlers/department-template.handler.js';
import * as Department<PERSON>andler from '#src/modules/user/handlers/department.handler.js';
import * as <PERSON>Handler from '#src/modules/user/handlers/role.handler.js';
import * as UserInvitationHandler from '#src/modules/user/handlers/user-invitation.handler.js';
import * as UserSSOAccountHandler from '#src/modules/user/handlers/user-sso-account.handler.js';
import * as UserHandler from '#src/modules/user/handlers/user.handler.js';

export {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DepartmentHandler,
  DepartmentTemplateHandler,
  UserHandler,
  UserInvitation<PERSON><PERSON><PERSON>,
  <PERSON>rSSOAccountHandler,
  RoleHandler,
};
