import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const CORE_ERROR_DEF = {
  serviceUnavailable: ['10000', 'error.sentence.serviceUnavailable', 503],
  unauthorised: ['10001', 'error.sentence.unauthorised', 401],
  forbidden: ['10002', 'error.sentence.forbidden', 403],
  notFound: ['10003', 'error.sentence.notFound', 404],
  dataNotFound: ['10004', 'error.sentence.dataNotFound', 404],
  unsupportedAction: ['10005', 'error.sentence.unsupportedAction', 405],
  versionConflict: ['10006', 'error.sentence.versionConflict', 409],
  inUse: ['10007', 'error.sentence.inUse', 409],
  unprocessable: ['10008', 'error.sentence.unprocessable', 422],
  tooManyRequests: ['10009', 'error.sentence.tooManyRequests', 429],

  // Validation errors
  invalidData: ['10400', 'error.validation.sentence.invalid', 400],
  alreadyExists: ['10401', 'error.validation.sentence.exists', 400],
  requiredData: ['10402', 'error.validation.sentence.required', 400],
  atLeastOneRequired: ['10403', 'error.validation.sentence.atLeastOneRequired', 400],
  format: ['10404', 'error.validation.sentence.format', 400],
  type: ['10405', 'error.validation.sentence.type', 400],
  allowedValues: ['10406', 'error.validation.sentence.enum', 400],
  exactLength: ['10407', 'error.validation.sentence.exactLength', 400],
  maximum: ['10408', 'error.validation.sentence.maximum', 400],
  maxLength: ['10409', 'error.validation.sentence.maxLength', 400],
  minimum: ['10410', 'error.validation.sentence.minimum', 400],
  minLength: ['10411', 'error.validation.sentence.minLength', 400],
  futureDateOnly: ['10412', 'error.validation.sentence.futureDateOnly', 400],
  containLowercase: ['10413', 'error.validation.sentence.mustContainLowercase', 400],
  containUppercase: ['10414', 'error.validation.sentence.mustContainUppercase', 400],
  containNumber: ['10415', 'error.validation.sentence.mustContainNumber', 400],
  containSpecialChar: ['10416', 'error.validation.sentence.mustContainSpecialCharacter', 400],
  invalidPattern: ['10417', 'error.validation.sentence.pattern', 400],

  // environment errors
  missingEnv: ['10500', 'error.sentence.missingEnv', 503],
  invalidEnv: ['10501', 'error.sentence.invalidEnv', 503],
};

export const coreError = createModuleErrors(MODULE_NAMES.CORE, CORE_ERROR_DEF);
