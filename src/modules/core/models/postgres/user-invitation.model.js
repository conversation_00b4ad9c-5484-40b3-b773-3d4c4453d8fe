import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
export default function (fastify) {
  class UserInvitation extends Model {
    static associate(models) {
      UserInvitation.belongsTo(models.UserAssociation, {
        foreignKey: 'userAssociationId',
        as: 'ua',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  UserInvitation.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      userAssociationId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      invitedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      invitedDate: {
        type: DataTypes.DATE(),
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      acceptedDate: {
        type: DataTypes.DATE(),
        allowNull: true,
      },
      cancelledDate: {
        type: DataTypes.DATE(),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(Object.values(UserConstant.USER_INVITATION_STATUSES)),
        allowNull: false,
        defaultValue: UserConstant.USER_INVITATION_STATUSES.PENDING,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'UserInvitation',
      tableName: 'user_invitations',
      underscored: true,
      timestamps: true,
      paranoid: false,
      sequelize: fastify.psql.connection,
      scopes: {},
    },
  );

  auditableMixin.applyAuditFields(UserInvitation);

  return UserInvitation;
}
