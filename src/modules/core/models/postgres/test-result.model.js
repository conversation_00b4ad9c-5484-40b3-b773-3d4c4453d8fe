import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
export default function (fastify, instance) {
  class TestResult extends Model {
    static associate(models) {
      TestResult.belongsTo(models.EntityApp, {
        foreignKey: 'testableId',
        constraints: false,
        as: 'testable',
      });
    }
  }

  TestResult.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      testableId: {
        type: DataTypes.UUID,
        allowNull: false,
        comment: 'The ID of the associated model instance',
      },
      testableType: {
        type: DataTypes.ENUM(Object.values(CoreConstant.TESTABLE_TYPES)),
        allowNull: false,
        comment: 'The associated model name (e.g., EntityApp, User, etc.)',
      },
      requestHeader: {
        type: DataTypes.JSONB,
        allowNull: false,
      },
      requestBody: {
        type: DataTypes.JSONB,
        allowNull: false,
      },
      response: {
        type: DataTypes.JSONB,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(Object.values(CoreConstant.TEST_RESULT_STATUSES)),
        allowNull: false,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'TestResult',
      tableName: 'test_results',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
    },
  );

  auditableMixin.applyAuditFields(TestResult);

  return TestResult;
}
