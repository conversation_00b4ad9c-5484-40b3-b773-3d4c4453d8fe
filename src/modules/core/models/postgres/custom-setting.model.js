import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin, versionedMixin } from '#src/mixins/index.js';

export default function (fastify, instance) {
  class CustomSetting extends Model {
    static associate(models) {
      CustomSetting.belongsTo(models.Setting, {
        foreignKey: 'parentId',
        as: 'setting',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  CustomSetting.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      parentId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      localisationId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      value: {
        type: DataTypes.STRING(100),
        allowNull: false,
        validate: {
          len: [1, 100],
          notEmpty: true,
        },
      },
      version: {
        type: DataTypes.BIGINT,
        allowNull: false,
        defaultValue: 1,
        validate: {
          min: 1,
          notNull: { msg: 'version is required' },
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'CustomSetting',
      tableName: 'custom_settings',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
    },
  );

  auditableMixin.applyAuditFields(CustomSetting);
  versionedMixin.applyVersioning(CustomSetting);

  return CustomSetting;
}
