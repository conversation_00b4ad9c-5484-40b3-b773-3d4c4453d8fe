import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { MediaAssetConstant } from '#src/modules/media/constants/index.js';

export default function (fastify, instance) {
  class MediaAsset extends Model {}

  MediaAsset.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
        comment: 'The entity ID this media belongs to',
      },
      fileType: {
        type: DataTypes.ENUM(Object.values(MediaAssetConstant.FILE_TYPE)),
        allowNull: false,
        comment: 'Type of media file (image, audio, or document)',
      },
      filePath: {
        type: DataTypes.STRING(2083),
        allowNull: false,
        comment: 'Path to the stored file on disk or cloud storage',
        validate: {
          len: [1, 2083],
        },
      },
      mimeType: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: 'MIME type of the file (e.g., image/jpeg, audio/mp3)',
        validate: {
          len: [1, 255],
        },
      },
      fileSize: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Size of the file in bytes or formatted size',
        validate: {
          min: 0,
        },
      },
      dimension: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: 'Dimensions of the media (e.g., "1920x1080" for images)',
        validate: {
          len: [0, 20],
        },
      },
      thumbnail: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        comment: 'Path to thumbnail version of the media if applicable',
        validate: {
          len: [0, 2083],
        },
      },
      metadata: {
        type: DataTypes.JSONB,
        allowNull: false,
        defaultValue: {},
        comment: 'Additional metadata about the media in JSON format',
      },
      altText: {
        type: DataTypes.STRING(150),
        allowNull: true,
        comment: 'Alternative text for accessibility purposes',
        validate: {
          len: [0, 150],
        },
      },
      visibility: {
        type: DataTypes.ENUM(Object.values(MediaAssetConstant.VISIBILITY)),
        allowNull: false,
        comment: 'Whether the media is publicly accessible or private',
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize: fastify.psql.connection,
      modelName: 'MediaAsset',
      tableName: 'media_assets',
      underscored: true,
      timestamps: true,
      paranoid: true,
    },
  );

  auditableMixin.applyAuditFields(MediaAsset);

  return MediaAsset;
}
