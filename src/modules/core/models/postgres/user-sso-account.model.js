import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
export default function (fastify) {
  class UserSSOAccount extends Model {
    static associate(models) {
      UserSSOAccount.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'user',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  UserSSOAccount.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
          len: [1, 100],
          notEmpty: true,
        },
      },
      sub: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
          len: [1, 100],
          notEmpty: true,
        },
      },
      registeredDate: {
        type: DataTypes.DATE(),
        defaultValue: Sequelize.NOW,
        allowNull: false,
      },
      completedDate: {
        type: DataTypes.DATE(),
        allowNull: true,
      },
      cancelledDate: {
        type: DataTypes.DATE(),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(Object.values(UserConstant.USER_SSO_STATUSES)),
        allowNull: false,
        defaultValue: UserConstant.USER_SSO_STATUSES.PENDING,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'UserSSOAccount',
      tableName: 'user_sso_accounts',
      underscored: true,
      timestamps: true,
      paranoid: false,
      sequelize: fastify.psql.connection,
      scopes: {},
    },
  );

  auditableMixin.applyAuditFields(UserSSOAccount);

  return UserSSOAccount;
}
