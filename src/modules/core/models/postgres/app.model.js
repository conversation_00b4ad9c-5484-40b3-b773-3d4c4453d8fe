import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
export default function (fastify) {
  class App extends Model {
    static associate(models) {
      App.hasMany(models.EntityApp, {
        foreignKey: 'appId',
        as: 'entityApp',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      // associatiation media asset
    }
  }

  App.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      icon: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      category: {
        type: DataTypes.STRING(50),
        allowNull: false,
        validate: {
          len: [1, 50],
          notEmpty: true,
        },
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        validate: {
          len: [1, 100],
          notEmpty: true,
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      apiUrl: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      testFeature: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'App',
      tableName: 'apps',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          unique: true,
          fields: ['name', 'category'],
          name: 'uniq_apps_name_category',
        },
      ],
    },
  );

  auditableMixin.applyAuditFields(App);

  return App;
}
