import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { DepartmentConstant } from '#src/modules/user/constants/index.js';

const { HIERARCHY } = CoreConstant;
const { NAVIGATION_TYPES } = DepartmentConstant;

export default function (fastify, instance) {
  class Module extends Model {
    static associate(models) {
      this.hasOne(models.Policy, {
        foreignKey: 'parentId',
        as: 'policy',
      });
      this.belongsTo(Module, {
        foreignKey: 'parentId',
        as: 'parent',
      });
      this.hasMany(Module, {
        foreignKey: 'parentId',
        as: 'children',
      });
      this.belongsToMany(models.Department, {
        through: models.DepartmentModule,
        as: 'departments',
        foreignKey: 'moduleId',
      });
    }
  }

  Module.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      parentId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'modules',
          key: 'id',
        },
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        validate: {
          len: [1, 100],
          notEmpty: true,
        },
      },
      hierarchy: {
        type: DataTypes.ENUM(Object.values(HIERARCHY)),
        allowNull: false,
      },
      navigationType: {
        type: DataTypes.ARRAY(DataTypes.ENUM(Object.values(NAVIGATION_TYPES))),
        defaultValue: [NAVIGATION_TYPES.SIDE],
        allowNull: false,
      },
      navigationPosition: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      navigationUrl: {
        type: DataTypes.STRING(100),
        allowNull: true,
        validate: {
          len: [1, 100],
          notEmpty: false,
        },
      },
      translationKey: {
        type: DataTypes.STRING(150),
        allowNull: false,
        validate: {
          len: [1, 150],
          notEmpty: true,
        },
      },
      level: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'Module',
      tableName: 'modules',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          unique: true,
          fields: ['hierarchy', 'name', 'level'],
        },
        {
          fields: ['parent_id'],
          name: 'idx_modules_parent',
        },
        {
          fields: ['hierarchy'],
          name: 'idx_modules_hierarchy',
        },
        {
          fields: ['level'],
          name: 'idx_modules_level',
        },
      ],
      scopes: {},
    },
  );

  auditableMixin.applyAuditFields(Module);

  return Module;
}
