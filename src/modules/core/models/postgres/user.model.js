import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';

const validateValidityDate = (validityDate) => {
  if (validityDate && new Date(validityDate) <= new Date()) {
    throw CoreError.futureDateOnly({ attribute: 'common.label.validityDate' });
  }
};

export default function (fastify) {
  class User extends Model {
    static associate(models) {
      User.hasMany(models.UserAssociation, {
        foreignKey: 'userId',
        as: 'ua',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      User.hasOne(models.UserMfaSetting, {
        foreignKey: 'userId',
        as: 'mfa',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      User.belongsTo(models.User, {
        foreignKey: 'parentId',
        as: 'parent',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  User.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      parentId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: true,
        validate: {
          len: [3, 50],
          notEmpty: false,
        },
      },
      username: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
          len: [3, 20],
          notEmpty: true,
        },
      },
      password: {
        type: DataTypes.STRING(100),
        allowNull: false,
        validate: {
          len: [1, 100],
          notEmpty: true,
        },
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
        unique: true,
        validate: {
          len: [1, 100],
          notEmpty: false,
        },
      },
      type: {
        type: DataTypes.ENUM(Object.values(UserConstant.USER_TYPES)),
        allowNull: false,
        defaultValue: UserConstant.USER_TYPES.NORMAL,
      },
      status: {
        type: DataTypes.ENUM(Object.values(UserConstant.USER_STATUSES)),
        allowNull: false,
        defaultValue: UserConstant.USER_STATUSES.ACTIVE,
      },
      validityDate: {
        type: DataTypes.DATE(),
        allowNull: true,
      },
      userAssociation: {
        type: DataTypes.VIRTUAL,
        get() {
          if (this.parent) {
            return this.parent.ua;
          }
          return this.ua;
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'User',
      tableName: 'users',
      underscored: true,
      timestamps: true,
      paranoid: false,
      sequelize: fastify.psql.connection,
      scopes: {},
      hooks: {
        beforeCreate: async (instance, options) => {
          const { validityDate } = instance;
          validateValidityDate(validityDate);
        },
        beforeUpdate: async (instance, options) => {
          const { validityDate } = instance;
          validateValidityDate(validityDate);
        },
      },
    },
  );

  auditableMixin.applyAuditFields(User);

  return User;
}
