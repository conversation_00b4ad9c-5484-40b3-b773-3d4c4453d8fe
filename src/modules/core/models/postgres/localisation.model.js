import { DataTypes, Model, Sequelize } from 'sequelize';

import { LocalisationConstant } from '#src/modules/setting/constants/index.js';

export default function (fastify, instance) {
  class Localisation extends Model {
    static associate(models) {
      Localisation.hasMany(models.CustomLocalisation, {
        foreignKey: 'parentId',
        as: 'customLocalisation',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      });
    }
  }

  Localisation.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      category: {
        type: DataTypes.ENUM(Object.values(LocalisationConstant.LOCALISATION_CATEGORIES)),
        allowNull: false,
        defaultValue: LocalisationConstant.LOCALISATION_CATEGORIES.CURRENCY,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      code: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      metadata: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'Localisation',
      tableName: 'localisations',
      underscored: true,
      timestamps: true,
      paranoid: true,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          unique: true,
          fields: ['category', 'code'],
        },
      ],
    },
  );

  return Localisation;
}
