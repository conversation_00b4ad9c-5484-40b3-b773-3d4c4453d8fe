import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { BulkJobConstant } from '#src/modules/bulk-job/constants/index.js';

const BulkJobModel = (fastify) => {
  class BulkJob extends Model {}

  BulkJob.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      type: {
        type: DataTypes.ENUM(Object.values(BulkJobConstant.BULK_JOB_TYPES)),
        allowNull: false,
        defaultValue: BulkJobConstant.BULK_JOB_TYPES.EXPORT,
      },
      dryRun: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      title: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      parameters: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      model: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      fileName: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      fileUrl: {
        type: DataTypes.STRING(2048),
        allowNull: true,
        validate: {
          isUrl: true,
        },
      },
      resultUrl: {
        type: DataTypes.STRING(2048),
        allowNull: true,
        validate: {
          isUrl: true,
        },
      },
      errorMessage: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      successCount: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      totalCount: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(Object.values(BulkJobConstant.BULK_JOB_STATUSES)),
        allowNull: false,
        defaultValue: BulkJobConstant.BULK_JOB_STATUSES.PENDING,
      },
      startedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      completedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize: fastify.psql.connection,
      modelName: 'BulkJob',
      tableName: 'bulk_jobs',
      timestamps: true,
      underscored: true,
    },
  );

  auditableMixin.applyAuditFields(BulkJob);

  return BulkJob;
};

export default BulkJobModel;
