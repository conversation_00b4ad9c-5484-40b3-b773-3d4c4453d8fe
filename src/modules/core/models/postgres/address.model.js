import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';

export default function (fastify, instance) {
  class Address extends Model {
    static associate(models) {
      Address.belongsTo(models.Entity, {
        foreignKey: 'entity_id',
        as: 'entity',
      });
    }
  }

  Address.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entity_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      street: {
        type: DataTypes.STRING(100),
        allowNull: false,
        validate: {
          len: [0, 100],
        },
      },
      city: {
        type: DataTypes.STRING(50),
        allowNull: false,
        validate: {
          len: [0, 50],
        },
      },
      state: {
        type: DataTypes.STRING(50),
        allowNull: false,
        validate: {
          len: [0, 50],
        },
      },
      postalCode: {
        type: DataTypes.STRING(20),
        allowNull: false,
        validate: {
          len: [0, 20],
        },
      },
      country: {
        type: DataTypes.STRING(50),
        allowNull: false,
        validate: {
          len: [0, 50],
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize: fastify.psql.connection,
      modelName: 'Address',
      tableName: 'addresses',
      underscored: true,
      timestamps: true,
    },
  );

  auditableMixin.applyAuditFields(Address);

  return Address;
}
