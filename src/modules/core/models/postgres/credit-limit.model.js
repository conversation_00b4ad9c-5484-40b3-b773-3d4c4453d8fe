import { DataTypes, Model, Sequelize } from 'sequelize';

import { versionedMixin } from '#src/mixins/index.js';

export default function (fastify, instance) {
  class CreditLimit extends Model {
    // to be uncomment when start work on merchant feature later
    // static associate(models) {
    //   CreditLimit.belongsTo(models.Entity, {
    //     foreignKey: 'entityId',
    //     as: 'entity',
    //     onDelete: 'RESTRICT',
    //     onUpdate: 'CASCADE',
    //   });
    // }
  }

  CreditLimit.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      credit: {
        type: DataTypes.DECIMAL(30, 8),
        allowNull: false,
      },
      version: {
        type: DataTypes.BIGINT,
        allowNull: false,
        defaultValue: 1,
        validate: {
          min: 1,
          notNull: { msg: 'version is required' },
        },
      },
    },
    {
      modelName: 'CreditLimit',
      sequelize: fastify.psql.connection,
      tableName: 'credit_limits',
      timestamps: true,
      underscored: true,
    },
  );

  versionedMixin.applyVersioning(CreditLimit);

  return CreditLimit;
}
