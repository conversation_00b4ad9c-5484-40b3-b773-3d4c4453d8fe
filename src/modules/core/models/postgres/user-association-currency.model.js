import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
export default function (fastify) {
  class UserAssociationCurrency extends Model {
    static associate(models) {
      UserAssociationCurrency.belongsTo(models.UserAssociation, {
        foreignKey: 'userAssociationId',
        as: 'ua',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      UserAssociationCurrency.belongsTo(models.CustomLocalisation, {
        foreignKey: 'currencyId',
        as: 'cl',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  UserAssociationCurrency.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      userAssociationId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      currencyId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      currencyCode: {
        type: DataTypes.VIRTUAL,
        get() {
          return this.cl.code;
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'UserAssociationCurrency',
      tableName: 'user_assoc_currencies',
      underscored: true,
      timestamps: true,
      paranoid: false,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          unique: true,
          fields: ['userAssociationId', 'currencyId'],
        },
      ],
      scopes: {},
    },
  );

  auditableMixin.applyAuditFields(UserAssociationCurrency);

  return UserAssociationCurrency;
}
