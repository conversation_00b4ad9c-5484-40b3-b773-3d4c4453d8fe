import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
export default function (fastify) {
  class UserMfaSetting extends Model {
    static associate(models) {
      UserMfaSetting.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'mfa',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  UserMfaSetting.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        unique: true,
      },
      secretKey: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
          len: [1, 100],
          notEmpty: true,
        },
      },
      status: {
        type: DataTypes.ENUM(Object.values(UserConstant.USER_MFA_STATUSES)),
        defaultValue: UserConstant.USER_MFA_STATUSES.DISABLED,
        allowNull: false,
      },
      setupStatus: {
        type: DataTypes.ENUM(Object.values(UserConstant.USER_MFA_SETUP_STATUSES)),
        defaultValue: UserConstant.USER_MFA_SETUP_STATUSES.INACTIVE,
        allowNull: false,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'UserMfaSetting',
      tableName: 'user_mfa_settings',
      underscored: true,
      timestamps: true,
      paranoid: false,
      sequelize: fastify.psql.connection,
      scopes: {},
    },
  );

  auditableMixin.applyAuditFields(UserMfaSetting);

  return UserMfaSetting;
}
