import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { AppConstant } from '#src/modules/setting/constants/index.js';
export default function (fastify, instance) {
  class EntityApp extends Model {
    static associate(models) {
      EntityApp.hasMany(models.TestResult, {
        foreignKey: 'testableId',
        as: 'testResult',
        constraints: false,
        scope: {
          testable_type: 'EntityApp',
        },
      });

      EntityApp.hasMany(models.AppConfig, {
        foreignKey: 'entityAppId',
        as: 'appConfig',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      EntityApp.belongsTo(models.App, {
        foreignKey: 'appId',
        as: 'app',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  EntityApp.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      appId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(Object.values(AppConstant.ENTITY_APP_STATUSES)),
        allowNull: false,
        defaultValue: AppConstant.ENTITY_APP_STATUSES.UNINSTALLED,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'EntityApp',
      tableName: 'entity_apps',
      underscored: true,
      timestamps: true,
      paranoid: true,
      sequelize: fastify.psql.connection,
    },
  );

  auditableMixin.applyAuditFields(EntityApp);

  return EntityApp;
}
