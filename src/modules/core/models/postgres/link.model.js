import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';

export default function (fastify, instance) {
  class Link extends Model {
    static associate(models) {
      Link.belongsTo(models.Entity, {
        foreignKey: 'entity_id',
        as: 'entity',
      });
    }
  }

  Link.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entity_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      type: {
        type: DataTypes.ENUM('email_domain', 'signin'),
        allowNull: false,
      },
      url: {
        type: DataTypes.STRING(2083),
        allowNull: false,
        validate: {
          len: [1, 2083],
          isUrl: true,
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize: fastify.psql.connection,
      modelName: 'Link',
      tableName: 'links',
      underscored: true,
      timestamps: true,
    },
  );

  auditableMixin.applyAuditFields(Link);

  return Link;
}
