import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin, versionedMixin } from '#src/mixins/index.js';
import { COMMON_STATUSES } from '#src/modules/core/constants/core.constant.js';

export default function (fastify, instance) {
  class CustomLocalisation extends Model {
    static associate(models) {
      CustomLocalisation.belongsTo(models.Localisation, {
        foreignKey: 'parentId',
        as: 'localisation',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  CustomLocalisation.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      parentId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      category: {
        type: DataTypes.VIRTUAL,
        get() {
          return this.localisation?.category;
        },
      },
      name: {
        type: DataTypes.VIRTUAL,
        get() {
          return this.localisation?.name;
        },
      },
      code: {
        type: DataTypes.VIRTUAL,
        get() {
          return this.localisation?.code;
        },
      },
      metadata: {
        type: DataTypes.VIRTUAL,
        get() {
          return this.localisation?.metadata;
        },
      },
      status: {
        type: DataTypes.ENUM(Object.values(COMMON_STATUSES)),
        allowNull: false,
        defaultValue: COMMON_STATUSES.ACTIVE,
      },
      exchangeRate: {
        type: DataTypes.DECIMAL(15, 6),
        allowNull: true,
        validate: {
          min: 0,
        },
      },
      version: {
        type: DataTypes.BIGINT,
        allowNull: false,
        defaultValue: 1,
        validate: {
          min: 1,
          notNull: { msg: 'version is required' },
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'CustomLocalisation',
      tableName: 'custom_localisations',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
    },
  );

  auditableMixin.applyAuditFields(CustomLocalisation);
  versionedMixin.applyVersioning(CustomLocalisation);

  return CustomLocalisation;
}
