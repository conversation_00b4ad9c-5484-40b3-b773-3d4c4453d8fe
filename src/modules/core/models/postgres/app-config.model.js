import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
export default function (fastify, instance) {
  class AppConfig extends Model {
    static associate(models) {
      AppConfig.belongsTo(models.EntityApp, {
        foreignKey: 'entityAppId',
        as: 'entityApp',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  AppConfig.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entityAppId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      configKey: {
        type: DataTypes.STRING(100),
        allowNull: false,
        validate: {
          len: [1, 100],
          notEmpty: true,
        },
      },
      configValue: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'AppConfig',
      tableName: 'app_configs',
      underscored: true,
      timestamps: true,
      paranoid: true,
      sequelize: fastify.psql.connection,
    },
  );

  auditableMixin.applyAuditFields(AppConfig);

  return AppConfig;
}
