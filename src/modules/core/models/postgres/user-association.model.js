import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
export default function (fastify) {
  class UserAssociation extends Model {
    static associate(models) {
      UserAssociation.hasMany(models.UserAssociationCurrency, {
        foreignKey: 'userAssociationId',
        as: 'uac',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      UserAssociation.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'user',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      UserAssociation.hasOne(models.UserInvitation, {
        foreignKey: 'userAssociationId',
        as: 'ui',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      UserAssociation.belongsTo(models.Entity, {
        foreignKey: 'entityId',
        as: 'entity',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      UserAssociation.belongsTo(models.Role, {
        foreignKey: 'roleId',
        as: 'role',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  UserAssociation.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      origin: {
        type: DataTypes.ENUM(Object.values(UserConstant.USER_ORIGINS)),
        allowNull: false,
        defaultValue: UserConstant.USER_ORIGINS.INTERNAL,
      },
      roleId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      currencyCodes: {
        type: DataTypes.VIRTUAL,
        get() {
          const uac = this.getDataValue('uac') || [];
          return uac.map((u) => u.currencyCode);
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'UserAssociation',
      tableName: 'user_associations',
      underscored: true,
      timestamps: true,
      paranoid: false,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          unique: true,
          fields: ['userId', 'entityId'],
        },
      ],
      scopes: {},
    },
  );

  auditableMixin.applyAuditFields(UserAssociation);

  return UserAssociation;
}
