import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin, versionedMixin } from '#src/mixins/index.js';
import { CoreConstant, EntityConstant } from '#src/modules/core/constants/index.js';
import { clearCacheWithPrefix } from '#src/utils/cache.util.js';

export default function (fastify, instance) {
  class Entity extends Model {
    static associate(models) {
      Entity.hasMany(models.Link, {
        foreignKey: 'entity_id',
        as: 'links',
      });
      Entity.hasOne(models.Address, {
        foreignKey: 'entity_id',
        as: 'addresses',
      });
      Entity.hasMany(models.CustomLocalisation, {
        foreignKey: 'entity_id',
        as: 'custom_localisations',
      });

      Entity.belongsTo(models.Entity, {
        foreignKey: 'parentId',
        as: 'parent',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      Entity.hasMany(models.Entity, {
        foreignKey: 'parentId',
        as: 'children',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });

      Entity.hasMany(models.UserAssociation, {
        foreignKey: 'entityId',
        as: 'ua',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      });
    }
  }

  Entity.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      parentId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      accessId: {
        type: DataTypes.STRING(12),
        unique: true,
        allowNull: false,
      },
      hierarchy: {
        type: DataTypes.ENUM(Object.values(CoreConstant.HIERARCHY)),
        allowNull: false,
      },
      code: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        validate: {
          len: [1, 50],
          notEmpty: true,
        },
      },
      prefix: {
        type: DataTypes.STRING(10),
        allowNull: false,
        validate: {
          len: [1, 10],
          notEmpty: true,
        },
      },
      name: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        validate: {
          len: [1, 50],
          notEmpty: true,
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(Object.values(EntityConstant.ENTITY_STATUSES)),
        allowNull: false,
        defaultValue: EntityConstant.ENTITY_STATUSES.ACTIVE,
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
        validate: {
          len: [0, 20],
        },
      },
      email: {
        type: DataTypes.STRING(50),
        allowNull: true,
        validate: {
          len: [0, 50],
          isEmail: true,
        },
      },
      logoDesktop: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      logoMobile: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      favicon: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      version: {
        type: DataTypes.BIGINT,
        allowNull: false,
        defaultValue: 1,
        validate: {
          min: 1,
          notNull: { msg: 'version is required' },
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'Entity',
      tableName: 'entities',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
      hooks: {
        afterCreate: async () => {
          fastify.redis &&
            (await clearCacheWithPrefix(fastify.redis, CoreConstant.MODULE_NAMES.ORGANISATION));
        },
        afterUpdate: async () => {
          fastify.redis &&
            (await clearCacheWithPrefix(fastify.redis, CoreConstant.MODULE_NAMES.ORGANISATION));
        },
      },
    },
  );

  auditableMixin.applyAuditFields(Entity);
  versionedMixin.applyVersioning(Entity);

  return Entity;
}
