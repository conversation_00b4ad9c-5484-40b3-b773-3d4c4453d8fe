import { DataTypes, Model, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CreditLimitConstant } from '#src/modules/credit-limit/index.js';

export default function (fastify, instance) {
  class CreditLimitTransaction extends Model {
    // to be uncomment when start work on merchant feature later
    // static associate(models) {
    //   CreditLimitTransaction.belongsTo(models.Entity, {
    //     foreignKey: 'entityId',
    //     as: 'entity',
    //     onDelete: 'RESTRICT',
    //     onUpdate: 'CASCADE',
    //   });
    // }
  }

  CreditLimitTransaction.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      referenceId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      type: {
        type: DataTypes.ENUM(Object.values(CreditLimitConstant.CREDIT_LIMIT_TRANSACTION_TYPES)),
        allowNull: false,
      },
      baseAmount: {
        type: DataTypes.DECIMAL(30, 8),
        allowNull: false,
      },
      exchangeRate: {
        type: DataTypes.DECIMAL(30, 8),
        allowNull: false,
        validate: {
          min: 0,
        },
      },
      amount: {
        type: DataTypes.DECIMAL(30, 8),
        allowNull: false,
      },
      beforeBalance: {
        type: DataTypes.DECIMAL(30, 8),
        allowNull: false,
        defaultValue: 0,
      },
      afterBalance: {
        type: DataTypes.DECIMAL(30, 8),
        allowNull: false,
        defaultValue: 0,
      },
      status: {
        type: DataTypes.ENUM(Object.values(CoreConstant.COMMON_TRANSACTION_STATUSES)),
        allowNull: false,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'CreditLimitTransaction',
      sequelize: fastify.psql.connection,
      tableName: 'credit_limit_transactions',
      timestamps: true,
      underscored: true,
    },
  );

  auditableMixin.applyAuditFields(CreditLimitTransaction);

  return CreditLimitTransaction;
}
