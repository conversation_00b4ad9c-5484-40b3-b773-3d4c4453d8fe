import { DataTypes, Model, Op, Sequelize } from 'sequelize';

import { auditableMixin } from '#src/mixins/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';

export default function (fastify, instance) {
  class Remark extends Model {}

  Remark.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      remarkableId: {
        type: DataTypes.UUID,
        allowNull: false,
        comment: 'UUID of the entity this remark is associated with',
      },
      remarkableType: {
        type: DataTypes.ENUM(Object.values(CoreConstant.REMARKABLE_TYPE)),
        allowNull: false,
        field: 'remarkable_type',
        comment: 'Type of entity this remark is associated with',
      },
      type: {
        type: DataTypes.ENUM(Object.values(CoreConstant.REMARK_TYPE)),
        allowNull: false,
        defaultValue: CoreConstant.REMARK_TYPE.NOTE,
      },
      content: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(Object.values(CoreConstant.REMARK_STATUSES)),
        allowNull: false,
        defaultValue: CoreConstant.REMARK_STATUSES.ACTIVE,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'Remark',
      tableName: 'remarks',
      underscored: true,
      timestamps: true,
      paranoid: true,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          fields: ['remarkable_id', 'remarkable_type', 'status'],
          name: 'idx_remarks_poly_status',
        },
        {
          fields: ['type', 'status'],
          name: 'idx_remarks_type_status',
        },
        {
          fields: ['deleted_at'],
          name: 'idx_remarks_deleted_at',
          where: {
            deleted_at: {
              [Op.ne]: null,
            },
          },
        },
      ],
      hooks: {
        beforeCreate: async (remark, options) => {
          if (remark.status === CoreConstant.REMARK_STATUSES.ACTIVE) {
            await Remark.update(
              { status: CoreConstant.REMARK_STATUSES.ARCHIVED },
              {
                where: {
                  remarkableId: remark.remarkableId,
                  remarkableType: remark.remarkableType,
                  type: remark.type,
                  status: CoreConstant.REMARK_STATUSES.ACTIVE,
                  deletedAt: null, // prevent updating soft-deleted records
                },
                transaction: options.transaction,
              },
            );
          }
        },
      },
    },
  );

  auditableMixin.applyAuditFields(Remark);

  return Remark;
}
