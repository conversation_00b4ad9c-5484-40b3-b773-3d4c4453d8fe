import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { EntityRepository } from '#src/modules/core/repository/index.js';

/**
 * Validates organisation fields for uniqueness
 * @param {Object} server - The server instance
 * @param {Object} fields - Object containing fields to validate
 * @param {string} fields.prefix - The organisation prefix to check
 * @param {string} fields.code - The organisation code to check
 * @param {string} [fields.excludeId] - Optional ID to exclude from the check (for updates)
 * @returns {Promise<boolean>} True if all fields are valid
 * @throws {OrganisationError} If any field is not unique
 */
export const validateOrganisationFields = async (server, { prefix, code, excludeId = null }) => {
  if (prefix) {
    const prefixWhereClause = {
      prefix,
      hierarchy: CoreConstant.HIERARCHY.ORGANISATION,
    };

    if (excludeId) {
      prefixWhereClause.id = { [server.psql.connection.Sequelize.Op.ne]: excludeId };
    }

    const prefixCount = await EntityRepository.count(server, prefixWhereClause);
    if (prefixCount > 0) {
      throw CoreError.alreadyExists({
        attribute: 'common.label.prefix',
        value: prefix,
      });
    }
  }

  if (code) {
    const codeWhereClause = {
      code,
      hierarchy: CoreConstant.HIERARCHY.ORGANISATION,
    };

    if (excludeId) {
      codeWhereClause.id = { [server.psql.connection.Sequelize.Op.ne]: excludeId };
    }

    const codeCount = await EntityRepository.count(server, codeWhereClause);
    if (codeCount > 0) {
      throw CoreError.alreadyExists({
        attribute: 'common.label.code',
        value: code,
      });
    }
  }

  return true;
};
