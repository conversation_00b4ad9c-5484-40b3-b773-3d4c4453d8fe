import { LocalisationError } from '#src/modules/setting/errors/index.js';
import { LocalisationRepository } from '#src/modules/setting/repository/index.js';
/**
 * Dynamically validates that the provided localisation ID exists and is of the correct category
 * @param {Object} server - The server instance
 * @param {string|Array} localisationId - Localisation ID or array of IDs to validate
 * @param {string} category - The category to validate against (CURRENCY, LANGUAGE, REGION)
 * @param {boolean} required - Whether this localisation is required
 * @returns {Promise<boolean>} True if localisation is valid
 */
export const validateLocalisation = async (server, localisationId, category) => {
  const ids = Array.isArray(localisationId) ? localisationId : [localisationId];

  const validCount = await LocalisationRepository.count(server, {
    id: { [server.psql.connection.Sequelize.Op.in]: ids },
    category: category,
  });

  if (validCount !== ids.length) {
    throw LocalisationError.invalidLocalisation(category);
  }

  return true;
};
