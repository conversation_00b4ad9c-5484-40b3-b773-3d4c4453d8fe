import { CoreError } from '#src/modules/core/errors/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import { UserRepository } from '#src/modules/user/repository/index.js';

const { USER_STATUSES, USER_TYPES } = UserConstant;
/**
 * Validates if an email is unique within the system.
 *
 * @param {Object} server - The server instance used for database operations.
 * @param {string} email - The email address to validate.
 * @param {string|null} [userId=null] - The ID of the user to exclude from the check, if any.
 * @returns {Promise<string>} A promise that resolves to the validated email if it is unique.
 * @throws {CoreError} If the email is already taken by another user.
 */
export const validateEmail = async (server, email, userId = null) => {
  const normalisedEmail = email.toLowerCase();
  const existEmail = await UserRepository.findUser(server, { email: normalisedEmail });
  if (existEmail && existEmail.id !== userId) {
    throw CoreError.alreadyExists({
      attribute: 'common.label.email',
      value: normalisedEmail,
    });
  }
  return normalisedEmail;
};

/**
 * Validates if a username is unique within the system and formats it for sub-accounts.
 *
 * @param {Object} server - The server instance used for database operations.
 * @param {string} username - The username to validate.
 * @param {string} type - The type of user, which determines if the username needs special formatting.
 * @param {Object|null} parentUser - The parent user object for sub-accounts, used to format the username.
 * @returns {Promise<string>} A promise that resolves to the validated and possibly formatted username if it is unique.
 * @throws {CoreError} If the username is already taken by another user.
 */
export const validateUsername = async (server, username, type, parentUser) => {
  let finalUsername = username;
  if (type === USER_TYPES.SUB_ACCOUNT) {
    finalUsername = `${parentUser.username}_${username}`;
  }

  const query = {
    filter_username_eq: finalUsername,
  };

  const existingUser = await UserRepository.findUser(server, query);
  if (existingUser) {
    throw CoreError.alreadyExists({
      attribute: 'common.label.username',
      value: finalUsername,
    });
  }
  return finalUsername;
};
