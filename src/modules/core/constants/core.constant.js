/* eslint-disable sonarjs/no-hardcoded-passwords */
export const HIERARCHY = {
  MERCHANT: 'merchant',
  ORGANISATION: 'organisation',
  ROOT: 'root',
  USER: 'user',
};

export const ACCESS_LEVEL_KEYS = {
  user: 'user',
  member: 'member',
  webhook: 'webhook',
};

export const PERMISSION_FIELDS = [
  'canRead',
  'canCreate',
  'canEdit',
  'canManage',
  'canImport',
  'canExport',
];

export const CACHE_SECOND = {
  SHORT: 10, // For rapidly changing data or debounce-type caching
  MEDIUM: 30, // 30 second – suitable for moderately volatile data
  STANDARD: 60, // 1 minute – good default for most general cache
  LONG: 3600, // 1 hour – stable data that changes infrequently
  DAILY: 86400, // 24 hours – rarely changing reference data
  WEEKLY: 604800, // 7 days – archive-type or external lookup cache
  NEVER: 0, // Used when caching is disabled
};

export const COMMON_STATUSES = {
  ACTIVE: 'active',
  DELETED: 'deleted',
  INACTIVE: 'inactive',
};

export const COMMON_TRANSACTION_STATUSES = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  VOIDED: 'voided',
};

export const ERROR_CODE = {
  VERSION_CONFLICT: '10013',
};

export const HCAPTCHA_RISK_LEVELS = {
  HIGH: 'high',
  LOW: 'low',
};

export const EVENTS = {
  APP_CENTER: 'appCenter',
  ACCESS_CONTROL: 'accessControl',
  AUDIT_TRAIL: 'auditTrail',
  AUTOMATION: 'automation',
  CREDIT_LIMIT_SETTING: 'creditLimitSetting',
  CURRENCY_SETTING: 'currencySetting',
  DATA_MASKING_SETTING: 'dataMaskingSetting',
  DEPARTMENT: 'department',
  DEPARTMENT_TEMPLATE: 'departmentTemplate',
  DEVELOPER_HUB: 'developerHub',
  GAME_PROVIDER: 'gameProvider',
  LANGUAGE_SETTING: 'languageSetting',
  LOCALISATION_SETTING: 'localisationSetting',
  LOGIN: 'login',
  LOGOUT: 'logout',
  MAINTENANCE: 'maintenance',
  MANAGE_MONITOR: 'manageMonitor',
  MEDIA: 'media',
  MEMBER_POINT: 'memberPoint',
  MEMBER_VIP: 'memberVIP',
  MEMBER: 'member',
  MERCHANT_CREDIT: 'merchantCredit',
  MERCHANT: 'merchant',
  ORGANISATION: 'organisation',
  OTP: 'OTP',
  PERSONAL_SETTING: 'personalSetting',
  REGION_SETTING: 'regionSetting',
  REGISTRATION_FORM: 'registrationForm',
  RESET_PASSWORD: 'resetPassword',
  RISK_GROUP: 'riskGroup',
  ROLE: 'role',
  SECURITY_ACCESS_CONTROL: 'securityAccessControl',
  SETTING: {
    PERSONAL: 'personalSetting',
    SAFETY: 'safety',
    THEMES: 'themesSetting',
  },
  SETTING_OPTIONS: {
    PERSONAL: 'personalSettingsOptions',
    SAFETY: 'safetySettingsOptions',
    THEMES: 'themeSettingsOptions',
  },
  TAG: 'tag',
  TRANSACTION: 'transaction',
  TRIGGERED_EVENT_LOG: 'triggeredEventLog',
  TWO_FACTOR_AUTHENTICATION: 'twoFactorAuthentication',
  USER_AUDIT_TRAIL: 'userAuditTrail',
  USER_EXTERNAL_INVITATION: 'userExternalInvitation',
  USER_HIERARCHY: 'userHierarchy',
  USER_LOGIN_LOG: 'userLoginLog',
  USER_SSO: 'userSSO',
  USER_SUB_ACCOUNT: 'userSubAccount',
  USER: 'user',
};

export const EVENT_ACTIONS = {
  ACCESS_CONTROL_UPDATED: 'accessControlUpdated',
  API_KEY_GENERATED: 'apiKeyGenerated',
  APP_INSTALLED: 'appInstalled',
  APP_UNINSTALLED: 'appUninstalled',
  ARCHIVED: 'archived',
  ASSIGNED: 'assigned',
  BASIC_INFORMATION_UPDATED: 'basicInformationUpdated',
  CREATED: 'created',
  DELETED: 'deleted',
  DETAILS_VIEWED: 'detailsViewed',
  DUPLICATED: 'duplicated',
  EDITED: 'edited',
  EXPORTED: 'exported',
  IMPORTED: 'imported',
  IP_BLACKLISTED: 'ipBlacklisted',
  KILL_SWITCH_ACTIVATED: 'killSwitchActivated',
  KILL_SWITCH_DEACTIVATED: 'killSwitchDeactivated',
  LOGIN: 'login',
  LOGOUT: 'logout',
  POLICY_UPDATED: 'policyUpdated',
  SEARCHED: 'searched',
  SETUP: 'setup',
  STATUS_UPDATED: 'statusUpdated',
  TEST_RAN: 'testRan',
  UNKNOWN_ACTION: 'unknownAction',
  UPDATED: 'updated',
  VIEWED: 'viewed',
};

export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};

export const MODULE_METHODS = {
  ASSIGN_USER: 'assignUser',
  CHECK_AVAILABILITY: 'checkAvailability',
  CREATE: 'create',
  DELETE: 'delete',
  EXPORT: 'export',
  INDEX: 'index',
  LOGIN: 'login',
  NAVIGATION: 'navigation',
  ONBOARD_USER: 'onboardUser',
  OPTION: 'option',
  REGISTER_SSO: 'registerSSO',
  REQUEST_2FA_LOGIN: 'request2faLogin',
  REQUEST_2FA_SETUP: 'request2faSetup',
  RESET_2FA: 'reset2fa',
  REVOKE_TRUSTED_DEVICE: 'revokeTrustedDevice',
  TEST: 'test',
  UPDATE_ACCESS_CONTROL: 'updateAccessControls',
  UPDATE_BASIC_INFORMATION: 'updateBasicInformation',
  UPDATE_LOGIN_ACCESS: 'updateLoginAccess',
  UPDATE_MAINTENANCE_STATUS: 'updateMaintenanceStatus',
  UPDATE_ORGANISATION: 'updateOrganisation',
  UPDATE_PERMISSION: 'updatePermissions',
  UPDATE_PERSONAL: 'updatePersonal',
  UPDATE_POLICY: 'updatePolicy',
  UPDATE_SAFETY: 'updateSafety',
  UPDATE_STATUS: 'updateStatus',
  UPDATE_THEMES: 'updateThemes',
  UPDATE: 'update',
  VIEW_THEMES: 'viewThemes',
  VIEW: 'view',
};

export const MODULE_NAMES = {
  ACCESS_CONTROL: 'accessControls',
  APP: 'apps',
  AUDIT_TRAIL: 'auditTrails',
  BULK_JOB: 'bulkJobs',
  CORE: 'core',
  CREDIT_LIMIT_TRANSACTION: 'creditLimitTransactions',
  CREDIT_LIMIT: 'creditLimits',
  DEPARTMENT: 'departments',
  DEPARTMENT_TEMPLATE: 'departmentTemplates',
  DEVELOPER_HUB: 'developerHubs',
  LOCALISATION: 'localisations',
  MEDIA: 'media',
  MODULE_POLICY: 'modulePolicies',
  ORGANISATION: 'organisations',
  ROLE: 'roles',
  SETTING: 'settings',
  USER: 'users',
};

export const REDACT_FIELDS = {
  PASSWORD: 'password',
  CONFIRM_PASSWORD: 'confirmPassword',
  API_KEY: 'apiKey',
};

export const REMARK_STATUSES = {
  ACTIVE: 'active',
  ARCHIVED: 'archived',
};

export const REMARK_TYPE = {
  AUDIT: 'audit',
  NOTE: 'note',
  SECURITY: 'security',
  SYSTEM: 'system',
  WARNING: 'warning',
};

export const REMARKABLE_TYPE = {
  IP_ACCESS_CONTROL: 'ip_access_control',
};

export const VIEW_ACTION = {
  SEARCHED: 'searched',
  VIEWED: 'viewed',
  DETAILS_VIEWED: 'detailsViewed',
};

export const CACHE_KEY_SEGMENTS = {
  ENTITY: 'entity',
  AUTH: 'auth',
  AUDIT_TRAIL: 'auditTrail',
};

export const TESTABLE_TYPES = {
  ENTITY_APP: 'EntityApp',
};

export const TEST_RESULT_STATUSES = {
  SUCCESS: 'success',
  FAILED: 'failed',
};
