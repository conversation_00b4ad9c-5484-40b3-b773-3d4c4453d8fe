import * as AddressRepository from '#src/modules/core/repository/postgres/address.repository.js';
import * as EntityRepository from '#src/modules/core/repository/postgres/entity.repository.js';
import * as LinkRepository from '#src/modules/core/repository/postgres/link.repository.js';
import * as ModuleRepository from '#src/modules/core/repository/postgres/module.repository.js';
import * as RemarkRepository from '#src/modules/core/repository/postgres/remark.repository.js';
import * as TestResultRepository from '#src/modules/core/repository/postgres/test-result.repository.js';

export {
  AddressRepository,
  EntityRepository,
  LinkRepository,
  ModuleRepository,
  RemarkRepository,
  TestResultRepository,
};
