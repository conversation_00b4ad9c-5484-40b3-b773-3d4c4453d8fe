/**
 * Repository for Address model operations
 */

/**
 * Creates a new address record
 * @param {Object} server - The server instance
 * @param {Object} data - The address data to create
 * @param {Object} options - Additional options for the create operation
 * @returns {Promise<Object>} The created address record
 */
export const create = async (server, data, options = {}) => {
  return await server.psql.Address.create(data, options);
};

/**
 * Finds or creates an address record
 * @param {Object} server - The server instance
 * @param {Object} query - The query object containing where and defaults
 * @param {Object} options - Additional options for the operation
 * @returns {Promise<Array>} Array containing the record and a boolean indicating if it was created
 */
export const findOrCreate = async (server, query, options = {}) => {
  return await server.psql.Address.findOrCreate({
    ...query,
    ...options,
  });
};
