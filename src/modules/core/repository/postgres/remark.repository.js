import { CoreConstant } from '#src/modules/core/constants/index.js';
const {
  REMARK_STATUSES: { ACTIVE, ARCHIVED },
} = CoreConstant;

/**
 * Creates a new remark in the database.
 * @param {Object} server - The server object containing the Postgres database connection.
 * @param {Object} data - The data for creating the new remark.
 * @param {Object} [options={}] - Additional options for creating the remark.
 * @returns {Promise<Object>} A promise that resolves to the newly created remark.
 */
export const create = async (server, data, options = {}) =>
  await server.psql.Remark.create(data, options);

/**
 * Finds an active remark associated with a specific remarkable entity.
 * @param {Object} server - The server object containing the Postgres database connection.
 * @param {string|number} remarkableId - The ID of the remarkable entity.
 * @param {string} remarkableType - The type of the remarkable entity.
 * @param {Object} [options={}] - Additional options for the database query.
 * @returns {Promise<Object|null>} A promise that resolves to the found active remark or null if not found.
 */
export const findActiveByRemarkable = async (server, remarkableId, remarkableType, options = {}) =>
  await server.psql.Remark.findOne({
    where: {
      remarkableId,
      remarkableType,
      status: ACTIVE,
    },
    ...options,
  });

/**
 * Archives a remark by updating its status to ARCHIVED.
 * @param {Object} remark - The remark object to be archived.
 * @param {Object} [options={}] - Additional options for updating the remark.
 * @returns {Promise<Object>} A promise that resolves to the updated remark.
 */
export const archive = async (remark, options = {}) =>
  await remark.update({ status: ARCHIVED }, options);
