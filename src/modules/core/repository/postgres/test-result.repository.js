/**
 * Creates a new test result entry in the database.
 *
 * @param {Object} server - The server instance containing the database connection.
 * @param {Object} data - The data object containing test result details to be saved.
 * @param {Object} [options={}] - Optional settings for the database operation.
 * @returns {Promise<Object>} A promise that resolves to the created test result object.
 */
export const createTestResult = async (server, data, options = {}) => {
  return await server.psql.TestResult.create(data, options);
};
