import { CoreConstant, LinkConstant } from '#src/modules/core/constants/index.js';
import { LOCALISATION_CATEGORIES } from '#src/modules/setting/constants/localisation.constant.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

const { CURRENCY } = LOCALISATION_CATEGORIES;

/**
 * Retrieves all organisation entries with pagination based on the given request and entity ID.
 *
 * @param {Object} request - The request object containing query parameters for pagination.
 * @param {Object} request.query - The query parameters from the request.
 * @param {Object} request.server - The server instance for applying pagination.
 * @param {string|number} entityId - The ID of the entity to filter the entity entries.
 * @returns {Promise<Object>} A promise that resolves to the paginated result of entity entries.
 */
export const findAll = async (fastify, query) => {
  const { Entity, CustomLocalisation, Localisation } = fastify.psql;

  const includes = [
    {
      model: CustomLocalisation,
      as: 'custom_localisations',
      required: false,
      where: {
        status: CoreConstant.COMMON_STATUSES.ACTIVE,
      },
      attributes: ['id', 'name', 'code'],
      include: [
        {
          model: Localisation,
          as: 'localisation',
          required: true,
          where: {
            category: CURRENCY,
          },
        },
      ],
    },
  ];

  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    Entity,
    includes,
  );

  return await applyOffsetPagination(fastify, Entity, query, whereFilter, includeFilter);
};

/**
 * Retrieves a organisation entry by its ID, scoped to specific organisation IDs.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Array<string|number>} entityIds - An array of entity IDs to scope the search.
 * @param {string|number} id - The unique identifier of the entity entry to retrieve.
 * @returns {Promise<Object|null>} A promise that resolves to the found entity entry or null if not found.
 */
export const findById = async (fastify, query) => {
  const { Address, CustomLocalisation, Entity, Link, Localisation } = fastify.psql;

  const includes = [
    {
      model: Address,
      as: 'addresses',
      required: false,
    },
    {
      model: Link,
      as: 'links',
      required: false,
      where: {
        type: LinkConstant.TYPE.EMAIL_DOMAIN,
      },
    },
    {
      model: CustomLocalisation,
      as: 'custom_localisations',
      required: false,
      where: {
        status: CoreConstant.COMMON_STATUSES.ACTIVE,
      },
      attributes: ['id', 'name', 'code'],
      include: [
        {
          model: Localisation,
          as: 'localisation',
          required: true,
          where: {
            category: CURRENCY,
          },
        },
      ],
    },
  ];
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    Entity,
    includes,
  );

  const record = await Entity.findOne({
    where: whereFilter,
    include: includeFilter,
  });

  return record;
};

/**
 * Creates a new entity entry in the database.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {string|number} entityId - The ID of the entity creating the entity.
 * @param {Object} entityData - An object containing the data for the new entity.
 * @returns {Promise<Object>} A promise that resolves to the newly created entity entry.
 */
export const create = async (fastify, entityId, entityData, options = {}) => {
  const newEntity = await fastify.psql.Entity.create(
    {
      ...entityData,
      entityId,
    },
    options,
  );

  return newEntity.toJSON();
};

/**
 * Updates a entity entry with new data.
 *
 * @param {Object} modelData - The current entity model instance to be updated.
 * @param {Object} updateData - An object containing the fields and values to update in the entity entry.
 * @returns {Promise<Object>} A promise that resolves to the updated entity entry.
 */
export const update = async (modelData, updateData, options = {}) => {
  return await modelData.update(updateData, options);
};

/**
 * Repository for Entity model operations
 */

/**
 * Counts entity records matching the criteria
 * @param {Object} server - The server instance
 * @param {Object} where - The where clause
 * @param {Object} options - Additional options for the count operation
 * @returns {Promise<number>} The count of matching records
 */
export const count = async (server, where, options = {}) => {
  return await server.psql.Entity.count({
    where,
    ...options,
  });
};

/**
 * Dynamically fetches an entity by either ID or accessId.
 *
 * @param {Object} server - Fastify instance
 * @param {Object} options - Options for fetching
 * @param {string} [options.id] - Entity ID
 * @param {string} [options.accessId] - Entity accessId
 * @returns {Promise<string|null>} The entity hierarchy or null if not found
 */
export const getEntity = async (server, { id, accessId }) => {
  const { Entity } = server.psql;

  const query = {};

  if (id) {
    query['filter_id_eq'] = id;
  }

  if (accessId) {
    query['filter_accessId_eq'] = accessId;
  }

  const includes = [
    {
      model: Entity,
      as: 'parent',
      required: false,
    },
  ];

  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    Entity,
    includes,
  );

  return await Entity.findOne({
    where: whereFilter,
    include: includeFilter,
  });
};
