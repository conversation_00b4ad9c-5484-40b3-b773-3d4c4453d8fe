import { buildWhereFromFilters } from '#src/utils/query.util.js';

/**
 * Creates a new link record
 * @param {Object} server - The server instance
 * @param {Object} data - The link data to create
 * @param {Object} options - Additional options for the create operation
 * @returns {Promise<Object>} The created link record
 */
export const create = async (server, data, options = {}) => {
  return await server.psql.Link.create(data, options);
};

/**
 * Finds or creates a link record
 * @param {Object} server - The server instance
 * @param {Object} query - The query object containing where and defaults
 * @param {Object} options - Additional options for the operation
 * @returns {Promise<Array>} Array containing the record and a boolean indicating if it was created
 */
export const findOrCreate = async (server, query, options = {}) => {
  return await server.psql.Link.findOrCreate({
    ...query,
    ...options,
  });
};

/**
 * Finds a single link record
 * @param {Object} fastify - The server instance
 * @param {Object} query - The query object containing filters
 * @param {Object} options - Additional options for the operation
 * @returns {Promise<Object|null>} The found record or null if not found
 */
export const findOne = async (fastify, query, options = {}) => {
  const { Link } = fastify.psql;
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(query, Link);

  return await Link.findOne({
    where: whereFilter,
    include: includeFilter,
    ...options,
  });
};
