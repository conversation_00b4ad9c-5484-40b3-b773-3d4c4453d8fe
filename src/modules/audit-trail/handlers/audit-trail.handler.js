import { AuditTrailService } from '#src/modules/audit-trail/services/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { initializeAuditMeta } from '#src/utils/audit-trail.util.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  EVENTS: { AUDIT_TRAIL: AUDIT_TRAIL_EVENT },
  EVENT_ACTIONS: { EXPORTED, SEARCHED, DETAILS_VIEWED },
  MODULE_NAMES: { AUDIT_TRAIL: AUDIT_TRAIL_MODULE },
  MODULE_METHODS: { INDEX, VIEW, EXPORT },
} = CoreConstant;

const MODULE = AUDIT_TRAIL_MODULE;

/**
 * Handles the retrieval of audit trail.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the audit trail with cursor pagination details.
 */
export const index = async (request, reply) => {
  initializeAuditMeta(request, {
    module: MODULE,
    event: AUDIT_TRAIL_EVENT,
    action: SEARCHED,
  });

  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => AuditTrailService.index(request), SHORT);

  await request.server.withAuditLogging({
    request,
  });

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Handles the retrieval of a single audit trail record by its ID.
 *
 * @param {Object} request - The request object containing the parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the audit trail record.
 */
export const view = async (request, reply) => {
  const { id } = request.params;

  initializeAuditMeta(
    request,
    {
      module: MODULE,
      event: AUDIT_TRAIL_EVENT,
      action: DETAILS_VIEWED,
    },
    id,
  );

  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);

  const result = await fetchFromCache(
    request.server.redis,
    cacheKey,
    () => AuditTrailService.view(request, id),
    SHORT,
  );

  await request.server.withAuditLogging({
    request,
    modelMapping: {
      AuditTrail: {
        beforeState: result,
      },
    },
  });

  const precomputedServiceFn = async () => result;

  return handleServiceResponse({
    request,
    reply,
    serviceFn: precomputedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Handles the export of audit trail records to a file.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the exported audit trail records.
 */
export const exportAuditTrail = async (request, reply) => {
  initializeAuditMeta(request, {
    module: MODULE,
    event: AUDIT_TRAIL_EVENT,
    action: EXPORTED,
  });

  const result = await AuditTrailService.exportAuditTrail(request);

  await request.server.withAuditLogging({
    request,
    modelMapping: {
      BulkJob: {
        afterState: result,
      },
    },
  });

  const precomputedServiceFn = async () => result;

  return handleServiceResponse({
    request,
    reply,
    serviceFn: precomputedServiceFn,
    module: MODULE,
    method: EXPORT,
  });
};
