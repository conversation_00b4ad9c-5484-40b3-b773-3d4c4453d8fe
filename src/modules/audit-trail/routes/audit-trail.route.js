import { AuditTrailHandler } from '#src/modules/audit-trail/handlers/index.js';
import { AuditTrailSchema } from '#src/modules/audit-trail/schemas/index.js';

/**
 * Audit Trail API routes.
 *
 * @param {import('fastify').FastifyInstance} fastify - Fastify instance
 * @param {Object} opts - Optional Fastify plugin options
 */
const AuditTrailRoute = (fastify) => {
  const PREFIX = '/:id';

  // Define the common access configuration for all routes
  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: true,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  // List all audit trails
  fastify.get('/', {
    schema: AuditTrailSchema.index,
    config: { name: 'auditTrail.list', access: commonAccessConfig },
    handler: AuditTrailHandler.index,
  });

  // View a specific audit trail
  fastify.get(PREFIX, {
    schema: AuditTrailSchema.view,
    config: { name: 'auditTrail.view', access: commonAccessConfig },
    handler: AuditTrailHandler.view,
  });

  // Export audit trail
  fastify.post('/export', {
    schema: AuditTrailSchema.exportAuditTrails,
    config: { name: 'auditTrail.export', access: commonAccessConfig },
    handler: AuditTrailHandler.exportAuditTrail,
  });
};

export default AuditTrailRoute;
