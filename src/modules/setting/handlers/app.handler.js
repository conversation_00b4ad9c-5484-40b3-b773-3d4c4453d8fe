import { CoreConstant } from '#src/modules/core/constants/index.js';
import { AppService } from '#src/modules/setting/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { APP },
  MODULE_METHODS: { CREATE, DELETE, INDEX, UPDATE, TEST, OPTION, VIEW },
} = CoreConstant;

const MODULE = APP;

/**
 * Retrieves a list of application settings, utilizing caching for improved performance.
 *
 * @param {Object} request - The request object containing the necessary data for fetching settings.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the list of application settings.
 */
export const index = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => AppService.index(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Handles the creation of a new application setting.
 *
 * @param {Object} request - The request object containing the data for the new setting.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the result of the creation operation.
 */
export const create = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: AppService.create,
    module: MODULE,
    method: CREATE,
  });

/**
 * Retrieves a specific application setting, utilizing caching for improved performance.
 *
 * @param {Object} request - The request object containing the necessary data for fetching the setting.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the requested application setting.
 */
export const view = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => AppService.view(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Handles the removal of an application setting.
 *
 * @param {Object} request - The request object containing the data for the setting to be removed.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the result of the removal operation.
 */
export const remove = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: AppService.remove,
    module: MODULE,
    method: DELETE,
  });

/**
 * Handles the update of an application setting.
 *
 * @param {Object} request - The request object containing the data for the setting to be updated.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the result of the update operation.
 */
export const update = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: AppService.update,
    module: MODULE,
    method: UPDATE,
  });

/**
 * Executes a test operation for the application settings.
 *
 * @param {Object} request - The request object containing the necessary data for the test operation.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the result of the test operation.
 */
export const runTest = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: AppService.runTest,
    module: MODULE,
    method: TEST,
  });

/**
 * Handles the retrieval of options for application settings.
 *
 * @param {Object} request - The request object containing the necessary data for retrieving options.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the available options for application settings.
 */
export const options = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: AppService.options,
    module: MODULE,
    method: OPTION,
  });
