import { CoreConstant } from '#src/modules/core/constants/index.js';
import { AccessControlService } from '#src/modules/setting/services/index.js';
import { initializeAuditMeta } from '#src/utils/audit-trail.util.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  EVENTS: { ACCESS_CONTROL: ACCESS_CONTROL_EVENT },
  EVENT_ACTIONS: { CREATED, UPDATED, SEARCHED, VIEWED_DETAILS, UPDATED_STATUS },
  MODULE_NAMES: { ACCESS_CONTROL },
  MODULE_METHODS: { CREATE, INDEX, VIEW, UPDATE, UPDATE_STATUS },
} = CoreConstant;

const MODULE = ACCESS_CONTROL;

/**
 * Handles the index request for access control settings.
 * This function generates a cache key, fetches data from cache or service,
 * and processes the response using the handleServiceResponse utility.
 *
 * @async
 * @param {Object} request - The incoming request object.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response.
 */
export const index = async (request, reply) => {
  initializeAuditMeta(request, {
    module: MODULE,
    event: ACCESS_CONTROL_EVENT,
    action: SEARCHED,
  });

  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(
      request.server.redis,
      cacheKey,
      () => AccessControlService.index(request),
      SHORT,
    );

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Handles the view request for a specific access control setting.
 * This function generates a cache key, fetches data from cache or service,
 * and processes the response using the handleServiceResponse utility.
 *
 * @async
 * @param {Object} request - The incoming request object containing details of the access control setting to view.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response containing the viewed access control setting.
 */
export const view = async (request, reply) => {
  const { id } = request.params;

  initializeAuditMeta(
    request,
    {
      module: MODULE,
      event: ACCESS_CONTROL_EVENT,
      action: VIEWED_DETAILS,
    },
    id,
  );

  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);

  const result = await fetchFromCache(
    request.server.redis,
    cacheKey,
    () => AccessControlService.view(request),
    SHORT,
  );

  const precomputedServiceFn = async () => result;

  return handleServiceResponse({
    request,
    reply,
    serviceFn: precomputedServiceFn,
    module: MODULE,
    method: VIEW,
    audit: {
      modelMapping: {
        IpAccessControl: {
          beforeState: result,
        },
      },
    },
  });
};

/**
 * Handles the creation of a new access control setting.
 * This function processes the create request and uses the handleServiceResponse utility
 * to manage the service call and response handling.
 *
 * @async
 * @param {Object} request - The incoming request object containing details for creating a new access control setting.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response containing the newly created access control setting.
 */
export const create = async (request, reply) => {
  initializeAuditMeta(request, {
    module: MODULE,
    event: ACCESS_CONTROL_EVENT,
    action: CREATED,
  });

  const { result, auditModelMapping } = await AccessControlService.create(request);

  const precomputedServiceFn = async () => result;

  return handleServiceResponse({
    request,
    reply,
    serviceFn: precomputedServiceFn,
    module: MODULE,
    method: CREATE,
    audit: {
      modelMapping: auditModelMapping,
    },
  });
};
/**
 * Handles the update request for an existing access control setting.
 * This function processes the update request and uses the handleServiceResponse utility
 * to manage the service call and response handling.
 *
 * @async
 * @param {Object} request - The incoming request object containing details for updating an existing access control setting.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response containing the updated access control setting.
 */
export const update = async (request, reply) => {
  const { id } = request.params;

  initializeAuditMeta(
    request,
    {
      module: MODULE,
      event: ACCESS_CONTROL_EVENT,
      action: UPDATED,
    },
    id,
  );

  const { result, auditModelMapping } = await AccessControlService.update(request);

  const precomputedServiceFn = async () => result;

  return handleServiceResponse({
    request,
    reply,
    serviceFn: precomputedServiceFn,
    module: MODULE,
    method: UPDATE,
    audit: {
      modelMapping: auditModelMapping,
    },
  });
};
/**
 * Handles the status update request for an existing access control setting.
 * This function processes the status update request and uses the handleServiceResponse utility
 * to manage the service call and response handling.
 *
 * @async
 * @param {Object} request - The incoming request object containing details for updating the status of an existing access control setting.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response containing the access control setting with updated status.
 */
export const updateStatus = async (request, reply) => {
  const { id } = request.params;

  initializeAuditMeta(
    request,
    {
      module: MODULE,
      event: ACCESS_CONTROL_EVENT,
      action: UPDATED_STATUS,
    },
    id,
  );

  const { result, auditModelMapping } = await AccessControlService.updateStatus(request);

  const precomputedServiceFn = async () => result;

  return handleServiceResponse({
    request,
    reply,
    serviceFn: precomputedServiceFn,
    module: MODULE,
    method: UPDATE_STATUS,
    audit: {
      modelMapping: auditModelMapping,
    },
  });
};
