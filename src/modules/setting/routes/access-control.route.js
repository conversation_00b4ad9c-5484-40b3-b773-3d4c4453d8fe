import { AccessControlHandler } from '#src/modules/setting/handlers/index.js';
import { AccessControlSchema } from '#src/modules/setting/schemas/index.js';

/**
 * Access Control API routes.
 *
 * @param {import('fastify').FastifyInstance} fastify - Fastify instance
 * @param {Object} opts - Optional Fastify plugin options
 */
const AccessControlRoute = (fastify, opts) => {
  const PREFIX = '/:id';

  // Define the common access configuration for all routes
  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: false,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  // List all access controls
  fastify.get('/', {
    schema: AccessControlSchema.index,
    config: { name: 'accessControl.list', access: commonAccessConfig },
    handler: AccessControlHandler.index,
  });

  // Create access control entry
  fastify.post('/', {
    schema: AccessControlSchema.create,
    config: { name: 'accessControl.create', access: commonAccessConfig },
    handler: AccessControlHandler.create,
  });

  // View a specific access control
  fastify.get(PREFIX, {
    schema: AccessControlSchema.view,
    config: { name: 'accessControl.view', access: commonAccessConfig },
    handler: AccessControlHandler.view,
  });

  // Update a specific access control entry
  fastify.put(PREFIX, {
    schema: AccessControlSchema.update,
    config: { name: 'accessControl.update', access: commonAccessConfig },
    handler: AccessControlHandler.update,
  });

  // Update status of a access control entry
  fastify.patch(`${PREFIX}/status`, {
    schema: AccessControlSchema.updateStatus,
    config: { name: 'accessControl.updateStatus', access: commonAccessConfig },
    handler: AccessControlHandler.updateStatus,
  });
};

export default AccessControlRoute;
