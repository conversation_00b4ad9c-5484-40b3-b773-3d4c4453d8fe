import { <PERSON><PERSON>perHubHandler } from '#src/modules/setting/handlers/index.js';
import { DeveloperHubSchema } from '#src/modules/setting/schemas/index.js';

/**
 * Developer hub API routes.
 *
 * @param {import('fastify').FastifyInstance} fastify - Fastify instance
 * @param {Object} opts - Optional Fastify plugin options
 */
const DevelopHubRoute = (fastify, opts) => {
  const PREFIX = '/:id';

  // Define the common access configuration for all routes
  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: false,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  // List all developer hubs
  fastify.get('/', {
    schema: DeveloperHubSchema.index,
    config: { name: 'developerHub.index', access: commonAccessConfig },
    handler: DeveloperHubHandler.index,
  });

  // Get additional info options for setting category
  fastify.get('/options', {
    schema: DeveloperHubSchema.options,
    config: { name: 'developerHub.options', access: commonAccessConfig },
    handler: DeveloperHubHandler.options,
  });

  // Create developer hub entry
  fastify.post('/', {
    schema: DeveloperHubSchema.create,
    config: { name: 'developerHub.create', access: commonAccessConfig },
    handler: DeveloperHubHandler.create,
  });

  // View a specific developer hub
  fastify.get(PREFIX, {
    schema: DeveloperHubSchema.view,
    config: { name: 'developerHub.view', access: commonAccessConfig },
    handler: DeveloperHubHandler.view,
  });

  // Update basic information of a developer hub entry
  fastify.patch(`${PREFIX}/basic-information`, {
    schema: DeveloperHubSchema.updateBasicInformation,
    config: { name: 'developerHub.updateBasicInformation', access: commonAccessConfig },
    handler: DeveloperHubHandler.updateBasicInformation,
  });

  // Update permission of a developer hub entry
  fastify.patch(`${PREFIX}/permissions`, {
    schema: DeveloperHubSchema.updatePermissions,
    config: { name: 'developerHub.updatePermissions', access: commonAccessConfig },
    handler: DeveloperHubHandler.updatePermissions,
  });

  // Update access controls of a developer hub entry
  fastify.patch(`${PREFIX}/access-controls`, {
    schema: DeveloperHubSchema.updateAccessControls,
    config: { name: 'developerHub.updateAccessControls', access: commonAccessConfig },
    handler: DeveloperHubHandler.updateAccessControls,
  });

  // Update status of a developer hub entry
  fastify.patch(`${PREFIX}/status`, {
    schema: DeveloperHubSchema.updateStatus,
    config: { name: 'developerHub.updateStatus', access: commonAccessConfig },
    handler: DeveloperHubHandler.updateStatus,
  });
};

export default DevelopHubRoute;
