import { App<PERSON><PERSON><PERSON> } from '#src/modules/setting/handlers/index.js';
import { AppSchema } from '#src/modules/setting/schemas/index.js';

/**
 * App API routes.
 *
 * @param {import('fastify').FastifyInstance} fastify - Fastify instance
 * @param {Object} opts - Optional Fastify plugin options
 */
const AppRoute = async (fastify, opts) => {
  // Define the common access configuration for all routes
  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: false,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  // List all App
  fastify.get('/', {
    schema: AppSchema.index,
    config: { name: 'app.list', policy: 'apps.canView', access: commonAccessConfig },
    handler: AppHandler.index,
  });

  // Install a new App
  fastify.post('/', {
    schema: AppSchema.create,
    config: { name: 'app.create', policy: 'apps.canCreate', access: commonAccessConfig },
    handler: AppHandler.create,
  });

  // View an App
  fastify.get('/:id', {
    schema: AppSchema.view,
    config: { name: 'app.view', policy: 'apps.canView', access: commonAccessConfig },
    handler: AppHandler.view,
  });

  // Update an App
  fastify.put('/:id', {
    schema: AppSchema.update,
    config: { name: 'app.update', policy: 'apps.canEdit', access: commonAccessConfig },
    handler: AppHandler.update,
  });

  // Delete an App
  fastify.delete('/:id', {
    schema: AppSchema.remove,
    config: { name: 'app.remove', policy: 'apps.canManage', access: commonAccessConfig },
    handler: AppHandler.remove,
  });

  // Run Test for an App
  fastify.post('/test/:id', {
    schema: AppSchema.runTest,
    config: { name: 'app.runTest', policy: 'apps.canManage', access: commonAccessConfig },
    handler: AppHandler.runTest,
  });

  // Run Test for an App (upload)
  fastify.post('/test-upload/:id', {
    schema: AppSchema.runTestUpload,
    config: { name: 'app.runTest', policy: 'apps.canManage', access: commonAccessConfig },
    handler: AppHandler.runTest,
  });

  // Get additional info options for App (e.g., config key option, test option)
  fastify.get(`/options`, {
    schema: AppSchema.options,
    config: { name: 'app.options', policy: 'apps.canView', access: commonAccessConfig },
    handler: AppHandler.options,
  });
};

export default AppRoute;
