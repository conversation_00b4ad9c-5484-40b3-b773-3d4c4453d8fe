import { LocalisationHandler } from '#src/modules/setting/handlers/index.js';
import { LocalisationSchema } from '#src/modules/setting/schemas/index.js';

/**
 * Localisation API routes.
 *
 * @param {import('fastify').FastifyInstance} fastify - Fastify instance
 * @param {Object} opts - Optional Fastify plugin options
 */
const LocalisationRoute = async (fastify, opts) => {
  const PREFIX = '/:id';

  // Define the common access configuration for all routes
  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: true,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  // List all localisations
  fastify.get('/', {
    schema: LocalisationSchema.index,
    config: { name: 'localisation.list', access: commonAccessConfig },
    handler: LocalisationHandler.index,
  });

  // View a specific localisation
  fastify.get(PREFIX, {
    schema: LocalisationSchema.view,
    config: { name: 'localisation.view', access: commonAccessConfig },
    handler: LocalisationHandler.view,
  });

  // Update a localisation entry
  fastify.put(PREFIX, {
    schema: LocalisationSchema.update,
    config: { name: 'localisation.update', access: commonAccessConfig },
    handler: LocalisationHandler.update,
  });

  // Update status of a localisation entry
  fastify.patch(`${PREFIX}/status`, {
    schema: LocalisationSchema.updateStatus,
    config: { name: 'localisation.updateStatus', access: commonAccessConfig },
    handler: LocalisationHandler.updateStatus,
  });
};

export default LocalisationRoute;
