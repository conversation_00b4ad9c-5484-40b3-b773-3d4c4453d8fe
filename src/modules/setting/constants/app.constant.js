export const ENTITY_APP_STATUSES = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  INSTALLED: 'installed',
  UNINSTALLED: 'uninstalled',
};

export const ENTITY_APP_OPTIONS = {
  'AWS S3': {
    configOptions: ['awsAccessKeyID', 'awsSecretKey', 'awsRegion', 'awsBucketName', 'baseUrl'],
    testOptions: ['file'],
  },
  'Exchangerate Host': {
    configOptions: ['apiAccessKey'],
    testOptions: [],
  },
  Telegram: {
    configOptions: ['telegramBotToken'],
    testOptions: ['chatId'],
  },
  Slack: {
    configOptions: [],
    testOptions: ['webhook'],
  },
  Discord: {
    configOptions: [],
    testOptions: ['webhook'],
  },
  hCaptcha: {
    configOptions: ['hCaptchaSiteKey'],
    testOptions: [],
  },
  Gmail: {
    configOptions: ['gmailClientId', 'gmailClientSecret'],
    testOptions: [],
  },
  SMTP: {
    configOptions: ['smtpHost', 'smtpPort', 'smtpUser', 'smtpPassword'],
    testOptions: ['email'],
  },
  SendGrid: {
    configOptions: ['sendgridApiKey', 'sendgridSender'],
    testOptions: ['email'],
  },
  'Payment System': {
    configOptions: ['secretCode', 'secretPassword'],
    testOptions: ['accountId'],
  },
};
