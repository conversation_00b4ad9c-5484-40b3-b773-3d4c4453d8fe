import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { AccessControlConstant } from '#src/modules/setting/constants/index.js';
import { DeveloperHubRepository } from '#src/modules/setting/repository/index.js';
import { AccessControlService, ApiRightService } from '#src/modules/setting/services/index.js';
import { clearCacheWithPrefix } from '#src/utils/cache.util.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

const {
  MODULE_NAMES: { DEVELOPER_HUB },
  MODULE_METHODS: { INDEX },
} = CoreConstant;
const MODULE = DEVELOPER_HUB;
export const CACHE_PREFIX = `${MODULE}`;

/**
 * Retrieves all developer hub entries.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} A promise that resolves to the paginated list of developer hub entries.
 */
export const index = async (request) => {
  const {
    entity: { id: entityId },
    server,
  } = request;

  const query = {
    ...request.query,
    filter_entityId_eq: entityId,
  };

  return await DeveloperHubRepository.findAll(server, query);
};

/**
 * Retrieves a specific developer hub entry by ID.
 * @param {Object} request - The request object containing params and body.
 * @returns {Promise<Object>} A promise that resolves to the developer hub entry.
 * @throws {CoreError} If the entry is not found or there's a version conflict.
 */
export const view = async (request) => {
  const {
    entity: { id: entityId },
    params: { id, withAssoc = true },
    server,
  } = request;
  const developerHub = await DeveloperHubRepository.findById(server, { entityId, id }, withAssoc);
  if (!developerHub) {
    throw CoreError.dataNotFound({ data: 'common.label.developerHub', attribute: 'ID', value: id });
  }
  return developerHub;
};

/**
 * Creates a new developer hub entry.
 * @param {Object} request - The request object containing body and entity information.
 * @returns {Promise<Object>} A promise that resolves to the created developer hub entry.
 */
export const create = async (request) => {
  const { body, entity, server, authInfo } = request;
  const { id: entityId } = entity;
  const { apiRights = [], ipAccessControls = [], ...developerHubData } = body;

  return withTransaction(server, {}, async (transaction) => {
    // Create Developer Hub
    const createdDeveloperHub = await DeveloperHubRepository.create(
      server,
      { ...developerHubData, entityId },
      { transaction, authInfoId: authInfo.id },
    );

    const auditModelMapping = {
      DeveloperHub: { afterState: createdDeveloperHub.toJSON() },
    };

    if (apiRights.length > 0) {
      // Create API Rights
      const apiRightRequest = {
        body: {
          apiRights,
          parentId: createdDeveloperHub.id,
        },
        entity,
        server,
        authInfo,
      };
      const createdApiRights = await ApiRightService.create(apiRightRequest, { transaction });
      createdDeveloperHub.set('apiRights', createdApiRights, { raw: true });

      auditModelMapping.ApiRight = createdApiRights.map((item) => ({ afterState: item.toJSON() }));
    }

    if (ipAccessControls.length > 0) {
      // Create IP Access Controls
      const accessControlPromises = ipAccessControls.map((control) => {
        const accessControlRequest = {
          body: {
            ...control,
            ruleType: AccessControlConstant.RULE_TYPE.ALLOWLIST,
            parentId: createdDeveloperHub.id,
          },
          entity,
          server,
          authInfo,
        };
        return AccessControlService.create(accessControlRequest, { transaction });
      });

      const createdAccessControls = await Promise.all(accessControlPromises);

      // Process resolved access control data
      const { ipAccessControlData, ipAccessControlAudit, remarkAudit } =
        createdAccessControls.reduce(
          (acc, item) => {
            if (item.auditModelMapping?.IpAccessControl) {
              acc.ipAccessControlAudit.push(item.auditModelMapping.IpAccessControl);
            }
            if (item.auditModelMapping?.Remark) {
              acc.remarkAudit.push(item.auditModelMapping.Remark);
            }

            acc.ipAccessControlData.push(item.result);
            return acc;
          },
          { ipAccessControlData: [], ipAccessControlAudit: [], remarkAudit: [] },
        );

      createdDeveloperHub.set('ipAccessControls', ipAccessControlData, { raw: true });

      auditModelMapping.IpAccessControl = ipAccessControlAudit;
      auditModelMapping.Remark = remarkAudit;
    }

    await clearCacheWithPrefix(server.redis, CACHE_PREFIX, entityId);

    return {
      result: createdDeveloperHub,
      auditModelMapping,
    };
  });
};

/**
 * Updates the basic information of a developer hub entry.
 * @param {Object} request - The request object containing the update data and authInfo information.
 * @param {Object} request.body - The body of the request containing the updated information.
 * @param {Object} request.authInfo - The authInfo object from the request.
 * @param {string} request.authInfo.id - The ID of the authInfo making the update.
 * @returns {Promise<Object>} A promise that resolves to the updated developer hub entry.
 */
export const updateBasicInformation = async (request) => {
  const {
    body,
    entity,
    authInfo: { id: authInfoId },
    server,
  } = request;
  request.params = { ...request.params, withAssoc: false };
  const developerHub = await view(request);

  if (body.expiryDate === '') {
    body.expiryDate = null;
  }

  await DeveloperHubRepository.update(developerHub, body, {
    authInfoId,
  });

  await clearCacheWithPrefix(server.redis, CACHE_PREFIX, entity.id);

  return {
    result: developerHub,
    auditModelMapping: {
      DeveloperHub: { afterState: developerHub },
    },
  };
};

/**
 * Updates the permissions for a developer hub entry.
 * This function updates the API rights associated with a developer hub and updates its version.
 *
 * @param {Object} request - The request object containing necessary data for the update.
 * @param {Object} request.body - The body of the request.
 * @param {Array} request.body.apiRights - An array of API rights to be updated.
 * @param {string} request.body.version - The version of the developer hub to be updated.
 * @param {Object} request.entity - The entity associated with the request.
 * @param {Object} request.server - The server object containing database connection.
 * @param {Object} request.authInfo - The authInfo object initiating the update.
 * @returns {Promise<Object>} A promise that resolves to the updated developer hub entry.
 */
export const updatePermissions = async (request) => {
  const {
    body: { apiRights, version },
    entity,
    server,
    authInfo,
  } = request;

  request.params = { ...request.params, withAssoc: false };
  const developerHub = await view(request);

  return withTransaction(server, {}, async (transaction) => {
    // Update the updatedAt field of the DeveloperHub to update the version of DeveloperHub
    const updatedDeveloperHub = await DeveloperHubRepository.update(
      developerHub,
      { updatedAt: new Date(), version },
      {
        transaction,
        authInfoId: authInfo.id,
      },
    );

    // Update permissions
    const upsertRightRequest = {
      body: {
        apiRights,
        parentId: developerHub.id,
      },
      entity,
      server,
      authInfo,
    };
    const { auditModelMapping: apiRightAuditModelMapping } = await ApiRightService.update(
      upsertRightRequest,
      { transaction },
    );

    await clearCacheWithPrefix(server.redis, CACHE_PREFIX, entity.id);

    return {
      result: updatedDeveloperHub,
      auditModelMapping: {
        DeveloperHub: { afterState: developerHub },
        ...apiRightAuditModelMapping,
      },
    };
  });
};

/**
 * Updates the access controls of a developer hub entry.
 * @param {Object} request - The request object containing params and body.
 * @returns {Promise<Object>} A promise that resolves to the updated developer hub entry.
 */
export const updateAccessControls = async (request) => {
  const { body, entity, server, authInfo } = request;
  request.params = { ...request.params, withAssoc: false };
  const developerHub = await view(request);

  return withTransaction(server, {}, async (transaction) => {
    // Update the updatedAt field of the DeveloperHub to update the version of DeveloperHub
    const updatedDeveloperHub = await DeveloperHubRepository.update(
      developerHub,
      { updatedAt: new Date(), version: body.version },
      { transaction, authInfoId: authInfo.id },
    );

    // Update access controls
    const bulkManageRequest = {
      body: {
        parentId: developerHub.id,
        ruleType: AccessControlConstant.RULE_TYPE.ALLOWLIST,
        ...body,
      },
      server,
      authInfo,
      entity,
    };
    const { auditModelMapping: accessControlAuditModelMapping } =
      await AccessControlService.bulkManage(bulkManageRequest, {
        transaction,
      });

    await clearCacheWithPrefix(server.redis, CACHE_PREFIX, entity.id);

    return {
      result: updatedDeveloperHub,
      auditModelMapping: {
        DeveloperHub: { afterState: developerHub },
        ...accessControlAuditModelMapping,
      },
    };
  });
};
