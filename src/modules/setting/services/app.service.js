import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { TestResultRepository } from '#src/modules/core/repository/index.js';
import { AppConstant } from '#src/modules/setting/constants/index.js';
import { AppError } from '#src/modules/setting/errors/index.js';
import { AppRepository } from '#src/modules/setting/repository/index.js';
import { encrypt } from '#src/utils/aes.util.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';
import { testStrategy } from '#src/utils/test-strategy.util.js';

/**
 * Retrieves a list of applications based on the provided query parameters and entity information.
 *
 * @param {Object} request - The request object containing necessary information.
 * @param {Object} request.query - The query parameters for filtering applications.
 * @param {string} request.query.name - The name filter for applications.
 * @param {string} request.query.category - The category filter for applications.
 * @param {Object} request.entity - The entity object.
 * @param {string} request.entity.id - The ID of the entity.
 * @param {Object} request.server - The server instance.
 *
 * @returns {Promise<Object>} A promise that resolves to the list of applications matching the query.
 */
export const index = async (request) => {
  const { entity } = request;

  const query = {
    ...request.query,
    'filter_entityApp.entityId_eq': entity.id,
  };

  const res = await AppRepository.findAll(request.server, query);

  return {
    ...res,
  };
};

/**
 * Installs a new application for a given entity if it is not already installed.
 *
 * @param {Object} request - The request object containing necessary information.
 * @param {Object} request.body - The body of the request.
 * @param {string} request.body.appId - The ID of the application to be installed.
 * @param {Object} request.entity - The entity object.
 * @param {string} request.entity.id - The ID of the entity.
 * @param {Object} request.server - The server instance.
 * @param {Object} request.authInfo - The authentication information.
 * @param {string} request.authInfo.id - The ID of the authenticated user.
 * @param {Object} [options={}] - Additional options for the transaction.
 *
 * @returns {Promise<void>} A promise that resolves when the application is successfully installed.
 *
 * @throws {AppError} Throws an error if the application is already installed.
 */
export const create = async (request, options = {}) => {
  const {
    body: { appId },
    entity: { id: entityId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  const app = await AppRepository.findById(server, {
    filter_id_eq: appId,
    'filter_entityApp.entityId_eq': entityId,
  });

  if (app && app.entityApp?.length > 0) {
    throw AppError.appAlreadyInstalled({ appId });
  } else {
    // Default status when installing an app
    let status = AppConstant.ENTITY_APP_STATUSES.INSTALLED;

    // If the app has no configurable options, mark it as ACTIVE immediately
    if (AppConstant.ENTITY_APP_OPTIONS[app.name]?.configOptions?.length == 0) {
      status = AppConstant.ENTITY_APP_STATUSES.ACTIVE;
    }

    const entityApp = await AppRepository.createEntityApp(
      server,
      { entityId, appId, status },
      { authInfoId },
    );
    return entityApp;
  }
};
/**
 * Removes an installed application for a given entity.
 *
 * @param {Object} request - The request object containing necessary information.
 * @param {Object} request.body - The body of the request.
 * @param {string} request.body.appId - The ID of the application to be removed.
 * @param {Object} request.entity - The entity object.
 * @param {string} request.entity.id - The ID of the entity.
 * @param {Object} request.server - The server instance.
 * @param {Object} request.authInfo - The authentication information.
 * @param {string} request.authInfo.id - The ID of the authenticated user.
 */
export const remove = async (request, options = {}) => {
  const {
    params: { id: appId },
    entity: { id: entityId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  const entityApp = await AppRepository.findEntityAppById(server, {
    filter_appId_eq: appId,
    filter_entityId_eq: entityId,
  });

  if (!entityApp) {
    throw CoreError.dataNotFound({ data: 'common.label.apps', attribute: 'ID', value: appId });
  }

  const existingConfig = (entityApp?.appConfig?.length ?? 0) > 0 ? entityApp.appConfig : null;
  return withTransaction(server, {}, async (transaction) => {
    await AppRepository.softDelete(entityApp, { transaction, authInfoId });
    if (existingConfig) {
      for (const config of entityApp.appConfig) {
        await AppRepository.softDelete(config, { transaction, authInfoId });
      }
    }
  });
};

/**
 * Retrieves detailed information about a specific installed application for a given entity.
 *
 * @param {Object} request - The request object containing necessary information.
 * @param {Object} request.params - The parameters of the request.
 * @param {string} request.params.id - The ID of the application to be viewed.
 * @param {Object} request.entity - The entity object.
 * @param {string} request.entity.id - The ID of the entity.
 * @param {Object} request.server - The server instance.
 *
 * @returns {Promise<Object>} A promise that resolves to an object containing the application's details,
 *                            including its status and configuration.
 *
 * @throws {AppError} Throws an error if the application is not found.
 */
export const view = async (request) => {
  const {
    params: { id: appId },
    entity: { id: entityId },
    server,
  } = request;

  const entityApp = await AppRepository.findEntityAppById(server, {
    filter_appId_eq: appId,
    filter_entityId_eq: entityId,
  });

  if (!entityApp) {
    throw CoreError.dataNotFound({ data: 'common.label.apps', attribute: 'ID', value: appId });
  }
  const app = {
    ...entityApp.app.toJSON(),
    status: entityApp.status,
  };

  return {
    app,
  };
};

/**
 * Updates the status and configuration of an installed application for a given entity.
 *
 * @param {Object} request - The request object containing necessary information.
 * @param {Object} request.params - The parameters of the request.
 * @param {string} request.params.id - The ID of the application to be updated.
 * @param {Object} request.body - The body of the request.
 * @param {string} request.body.status - The new status of the application.
 * @param {Object} request.body.config - The configuration object for the application.
 * @param {Object} request.entity - The entity object.
 * @param {string} request.entity.id - The ID of the entity.
 * @param {Object} request.server - The server instance.
 * @param {Object} request.authInfo - The authentication information.
 * @param {string} request.authInfo.id - The ID of the authenticated user.
 * @param {Object} [options={}] - Additional options for the transaction.
 *
 * @returns {Promise<void>} A promise that resolves when the application is successfully updated.
 *
 * @throws {AppError} Throws an error if the application is not found.
 */
export const update = async (request, options = {}) => {
  const {
    params: { id: appId },
    body: { status, config },
    entity: { id: entityId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  const entityApp = await AppRepository.findEntityAppById(server, {
    filter_appId_eq: appId,
    filter_entityId_eq: entityId,
  });

  if (!entityApp) {
    throw CoreError.dataNotFound({ data: 'common.label.apps', attribute: 'ID', value: appId });
  }

  return withTransaction(server, options, async (t) => {
    await AppRepository.update(
      entityApp,
      {
        status,
      },
      { transaction: t, authInfoId },
    );

    if (config && typeof config === 'object') {
      for (const [key, val] of Object.entries(config)) {
        const encryptedValue = encrypt(val);
        await AppRepository.upsertAppConfig(
          server,
          {
            entityAppId: entityApp.id,
            configKey: key,
            configValue: encryptedValue,
          },
          {
            transaction: t,
            authInfoId,
          },
        );
      }
    }
  });
};

/**
 * Executes a test strategy for a specified application and records the result.
 *
 * @param {Object} request - The request object containing necessary information.
 * @param {Object} request.params - The parameters of the request.
 * @param {string} request.params.id - The ID of the application to be tested.
 * @param {Object} request.entity - The entity object.
 * @param {string} request.entity.id - The ID of the entity.
 * @param {Object} request.server - The server instance.
 * @param {Object} request.authInfo - The authentication information.
 * @param {string} request.authInfo.id - The ID of the authenticated user.
 * @param {Object} [options={}] - Additional options for the transaction.
 *
 * @returns {Promise<Object>} A promise that resolves to an object containing the test result for the application.
 *
 * @throws {AppError} Throws an error if the application is not found or if the application configuration is missing.
 */
export const runTest = async (request, options = {}) => {
  const {
    params: { id: appId },
    entity: { id: entityId },
    server,
    authInfo: { id: authInfoId },
  } = request;

  // Find the active entity app
  const entityApp = await AppRepository.findEntityAppById(server, {
    filter_appId_eq: appId,
    filter_entityId_eq: entityId,
    filter_status_eq: AppConstant.ENTITY_APP_STATUSES.ACTIVE,
  });

  // Validate: app must be installed and active
  if (!entityApp) {
    throw AppError.appNotInstalled({ appId });
  }

  // Validate: app must support testing
  if (entityApp.app.testFeature === false) {
    throw AppError.appNotSupportTest();
  }

  // Get configs from entity app
  const appConfigs = entityApp?.appConfig || [];

  // Validate: if configs are required but missing → error
  if (
    appConfigs.length === 0 &&
    AppConstant.ENTITY_APP_OPTIONS[entityApp.app.name]?.configOptions?.length > 0
  ) {
    throw AppError.appConfigNotFound({ appId });
  }

  // Find corresponding test strategy by app name
  const appName = entityApp.app.name;
  const strategy = testStrategy[appName];
  if (!strategy) {
    throw AppError.testStrategyNotFound({ appName });
  }

  // Execute the strategy with configs and request body
  const result = await strategy(appConfigs, request.body);

  const status =
    result && result.success === true
      ? CoreConstant.TEST_RESULT_STATUSES.SUCCESS
      : CoreConstant.TEST_RESULT_STATUSES.FAILED;

  // Extract only the filename if an upload is present
  // avoids potential issues with large data sizes
  let requestBodyToStore;
  if (request.body.upload) {
    requestBodyToStore = { filename: request.body.upload.filename };
  } else {
    requestBodyToStore = { ...request.body };
  }

  await TestResultRepository.createTestResult(
    server,
    {
      testableId: entityApp.id,
      testableType: CoreConstant.TESTABLE_TYPES.ENTITY_APP,
      requestHeader: result.requestHeaders || {},
      requestBody: requestBodyToStore,
      response: result.data || {},
      status,
    },
    { authInfoId },
  );

  if (!result?.success) {
    let error = result.error;
    throw AppError.testFailed({ appName, error });
  }

  return { ...result };
};

/**
 * Provides configuration and test options for various services.
 *
 * @returns {Promise<Object>} A promise that resolves to an object containing configuration and test options
 *                            for different services. Each service has a list of configuration options and
 *                            test options.
 */
export const options = async () => {
  return AppConstant.ENTITY_APP_OPTIONS;
};
