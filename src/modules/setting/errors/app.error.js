import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const APP_ERROR_DEF = {
  appAlreadyInstalled: ['90007', 'error.apps.sentence.installed', 409],
  appNotInstalled: ['90008', 'error.apps.sentence.notInstalled', 404],
  appConfigNotFound: ['90004', 'error.apps.sentence.configNotFound', 404],
  appNotSupportTest: ['90005', 'error.apps.sentence.notSupportTest', 422],
  testStrategyNotFound: ['90006', 'error.apps.sentence.testStrategyNotFound', 404],
  testFailed: ['90009', 'error.apps.sentence.testFailed', 400],
};

export const appError = createModuleErrors(MODULE_NAMES.APP, APP_ERROR_DEF);
