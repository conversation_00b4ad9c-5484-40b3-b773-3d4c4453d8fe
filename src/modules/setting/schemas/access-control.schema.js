import startCase from 'lodash/startCase.js';

import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import {
  COMMON_HEADERS,
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { AccessControlConstant } from '#src/modules/setting/constants/index.js';

const {
  COMMON_PROPERTIES,
  CREATE_RESPONSE,
  ERROR_RESPONSE,
  REQ_PARAM_UUID,
  UPDATE_RESPONSE,
  VIEW_RESPONSE,
} = CoreSchema;

const { RULE_TYPE, STATUSES } = AccessControlConstant;

const { ACCESS_CONTROL } = MODULE_NAMES;
export const ACCESS_CONTROL_NAME = startCase(ACCESS_CONTROL).toLowerCase();

export const TAGS = ['BO / Setting / Security Control / Access Control'];

export const ACCESS_CONTROL_PROPERTIES = {
  ipAddress: {
    oneOf: [
      { type: 'string', pattern: '^([0-9]{1,3}\\.){3}[0-9]{1,3}(/[0-9]{1,2})?$' }, // IPv4 with optional subnet
      { type: 'string', pattern: '^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}(/[0-9]{1,3})?$' }, // IPv6 with optional subnet
    ],
  },
  ruleType: { type: 'string', enum: Object.values(RULE_TYPE) },
  remark: { type: 'string' },
  validityDate: {
    type: 'string',
    format: 'date-time',
  },
  status: { type: 'string', enum: Object.values(STATUSES) },
};

export const UPDATE_BODY_PROPERTIES = () => {
  const { status, ...updateBody } = ACCESS_CONTROL_PROPERTIES;
  return updateBody;
};

export const ACCESS_CTRL_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  ...ACCESS_CONTROL_PROPERTIES,
};

export const index = {
  tags: TAGS,
  summary: `Get a list of ${ACCESS_CONTROL_NAME}`,
  headers: COMMON_HEADERS,
  querystring: {
    type: 'object',
    properties: {
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      filter_ruleType_in: { type: 'string' },
      filter_status_in: { type: 'string' },
      filter_validityDate_between: { type: 'string' },
      filter_ipAddress_ipContains: {
        oneOf: [
          { type: 'string', pattern: '^([0-9]{1,3}\\.){3}[0-9]{1,3}(/[0-9]{1,2})?$' }, // IPv4 with optional subnet
          { type: 'string', pattern: '^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}(/[0-9]{1,3})?$' }, // IPv6 with optional subnet
        ],
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: ACCESS_CTRL_RES_PROPERTIES,
          },
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

export const view = {
  tags: TAGS,
  summary: `View a ${ACCESS_CONTROL_NAME}`,
  headers: COMMON_HEADERS,
  params: REQ_PARAM_UUID,
  response: VIEW_RESPONSE({ type: 'object', properties: ACCESS_CTRL_RES_PROPERTIES }),
};

export const create = {
  tags: TAGS,
  summary: `Create a ${ACCESS_CONTROL_NAME}`,
  headers: COMMON_HEADERS,
  body: {
    type: 'object',
    properties: ACCESS_CONTROL_PROPERTIES,
    required: ['ruleType', 'ipAddress'],
  },
  response: CREATE_RESPONSE({ type: 'object', properties: ACCESS_CTRL_RES_PROPERTIES }),
};

export const update = {
  tags: TAGS,
  summary: `Update ${ACCESS_CONTROL_NAME}`,
  headers: COMMON_HEADERS,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: UPDATE_BODY_PROPERTIES(),
    required: ['ipAddress'],
    additionalProperties: false,
  },
  response: UPDATE_RESPONSE,
};

export const updateStatus = {
  tags: TAGS,
  summary: `Update ${ACCESS_CONTROL_NAME} status`,
  headers: COMMON_HEADERS,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      status: { type: 'string', enum: Object.values(STATUSES) },
    },
    required: ['status'],
    additionalProperties: false,
  },
  response: UPDATE_RESPONSE,
};
