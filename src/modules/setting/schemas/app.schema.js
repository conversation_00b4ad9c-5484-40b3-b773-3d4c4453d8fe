import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { AppConstant } from '#src/modules/setting/constants/index.js';

const {
  COMMON_PROPERTIES,
  CREATE_RESPONSE,
  ERROR_RESPONSE,
  REMOVE_RESPONSE,
  REQ_PARAM_UUID,
  UPDATE_RESPONSE,
  VIEW_RESPONSE,
} = CoreSchema;

const {
  MODULE_NAMES: { APP },
} = CoreConstant;

const TAGS = ['BO / Setting / App Center'];

const APP_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  id: { type: 'string', format: 'uuid' },
  category: { type: 'string' },
  name: { type: 'string' },
  description: { type: 'string' },
  testFeature: { type: 'boolean' },
  status: { type: 'string', enum: Object.values(AppConstant.ENTITY_APP_STATUSES) },
};

const APP_CREATE_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  id: { type: 'string', format: 'uuid' },
  category: { type: 'string' },
  name: { type: 'string' },
  testFeature: { type: 'boolean' },
};

const APP_VIEW_RES_PROPERTIES = {
  type: 'object',
  properties: {
    app: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        category: { type: 'string' },
        name: { type: 'string' },
        status: { type: 'string', enum: Object.values(AppConstant.ENTITY_APP_STATUSES) },
      },
    },
  },
};

/**
 * List endpoint schema for app center.
 */
export const index = {
  tags: TAGS,
  summary: `Get a ${APP} center list`,
  querystring: {
    type: 'object',
    properties: {
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      filter_category_eq: { type: 'string' },
      filter_name_iLike: { type: 'string' },
      sortBy: {
        oneOf: [{ type: 'string' }, { type: 'array' }],
        description: 'Sorting field in the format (field:order)',
        default: 'name:asc',
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: {
            properties: APP_RES_PROPERTIES,
          },
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * Create endpoint schema for the app center.
 */
export const create = {
  tags: TAGS,
  summary: `Create a ${APP}`,
  body: {
    type: 'object',
    properties: {
      appId: { type: 'string', format: 'uuid' },
    },
    required: ['appId'],
  },
  response: CREATE_RESPONSE(APP_CREATE_RES_PROPERTIES),
};

/**
 * Schema definition for the remove endpoint in the app center.
 */
export const remove = {
  tags: TAGS,
  summary: `Remove an ${APP}`,
  params: REQ_PARAM_UUID,
  response: REMOVE_RESPONSE,
};

/**
 * Update an app.
 */
export const update = {
  tags: TAGS,
  summary: `Update an ${APP}`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      status: { type: 'string', enum: Object.values(AppConstant.ENTITY_APP_STATUSES) },
      config: {
        type: 'object',
        additionalProperties: true,
        description: 'Configuration settings for the app',
      },
    },
    required: ['status'],
  },
  response: UPDATE_RESPONSE,
};

// View
export const view = {
  tags: TAGS,
  summary: `View a ${APP}`,
  params: REQ_PARAM_UUID,
  response: VIEW_RESPONSE(APP_VIEW_RES_PROPERTIES),
};

// Test an app
export const runTest = {
  tags: TAGS,
  summary: `Test run an ${APP}`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    description: 'Send as JSON',
    properties: {
      email: { type: 'string', minLength: 0 },
      channelId: { type: 'string', minLength: 0 },
      accountId: { type: 'string', minLength: 0 },
      webhook: { type: 'string', minLength: 0 },
    },
    required: [],
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object', additionalProperties: true },
            error: { type: 'object', additionalProperties: true },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

// Test an app (upload file)
export const runTestUpload = {
  tags: TAGS,
  summary: `Test run an ${APP}`,
  params: REQ_PARAM_UUID,
  consumes: ['multipart/form-data'],
  body: {
    type: 'object',
    properties: {
      upload: {
        isFile: true,
        type: 'object',
        properties: {
          filename: { type: 'string', description: 'Name of the file' },
          mimetype: {
            type: 'string',
            enum: ['image/jpeg', 'image/png', 'application/pdf'],
            description: 'MIME type of the file',
          },
          encoding: { type: 'string', description: 'File encoding' },
        },
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          additionalProperties: true,
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

// Option
export const options = {
  tags: TAGS,
  summary: `Get ${APP} config option`,
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
          additionalProperties: {
            type: 'object',
            properties: {
              configOptions: {
                type: 'array',
                items: { type: 'string' },
              },
              testOptions: {
                type: 'array',
                items: { type: 'string' },
              },
            },
            required: ['configOptions', 'testOptions'],
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};
