import startCase from 'lodash/startCase.js';

import { COMMON_STATUSES, MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { ACCESS_CONTROL_PROPERTIES } from '#src/modules/setting/schemas/access-control.schema.js';
const {
  COMMON_PROPERTIES,
  CREATE_RESPONSE,
  ERROR_RESPONSE,
  REQ_PARAM_UUID,
  UPDATE_RESPONSE,
  VIEW_RESPONSE,
} = CoreSchema;
const { PERMISSION_FIELDS } = CoreConstant;

const { DEVELOPER_HUB } = MODULE_NAMES;
const DEVELOPER_HUB_NAME = startCase(DEVELOPER_HUB).toLowerCase();

const TAGS = [`BO / Setting / Developer Hub`];

const BASIC_INFO_BODY_PROP = {
  application: { type: 'string' },
  apiKey: {
    type: 'string',
    minLength: 32,
    maxLength: 32,
    pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z0-9]{32}$',
  },
  expiryDate: { type: 'string', format: 'date-time' },
  status: { type: 'string', enum: Object.values(COMMON_STATUSES) },
};
const { apiKey, ...BASIC_INFO_RES_PROP } = BASIC_INFO_BODY_PROP;

const { ruleType, status, validityDate, ...ACCESS_CTRL_PROPERRIES } = ACCESS_CONTROL_PROPERTIES;

const API_RIGHTS_RES_PROPERTIES = {
  moduleId: { type: 'string', format: 'uuid' },
  moduleName: { type: 'string' },
  permissions: {
    type: 'array',
    items: { type: 'string', enum: PERMISSION_FIELDS },
  },
};
const { moduleName, ...API_RIGHTS_BODY_PROP } = API_RIGHTS_RES_PROPERTIES;

const DEVELOPER_HUB_RES_PROPERTIES = {
  version: { type: 'integer' },
  ...COMMON_PROPERTIES,
  ...BASIC_INFO_RES_PROP,
  apiRights: {
    type: 'array',
    items: {
      type: 'object',
      properties: API_RIGHTS_RES_PROPERTIES,
    },
  },
  ipAccessControls: {
    type: 'array',
    items: {
      type: 'object',
      properties: ACCESS_CTRL_PROPERRIES,
    },
  },
};

export const index = {
  tags: TAGS,
  summary: `Get a list of ${DEVELOPER_HUB_NAME}`,
  querystring: {
    type: 'object',
    properties: {
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      filter_application_iLike: { type: 'string' },
      filter_status_in: { type: 'string' },
      filter_expiryDate_between: { type: 'string' },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              ...COMMON_PROPERTIES,
              ...BASIC_INFO_RES_PROP,
            },
          },
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

export const create = {
  tags: TAGS,
  summary: `Create a ${DEVELOPER_HUB_NAME}`,
  body: {
    type: 'object',
    properties: {
      ...BASIC_INFO_BODY_PROP,
      apiRights: {
        type: 'array',
        items: {
          type: 'object',
          properties: API_RIGHTS_BODY_PROP,
          required: ['moduleId', 'permissions'],
        },
      },
      ipAccessControls: {
        type: 'array',
        items: {
          type: 'object',
          properties: ACCESS_CTRL_PROPERRIES,
          required: ['ipAddress'],
        },
      },
    },
    required: ['application', 'apiKey'],
  },
  response: CREATE_RESPONSE({ type: 'object', properties: DEVELOPER_HUB_RES_PROPERTIES }),
};

export const view = {
  tags: TAGS,
  summary: `View a ${DEVELOPER_HUB_NAME}`,
  params: REQ_PARAM_UUID,
  response: VIEW_RESPONSE({
    type: 'object',
    properties: DEVELOPER_HUB_RES_PROPERTIES,
  }),
};

export const updateBasicInformation = {
  tags: TAGS,
  summary: `Update ${DEVELOPER_HUB_NAME} basic information`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      application: { type: 'string' },
      version: { type: 'number' },
      expiryDate: {
        oneOf: [
          { type: 'string', format: 'date-time' },
          { type: 'string', enum: [''] },
          { type: 'null' },
        ],
      },
    },
    additionalProperties: false,
    required: ['version', 'application'],
  },
  response: UPDATE_RESPONSE,
};

export const updatePermissions = {
  tags: TAGS,
  summary: `Update a ${DEVELOPER_HUB_NAME} permissions`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      version: { type: 'number' },
      apiRights: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            ...API_RIGHTS_BODY_PROP,
          },
          required: ['moduleId', 'permissions'],
        },
      },
    },
    required: ['version', 'apiRights'],
  },
  response: UPDATE_RESPONSE,
};

export const updateAccessControls = {
  tags: TAGS,
  summary: `Update ${DEVELOPER_HUB_NAME} access controls`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      version: { type: 'number' },
      ipAccessControls: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            ipAddress: {
              oneOf: [
                { type: 'string', pattern: '^([0-9]{1,3}\\.){3}[0-9]{1,3}(/[0-9]{1,2})?$' }, // IPv4 with optional subnet
                {
                  type: 'string',
                  pattern: '^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}(/[0-9]{1,3})?$',
                }, // IPv6 with optional subnet
              ],
            },
            remark: { type: 'string' },
          },
          required: ['ipAddress'],
        },
      },
    },
    required: ['version', 'ipAccessControls'],
  },
  response: UPDATE_RESPONSE,
};

export const updateStatus = {
  tags: TAGS,
  summary: `Update a ${DEVELOPER_HUB_NAME} status`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      version: { type: 'number' },
      status: { type: 'string', enum: Object.values(COMMON_STATUSES) },
    },
    required: ['version', 'status'],
  },
  response: UPDATE_RESPONSE,
};

export const options = {
  tags: TAGS,
  summary: 'Get information',
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            modulePermissions: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', format: 'uuid' },
                  name: { type: 'string' },
                  availablePermissions: {
                    type: 'array',
                    items: { type: 'string', enum: PERMISSION_FIELDS },
                  },
                },
              },
            },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};
