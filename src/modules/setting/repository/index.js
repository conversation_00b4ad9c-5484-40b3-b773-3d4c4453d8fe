import * as ApiRightRepository from '#src/modules/setting/repository/api-right.repository.js';
import * as AppRepository from '#src/modules/setting/repository/app.repository.js';
import * as CustomLocalisationRepository from '#src/modules/setting/repository/custom-localisation.repository.js';
import * as DeveloperHubRepository from '#src/modules/setting/repository/developer-hub.repository.js';
import * as IpAccessControlRepository from '#src/modules/setting/repository/ip-access-control.repository.js';
import * as LocalisationRepository from '#src/modules/setting/repository/localisation.repository.js';
import * as SettingRepository from '#src/modules/setting/repository/setting.repository.js';

export {
  ApiRightRepository,
  AppRepository,
  CustomLocalisationRepository,
  DeveloperHubRepository,
  IpAccessControlRepository,
  LocalisationRepository,
  SettingRepository,
};
