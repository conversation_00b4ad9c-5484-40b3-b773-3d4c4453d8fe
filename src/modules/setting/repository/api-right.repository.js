import { Op } from 'sequelize';

import { CoreConstant } from '#src/modules/core/constants/index.js';

const { PERMISSION_FIELDS } = CoreConstant;

/**
 * Upserts (creates or updates) multiple API rights in the database.
 *
 * @param {Object} server - The server object containing the database connection.
 * @param {Array} createData - An array of objects representing the API rights to be upserted.
 * @param {Object} [options={}] - Additional options to pass to the Sequelize findOrCreate and update methods.
 *
 * @returns {Promise<Array>} A promise that resolves to an array of the upserted API right instances.
 */
export const upsert = async (server, createData, options = {}) => {
  const upsertPromises = createData.map(async (data) => {
    const [apiRight, created] = await server.psql.ApiRight.findOrCreate({
      where: {
        parentId: data.parentId,
        moduleId: data.moduleId,
      },
      defaults: data,
      ...options,
    });

    if (!created) {
      const updateData = PERMISSION_FIELDS.reduce((acc, field) => {
        if (data[field] !== undefined) {
          acc[field] = data[field];
        }
        return acc;
      }, {});

      if (Object.keys(updateData).length > 0) {
        await apiRight.update(updateData, options);
      }
    }

    return apiRight;
  });

  return Promise.all(upsertPromises);
};

/**
 * Removes API rights from the database based on the specified criteria.
 *
 * @param {Object} server - The server object containing the database connection.
 * @param {number|string} parentId - The ID of the parent entity associated with the API rights.
 * @param {Array<number|string>} moduleIds - An array of module IDs to exclude from deletion.
 * @param {Object} [options={}] - Additional options to pass to the Sequelize destroy method.
 *
 * @returns {Promise<number>} A promise that resolves to the number of destroyed records.
 */
export const remove = async (server, parentId, moduleIds, options = {}) => {
  return await server.psql.ApiRight.destroy({
    where: {
      parentId,
      moduleId: { [Op.notIn]: moduleIds },
    },
    ...options,
  });
};
