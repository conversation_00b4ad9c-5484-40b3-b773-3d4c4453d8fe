import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

/**
 * Retrieves all developer hub entries with pagination for a specific entity.
 * @param {Object} fastify - The Fastify instance containing the PostgreSQL models.
 * @param {Object} query - The query parameters for filtering and pagination.
 * @returns {Promise<Object>} A promise that resolves to an object containing paginated developer hub entries.
 */
export const findAll = async (fastify, query) => {
  const { DeveloperHub } = fastify.psql;

  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(query, DeveloperHub);

  return await applyOffsetPagination(fastify, DeveloperHub, query, whereFilter, includeFilter);
};

/**
 * Retrieves a specific developer hub entry by its ID and entity ID.
 * @param {Object} server - The server object containing database access methods.
 * @param {Object} params - The parameters object containing entityId and id.
 * @param {string} params.entityId - The ID of the entity associated with the developer hub entry.
 * @param {string|number} params.id - The unique identifier of the developer hub entry to retrieve.
 * @param {boolean} [withAssoc=true] - Whether to include associated data (apiRights and ipAccessControls).
 * @returns {Promise<Object|null>} A promise that resolves to the found developer hub entry object, or null if not found.
 */
export const findById = async (server, { entityId, id }, withAssoc = true) =>
  await server.psql.DeveloperHub.findOne({
    where: {
      id,
      entityId,
    },
    ...(withAssoc && {
      include: [
        { association: 'apiRights' },
        {
          association: 'ipAccessControls',
          include: [
            {
              association: 'activeRemark',
            },
          ],
        },
      ],
    }),
  });

/**
 * Retrieves a developer hub entry by its API key.
 * @param {Object} server - The server object containing database access methods.
 * @param {string} apiKey - The API key to search for in the database.
 * @returns {Promise<Object|null>} A promise that resolves to the found developer hub entry object, or null if not found.
 */
export const findByApiKey = async (server, apiKey) =>
  await server.psql.DeveloperHub.findOne({
    where: { apiKey },
  });

/**
 * Creates a new developer hub entry in the database.
 * @param {Object} server - The server object containing database access methods.
 * @param {Object} data - The data object containing the information for the new developer hub entry.
 * @param {Object} [options={}] - Optional parameters to be passed to the create method.
 * @returns {Promise<Object>} A promise that resolves to the newly created developer hub entry object.
 */
export const create = async (server, data, options = {}) =>
  await server.psql.DeveloperHub.create(data, options);

/**
 * Updates an existing developer hub entry in the database.
 * @param {Object} modelData - The model instance of the developer hub entry to be updated.
 * @param {Object} updateData - An object containing the fields and values to be updated.
 * @param {Object} [options={}] - Optional parameters to be passed to the update method.
 * @returns {Promise<Object>} A promise that resolves to the updated developer hub entry object.
 */
export const update = async (modelData, updateData, options = {}) =>
  await modelData.update(updateData, options);
