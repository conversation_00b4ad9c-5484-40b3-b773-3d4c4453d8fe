import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

/**
 * Retrieves all App entries from the database, applying filters and pagination.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Object} query - The query parameters for filtering and paginating the App entries.
 * @returns {Promise<Array>} A promise that resolves to an array of App entries, possibly empty if no entries match the filters.
 */
export const findAll = async (fastify, query) => {
  const { App } = fastify.psql;

  const includes = [
    {
      association: 'entityApp',
      required: false,
      include: [
        {
          association: 'testResult',
          required: false,
        },
      ],
    },
  ];
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    App,
    includes,
  );

  return await applyOffsetPagination(fastify, App, query, whereFilter, includeFilter);
};

/**
 * Retrieves a single App entry by its ID, including related entityApp and appConfig associations.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Object} query - The query parameters for filtering the App entry.
 * @returns {Promise<Object|null>} A promise that resolves to the App entry if found, otherwise null.
 */
export const findById = async (fastify, query) => {
  const { App } = fastify.psql;
  const includes = [
    {
      association: 'entityApp',
      required: false,
      include: [
        {
          association: 'appConfig',
          required: false,
        },
      ],
    },
  ];
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    App,
    includes,
  );

  const res = await App.findOne({
    where: whereFilter,
    include: includeFilter,
  });

  return res;
};

/**
 * Updates the provided model data with the specified update data.
 *
 * @param {Object|Array} modelData - The model instance(s) to be updated. Can be a single instance or an array of instances.
 * @param {Object} updateData - The data to update the model instance(s) with.
 * @param {Object} [options={}] - Optional settings for the update operation.
 * @returns {Promise<Object|Array>} A promise that resolves to the updated model instance(s).
 */
export const update = async (modelData, updateData, options = {}) => {
  if (Array.isArray(modelData)) {
    return await Promise.all(modelData.map((item) => item.update(updateData, options)));
  }

  return await modelData.update(updateData, options);
};

/**
 * Removes the provided model instance(s) from the database.
 *
 * @param {Object|Array} modelData - The model instance(s) to be removed. Can be a single instance or an array of instances.
 * @param {Object} [options={}] - Optional settings for the remove operation.
 * @returns {Promise<void>} A promise that resolves when the model instance(s) have been removed.
 */
export const softDelete = async (modelData, options = {}) =>
  await modelData.destroy({
    ...options,
  });

/**
 * Retrieves a single EntityApp entry by its ID, including related appConfig associations.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Object} query - The query parameters for filtering the EntityApp entry.
 * @returns {Promise<Object|null>} A promise that resolves to the EntityApp entry if found, otherwise null.
 */
export const findEntityAppById = async (fastify, query) => {
  const { EntityApp } = fastify.psql;
  const includes = [
    {
      association: 'appConfig',
      required: false,
    },
    {
      association: 'app',
      required: false,
    },
  ];
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    EntityApp,
    includes,
  );

  const res = await EntityApp.findOne({
    where: whereFilter,
    include: includeFilter,
  });

  return res;
};

/**
 * Creates a new EntityApp entry in the database with the provided data.
 *
 * @param {Object} server - The server instance providing access to the database.
 * @param {Object} data - The data to create the new EntityApp entry with.
 * @param {Object} [options={}] - Optional settings for the create operation.
 * @returns {Promise<Object>} A promise that resolves to the newly created EntityApp entry.
 */
export const createEntityApp = async (server, data, options = {}) => {
  return await server.psql.EntityApp.create(data, options);
};

/**
 * Inserts or updates an AppConfig entry in the database based on the provided data.
 * If an entry with the specified entityAppId and configKey exists, it updates the configValue.
 * Otherwise, it creates a new entry with the given data.
 *
 * @param {Object} server - The server instance providing access to the database.
 * @param {Object} data - The data for the AppConfig entry, including entityAppId, configKey, and configValue.
 * @param {Object} [options={}] - Optional settings for the findOrCreate and update operations.
 * @returns {Promise<Object>} A promise that resolves to the existing or newly created AppConfig entry.
 */
export const upsertAppConfig = async (server, data, options = {}) => {
  const { AppConfig } = server.psql;

  const { entityAppId, configKey, configValue } = data;

  const [existing, created] = await AppConfig.findOrCreate({
    where: {
      entityAppId,
      configKey,
    },
    defaults: {
      configValue,
    },
    ...options,
  });

  if (!created) {
    await existing.update(
      {
        configValue,
      },
      options,
    );
  }

  return existing;
};
