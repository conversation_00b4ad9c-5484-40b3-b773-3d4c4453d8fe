import { buildWhereFromFilters } from '#src/utils/query.util.js';

/**
 * Retrieves settings based on the provided query parameters, including associated custom settings.
 *
 * @param {Object} server - The server object containing database models.
 * @param {Object} query - The query parameters for filtering settings.
 * @returns {Promise<Array>} A promise that resolves to an array of Setting objects with associated CustomSetting.
 */
export const findByCategoryAndAccess = async (server, query) => {
  const { Setting } = server.psql;

  const includes = [
    {
      association: 'customSettings',
      as: 'customSettings',
      required: false,
    },
  ];
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    Setting,
    includes,
  );

  const settings = await Setting.findAll({
    where: whereFilter,
    include: includeFilter,
  });

  return settings.map((setting) => setting.toSafeObject());
};

/**
 * Upserts (inserts or updates) custom settings for multiple entities.
 *
 * @param {Object} server - The server object containing database models.
 * @param {Array<Object>} updateData - An array of objects containing the custom setting data to upsert.
 * @param {string|number} updateData[].entityId - The ID of the entity associated with the custom setting.
 * @param {string|number} updateData[].parentId - The ID of the parent setting.
 * @param {*} updateData[].value - The value to be set for the custom setting.
 * @param {Object} [options={}] - Additional options to be passed to the database operations.
 * @returns {Promise<Array>} A promise that resolves to an array of results from the upsert operations.
 */
export const upsertCustomSettings = async (server, updateData, options = {}) => {
  const { CustomSetting } = server.psql;

  const upsertPromises = updateData.map(async (update) => {
    const { entityId, parentId, value, version } = update;

    const [customSetting, created] = await CustomSetting.findOrCreate({
      where: {
        parentId,
        entityId,
      },
      defaults: {
        value,
        version,
      },
      ...options,
    });

    if (!created) {
      await customSetting.update({ value, version }, options);
    }
  });

  const results = await Promise.all(upsertPromises);
  return results;
};

/**
 * Retrieves a single setting by category and field, including any associated custom settings.
 *
 * @param {Object} server - The server object containing database models.
 * @param {Object} settingIdentifier - The identifier for the specific setting.
 * @param {string} settingIdentifier.category - The category of the setting (e.g., 'safety', 'general').
 * @param {string} settingIdentifier.field - The field name of the setting (e.g., 'maintenanceStatus').
 * @param {Object} [query={}] - Optional query parameters for additional filtering.
 * @returns {Promise<Object|null>} A promise that resolves to the requested setting or null if not found.
 */
export const getSingleSetting = async (server, settingIdentifier, query = {}) => {
  const { Setting } = server.psql;
  const { category, field } = settingIdentifier;

  if (!category || !field) {
    throw new Error('Both category and field must be provided to get a single setting');
  }

  const includes = [
    {
      association: 'customSettings',
      as: 'customSettings',
      required: false,
    },
  ];

  const { where: additionalFilters, include: includeFilter } = buildWhereFromFilters(
    query,
    Setting,
    includes,
  );

  const whereFilter = {
    ...additionalFilters,
    category,
    field,
  };

  const setting = await Setting.findOne({
    where: whereFilter,
    include: includeFilter,
  });

  return setting ? setting.toSafeObject() : null;
};
