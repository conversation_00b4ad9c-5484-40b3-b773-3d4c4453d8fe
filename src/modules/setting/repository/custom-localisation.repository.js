/**
 * Repository for CustomLocalisation model operations
 */

/**
 * Creates a new custom localisation record
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Object} data - The custom localisation data to create
 * @param {Object} options - Additional options for the create operation
 * @returns {Promise<Object>} The created custom localisation record
 */
export const create = async (fastify, data, options = {}) => {
  return await fastify.psql.CustomLocalisation.create(data, options);
};

/**
 * Finds all custom localisations matching the criteria
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Object} query - The query object
 * @param {Object} options - Additional options for the find operation
 * @returns {Promise<Array>} Array of custom localisation records
 */
export const findAll = async (fastify, query, options = {}) => {
  return await fastify.psql.CustomLocalisation.findAll({
    ...query,
    ...options,
  });
};

/**
 * Updates custom localisation records matching the criteria
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Object} data - The data to update
 * @param {Object} where - The where clause
 * @param {Object} options - Additional options for the update operation
 * @returns {Promise<Array>} Array containing the number of affected rows and the affected rows
 */
export const update = async (fastify, data, where, options = {}) => {
  return await fastify.psql.CustomLocalisation.update(data, {
    where,
    ...options,
  });
};

/**
 * Finds or creates a custom localisation record
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Object} query - The query object containing where and defaults
 * @param {Object} options - Additional options for the operation
 * @returns {Promise<Array>} Array containing the record and a boolean indicating if it was created
 */
export const findOrCreate = async (fastify, query, options = {}) => {
  return await fastify.psql.CustomLocalisation.findOrCreate({
    ...query,
    ...options,
  });
};
