/**
 * Creates a new bulk job in the database.
 *
 * @param {Object} fastify - The Fastify instance.
 * @param {Object} data - The data for the bulk job to be created.
 * @param {Object} user - The user creating the bulk job.
 *
 * @returns {Object} - An object containing the created bulk job or an error message.
 * - If successful, the object will contain the created bulk job.
 * - If an error occurs, the object will contain an 'error' property with the error message.
 */
export const create = async (fastify, data, user) => {
  const options = {
    userId: user.id,
  };
  return await fastify.psql.BulkJob.create(data, options);
};
