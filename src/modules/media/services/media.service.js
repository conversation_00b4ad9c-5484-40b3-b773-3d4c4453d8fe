import { MediaAssetConstant } from '#src/modules/media/constants/index.js';
import { MediaError } from '#src/modules/media/errors/index.js';
import { MediaAssetRepository } from '#src/modules/media/repository/index.js';
import { uploadToS3 } from '#src/utils/upload.util.js';
/**
 * Uploads a file to S3 and creates a media asset record
 * @param {Object} request - The Fastify request object
 * @returns {Promise<Object>} The created media asset record
 */
export const create = async (request) => {
  if (!request.body?.file) {
    throw MediaError.missingFile();
  }

  const { file, visibility = 'public', altText = null, metadata = {} } = request.body;
  const authInfoId = request.authInfo?.id;
  const entityId = request.entity?.id;

  let filePath, fileBuffer, fileName, mimeType;
  try {
    ({ filename: fileName, mimetype: mimeType, buffer: fileBuffer } = file);
    fileBuffer = fileBuffer || (await file.toBuffer());

    filePath = await uploadToS3(fileBuffer, fileName, mimeType, request.server);
  } catch (error) {
    throw MediaError.uploadFailed(error.message);
  }

  let fileType = MediaAssetConstant.FILE_TYPE.DOCUMENT;
  if (mimeType.startsWith('image/')) {
    fileType = MediaAssetConstant.FILE_TYPE.IMAGE;
  } else if (mimeType.startsWith('audio/')) {
    fileType = MediaAssetConstant.FILE_TYPE.AUDIO;
  }

  const fileSize = fileBuffer.length;

  const mediaAssetData = {
    entityId,
    fileType,
    filePath,
    mimeType,
    fileSize,
    altText,
    visibility,
    metadata: {
      originalFilename: fileName,
      ...metadata,
    },
  };

  return await MediaAssetRepository.create(request.server, mediaAssetData, {
    authInfoId,
  });
};
