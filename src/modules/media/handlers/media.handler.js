import { CoreConstant } from '#src/modules/core/constants/index.js';
import { MediaService } from '#src/modules/media/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  MODULE_NAMES: { MEDIA },
  MODULE_METHODS: { CREATE },
} = CoreConstant;

const MODULE = MEDIA;

/**
 * Handles the upload of media files.
 *
 * @param {Object} request - The request object containing the file to upload.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the uploaded file URL and filename.
 */
export const create = async (request, reply) => {
  return handleServiceResponse({
    request,
    reply,
    serviceFn: MediaService.create,
    module: MODULE,
    method: CREATE,
  });
};
