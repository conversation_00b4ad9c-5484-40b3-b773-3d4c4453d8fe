import { MediaHandler } from '#src/modules/media/handlers/index.js';
import * as MediaSchema from '#src/modules/media/schemas/media.schema.js';

/**
 * Media routes
 * @param {object} fastify - Fastify instance
 * @param {object} options - Route options
 */
export default async function (fastify, options) {
  const commonAccessConfig = {
    user: true,
    member: true,
    webhook: false,
    public: false,
    ipWhitelist: [],
  };

  fastify.post('/', {
    schema: MediaSchema.create,
    config: { name: 'media.create', access: commonAccessConfig },
    handler: MediaHandler.create,
    attachValidation: true,
  });
}
