/**
 * Creates a new media asset in the database
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} mediaAssetData - The data for the new media asset
 * @param {Object} options - Additional options for the create operation
 * @returns {Promise<Object>} The created media asset as a plain JavaScript object
 */
export const create = async (fastify, mediaAssetData, options = {}) => {
  const newMediaAsset = await fastify.psql.MediaAsset.create(mediaAssetData, options);

  return newMediaAsset.toJSON();
};

/**
 * Find a media asset by its primary key
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {string|number} id - The primary key of the media asset
 * @param {Object} options - Additional options for the query
 * @returns {Promise<Object|null>} The found media asset or null if not found
 */
export const findByPk = async (fastify, id, options = {}) => {
  const mediaAsset = await fastify.psql.MediaAsset.findByPk(id, options);

  return mediaAsset ? mediaAsset.toJSON() : null;
};
