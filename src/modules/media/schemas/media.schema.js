import { CoreSchema } from '#src/modules/core/schemas/index.js';

const { ERROR_RESPONSE } = CoreSchema;

const TAGS = ['General / Media Management'];

/**
 * Upload media schema.
 */
export const create = {
  tags: TAGS,
  summary: 'Upload media file',
  description: 'Upload a media file (image, video, audio, document)',
  consumes: ['multipart/form-data'],
  body: {
    type: 'object',
    properties: {
      file: {
        type: 'string',
        format: 'binary',
        description: 'Media file to upload',
      },
    },
    required: ['file'],
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            filePath: { type: 'string' },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};
