import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { EntityConstant } from '#src/modules/core/constants/index.js';
import {
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';

const {
  COMMON_PROPERTIES,
  ERROR_RESPONSE,
  REQ_PARAM_UUID,
  CREATE_RESPONSE,
  UPDATE_RESPONSE,
  VIEW_RESPONSE,
} = CoreSchema;
const { ORGANISATION } = MODULE_NAMES;

const TAGS = ['BO / Organisation Management / Organisation'];

/**
 * Response schema properties for organisations.
 */
const ORGANISATION_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  accessId: { type: 'string' },
  hierarchy: { type: 'string' },
  code: { type: 'string' },
  prefix: { type: 'string' },
  name: { type: 'string' },
  description: { type: 'string' },
  emailDomain: { type: 'string' },
  currencies: {
    type: 'array',
    items: {
      type: 'object',
      properties: {
        code: { type: 'string' },
      },
    },
  },
  address: {
    type: 'object',
    properties: {
      street: { type: 'string' },
      city: { type: 'string' },
      state: { type: 'string' },
      postalCode: { type: 'string' },
      country: { type: 'string' },
    },
  },
  entityId: { type: 'string', format: 'uuid' },
  metadata: { type: 'object', additionalProperties: true },
  status: { type: 'string', enum: Object.values(EntityConstant.ENTITY_STATUSES) },
  version: { type: 'integer' },
};

/**
 * List endpoint schema for organisations.
 */
export const index = {
  tags: TAGS,
  summary: `Get a list of ${ORGANISATION}`,
  querystring: {
    type: 'object',
    properties: {
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      filter_id_eq: { type: 'string' },
      filter_code_eq: { type: 'string' },
      filter_name_eq: { type: 'string' },
      filter_prefix_eq: { type: 'string' },
      filter_accessId_eq: { type: 'string' },
      filter_status_in: { type: 'string' },
      sortBy: {
        oneOf: [{ type: 'string' }, { type: 'array' }],
        description: 'Sorting field in the format (field:order)',
        default: 'name:asc',
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: ORGANISATION_RES_PROPERTIES,
          },
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * View endpoint schema for organisation.
 */
export const view = {
  tags: TAGS,
  summary: `View a ${ORGANISATION}`,
  params: REQ_PARAM_UUID,
  response: VIEW_RESPONSE({
    type: 'object',
    properties: {
      ...ORGANISATION_RES_PROPERTIES,
      phone: { type: 'string' },
      email: { type: 'string' },
    },
  }),
};

/**
 * Create organisation schema.
 */
export const create = {
  tags: TAGS,
  summary: `Create a ${ORGANISATION}`,
  body: {
    type: 'object',
    properties: {
      name: { type: 'string' },
      prefix: { type: 'string' },
      code: { type: 'string' },
      currencyIds: {
        type: 'array',
        items: {
          type: 'string',
          format: 'uuid',
          description: 'Currency UUID',
        },
      },
      description: { type: 'string' },
      phone: { type: 'string' },
      email: { type: 'string' },
      address: {
        type: 'object',
        properties: {
          street: { type: 'string' },
          city: { type: 'string' },
          state: { type: 'string' },
          postalCode: { type: 'string' },
          country: { type: 'string' },
        },
      },
      emailDomain: { type: 'string' },
      logoDesktop: { type: 'string', format: 'uuid' },
      logoMobile: { type: 'string', format: 'uuid' },
      favicon: { type: 'string', format: 'uuid' },
      language: { type: 'string', format: 'uuid' },
      region: { type: 'string', format: 'uuid' },
      timeFormat: { type: 'string' },
      dateFormat: { type: 'string' },
    },
    required: [
      'prefix',
      'code',
      'name',
      'currencyIds',
      'region',
      'language',
      'timeFormat',
      'dateFormat',
    ],
  },
  response: CREATE_RESPONSE,
};

/**
 * Update basic information schema for organisation.
 */
export const updateBasicInformation = {
  tags: TAGS,
  summary: `Update basic information of a ${ORGANISATION}`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      description: { type: 'string' },
      currencyIds: {
        type: 'array',
        minItems: 1,
        uniqueItems: true,
        items: {
          type: 'string',
          format: 'uuid',
          description: 'Currency UUID',
        },
      },
    },
    required: ['currencyIds', 'version'],
  },
  response: UPDATE_RESPONSE,
};

/**
 * Update contact information schema for organisation.
 */
export const updateContactInformation = {
  tags: TAGS,
  summary: `Update contact information of a ${ORGANISATION}`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      phone: { type: 'string' },
      email: { type: 'string' },
      address: {
        type: 'object',
        properties: {
          street: { type: 'string' },
          city: { type: 'string' },
          state: { type: 'string' },
          postalCode: { type: 'string' },
          country: { type: 'string' },
        },
      },
    },
    required: ['version'],
  },
  response: UPDATE_RESPONSE,
};

/**
 * Update organisation link schema for organisation.
 */
export const updateOrganisationLink = {
  tags: TAGS,
  summary: `Update email domain of a ${ORGANISATION}`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      emailDomain: { type: 'string' },
    },
    required: ['emailDomain', 'version'],
  },
  response: UPDATE_RESPONSE,
};

/**
 * Update status schema for organisation.
 */
export const updateStatus = {
  tags: TAGS,
  summary: `Update a ${ORGANISATION} status`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      status: { type: 'string', enum: Object.values(EntityConstant.ENTITY_STATUSES) },
    },
    required: ['status', 'version'],
  },
  response: UPDATE_RESPONSE,
};

/**
 * Dropdown endpoint schema for organisation.
 */
export const organisationDropdown = {
  tags: TAGS,
  summary: `Get a dropdown list of ${ORGANISATION}`,
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              code: { type: 'string' },
              name: { type: 'string' },
              accessId: { type: 'string' },
            },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * Options endpoint schema for organisation.
 */
export const options = {
  tags: TAGS,
  summary: `Get a options list of ${ORGANISATION} settings`,
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            language: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', format: 'uuid' },
                  name: { type: 'string' },
                },
              },
            },
            region: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', format: 'uuid' },
                  name: { type: 'string' },
                },
              },
            },
            currency: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', format: 'uuid' },
                  name: { type: 'string' },
                },
              },
            },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * Get theme settings schema for organisation.
 */
export const getThemes = {
  tags: TAGS,
  summary: `Get theme settings of a ${ORGANISATION}`,
  params: REQ_PARAM_UUID,
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            logoDesktop: { type: 'string', nullable: true },
            logoMobile: { type: 'string', nullable: true },
            favicon: { type: 'string', nullable: true },
          },
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * Update theme settings schema for organisation.
 */
export const updateThemes = {
  tags: TAGS,
  summary: `Update theme settings of a ${ORGANISATION}`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      logoDesktop: { type: 'string', format: 'uuid' },
      logoMobile: { type: 'string', format: 'uuid' },
      favicon: { type: 'string', format: 'uuid' },
    },
    additionalProperties: false,
  },
  response: UPDATE_RESPONSE,
};
