import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';

const { COMMON_PROPERTIES, ERROR_RESPONSE, REQ_PARAM_UUID, VIEW_RESPONSE, UPDATE_RESPONSE } =
  CoreSchema;
const { ORGANISATION } = MODULE_NAMES;

const TAGS = ['BO / Organisation Management / Organisation / Setting / Currency'];

/**
 * Response schema properties for organisation currency.
 */
const CURRENCY_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  id: { type: 'string', format: 'uuid' },
  name: { type: 'string' },
  code: { type: 'string' },
  symbol: { type: 'string' },
  status: { type: 'string', enum: Object.values(CoreConstant.COMMON_STATUSES) },
};

/**
 * List endpoint schema for organisations currency.
 */
export const index = {
  tags: TAGS,
  summary: `Get a list of ${ORGANISATION} currency`,
  querystring: {
    type: 'object',
    properties: {
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      'filter_localisation.code_in': { type: 'string' },
      filter_status_in: { type: 'string' },
      sortBy: {
        oneOf: [{ type: 'string' }, { type: 'array' }],
        description: 'Sorting field in the format (field:order)',
        default: 'localisation.name:asc',
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: CURRENCY_RES_PROPERTIES,
          },
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * View endpoint schema for organisation currency.
 */
export const view = {
  tags: TAGS,
  summary: `View a ${ORGANISATION} currency`,
  params: REQ_PARAM_UUID,
  response: VIEW_RESPONSE({
    type: 'object',
    properties: {
      ...CURRENCY_RES_PROPERTIES,
      exchangeRate: { type: 'number' },
      symbol: { type: 'string' },
    },
  }),
};

/**
 * Update status schema for organisation currency.
 */
export const updateStatus = {
  tags: TAGS,
  summary: `Update a ${ORGANISATION} currency status`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      status: { type: 'string', enum: Object.values(CoreConstant.COMMON_STATUSES) },
    },
    required: ['status'],
  },
  response: UPDATE_RESPONSE,
};
