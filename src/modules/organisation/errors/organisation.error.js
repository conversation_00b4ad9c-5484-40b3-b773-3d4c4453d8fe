import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const ORGANISATION_ERROR_DEF = {
  organisationCreationFailed: ['51407', 'error.sentence.organisationCreationFailed', 500],
  organisationUpdateFailed: ['51408', 'error.sentence.organisationUpdateFailed', 500],
  noCurrenciesProvided: ['51411', 'error.sentence.noCurrenciesProvided', 400],
  invalidCurrencies: ['51412', 'error.sentence.invalidCurrencies', 400],
  invalidStatus: ['51414', 'error.sentence.invalidStatus', 400],
};

export const organisationError = createModuleErrors(
  MODULE_NAMES.ORGANISATION,
  ORGANISATION_ERROR_DEF,
);
