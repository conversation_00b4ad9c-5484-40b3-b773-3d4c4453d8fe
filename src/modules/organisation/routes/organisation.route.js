import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OrganisationHandler } from '#src/modules/organisation/handlers/index.js';
import { CurrencySchema, OrganisationSchema } from '#src/modules/organisation/schemas/index.js';

/**
 * Organisation API routes.
 *
 * @param {import('fastify').FastifyInstance} fastify - Fastify instance
 * @param {Object} opts - Optional Fastify plugin options
 */
const OrganisationRoute = async (fastify, opts) => {
  const PREFIX = '/:id';
  const CURRENCY_SETTING_PREFIX = '/setting/currency';

  // Define the common access configuration for all routes
  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: true,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  // List all organisations
  fastify.get('/', {
    schema: OrganisationSchema.index,
    config: { name: 'organisation.list', access: commonAccessConfig },
    handler: OrganisationHandler.index,
  });

  // View a specific organisation
  fastify.get(PREFIX, {
    schema: OrganisationSchema.view,
    config: { name: 'organisation.view', access: commonAccessConfig },
    handler: OrganisationHandler.view,
  });

  // Create a new organisation
  fastify.post('/', {
    schema: OrganisationSchema.create,
    config: {
      name: 'organisation.create',
      access: commonAccessConfig,
      sanitiseHtml: {
        ignoreFields: [''],
        options: {
          ADD_ALLOWED_TAGS: ['a'],
          ADD_ALLOWED_ATTR: ['a'],
        },
      },
    },
    handler: OrganisationHandler.create,
  });

  fastify.patch(`${PREFIX}/basic-information`, {
    schema: OrganisationSchema.updateBasicInformation,
    config: { name: 'organisation.updateBasicInformation', access: commonAccessConfig },
    handler: OrganisationHandler.updateBasicInformation,
  });

  fastify.patch(`${PREFIX}/contact-information`, {
    schema: OrganisationSchema.updateContactInformation,
    config: { name: 'organisation.updateContactInformation', access: commonAccessConfig },
    handler: OrganisationHandler.updateContactInformation,
  });

  fastify.patch(`${PREFIX}/organisation-link`, {
    schema: OrganisationSchema.updateOrganisationLink,
    config: { name: 'organisation.updateOrganisationLink', access: commonAccessConfig },
    handler: OrganisationHandler.updateOrganisationLink,
  });

  // Update status of a organisation entry
  fastify.patch(`${PREFIX}/status`, {
    schema: OrganisationSchema.updateStatus,
    config: { name: 'organisation.updateStatus', access: commonAccessConfig },
    handler: OrganisationHandler.updateStatus,
  });

  // Organisation list for switching
  fastify.get('/organisation-dropdown', {
    schema: OrganisationSchema.organisationDropdown,
    config: { name: 'organisation.organisationDropdown', access: commonAccessConfig },
    handler: OrganisationHandler.organisationDropdown,
  });

  // Options for dropdown in settings
  fastify.get('/options', {
    schema: OrganisationSchema.options,
    config: { name: 'organisation.options', access: commonAccessConfig },
    handler: OrganisationHandler.options,
  });

  // Update currency status
  fastify.patch(`${CURRENCY_SETTING_PREFIX}${PREFIX}/status`, {
    schema: CurrencySchema.updateStatus,
    config: { name: 'organisation.setting.currency.updateStatus', access: commonAccessConfig },
    handler: CurrencyHandler.updateStatus,
  });

  // View a specific currency
  fastify.get(`${CURRENCY_SETTING_PREFIX}${PREFIX}`, {
    schema: CurrencySchema.view,
    config: { name: 'organisation.setting.currency.view', access: commonAccessConfig },
    handler: CurrencyHandler.view,
  });

  // List all currencies
  fastify.get(`${CURRENCY_SETTING_PREFIX}`, {
    schema: CurrencySchema.index,
    config: { name: 'organisation.setting.currency.index', access: commonAccessConfig },
    handler: CurrencyHandler.index,
  });

  // Get organisation logo and favicon
  fastify.get(`${PREFIX}/themes`, {
    schema: OrganisationSchema.getThemes,
    config: { name: 'organisation.getThemes', access: commonAccessConfig },
    handler: OrganisationHandler.getThemes,
  });

  // Update organisation logo and favicon
  fastify.put(`${PREFIX}/themes`, {
    schema: OrganisationSchema.updateThemes,
    config: { name: 'organisation.updateThemes', access: commonAccessConfig },
    handler: OrganisationHandler.updateThemes,
  });
};

export default OrganisationRoute;
