import { CoreConstant } from '#src/modules/core/constants/index.js';
import { OrganisationService } from '#src/modules/organisation/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { ORGANISATION },
  MODULE_METHODS: {
    INDEX,
    VIEW,
    CREATE,
    UPDATE,
    UPDATE_STATUS,
    OPTION,
    UPDATE_THEMES,
    VIEW_THEMES,
  },
} = CoreConstant;

const MODULE = ORGANISATION;

/**
 * Handles the retrieval of organisation data with pagination.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the organisation data and pagination details.
 */
export const index = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => OrganisationService.index(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Retrieves a specific organisation entry by ID.
 *
 * @param {Object} request - The request object containing the ID parameter.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the organisation entry.
 */
export const view = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => OrganisationService.view(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Handles the creation of a new organisation.
 *
 * @param {Object} request - The request object containing the new organisation data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the newly created organisation.
 */
export const create = async (request, reply) => {
  return handleServiceResponse({
    request,
    reply,
    serviceFn: OrganisationService.create,
    module: MODULE,
    method: CREATE,
  });
};

/**
 * Updates the basic information of an organisation.
 *
 * @param {Object} request - The request object containing the ID parameter and basic information data (description, currencies, logos, etc.).
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated organisation with basic information.
 */
export const updateBasicInformation = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: OrganisationService.updateBasicInformation,
    module: MODULE,
    method: UPDATE,
  });

/**
 * Updates the contact information of an organisation.
 *
 * @param {Object} request - The request object containing the ID parameter and contact information data (phone, email, address).
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated organisation with contact information.
 */
export const updateContactInformation = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: OrganisationService.updateContactInformation,
    module: MODULE,
    method: UPDATE,
  });

/**
 * Updates the email domain link of an organisation.
 *
 * @param {Object} request - The request object containing the ID parameter and email domain data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated organisation with email domain link.
 */
export const updateOrganisationLink = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: OrganisationService.updateOrganisationLink,
    module: MODULE,
    method: UPDATE,
  });

/**
 * Updates the status of a organisation entry.
 *
 * @param {Object} request - The request object containing the ID parameter and the updated status.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated organisation entry.
 */
export const updateStatus = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: OrganisationService.updateStatus,
    module: MODULE,
    method: UPDATE_STATUS,
  });

/**
 * Retrieves a dropdown list of entities.
 *
 * @param {Object} request - The request object containing query parameters (e.g., entityId).
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the list of entities for dropdown.
 */
export const organisationDropdown = async (request, reply) => {
  return handleServiceResponse({
    request,
    reply,
    serviceFn: OrganisationService.organisationDropdown,
    module: MODULE,
    method: OPTION,
  });
};

/**
 * Retrieves a dropdown options of entities setting.
 *
 * @param {Object} request - The request object containing query parameters (e.g., entityId).
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the list of entities' settings for dropdown.
 */
export const options = async (request, reply) => {
  return handleServiceResponse({
    request,
    reply,
    serviceFn: OrganisationService.options,
    module: MODULE,
    method: OPTION,
  });
};

/**
 * Retrieves theme settings (logos and favicon) for an organisation.
 *
 * @param {Object} request - The request object containing the organisation ID parameter.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the organisation's theme data
 *                           (logoDesktop, logoMobile, favicon with their file paths).
 */
export const getThemes = async (request, reply) => {
  return handleServiceResponse({
    request,
    reply,
    serviceFn: OrganisationService.getThemes,
    module: MODULE,
    method: VIEW_THEMES,
  });
};

/**
 * Updates the theme settings (logos and favicon) for an organisation.
 *
 * @param {Object} request - The request object containing the organisation ID and theme data
 *                          (logoDesktop, logoMobile, favicon files or URLs).
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the updated organisation's theme settings
 *                           with status information and file paths.
 */
export const updateThemes = async (request, reply) => {
  return handleServiceResponse({
    request,
    reply,
    serviceFn: OrganisationService.updateThemes,
    module: MODULE,
    method: UPDATE_THEMES,
  });
};
