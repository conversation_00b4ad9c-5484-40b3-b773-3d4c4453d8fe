import { CoreConstant } from '#src/modules/core/constants/index.js';
import { LOCALISATION_CATEGORIES } from '#src/modules/setting/constants/localisation.constant.js';
import { LocalisationService } from '#src/modules/setting/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';
const { CURRENCY } = LOCALISATION_CATEGORIES;

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { ORGANISATION },
  MODULE_METHODS: { INDEX, VIEW, UPDATE_STATUS },
} = CoreConstant;

const MODULE = ORGANISATION;

/**
 * Handles the index operation for currency settings.
 * Fetches data from cache if available, otherwise calls the CurrencyService.index method.
 *
 * @param {Object} request - The Hapi request object.
 * @param {Object} reply - The Hapi reply interface.
 *
 * @returns {Promise} - A promise that resolves to the response data.
 */
export const index = async (request, reply) => {
  request.query = {
    ...request.query,
    'filter_localisation.category_eq': CURRENCY,
  };
  const cacheKey = generateCacheKey(`${MODULE}_setting_currency_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => LocalisationService.index(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Handles the view operation for currency settings.
 * Fetches data from cache if available, otherwise calls the CurrencyService.view method.
 *
 * @param {Object} request - The Hapi request object containing request details.
 * @param {Object} reply - The Hapi reply interface used to send responses.
 *
 * @returns {Promise} - A promise that resolves to the response data.
 */
export const view = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_setting_currency_${VIEW}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => LocalisationService.view(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Handles the update status operation for currency settings.
 * Directly calls the CurrencyService.updateStatus method to update the status of a currency setting.
 *
 * @param {Object} request - The Hapi request object containing request details.
 * @param {Object} reply - The Hapi reply interface used to send responses.
 *
 * @returns {Promise} - A promise that resolves to the response data.
 */
export const updateStatus = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: LocalisationService.updateStatus,
    module: MODULE,
    method: UPDATE_STATUS,
  });
