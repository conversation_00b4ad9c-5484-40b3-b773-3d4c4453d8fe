import crypto from 'crypto';

import { CoreConstant, EntityConstant, LinkConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import {
  AddressRepository,
  EntityRepository,
  LinkRepository,
} from '#src/modules/core/repository/index.js';
import {
  LocalisationValidation,
  OrganisationValidation,
} from '#src/modules/core/validations/index.js';
import { MediaAssetRepository } from '#src/modules/media/repository/index.js';
import { OrganisationError } from '#src/modules/organisation/errors/index.js';
import { LOCALISATION_CATEGORIES } from '#src/modules/setting/constants/localisation.constant.js';
import { CustomLocalisationRepository } from '#src/modules/setting/repository/index.js';
import { LocalisationService } from '#src/modules/setting/services/index.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

const { CURRENCY, LANGUAGE, REGION } = LOCALISATION_CATEGORIES;

/**
 * Retrieves all organisation entries for a given entity.
 * @param {Object} request - The request object containing organisation information.
 * @returns {Promise<Object>} A promise that resolves to the list of organisation entries.
 */
export const index = async (request) => {
  const { entity } = request;
  const query = {
    ...request.query,
    filter_parentId_eq: entity.id,
  };

  const result = await EntityRepository.findAll(request.server, query);

  if (result.rows) {
    result.rows = result.rows.map((org) => {
      const orgData = org.toJSON ? org.toJSON() : org;

      return {
        ...orgData,
        currencies: orgData.custom_localisations
          ? orgData.custom_localisations.map((cl) => ({
              name: cl.localisation?.name,
              code: cl.localisation?.code,
            }))
          : [],
      };
    });
  }

  return result;
};

/**
 * Retrieves a specific organisation entry by ID.
 * @param {Object} request - The request object containing params, body, and organisation information.
 * @returns {Promise<Object>} A promise that resolves to the organisation entry.
 */
export const view = async (request) => {
  const { id } = request.params;
  const { entity, server } = request;

  const query = {
    filter_id_eq: id,
    filter_parentId_eq: entity.id,
  };

  const organisationRow = await EntityRepository.findById(server, query);

  if (!organisationRow) {
    throw CoreError.dataNotFound({
      data: 'common.label.organisation',
      attribute: 'ID',
      value: id,
    });
  }

  const fullOrganisationInfo = {
    ...organisationRow.toJSON(),
    address: organisationRow.addresses ? organisationRow.addresses.toJSON() : null,
    emailDomain:
      organisationRow.links && organisationRow.links.length > 0
        ? organisationRow.links[0].url
        : null,
    currencies: organisationRow.custom_localisations
      ? organisationRow.custom_localisations.map((cl) => ({
          name: cl.localisation.name,
          code: cl.localisation.code,
        }))
      : [],
  };

  return fullOrganisationInfo;
};

/**
 * Creates a new organisation entry.
 * @param {Object} request - The request object containing body and organisation information.
 * @returns {Promise<Object>} A promise that resolves to the newly created organisation entry.
 * @throws {Error} If there's an error during the creation process.
 */
export const create = async (request) => {
  const { body, entity, server } = request;

  const { prefix, code, emailDomain, address, currencyIds, language, region, ...organisationData } =
    body;

  await OrganisationValidation.validateOrganisationFields(server, {
    prefix,
    code,
  });

  await LocalisationValidation.validateLocalisation(server, currencyIds, CURRENCY);
  await LocalisationValidation.validateLocalisation(server, language, LANGUAGE);
  await LocalisationValidation.validateLocalisation(server, region, REGION);

  const accessId = generateAccessId();

  const newOrganisationData = {
    ...organisationData,
    parentId: request.entity.id,
    hierarchy: CoreConstant.HIERARCHY.ORGANISATION,
    prefix: prefix,
    code: code,
    accessId: accessId,
    status: CoreConstant.COMMON_STATUSES.ACTIVE,
  };
  return withTransaction(server, {}, async (transaction) => {
    const newOrganisation = await EntityRepository.create(server, entity.id, newOrganisationData, {
      transaction,
      authInfoId: request.authInfo.id,
    });

    if (!newOrganisation) {
      throw OrganisationError.organisationCreationFailed();
    }

    if (emailDomain) {
      await LinkRepository.create(
        server,
        {
          entity_id: newOrganisation.id,
          type: LinkConstant.TYPE.EMAIL_DOMAIN,
          url: emailDomain,
        },
        { transaction, authInfoId: request.authInfo.id },
      );
    }

    if (address) {
      await AddressRepository.create(
        server,
        {
          entity_id: newOrganisation.id,
          ...address,
        },
        { transaction, authInfoId: request.authInfo.id },
      );
    }
    const localisationIds = [...currencyIds, language, region].filter(Boolean);

    for (const parentId of localisationIds) {
      await CustomLocalisationRepository.create(
        server,
        {
          entityId: newOrganisation.id,
          parentId,
          status: CoreConstant.COMMON_STATUSES.ACTIVE,
        },
        { transaction, authInfoId: request.authInfo.id },
      );
    }

    return newOrganisation;
  });
};

/**
 * Updates basic information of an organisation
 * @param {Object} request - The request object containing params and body
 * @returns {Promise<Object>} A promise that resolves to the updated organisation
 */
export const updateBasicInformation = async (request) => {
  const { id } = request.params;
  const { body, server, entity } = request;
  const { currencyIds, description, version, logoDesktop, logoMobile, favicon } = body;

  await LocalisationValidation.validateLocalisation(server, currencyIds, CURRENCY);
  const query = {
    filter_id_eq: id,
    filter_parentId_eq: entity.id,
  };

  const organisationRow = await EntityRepository.findById(server, query);

  if (!organisationRow) {
    throw CoreError.dataNotFound({
      data: 'common.label.organisation',
      attribute: 'ID',
      value: id,
    });
  }

  return withTransaction(server, {}, async (transaction) => {
    const updatedData = {
      description,
      updated_by: request.authInfo.id,
      version,
      logoDesktop,
      logoMobile,
      favicon,
    };

    const updatedOrganisation = await EntityRepository.update(organisationRow, updatedData, {
      transaction,
      authInfoId: request.authInfo.id,
    });

    if (!updatedOrganisation) {
      throw OrganisationError.organisationUpdateFailed();
    }

    await updateCurrencyLocalizations(server, id, currencyIds, request.authInfo, transaction);

    return updatedOrganisation;
  });
};

/**
 * Updates contact information of an organisation
 * @param {Object} request - The request object containing params and body
 * @returns {Promise<Object>} A promise that resolves to the updated organisation
 */
export const updateContactInformation = async (request) => {
  const { id } = request.params;
  const { body, server, entity } = request;
  const { phone, email, address, version } = body;

  const query = {
    filter_id_eq: id,
    filter_parentId_eq: entity.id,
  };

  const organisationRow = await EntityRepository.findById(server, query);

  if (!organisationRow) {
    throw CoreError.dataNotFound({
      data: 'common.label.organisation',
      attribute: 'ID',
      value: id,
    });
  }

  return withTransaction(server, {}, async (transaction) => {
    const updatedData = {
      phone,
      email,
      version,
      updated_by: request.authInfo.id,
    };

    const updatedOrganisation = await EntityRepository.update(organisationRow, updatedData, {
      transaction,
      authInfoId: request.authInfo.id,
    });

    if (!updatedOrganisation) {
      throw OrganisationError.organisationUpdateFailed();
    }

    if (address) {
      await AddressRepository.findOrCreate(
        server,
        {
          where: {
            entity_id: id,
          },
          defaults: {
            ...address,
          },
        },
        { transaction, authInfoId: request.authInfo.id },
      ).then(([addressRecord, created]) => {
        if (!created) {
          return addressRecord.update(
            {
              ...address,
            },
            { transaction, authInfoId: request.authInfo.id },
          );
        }
      });
    }

    return updatedOrganisation;
  });
};

/**
 * Updates organisation link (email domain)
 * @param {Object} request - The request object containing params and body
 * @returns {Promise<Object>} A promise that resolves to the updated organisation
 */
export const updateOrganisationLink = async (request) => {
  const { id } = request.params;
  const { body, server, entity } = request;
  const { emailDomain } = body;

  const query = {
    filter_id_eq: id,
    filter_parentId_eq: entity.id,
  };

  const organisationRow = await EntityRepository.findById(server, query);

  if (!organisationRow) {
    throw CoreError.dataNotFound({
      data: 'common.label.organisation',
      attribute: 'ID',
      value: id,
    });
  }

  return withTransaction(server, {}, async (transaction) => {
    if (emailDomain !== undefined) {
      await LinkRepository.findOrCreate(
        server,
        {
          where: {
            entity_id: id,
            type: LinkConstant.TYPE.EMAIL_DOMAIN,
          },
          defaults: {
            url: emailDomain,
          },
        },
        { transaction, authInfoId: request.authInfo.id },
      ).then(([link, created]) => {
        if (!created) {
          return link.update(
            {
              url: emailDomain,
            },
            { transaction, authInfoId: request.authInfo.id },
          );
        }
      });
    }

    return organisationRow;
  });
};

/**
 * Updates the status of a organisation entry.
 * @param {Object} request - The request object containing params, body, and entity information.
 * @returns {Promise<Object>} A promise that resolves to the updated organisation entry.
 * @throws {LocalisationError} If the organisation is in use by child entities when trying to deactivate.
 */
export const updateStatus = async (request) => {
  const { body, server, params } = request;
  const { id } = params;
  const { status } = body;
  const query = {
    filter_id_eq: id,
  };

  const organisationRow = await EntityRepository.findById(server, query);
  if (!organisationRow) {
    throw CoreError.dataNotFound({
      data: 'common.label.organisation',
      attribute: 'ID',
      value: id,
    });
  }

  return await EntityRepository.update(organisationRow, { status }, request.user);
};

/**
 * Retrieves a list of entities for dropdown purposes.
 * @param {Object} request - The request object containing query parameters and server information.
 * @returns {Promise<Array<Object>>} A promise that resolves to an array of entity objects.
 *                                   Each object contains 'code', 'name', and 'accessId' properties.
 * @throws {Error} Throws an error if there's an issue with the database query or processing.
 */
export const organisationDropdown = async (request) => {
  const allOrganisation = await index(request);
  return allOrganisation.rows
    .map(({ code, name, accessId }) => ({ code, name, accessId }))
    .sort((a, b) => a.name.localeCompare(b.name));
};

/**
 * Retrieves a list of entities' settings for dropdown purposes.
 * @param {Object} request - The request object containing query parameters and server information.
 * @throws {Error} Throws an error if there's an issue with the database query or processing.
 */
export const options = async (request) => {
  const localisationPromises = [LANGUAGE, REGION, CURRENCY].map(async (localisation) => {
    const localisationRequest = {
      ...request,
      query: { ...request.query, 'filter_localisation.category_eq': localisation },
      server: request.server,
    };
    return {
      category: localisation,
      options: await LocalisationService.generateDropdown(localisationRequest, true),
    };
  });

  const localisationResults = await Promise.all(localisationPromises);
  const localisations = localisationResults.reduce((acc, { category, options }) => {
    acc[category] = options;
    return acc;
  }, {});

  return {
    ...localisations,
  };
};

/**
 * Generates an access ID based on the pattern {10-99}{unix timestamp}
 * @returns {string} The generated access ID
 */
const generateAccessId = () => {
  const timestamp = Math.floor(Date.now() / 1000);
  const randomBytes = crypto.randomBytes(2);
  const prefix = (randomBytes.readUInt16BE(0) % 90) + 10;
  return `${prefix}${timestamp}`;
};

/**
 * Updates the status of currency-related custom localizations for an entity
 * @param {Object} server - The server instance
 * @param {string} entityId - The entity ID
 * @param {Array} currencyIds - Array of currency IDs to set as active
 * @param {Object} authInfo - Authentication information for tracking who made the changes
 * @param {Object} transaction - The transaction object
 * @returns {Promise<void>}
 */
const updateCurrencyLocalizations = async (
  server,
  entityId,
  currencyIds,
  authInfo,
  transaction,
) => {
  const currencyCustomLocalisations = await CustomLocalisationRepository.findAll(server, {
    where: { entityId },
    include: [
      {
        model: server.psql.Localisation,
        as: 'localisation',
        where: { category: CURRENCY },
        required: true,
      },
    ],
    transaction,
  });

  if (currencyCustomLocalisations.length > 0) {
    const currencyCustomLocalisationIds = currencyCustomLocalisations.map((cl) => cl.id);

    await CustomLocalisationRepository.update(
      server,
      {
        status: CoreConstant.COMMON_STATUSES.INACTIVE,
        updated_by: authInfo.id,
      },
      {
        id: { [server.psql.connection.Sequelize.Op.in]: currencyCustomLocalisationIds },
      },
      { transaction },
    );
  }

  for (const currencyId of currencyIds) {
    await CustomLocalisationRepository.findOrCreate(
      server,
      {
        where: {
          entityId,
          parentId: currencyId,
        },
        defaults: {
          status: CoreConstant.COMMON_STATUSES.ACTIVE,
        },
      },
      { transaction: transaction, authInfoId: authInfo.id },
    ).then(([record, created]) => {
      if (!created) {
        return CustomLocalisationRepository.update(
          server,
          {
            status: CoreConstant.COMMON_STATUSES.ACTIVE,
            updated_by: authInfo.id,
          },
          { id: record.id },
          { transaction },
        );
      }
    });
  }
};

/**
 * Gets the current theme settings for an organization
 * @param {Object} request - The request object containing params and server information
 * @returns {Promise<Object>} A promise that resolves to the theme settings with just the file paths
 * @throws {OrganisationError} If the organization is not found
 */
export const getThemes = async (request) => {
  const { id } = request.params;
  const { server } = request;

  const query = {
    filter_id_eq: id,
  };

  const organisationRow = await EntityRepository.findById(server, query);

  if (!organisationRow) {
    throw CoreError.dataNotFound({
      data: 'common.label.organisation',
      attribute: 'ID',
      value: id,
    });
  }

  const themeData = {
    logoDesktop: null,
    logoMobile: null,
    favicon: null,
  };

  for (const key of Object.values(EntityConstant.IMAGE_TYPE)) {
    if (organisationRow[key]) {
      const mediaAsset = await MediaAssetRepository.findByPk(server, organisationRow[key]);
      if (mediaAsset) {
        themeData[key] = mediaAsset.filePath;
      }
    }
  }

  return themeData;
};

/**
 * Updates theme assets (logo and favicon) for an organization
 *
 * @param {Object} request - The request object
 * @returns {Promise<Object>} A promise that resolves to the updated organization with theme URLs
 * @throws {OrganisationError} If the organization is not found or update fails
 */
export const updateThemes = async (request) => {
  const { id } = request.params;
  const { body, server } = request;
  const { logoDesktop, logoMobile, favicon } = body;

  const query = {
    filter_id_eq: id,
  };

  const organisationRow = await EntityRepository.findById(server, query);

  if (!organisationRow) {
    throw CoreError.dataNotFound({
      data: 'common.label.organisation',
      attribute: 'ID',
      value: id,
    });
  }

  const themeAssets = {
    logoDesktop,
    logoMobile,
    favicon,
  };

  const updatedOrganisation = await EntityRepository.update(organisationRow, themeAssets, {
    authInfoId: request.authInfo.id,
  });

  if (!updatedOrganisation) {
    throw OrganisationError.organisationUpdateFailed();
  }

  return themeAssets;
};
