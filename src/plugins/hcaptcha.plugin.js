import fp from 'fastify-plugin';
import { verify } from 'hcaptcha';

import { CoreConstant } from '#src/modules/core/constants/index.js';

/**
 * Initializes and configures the hCaptcha plugin for Fastify.
 * This plugin provides hCaptcha verification functionality to protect routes from bots.
 *
 * @async
 * @param {Object} fastify - The Fastify instance to which the plugin is being added.
 * @param {Object} options - Configuration options for the plugin (currently unused).
 * @returns {void}
 */
const hcaptchaPlugin = async (fastify, options) => {
  // To be get from app center
  const hcaptchaSecret = '0x0000000000000000000000000000000000000000';

  fastify.decorate('hcaptcha', async (request, reply) => {
    request.riskLevel = CoreConstant.HCAPTCHA_RISK_LEVELS.HIGH;

    try {
      const token = request.body?.['h-captcha-response'];

      if (!token) {
        fastify.log.warn('hCaptcha token is missing, proceeding with high risk assessment');
        return;
      }

      const data = await verify(hcaptchaSecret, token, request.ip);

      if (data.success) {
        request.riskLevel = CoreConstant.HCAPTCHA_RISK_LEVELS.LOW;
      }
    } catch (error) {
      fastify.log.error(`hCaptcha verification error: ${error.message}`);
    }
  });
};

export default fp(hcaptchaPlugin, {
  name: 'hcaptcha',
  dependencies: ['@fastify/env'],
});
