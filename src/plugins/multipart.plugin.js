import fastifyMultipart from '@fastify/multipart';
import fp from 'fastify-plugin';

import ajv from '#src/utils/validation.util.js'; // Import the configured Ajv instance

/**
 * Registers the file upload and validation plugin with Fastify.
 *
 * @param {object} fastify - The Fastify instance.
 * @param {object} opts - Plugin options (optional).
 */
export default fp(async (fastify, opts) => {
  // File upload setup with Fastify Multipart
  fastify.register(fastifyMultipart, {
    attachFieldsToBody: true,
    limits: {
      fileSize: 5 * 1024 * 1024, // 5 MB file size limit
    },
    ...opts, // Allow overriding options
  });

  // Set the Ajv instance as the schema compiler
  fastify.setValidatorCompiler(({ schema }) => ajv.compile(schema));
});
