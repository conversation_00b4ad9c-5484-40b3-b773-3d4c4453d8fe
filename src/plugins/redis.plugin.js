import { fastifyRedis } from '@fastify/redis';
import fp from 'fastify-plugin';

export default fp(async (fastify) => {
  try {
    // keep existing default client (db: 2) on fastify.redis
    await fastify.register(fastifyRedis, {
      host: fastify.config.REDIS_HOST,
      port: fastify.config.REDIS_PORT,
      db: 2, // existing cache DB
    });

    // add a SECOND client on fastify.redis.db3
    await fastify.register(fastifyRedis, {
      host: fastify.config.REDIS_HOST,
      port: fastify.config.REDIS_PORT,
      db: 3, // the DB you want
      namespace: 'db3', // exposes fastify.redis.db3
    });

    fastify.log.info('Redis connections established (default=db2, db3 namespace)');
  } catch (error) {
    fastify.log.error(error, 'Failed to initialize Redis plugin:');
    throw error;
  }
});
