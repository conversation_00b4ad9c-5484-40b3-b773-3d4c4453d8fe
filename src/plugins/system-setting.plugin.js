import fp from 'fastify-plugin';

import { LocalisationRepository } from '#src/modules/setting/repository/index.js';

/**
 * Each loader returns a partial settings object.
 * Throw to fail the whole load (start or reload).
 * @type {Array<(fastify: FastifyInstance) => Promise<Object>>}
 */
const loaders = [
  async (fastify) => {
    const baseCurrency = await LocalisationRepository.findBaseCurrency(fastify);
    if (!baseCurrency) {
      throw new Error('Base currency not found');
    }
    return { baseCurrency };
  },
  // Add more loaders here when needed.
];

/**
 * Build all settings by running loaders.
 */
const loadAll = async (fastify) => {
  const settings = {};
  for (const loader of loaders) {
    const partial = await loader(fastify);
    Object.assign(settings, partial);
  }
  return settings;
};

const systemSettingPlugin = async (fastify) => {
  let cache = {};
  let inFLight = null; // prevent concurrent reloads

  const initialLoad = async () => {
    cache = await loadAll(fastify);
    fastify.log.info(`System settings initialised: ${Object.keys(cache).join(', ') || '(none)'}`);
  };

  const reload = async () => {
    if (inFLight) {
      return inFLight;
    } // reuse inFLight promise
    inFLight = (async () => {
      const next = await loadAll(fastify);
      cache = next; // atomic swap
      fastify.log.info(`System settings reloaded: ${Object.keys(cache).join(', ') || '(none)'}`);
    })();
    try {
      await inFLight;
    } finally {
      inFLight = null;
    }
  };

  await initialLoad();

  fastify.decorate('systemSetting', {
    get: () => cache,
    getValue: (key) => cache?.[key],
    keys: () => Object.keys(cache),
    reload,
  });
};

export default fp(systemSettingPlugin, {
  name: 'systemSetting',
  dependencies: ['postgres', 'mongo'],
});
