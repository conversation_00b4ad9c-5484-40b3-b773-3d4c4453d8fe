import Typesense from 'typesense';

import { DatabaseFactory } from '#src/factories/database.factory.js';

export class TypesensePlugin extends DatabaseFactory {
  async connect() {
    const { TYPESENSE_API_KEY, TYPESENSE_HOST, TYPESENSE_PORT, TYPESENSE_PROTOCOL } =
      this.fastify.config;

    this.typesense = new Typesense.Client({
      nodes: [
        {
          host: TYPESENSE_HOST,
          port: TYPESENSE_PORT,
          protocol: TYPESENSE_PROTOCOL,
        },
      ],
      apiKey: TYPESENSE_API_KEY,
      connectionTimeoutSeconds: 2,
    });
    await this.typesense.health.retrieve();
    this.fastify.log.info('Typesense connection established');
  }

  decorate() {
    this.fastify.decorate('typesense', this.typesense);
  }
}

export default TypesensePlugin.createPlugin('typesense', false);
