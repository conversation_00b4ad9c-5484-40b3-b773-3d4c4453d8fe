import mongoose from 'mongoose';

import { DatabaseFactory } from '#src/factories/database.factory.js';

export class MongoosePlugin extends DatabaseFactory {
  async connect() {
    const { MONGO_ROOT_USERNAME, MONGO_ROOT_PASSWORD, MONGO_HOST, MONGO_PORT, MONGO_DB } =
      this.fastify.config;
    this.uri = `mongodb://${MONGO_ROOT_USERNAME}:${MONGO_ROOT_PASSWORD}@${MONGO_HOST}:${MONGO_PORT}/${MONGO_DB}`;
    await mongoose.connect(this.uri, { autoIndex: false });
    this.fastify.log.info('MongoDB connection established');
  }

  decorate() {
    this.fastify.decorate('mongo', {
      connection: mongoose.connection,
    });
  }
}

export default MongoosePlugin.createPlugin('mongo', true);
