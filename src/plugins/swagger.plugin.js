import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import fp from 'fastify-plugin';

import { swaggerConfig, swaggerUiConfig } from '#config/swagger/swagger.config.js';

export default fp(async (fastify, opts) => {
  await fastify.register(swagger, swaggerConfig);
  await fastify.register(swaggerUi, swaggerUiConfig);
  fastify.log.info('Swagger plugin registered successfully');
});
