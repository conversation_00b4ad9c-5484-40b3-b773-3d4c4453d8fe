import createDOMPurify from 'dompurify';
import fp from 'fastify-plugin';
import { J<PERSON><PERSON> } from 'jsdom';

import sanitiseConfig from '#config/sanitiser.config.js';

export default fp(
  async (fastify) => {
    const window = new JSDOM('').window;
    const DOMPurify = createDOMPurify(window);

    const defaultConfig = sanitiseConfig;

    const sanitiseHtml = (html, config = {}) => {
      if (html == null) {
        return html;
      }

      return DOMPurify.sanitize(String(html), config);
    };

    const sanitiseData = (data, ignoreFields = [], customConfig = {}) => {
      const fieldsToIgnore = new Set([...sanitiseConfig.ignoreFields, ...ignoreFields]);
      const mergedConfig = { ...defaultConfig };

      ['ALLOWED_TAGS', 'ALLOWED_ATTR'].forEach((key) => {
        if (customConfig[key]) {
          mergedConfig[key] = customConfig[key];
        } else if (customConfig[`ADD_${key}`]) {
          mergedConfig[key] = [...new Set([...defaultConfig[key], ...customConfig[`ADD_${key}`]])];
        }
      });

      if (!data || typeof data !== 'object') {
        return typeof data === 'string' ? sanitiseHtml(data, mergedConfig) : data;
      }

      if (Array.isArray(data)) {
        return data.map((item) => sanitiseData(item, ignoreFields, customConfig));
      }

      return Object.entries(data).reduce((acc, [key, value]) => {
        if (fieldsToIgnore.has(key)) {
          acc[key] = value;
        } else if (typeof value === 'string') {
          acc[key] = sanitiseHtml(value, mergedConfig);
        } else if (value && typeof value === 'object') {
          acc[key] = sanitiseData(value, ignoreFields, customConfig);
        } else {
          acc[key] = value;
        }
        return acc;
      }, {});
    };

    fastify.decorate('sanitiseHtml', sanitiseHtml);
    fastify.decorate('sanitiseData', sanitiseData);
  },
  {
    name: 'sanitiser',
  },
);
