import fastifyEnv from '@fastify/env';
import fp from 'fastify-plugin';

import envSchema from '#src/schemas/env.schema.js';

const configOptions = {
  confKey: 'config', // Access environment variables via fastify.config
  data: process.env,
  dotenv: true,
  schema: envSchema,
};

// A Fastify plugin to manage and validate environment variables.
const envPlugin = async (fastify, opts) => {
  // Register the @fastify/env plugin
  await fastify.register(fastifyEnv, configOptions);
};

export default fp(envPlugin, { name: 'env' });
