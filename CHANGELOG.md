## Changelog

All notable changes to this project will be documented in this file.

### v0.0.24

- implement google login
- recursive updateDescendantPolicies blow stack
- implement username password login
- navigation building repeated filters and recursion
- fix N+1 DB round trips in role validation js
- BE Set up factory girl for generating test data factories
- implement root level system settings for authentication control
- setup hcaptcha integration for authentication

#### New Features

-  implement google login   [`8bb9176`](https://bitbucket.org/aiodintech/wlsapi/commits/8bb917683955ea6202b3af9cbd6df1ca0ec58cbc)
-  implement google login   [`66654a6`](https://bitbucket.org/aiodintech/wlsapi/commits/66654a6a5ecd248750347e77aa356ce6d4855f13)
-  enhance department and entity factories with policy settings and hierarchy management   [`2de0923`](https://bitbucket.org/aiodintech/wlsapi/commits/2de092356f3eea62c1632b46ae54d8e1134933b4)
-  implement google login   [`6139553`](https://bitbucket.org/aiodintech/wlsapi/commits/6139553a344c2259d93c0d2f1332e28bf8e015db)
-  add link factory for generating Link records with entity and url handling   [`a32e884`](https://bitbucket.org/aiodintech/wlsapi/commits/a32e88434e33cfc3c54afdad3af4041ea14ff5ae)
-  initial setup for Sequelize factories   [`f885e6a`](https://bitbucket.org/aiodintech/wlsapi/commits/f885e6ada9c13996f1d6331cc54fa4d2dccbbe4a)
-  add department factory and wire up associations   [`f0d3b54`](https://bitbucket.org/aiodintech/wlsapi/commits/f0d3b546f9812f87d592d183622da3c557d3e4b1)
-  allow dynamic attributes in run-factory and department factory   [`85ebbe9`](https://bitbucket.org/aiodintech/wlsapi/commits/85ebbe965bdd6d382b4f01b7af7ca3a4165e0282)
-  add entity factory for generating test data   [`dd0afe4`](https://bitbucket.org/aiodintech/wlsapi/commits/dd0afe47bde0a20ae5e60c73995db4232fe9d8a5)
-  implement google login   [`6ad906e`](https://bitbucket.org/aiodintech/wlsapi/commits/6ad906eb6cb2f57d945d68a6d04e4ed7b5c4aed3)
-  implement batching for module retrieval by IDs using lodash   [`a08e120`](https://bitbucket.org/aiodintech/wlsapi/commits/a08e120ebff42b5d2965d251a94351f5459df7f2)
-  add module association to department template on afterCreate hook   [`3484ed3`](https://bitbucket.org/aiodintech/wlsapi/commits/3484ed31c4981b13b4b53c0b15a6cd4e6001672f)
-  add rate limit configuration for authentication endpoints   [`47fe446`](https://bitbucket.org/aiodintech/wlsapi/commits/47fe4465bdf9c95d54d9b5cad47799f78d1de684)
-  setup model associations in loadModels   [`9b65802`](https://bitbucket.org/aiodintech/wlsapi/commits/9b65802c2b4f3515718070a5c9c8d0acf7f8a707)
-  implement google login   [`5995584`](https://bitbucket.org/aiodintech/wlsapi/commits/599558407ee02d64a3dcf2d124714b1b6301572d)
-  enhance department name generation with suffix and length constraint   [`4a3c9b1`](https://bitbucket.org/aiodintech/wlsapi/commits/4a3c9b1ff1410d12751aa52dff813f180c8ddbc4)

#### Bug Fixes

-  ensure department modules preserve hierarchy level in factory   [`3c2ffdd`](https://bitbucket.org/aiodintech/wlsapi/commits/3c2ffdd21d8c6523fe748bff0fd26e695dc514ee)
-  enforce username format by type with regex validation   [`fe4ab49`](https://bitbucket.org/aiodintech/wlsapi/commits/fe4ab49f51cb49513c2e51ee22af8093b1c92c6f)
-  reorganize constants for improved readability   [`690c6f0`](https://bitbucket.org/aiodintech/wlsapi/commits/690c6f0195f30f9b834f6ad2fd27dc404ff5e4c6)
-  add entityId checking for departmentTemplate   [`49bc26c`](https://bitbucket.org/aiodintech/wlsapi/commits/49bc26c7e4c6809fe350628b372edbff7193a35a)
-  correct typo in rate limit description and update cSpell words list   [`ce6fa7d`](https://bitbucket.org/aiodintech/wlsapi/commits/ce6fa7d093400b8caade16914db15fddd792bbd6)
-  correct parameter order in findByIdWithModulePolicies calls   [`acb6e99`](https://bitbucket.org/aiodintech/wlsapi/commits/acb6e997c8ed02fdf7401de26cf57a984ff4e58b)
-  remove duplicate index success message in success-message utility   [`d87c37c`](https://bitbucket.org/aiodintech/wlsapi/commits/d87c37c6eee9424d747625f47b0a663fa481178f)
-  update `AWS CDN URL` for `Language Localisation System Translation`   [`643ca69`](https://bitbucket.org/aiodintech/wlsapi/commits/643ca6955baab1efbf5717118c2f5ba0039c1720)
-  allow underscores in username regex pattern   [`cdba652`](https://bitbucket.org/aiodintech/wlsapi/commits/cdba652fc12d19f23f385fd9271921d0c418789f)
-  add missing "organisation" module name in core constant   [`1db607d`](https://bitbucket.org/aiodintech/wlsapi/commits/1db607da8f25ead11854e9f499db43e16159f0e1)

#### Chores & Housekeeping

-  replace if statement with .then chain for logging   [`22ecce8`](https://bitbucket.org/aiodintech/wlsapi/commits/22ecce8b5ca6d65f0d45e1f8260efe52828fb19e)

#### Documentation Changes

-  fine-tune indentation in README for better readability   [`563e663`](https://bitbucket.org/aiodintech/wlsapi/commits/563e6631cc6810e4a524e787e28b15574729d2ae)
-  add README with usage instructions for factory   [`c18da7c`](https://bitbucket.org/aiodintech/wlsapi/commits/c18da7ca82e8f7ce84be0d95ba0703929cc91efd)
-  fine-tune indentation in README for better readability   [`dd59b9c`](https://bitbucket.org/aiodintech/wlsapi/commits/dd59b9cc577a1a2e34d122bb2a8869ce45c6691a)
-  add side notes for factory usage in readme   [`2bdbdfe`](https://bitbucket.org/aiodintech/wlsapi/commits/2bdbdfe4138e511fe5a55d261b8df166db250d8b)

#### Refactoring and Updates

-  refactor imports for consistency and clarity   [`158a5c9`](https://bitbucket.org/aiodintech/wlsapi/commits/158a5c92a5cebef208755b9b5d80f8cc6ff8fd94)
-  update systemSetting API to use getter methods for improved access and cache management   [`52749f1`](https://bitbucket.org/aiodintech/wlsapi/commits/52749f162049a0a4f8c8a4ae9a54482af78f0cf7)
-  update Redis client usage to include db3 for failed login attempts tracking   [`980b17d`](https://bitbucket.org/aiodintech/wlsapi/commits/980b17d778a46316fbc70bd98d492a10431075b9)
-  add exclude key for universal cache   [`8473951`](https://bitbucket.org/aiodintech/wlsapi/commits/84739510917a1989b9a04684c6fe9070ed2e82cc)
-  autoload models and factories   [`09c11b7`](https://bitbucket.org/aiodintech/wlsapi/commits/09c11b780c602cbcc1077eafe3b9d8de92777458)
-  update TAGS for improved categorization across schemas   [`33b1f8a`](https://bitbucket.org/aiodintech/wlsapi/commits/33b1f8abe56887f5910a7baa47c8a483f7875416)
-  remove currency, region and language module   [`86e98a9`](https://bitbucket.org/aiodintech/wlsapi/commits/86e98a981cef19c40070a4d56ab8fce15cd9955b)
-  move afterBuild logic into withHierarchy   [`c438304`](https://bitbucket.org/aiodintech/wlsapi/commits/c438304a8c922a7a3d2284e3f24b6edc1decaf01)
-  reorganize constants for improved readability   [`690c6f0`](https://bitbucket.org/aiodintech/wlsapi/commits/690c6f0195f30f9b834f6ad2fd27dc404ff5e4c6)
-  add rate limit configuration for authentication endpoints   [`47fe446`](https://bitbucket.org/aiodintech/wlsapi/commits/47fe4465bdf9c95d54d9b5cad47799f78d1de684)

<!-- auto-changelog-above -->

### v0.0.23

- update cache clearing function and add tests
- app center integration
- organisation management
- add seeder for new modules
- batch policy updates instead of many small queries
- user creation bug
- implement entity and role associations in user model and update related services
- personal setting language can be retrieved from localisationmetadata
- comparing same role path in role service js
- enum_entities_hierarchy duplicated
- checkPolicies performance enhancement
- organisation management theme setting
- currency setting
- organisation management crud
- ppp center CRUD

#### New Features

-  add department association to Role and update UserAssociation model   [`222257c`](https://bitbucket.org/aiodintech/wlsapi/commits/222257c6fe0b7248478f04de5b5c23167128b9e0)
-  update app categories to use enum and enhance model associations   [`3f4b142`](https://bitbucket.org/aiodintech/wlsapi/commits/3f4b142c551315c7d9cc17550c00e7c0120cbd7e)
-  merge develop into feature branch   [`faa7cce`](https://bitbucket.org/aiodintech/wlsapi/commits/faa7cce93c069849d96eec107c471026e59e523b)
-  update personal option dropdown, language native name   [`3844aa5`](https://bitbucket.org/aiodintech/wlsapi/commits/3844aa5c6ef43e1b87831a3cf99bf2f0a7febf99)
-  add app service and repository with CRUD operations   [`62c2888`](https://bitbucket.org/aiodintech/wlsapi/commits/62c28881571cfb4800162489f01e9c5e8ada8bcb)
-  add unit tests for various utility functions for app center integration   [`52582cc`](https://bitbucket.org/aiodintech/wlsapi/commits/52582cc655e24b7f88b4a50b2f7a385d5afae4ea)
-  implement option and view method method to retrieve app data   [`fab5f66`](https://bitbucket.org/aiodintech/wlsapi/commits/fab5f66ee7d7675c204c353895be1565945872d1)
-  organisation management theme settings   [`fe8d5b0`](https://bitbucket.org/aiodintech/wlsapi/commits/fe8d5b009a5ccc5c6c21d22f3f5138c32019fefc)
-  organisation management theme settings   [`abe05b1`](https://bitbucket.org/aiodintech/wlsapi/commits/abe05b1f35889eca039970d92856c53815ad4515)
-  implement test strategy for various services   [`82e5343`](https://bitbucket.org/aiodintech/wlsapi/commits/82e53435bd4ff0095b5000a6320bb52261d5e939)
-  add currency management functionality with CRUD operations   [`e1059fa`](https://bitbucket.org/aiodintech/wlsapi/commits/e1059fa2214dea0d3b09a377f256c70567c2f5ed)
-  implement payment system utility and corresponding unit tests   [`5f5780f`](https://bitbucket.org/aiodintech/wlsapi/commits/5f5780f48578de9c9fc7b023ceff536eeb44f3e2)
-  organisation management theme settings   [`dbd4ce9`](https://bitbucket.org/aiodintech/wlsapi/commits/dbd4ce9b4566dc9ee3e343949706e58e252ec487)
-  add option constant and refactor options api   [`3c88379`](https://bitbucket.org/aiodintech/wlsapi/commits/3c88379bf60c19df97b0b74c229c66bb84e18ed5)
-  add unique constraints to entity model fields and indexes   [`0ea66c3`](https://bitbucket.org/aiodintech/wlsapi/commits/0ea66c3cb53b3bc6cc028ff3dd394d235371041e)
-  organisation management theme settings   [`29be5b1`](https://bitbucket.org/aiodintech/wlsapi/commits/29be5b1d34ef842a104c26545aaabed82ac34fe9)
-  add runTest endpoint schema for app testing   [`ec490bb`](https://bitbucket.org/aiodintech/wlsapi/commits/ec490bb2c182120e5314814913bac22b8b32eba0)
-  organisation management   [`11cdc5a`](https://bitbucket.org/aiodintech/wlsapi/commits/11cdc5af34d5879eaa4c0a044c1e4249fa715e8f)
-  organisation management theme settings   [`f534cfe`](https://bitbucket.org/aiodintech/wlsapi/commits/f534cfee256e1b7f1fa7d04fc636c5e8f7d563d3)
-  update `fileSize` attribute type to `INTEGER` and adjust validation   [`6a96ae7`](https://bitbucket.org/aiodintech/wlsapi/commits/6a96ae726765df7e423461026282421e9f3099ae)
-  organisation management theme settings   [`67bab3b`](https://bitbucket.org/aiodintech/wlsapi/commits/67bab3b6b1ef120314b40a1b40999a104eaf6aa7)
-  normalize enum values and enforce URL length validation   [`f0b81f4`](https://bitbucket.org/aiodintech/wlsapi/commits/f0b81f4dc19cddc200de98a6962da69c10780007)
-  optimize media_assets index for improved query performance   [`1416bfd`](https://bitbucket.org/aiodintech/wlsapi/commits/1416bfdc7629bb0bbb42ccd569387f1292c089bd)
-  organisation management theme settings   [`904ef33`](https://bitbucket.org/aiodintech/wlsapi/commits/904ef33f1d356f8ed6f805c5df53534844aa98f4)
-  organisation management theme settings   [`509c826`](https://bitbucket.org/aiodintech/wlsapi/commits/509c826fc42b1b9140882507ecece6b19d1ee770)
-  add email validation to entity model fields   [`342f103`](https://bitbucket.org/aiodintech/wlsapi/commits/342f1036f82128e2903d556164805e35a531f811)

#### Bug Fixes

-  remove unused modules at the moment   [`4e45ee3`](https://bitbucket.org/aiodintech/wlsapi/commits/4e45ee38c2b2e3404fd164df29dd07696bdd3a07)
-  refine module policy seeder position and increase navigationUrl length to 100 characters   [`9f4185f`](https://bitbucket.org/aiodintech/wlsapi/commits/9f4185feff4ff67dcf942aa10ba0974dc5b7eae5)
-  organisation management   [`16f4956`](https://bitbucket.org/aiodintech/wlsapi/commits/16f495668c8f1bed2dead8bec41ddb1fa51248fd)
-  add handling for circular references and redaction in arrays   [`b57c34a`](https://bitbucket.org/aiodintech/wlsapi/commits/b57c34a59e6e4919c2f2a9bc0bd2e0dee3a6dd23)
-  update fetchFromCache to use resolver instead of callback for improved clarity   [`30de711`](https://bitbucket.org/aiodintech/wlsapi/commits/30de7111d0d5a3577852b51ebfef9751fba6b786)
-  update cache key generation for localisation   [`6408f25`](https://bitbucket.org/aiodintech/wlsapi/commits/6408f2540650526e2a050ca03ca89564b2dc7bf1)
-  conditionally persist currency links for merchant hierarchy in user creation   [`c8b77e4`](https://bitbucket.org/aiodintech/wlsapi/commits/c8b77e403196b1238409953f99b45bccf3ba6ff3)
-  correct policy references in role route configurations   [`1544ebb`](https://bitbucket.org/aiodintech/wlsapi/commits/1544ebb1ee7cf56b754ccc599a913470ca80341e)
-  update entity access level to hierarchy in create function   [`ded641a`](https://bitbucket.org/aiodintech/wlsapi/commits/ded641a1f54896993aeca7335bf2a369ec7757df)
-  update import statement for uploadToS3 to use named import   [`9ba0406`](https://bitbucket.org/aiodintech/wlsapi/commits/9ba0406174fd4e05dad3e91f8e8c13dcfa0057eb)
-  update fetchfromcache to remove null argument for improved functionality   [`a9a5a34`](https://bitbucket.org/aiodintech/wlsapi/commits/a9a5a34ee455075bf22d4d6fc6e29f3a90bb4fb5)
-  add missing seed:all script   [`77419c9`](https://bitbucket.org/aiodintech/wlsapi/commits/77419c938a403f02badeac68fee645af64cdacab)
-  relocate user-related migrations and update user service to include hierarchy   [`b942d93`](https://bitbucket.org/aiodintech/wlsapi/commits/b942d93867fd8cc309460d18f2479d95bd90fac9)
-  core constant variable being renamed after merge dev   [`a832a33`](https://bitbucket.org/aiodintech/wlsapi/commits/a832a33c9c1f5720e009e8dcd9510a90c5e19131)
-  reorder AutoLoad registration to load plugins before hooks   [`43dc055`](https://bitbucket.org/aiodintech/wlsapi/commits/43dc0553fef668a8ebf9033648e7592915c7563b)
-  fix generateCacheKey not working correctly if url and query is not match   [`f97a945`](https://bitbucket.org/aiodintech/wlsapi/commits/f97a9457d5285126b97dccfbae0030b1ce41f78b)
-  improve test coverage and refactor app modules   [`c49f98b`](https://bitbucket.org/aiodintech/wlsapi/commits/c49f98b2e2b94b4171999e651799a83140a7f5dd)
-  update model associations and improve service methods for better functionality   [`53d944c`](https://bitbucket.org/aiodintech/wlsapi/commits/53d944c02247498bf54a1517f61aa0b4cf861d3f)
-  remove default UUID generation and update indexes for entity_apps and app_configs migrations   [`95cd09a`](https://bitbucket.org/aiodintech/wlsapi/commits/95cd09a0596ce54e7550dc3f0ac16b36c79f47e6)
-  add belongsTo association for App model in EntityApp and update tests   [`f8d3223`](https://bitbucket.org/aiodintech/wlsapi/commits/f8d32231174d56152c48b8c12e082671c8e3867e)
-  reorder delete route definition for consistency in App API routes   [`a62c8bc`](https://bitbucket.org/aiodintech/wlsapi/commits/a62c8bc41dd946775ee9fbd42f49d61ebeeaaffe)
-  remove unused parameter from App model export function and ensure unique index name consistency   [`7a93841`](https://bitbucket.org/aiodintech/wlsapi/commits/7a938418edf10f812d06aac4f7e9c33d7163dc26)
-  reorder API_PREFIX declaration for consistency in route prefix generation   [`aa64c53`](https://bitbucket.org/aiodintech/wlsapi/commits/aa64c53f364476bcd05a64ef9e1215a9d7cf097e)

#### Documentation Changes

-  update docstrings to include usage example   [`6014e7d`](https://bitbucket.org/aiodintech/wlsapi/commits/6014e7db763fc0c4f69b10aa7eed6c4c0521fb31)
-  update sonarqube quality gate badge   [`5de0061`](https://bitbucket.org/aiodintech/wlsapi/commits/5de006171192cbda4fab6bc63ef17b379308760f)

#### Refactoring and Updates

-  refactor tests to use Axios instead of fetch, update error handling, and improve assertions   [`be9af19`](https://bitbucket.org/aiodintech/wlsapi/commits/be9af19ddfd46d03fbef54d4c61a5be01c3f7386)
-  streamline error handling and response structure across utilities and services   [`40d9092`](https://bitbucket.org/aiodintech/wlsapi/commits/40d9092cb8e1bc03628c61c9475c1288dc47220d)
-  enhance cache utility functions and update key generation logic   [`0e17e9e`](https://bitbucket.org/aiodintech/wlsapi/commits/0e17e9e2833e51ef81447a1f201eece3b0f9b6c4)
-  streamline cache utility functions and update cache key generation logic   [`77f1547`](https://bitbucket.org/aiodintech/wlsapi/commits/77f154784c6f8bb6b84c73dd4ad63774f91b81c3)
-  enhance error handling across utility functions for improved consistency   [`e1f660c`](https://bitbucket.org/aiodintech/wlsapi/commits/e1f660c301c10cde4c20a101d2b89363b9ea138d)
-  improve error handling in axios instance and add unit tests   [`40b9824`](https://bitbucket.org/aiodintech/wlsapi/commits/40b9824734e348932c41a872d866072937b03790)
-  extract user mapping logic into reusable function   [`4861cb1`](https://bitbucket.org/aiodintech/wlsapi/commits/4861cb198a4cede97b25a459147502f6b062ce0c)
-  sort route in asc order   [`61608a1`](https://bitbucket.org/aiodintech/wlsapi/commits/61608a184c9729f939a7b6a9fafed4347d404bfc)
-  enhance role policy checks and unify cache key naming convention   [`9b59d40`](https://bitbucket.org/aiodintech/wlsapi/commits/9b59d40135a31a2df978f2199fecd9b40170aec3)
-  standardize cache key formatting across modules and update media schema tags   [`1310fd8`](https://bitbucket.org/aiodintech/wlsapi/commits/1310fd8b6afcbaff073ed5527c9eb4074d3b671e)
-  update ENUM types and improve constraints in migration files   [`3167612`](https://bitbucket.org/aiodintech/wlsapi/commits/3167612e189ce3ce09679fd55f1219e792587933)
-  refactor currency handling to use localisation service and update routes   [`0c98672`](https://bitbucket.org/aiodintech/wlsapi/commits/0c98672c839e5e6a6ffbf6b2cac3be21d4c48bf2)
-  improve error handling in sendSlackMessage function   [`bc6c714`](https://bitbucket.org/aiodintech/wlsapi/commits/bc6c714f0763e08c0d860361099b38b71c9bee96)
-  improve documentation and formatting for getSecretKey, encrypt, and decrypt functions   [`cc77ade`](https://bitbucket.org/aiodintech/wlsapi/commits/cc77adec25f44aac482ce1880733909566e11e2a)
-  rename test strategy import   [`ded5d2d`](https://bitbucket.org/aiodintech/wlsapi/commits/ded5d2d047feba62572878fd3261df184eb394d1)

#### Changes to Test Assests

-  rename cache util unit test   [`8e8188d`](https://bitbucket.org/aiodintech/wlsapi/commits/8e8188dcda8672b9f4f2b8bac05cd0f3db5e4ba0)
-  add unit test for softDelete method in App Repository   [`d71d31e`](https://bitbucket.org/aiodintech/wlsapi/commits/d71d31e2d44af5dfb2f6f79d596ad30757239396)

<!-- auto-changelog-above -->

### v0.0.22

- implement centralised audit trail to access control
- add missing backend translation
- policy setting repository upsert call signature mismatch in role service js
- add merchant credit limit related migration
- personal setting ceate form component
- user management SSO
- user roles and hierarchies navigation and middleware
- add department seeder to develop branch
- merge organisation and roles seeder & migration files to develop branch
- user management invitation
- user roles and hierarchies crud
- department CRUD
- department template
- implement user management crud
- department module policies

#### New Features

-  organisation management crud   [`e15c634`](https://bitbucket.org/aiodintech/wlsapi/commits/e15c63433d9bbd7090bf4fb5b2024c81c78e7f66)
-  improve test quality   [`2aa8626`](https://bitbucket.org/aiodintech/wlsapi/commits/2aa8626bbcdc3f2be003767206bd59cb8b734efd)
-  remove audit logging from 'updateStatus' function   [`6636b02`](https://bitbucket.org/aiodintech/wlsapi/commits/6636b0220022fde1d70b2aa6a283767161a64335)
-  enhance audit trail utility functions and improve response modification logic   [`a413b6d`](https://bitbucket.org/aiodintech/wlsapi/commits/a413b6df4f056a1c317a590b4d4a9c5c9f1912cc)
-  user roles and hierarchies navigation and middleware   [`d54c5d5`](https://bitbucket.org/aiodintech/wlsapi/commits/d54c5d58f83581146cd44a0fe4e8410934c3d456)
-  user roles and hierarchies navigation and middleware   [`2ddcf1e`](https://bitbucket.org/aiodintech/wlsapi/commits/2ddcf1e37d7d4b91610100687f6de5eabb13c5d3)
-  user roles and hierarchies navigation and middleware   [`cd92b0b`](https://bitbucket.org/aiodintech/wlsapi/commits/cd92b0b5cd2316eb0fbc0080f800b7f0e269a4e1)
-  user roles and hierarchies navigation and middleware   [`2b17cc0`](https://bitbucket.org/aiodintech/wlsapi/commits/2b17cc0b3432fbfd66f3c08336372385d113a69a)
-  user roles and hierarchies navigation and middleware   [`d4bc259`](https://bitbucket.org/aiodintech/wlsapi/commits/d4bc25930178f4c4caeefc0d556576bb9006921f)
-  enhance m_enum to conditionally translate string values and update related tests   [`b606953`](https://bitbucket.org/aiodintech/wlsapi/commits/b60695343dcb6235688c390a228e52a97d26a5a8)
-  add translateParams util for recursive translation of interpolation params   [`b6d8b9d`](https://bitbucket.org/aiodintech/wlsapi/commits/b6d8b9d2ae0b473624d3ab6687dee239b1684baf)
-  refactor 'withAuditLogging' into 'onResponse' hook for access control   [`2ae520e`](https://bitbucket.org/aiodintech/wlsapi/commits/2ae520e8d5acee3950f1a3d9f4cd92827f08607e)
-  update error definitions for improved localization support across multiple modules   [`8976deb`](https://bitbucket.org/aiodintech/wlsapi/commits/8976deba4d0deedb843dc1230dbaf509629989ad)
-  enhance createCustomError to support translation parameters and update related tests   [`c1d6060`](https://bitbucket.org/aiodintech/wlsapi/commits/c1d60607bfbd6da8c68df16e34f14dcd57db1908)
-  update core error definitions and tests for improved localization support   [`46c1c01`](https://bitbucket.org/aiodintech/wlsapi/commits/46c1c019238c768553bd9c37c00ae9da2d3cac48)
-  add clear cache for setting update; standardise setting option res format   [`bd768d7`](https://bitbucket.org/aiodintech/wlsapi/commits/bd768d78e52755e981ad72c1c7f8b3b4c3041ceb)
-  user roles and hierarchies navigation and middleware   [`838534a`](https://bitbucket.org/aiodintech/wlsapi/commits/838534a2ca679c1f86046923898e11f81d22d04a)
-  integrate translation utility into SuccessMessage for improved message localization   [`8e329e7`](https://bitbucket.org/aiodintech/wlsapi/commits/8e329e7bbadc6e7ec7d893dcb82756bc89525663)
-  standardise service return to use  instead of audit   [`4a848e3`](https://bitbucket.org/aiodintech/wlsapi/commits/4a848e38573c2ae4f718024b116496f2adcf0442)
-  refactor code to enhance readability   [`8191b27`](https://bitbucket.org/aiodintech/wlsapi/commits/8191b2771271f32d235aba6721921228da7afd90)
-  refine module processing logic to filter by allowed hierarchies   [`c7896c0`](https://bitbucket.org/aiodintech/wlsapi/commits/c7896c018982ff6c5370b862f7465cb50f3cd145)
-  fix pipeline failure due to test error   [`13da4ac`](https://bitbucket.org/aiodintech/wlsapi/commits/13da4ac6dc84b6c699e02822ddcd9e33ce06701a)
-  user roles and hierarchies navigation and middleware   [`4967b21`](https://bitbucket.org/aiodintech/wlsapi/commits/4967b2105a6d807fe29a3e7d02902f3126a05d1f)
-  refactor code   [`65e22dd`](https://bitbucket.org/aiodintech/wlsapi/commits/65e22ddd1edbde1207e825466389f6a5e9510550)
-  user roles and hierarchies navigation and middleware   [`edb392e`](https://bitbucket.org/aiodintech/wlsapi/commits/edb392eb172b04e9b3eb2431dfb16af5ffc1524a)
-  user roles and hierarchies navigation and middleware   [`c4c9795`](https://bitbucket.org/aiodintech/wlsapi/commits/c4c97956d5cb494d870ccb7f5b522856044feb94)
-  update personal setting language option return code   [`e2019be`](https://bitbucket.org/aiodintech/wlsapi/commits/e2019bed18cb39693c7166a13ca69dac6d45e52b)
-  organisation management crud   [`ec05b3e`](https://bitbucket.org/aiodintech/wlsapi/commits/ec05b3ea0773bcb840f322dbb0f0913ea76a8a74)
-  user roles and hierarchies navigation and middleware   [`6e8f203`](https://bitbucket.org/aiodintech/wlsapi/commits/6e8f203f527ac30172d5e13c4c8deb54da99c1d8)
-  user roles and hierarchies navigation and middleware   [`055dee5`](https://bitbucket.org/aiodintech/wlsapi/commits/055dee5c4400804983c84988948cacb26fa5b60d)
-  refactor code   [`24c61c5`](https://bitbucket.org/aiodintech/wlsapi/commits/24c61c5af126f0517bc7998e58de4a79956ff04c)
-  implement sso onboarding   [`0663006`](https://bitbucket.org/aiodintech/wlsapi/commits/0663006629e7803c0ca07c7ac91fbb0b3cceae23)
-  add department crud routes and handlers   [`afafd24`](https://bitbucket.org/aiodintech/wlsapi/commits/afafd240edbb108ff9e1d48cfffdb1c7c5322a24)
-  handle update unprocessable checking   [`5a546b4`](https://bitbucket.org/aiodintech/wlsapi/commits/5a546b4196b77eac09e82f89a420406f7cbe9aad)
-  add sub account index   [`da42f0f`](https://bitbucket.org/aiodintech/wlsapi/commits/da42f0f004cb86001f0c49ff676aa01edf9f1951)
-  add update unprocessable checking   [`be30b07`](https://bitbucket.org/aiodintech/wlsapi/commits/be30b07175075f4e3fbfb9991197402d0a575aa7)
-  user roles and hierarchies navigation and middleware   [`3a78136`](https://bitbucket.org/aiodintech/wlsapi/commits/3a78136087817fc24f0c8bd412b53d7779fd5ee7)
-  update description is nullable   [`89cf7c8`](https://bitbucket.org/aiodintech/wlsapi/commits/89cf7c898b3a9b1926bd85209121b0eb41944c71)
-  update migration and affected function   [`50a5247`](https://bitbucket.org/aiodintech/wlsapi/commits/50a5247b782c8e50b81ca8df377d3a841e59754c)
-  implement user management external invitation   [`03eddac`](https://bitbucket.org/aiodintech/wlsapi/commits/03eddac106c778c0ebfa5e4cf9cdd33086b6397e)
-  add option API, update params casing, update model remove default value   [`79a44c2`](https://bitbucket.org/aiodintech/wlsapi/commits/79a44c24f4d573fa97c0d91bf43a4b62102aa6aa)
-  separate update API based on sections, update schema, add caching to index and view   [`b6b34c4`](https://bitbucket.org/aiodintech/wlsapi/commits/b6b34c4985fb7c245c56da8b75fa5442a56adab4)
-  user roles and hierarchies navigation and middleware   [`9eb6a96`](https://bitbucket.org/aiodintech/wlsapi/commits/9eb6a96b1216633b427671c71672d68a4aeff956)
-  add getModulePolicyOption API   [`d833176`](https://bitbucket.org/aiodintech/wlsapi/commits/d833176e4f97a0eab56656ca58ca0f1fac82a96a)
-  update user repository to use eager loading and format return response   [`6e5c7f1`](https://bitbucket.org/aiodintech/wlsapi/commits/6e5c7f1bae981ed12c1f7ccddbfec5c9d4b6505d)
-  department template API operations   [`1e68433`](https://bitbucket.org/aiodintech/wlsapi/commits/1e6843377eb1e7c3d4a37289f6284d3ec1ed2660)
-  department template apis - index, view, create   [`0d9415e`](https://bitbucket.org/aiodintech/wlsapi/commits/0d9415ef491d996b52d9c1e411229f0f8c29efc6)
-  department template api - update module policy   [`6549be1`](https://bitbucket.org/aiodintech/wlsapi/commits/6549be1b3555dbbdeb57e0f4ff4cc7f36adb16df)
-  update error to core error, user schema remove default value, update option api   [`4a29386`](https://bitbucket.org/aiodintech/wlsapi/commits/4a29386360f46a7916e680927f55339953caa187)
-  update query after merge in dynamic filter   [`3a6e159`](https://bitbucket.org/aiodintech/wlsapi/commits/3a6e159bd2c26a76138cc3bacaed1eb71ba69c15)
-  add findbyId   [`541bb20`](https://bitbucket.org/aiodintech/wlsapi/commits/541bb20bdfeeac90ed6bf2144c70ec24c24bfadb)
-  user roles and hierarchies navigation and middleware   [`69b77e0`](https://bitbucket.org/aiodintech/wlsapi/commits/69b77e056540aa43ddf55bbdeee615df327b8d14)
-  update error to core error, user schema remove default value, update option api   [`78f027f`](https://bitbucket.org/aiodintech/wlsapi/commits/78f027f1a3d06e24c920ed0f407ad93c51327d64)
-  add filter for index   [`9003f4d`](https://bitbucket.org/aiodintech/wlsapi/commits/9003f4d9580e4504780c6a8ba94ed64f9593b32a)
-  handle if modules have no any policies   [`d7c9538`](https://bitbucket.org/aiodintech/wlsapi/commits/d7c9538fbf38a2d22b2a9a0888539a5596370cdb)

#### Bug Fixes

-  fix inconsistant audit log structure & missing audit log entry   [`564cfcc`](https://bitbucket.org/aiodintech/wlsapi/commits/564cfcc986fb65d5c53f70c12cc4ec1b22e34da2)
-  add deepToJSON utility to improve object parsing in computeUpdateDiff   [`d00dedb`](https://bitbucket.org/aiodintech/wlsapi/commits/d00dedb3ad350d8496b354f178924f8c546e49d6)
-  update error messages in service tests for clarity and consistency   [`8ebf5bc`](https://bitbucket.org/aiodintech/wlsapi/commits/8ebf5bc1a523d945173725b49e8b2bddd256ad3b)
-  standardize role path formatting to lowercase and underscores   [`93ecf9b`](https://bitbucket.org/aiodintech/wlsapi/commits/93ecf9bb553160bd639fac89aca82b142abfb481)
-  simplify index function and enhance status update logic   [`a9e40b9`](https://bitbucket.org/aiodintech/wlsapi/commits/a9e40b9576e086f91072637515cb56502b370d38)
-  simplify user association check and update invitation status handling   [`175467c`](https://bitbucket.org/aiodintech/wlsapi/commits/175467cb6135740afa7014a3c91d8faac81ce5d2)
-  add ignoredKeys argument in computeUpdateDiff   [`12020cd`](https://bitbucket.org/aiodintech/wlsapi/commits/12020cde49720d8c19237691f4101e0702dc84ea)
-  update namespaces from 'api' to 'string' for consistency   [`6e82e45`](https://bitbucket.org/aiodintech/wlsapi/commits/6e82e45a873cef2439c39e53f425c6e47634cc9f)
-  add entityId to onboard schema and make it required   [`c849e6c`](https://bitbucket.org/aiodintech/wlsapi/commits/c849e6c04f4b69ae522ce1e9db8dc9e8f43cd1ce)
-  improve currency sorting logic in `updateOrganisation` function   [`59be226`](https://bitbucket.org/aiodintech/wlsapi/commits/59be226aa54e84a3910c0070910387f24aad9aca)
-  update versionConflict error to include version object in throw statements   [`f3af06d`](https://bitbucket.org/aiodintech/wlsapi/commits/f3af06db6addb900eb282490a2e726fff770278a)
-  correct spelling errors in comments and update index name for user invitations   [`1a0d768`](https://bitbucket.org/aiodintech/wlsapi/commits/1a0d768e00a53d5009b0c148a8574c25ac71de78)
-  update hierarchy property to use enum for access levels   [`47a3870`](https://bitbucket.org/aiodintech/wlsapi/commits/47a387093783f22c663178a76aee3e09039f0af1)
-  update test description and expected field for invitation status approval   [`c8054dd`](https://bitbucket.org/aiodintech/wlsapi/commits/c8054dda5928c33925983bae53b86daa18d3c59f)
-  reorder error definitions for clarity and consistency   [`b166dbf`](https://bitbucket.org/aiodintech/wlsapi/commits/b166dbf8b0a6afe6f83a1ab9479c855e318b9ba4)
-  move routeConfig declaration for clarity   [`54e88a6`](https://bitbucket.org/aiodintech/wlsapi/commits/54e88a61f01cbbc43e41dd9c83b56c9956dee705)
-  ensure consistent return type for namespace-specific translators   [`060d490`](https://bitbucket.org/aiodintech/wlsapi/commits/060d4908bcbecf211b50eaf85a1b1ec5b5f6b6ae)
-  include userroute in the user module imports   [`97c45a7`](https://bitbucket.org/aiodintech/wlsapi/commits/97c45a76c195c9b5975387b918b82e0efa16326f)
-  ensure proper execution order in remove function   [`5bfc046`](https://bitbucket.org/aiodintech/wlsapi/commits/5bfc0469b551e8c43761e7afe6c7aa75c62ed8bf)
-  reorder user error definitions for consistency   [`3e061b1`](https://bitbucket.org/aiodintech/wlsapi/commits/3e061b1510284e50efd2ba27c0e4589184c2f672)
-  correct enum user status value   [`95acf8f`](https://bitbucket.org/aiodintech/wlsapi/commits/95acf8fd5a425ecbb07767a59e69dba75616e2d2)
-  correct error code for inactive parent status   [`ad03c93`](https://bitbucket.org/aiodintech/wlsapi/commits/ad03c93384bb79f641539d6d04c33563cca4e79d)
-  update user route and service tests for sub-account and user creation   [`83b80f4`](https://bitbucket.org/aiodintech/wlsapi/commits/83b80f469c67b20b11798378b2563e48912f2182)
-  enhance updateStatus to handle sub-account activation restrictions based on parent status   [`b0bd30d`](https://bitbucket.org/aiodintech/wlsapi/commits/b0bd30d37dfa0393accbc4781dc104012a3f762c)
-  enhance updateStatus to deactivate sub-accounts when user status is set to INACTIVE   [`c526a6d`](https://bitbucket.org/aiodintech/wlsapi/commits/c526a6dd5de8c1e1b9d34f8bd749083e3af0b104)
-  normalize email and username to lowercase in validation functions   [`a7a526a`](https://bitbucket.org/aiodintech/wlsapi/commits/a7a526aeb2a0352f9ffb7266e4c9b44582662f3e)
-  update index fields in models to match camelCase convention   [`c4dd95c`](https://bitbucket.org/aiodintech/wlsapi/commits/c4dd95cff1628b1af7bd1ea45b0d037d5dee5282)
-  update function documentation and correct parameter name from 'modal' to 'model' in user repository   [`45beba7`](https://bitbucket.org/aiodintech/wlsapi/commits/45beba7507bb0d6f813e329634f3d98eeddb4207)
-  correct function documentation for create method and update parameter name in UserAssociation repository   [`622bd24`](https://bitbucket.org/aiodintech/wlsapi/commits/622bd248ac2e528739618b47b542c6197e74f0b4)
-  correct parameter name from 'modal' to 'model' in update function of UserMfaSetting repository   [`6b6ca41`](https://bitbucket.org/aiodintech/wlsapi/commits/6b6ca41431912763a69d65f0441cf58bf5cdf7f8)
-  update validation length for name and username fields to enforce minimum character requirements   [`af2d878`](https://bitbucket.org/aiodintech/wlsapi/commits/af2d878a25ad5f8746f1413e20940c6c00c3589c)
-  update summary for updateStatus endpoint to reflect correct functionality   [`f41181f`](https://bitbucket.org/aiodintech/wlsapi/commits/f41181f251b30ebc604bc31f15513ba01e609009)
-  update index fields in UserAssociationCurrency model to match camelCase convention   [`f5e1650`](https://bitbucket.org/aiodintech/wlsapi/commits/f5e1650e62a047668413d2bd1d3a98d0d1c4952d)
-  update USERNAME_REGEX to enforce length constraints of 3 to 20 characters   [`fd5bb06`](https://bitbucket.org/aiodintech/wlsapi/commits/fd5bb06642ef3c612a7dc3251806cb8c5e3c6fc3)
-  correct function documentation for create method in UserAssociationCurrency repository   [`3a654d7`](https://bitbucket.org/aiodintech/wlsapi/commits/****************************************)
-  ensure userId is unique and set default status for UserMfaSetting model   [`607c2cf`](https://bitbucket.org/aiodintech/wlsapi/commits/607c2cf1e76e11579f581fbacedd066252035dcc)
-  fix query filter issue   [`0097639`](https://bitbucket.org/aiodintech/wlsapi/commits/00976392b3b2af65d8d9ab9b238e8bbdebf31cbc)
-  fix datatype   [`eb0e74a`](https://bitbucket.org/aiodintech/wlsapi/commits/eb0e74ad5e2dba3a6d0dcbbb5242531310fc23cb)
-  add newline at end of file in department.constant.js   [`e177e45`](https://bitbucket.org/aiodintech/wlsapi/commits/e177e45cb9fd2043b7b49a4555dc9515603f2657)

#### Chores & Housekeeping

-  add extra_hosts configuration for wlsapi service   [`19dc11b`](https://bitbucket.org/aiodintech/wlsapi/commits/19dc11ba277dda47e2f0116e97f4af3b9019bea8)
-  update sort import   [`1dd12e0`](https://bitbucket.org/aiodintech/wlsapi/commits/1dd12e0edfa6c33c1dbb4a5f2a22f66021060a61)
-  update db indexing   [`1f04a8a`](https://bitbucket.org/aiodintech/wlsapi/commits/1f04a8af228a5fe603f1610bc377aed34519ec3e)
-  add init migration & seeder   [`52e0a85`](https://bitbucket.org/aiodintech/wlsapi/commits/52e0a85d0da1326195133e8cfa94fd7429681b06)
-  add init migration & seeder   [`9f14ae1`](https://bitbucket.org/aiodintech/wlsapi/commits/9f14ae18c738139269035252ec27e0e66469f915)
-  update module policy seeder that different hierarchy have different policies   [`4976fcf`](https://bitbucket.org/aiodintech/wlsapi/commits/4976fcf6cf6b2abe2731f5f8f75ddb4b55006ee8)
-  add department & department_modules migration   [`217b521`](https://bitbucket.org/aiodintech/wlsapi/commits/217b521142b543d4e855a64c1ea3a2f48754d82a)
-  add navigation_type field to module table   [`59aef09`](https://bitbucket.org/aiodintech/wlsapi/commits/59aef09b0be6ee9b5992ab7cb812adc571063423)
-  minor update   [`c5f7327`](https://bitbucket.org/aiodintech/wlsapi/commits/c5f7327212d33a7f41701a6bfa29e416d7241c29)
-  migration rename enum_modules_hierarchy to enum_common_hierarchy   [`766b7d7`](https://bitbucket.org/aiodintech/wlsapi/commits/766b7d78b5b31bc01a4d8060cef627f535968cef)
-  update migration - module   [`67c1d72`](https://bitbucket.org/aiodintech/wlsapi/commits/67c1d72da4a271645dda6ca5e5833d288ccde192)
-  update migration - module   [`4755700`](https://bitbucket.org/aiodintech/wlsapi/commits/4755700db73d55f0b50aae61155824c8d5ad6d4f)
-  fix code lint issue   [`083b865`](https://bitbucket.org/aiodintech/wlsapi/commits/083b8650295e3a30a2023cb990115120377f78c6)

#### Documentation Changes

-  update comment   [`ced79ec`](https://bitbucket.org/aiodintech/wlsapi/commits/ced79ec603d6bc2c0098f9a96b83e86e57d2c0a2)

#### Refactoring and Updates

-  standardize translation keys in core constant and update related tests   [`fee6f4b`](https://bitbucket.org/aiodintech/wlsapi/commits/fee6f4b446b8f22ba748a3df45ea66ac280134e0)
-  update error handling with more specific message in role module and use CoreError for consistency   [`5234b35`](https://bitbucket.org/aiodintech/wlsapi/commits/5234b35e5aef0fb3c4dcd3958e872fcba40e5179)
-  reorder and update error definitions across all modules   [`afb1833`](https://bitbucket.org/aiodintech/wlsapi/commits/afb18331842f8326ff8b43047bc6e3912ac3af62)
-  update error handling in services and models files across all modules to use CoreError for consistency   [`f164ed6`](https://bitbucket.org/aiodintech/wlsapi/commits/f164ed6e614c4e53f42622e02de58174f41a9cf5)
-  remove unused `formatErrorResponse` utility and update error handling in tests   [`11434e7`](https://bitbucket.org/aiodintech/wlsapi/commits/11434e722b2034d897eac8b0ae4cb4bdeae1d9e6)
-  enhance code readability with additional comments and structured query handling   [`7e8f816`](https://bitbucket.org/aiodintech/wlsapi/commits/7e8f816a9208cfd0286e1bb644f77940377b57d3)
-  improve error handling and authentication flow in hooks   [`c9595ca`](https://bitbucket.org/aiodintech/wlsapi/commits/c9595cadd9cf989bd3074621967c434fc56da89b)
-  standardize event naming and update related unit tests   [`141ca5b`](https://bitbucket.org/aiodintech/wlsapi/commits/141ca5b60232709bf118d6c68098da934cf1d33e)
-  update error handling in department model and service to use CoreError for consistency   [`783e9cc`](https://bitbucket.org/aiodintech/wlsapi/commits/783e9ccb487af8a835c77d035a93a59fc5efa07b)
-  enhance error handling in audit trail service and tests for improved clarity   [`f6a437a`](https://bitbucket.org/aiodintech/wlsapi/commits/f6a437ada4dd5db41eafaad672298cad7627950a)
-  update error handling in localisation module and use CoreError for consistency   [`1dacb15`](https://bitbucket.org/aiodintech/wlsapi/commits/1dacb150a3be58c05d0b0b2ed502e5b81e3675bb)
-  update error handling to provide more descriptive messages in developer hub module   [`535cee6`](https://bitbucket.org/aiodintech/wlsapi/commits/535cee6c570363d52629c5991a4da09b21d86594)
-  update error handling in IP access control model and service to use CoreError for consistency   [`af50a18`](https://bitbucket.org/aiodintech/wlsapi/commits/af50a188aa3c7229c627643ecf10cedd39c485a6)
-  update error handling in setting module and use CoreError for consistency   [`70403f4`](https://bitbucket.org/aiodintech/wlsapi/commits/70403f456b9091170a6b38d2f23c8d6f5af62005)
-  merge validation to validations folder   [`a0b3967`](https://bitbucket.org/aiodintech/wlsapi/commits/a0b3967a4a7421943b6ed90d794619ce442b7d78)
-  consolidate import statements in user module index   [`c5a0cbb`](https://bitbucket.org/aiodintech/wlsapi/commits/c5a0cbbca60dcc60e0bb1dad6b4f9ada5c371af3)
-  remove error handling from bulk job creation for streamlined execution   [`006eff7`](https://bitbucket.org/aiodintech/wlsapi/commits/006eff71c08eba2f673b96f9c28c50f521e6b65a)
-  remove unused constraints function and add foreign key reference for user_association_id   [`b0f5d52`](https://bitbucket.org/aiodintech/wlsapi/commits/b0f5d527ad38902ac0b0705c4a8dcc13d07ac1c2)
-  optimize code   [`dce9110`](https://bitbucket.org/aiodintech/wlsapi/commits/dce9110b3f97643c320e8ce93a9066a6c153c0d5)
-  update policy keys   [`12e43f6`](https://bitbucket.org/aiodintech/wlsapi/commits/12e43f61edc6673a18f589fe87524e80f9edb22a)
-  change department status to common status, change remove response http code to 200   [`b1ec591`](https://bitbucket.org/aiodintech/wlsapi/commits/b1ec591d6eae3d5084beb5b447926e239a136521)
-  enhance department schema by introducing base properties and restructuring item levels   [`688afd5`](https://bitbucket.org/aiodintech/wlsapi/commits/688afd5c09ca59c2681c63ae869f0b1b451e0713)
-  optimize code   [`37d66e7`](https://bitbucket.org/aiodintech/wlsapi/commits/37d66e7b5d98d2f1c21bc81d1d34de39493c43fc)
-  optimize code   [`9180741`](https://bitbucket.org/aiodintech/wlsapi/commits/9180741a0021b5d6bc219031667dcd811ba728b7)
-  index able to filter template or non-template   [`6c2bc08`](https://bitbucket.org/aiodintech/wlsapi/commits/6c2bc0895bd018b56f10260c077a543f39f8e25d)
-  remove async from handler functions for improved performance   [`d953d39`](https://bitbucket.org/aiodintech/wlsapi/commits/d953d396c768cd2bc75d7894efcf98b34ab36096)
-  consolidate dirty flag checks in updateOrganisation and updateLoginAccess functions   [`ee6ed3a`](https://bitbucket.org/aiodintech/wlsapi/commits/ee6ed3a0581a43ae5889f8d7ef782870fdb8c1b5)
-  standardize naming conventions in module policy data   [`3758aa1`](https://bitbucket.org/aiodintech/wlsapi/commits/3758aa1df96cbc1a09c9c9b1f238d0899cd46a1d)
-  remove unused associate method from PolicySetting model   [`4e8b040`](https://bitbucket.org/aiodintech/wlsapi/commits/4e8b040b4d248374901d0e62e8b74af305b2eb50)
-  optimize code   [`969ff61`](https://bitbucket.org/aiodintech/wlsapi/commits/969ff61e089cb041e94fdb8e197f130127b263e2)
-  optimize code   [`46a31f7`](https://bitbucket.org/aiodintech/wlsapi/commits/46a31f7476da375a6db2cd50d0b7abe63f62a930)
-  refactor route guard   [`ed8b967`](https://bitbucket.org/aiodintech/wlsapi/commits/ed8b96740d7b1b8e995dce1722a094470f976d9e)
-  move module query to core repository   [`dbb014a`](https://bitbucket.org/aiodintech/wlsapi/commits/dbb014aba7674e5cc4f3788ce909ef80296836d7)
-  rename function name   [`f7c47d5`](https://bitbucket.org/aiodintech/wlsapi/commits/f7c47d593afe1f9a049a225b7e0294678a43ad26)
-  minor update migration   [`5c3834d`](https://bitbucket.org/aiodintech/wlsapi/commits/5c3834d373972a856d371590390b77ab35bd28c3)

#### Changes to Test Assests

-  improve coverage   [`5d435bf`](https://bitbucket.org/aiodintech/wlsapi/commits/5d435bf94e908204df25cff24dbf603b2dcefe82)
-  remove unrelated test in bulk job   [`93e05a6`](https://bitbucket.org/aiodintech/wlsapi/commits/93e05a6e397b8d80e5958054a878b0d88a99097f)
-  optimize coverage of success message util test   [`6bd6d38`](https://bitbucket.org/aiodintech/wlsapi/commits/6bd6d382834e5f06f7303426dcc52bf334a5ce49)
-  department template unit test   [`5847abe`](https://bitbucket.org/aiodintech/wlsapi/commits/5847abe63e12b7df0881957e55f3636cf0e8e27e)
-  module and policy unit tests   [`ca6e38b`](https://bitbucket.org/aiodintech/wlsapi/commits/ca6e38b6a516356e848efd18a091a9f08691e240)

#### Style Changes

-  update format   [`56e7f3e`](https://bitbucket.org/aiodintech/wlsapi/commits/56e7f3e54a88f8c0dd0f04b0c34161dc2ce3d99a)

<!-- auto-changelog-above -->

### v0.0.21

-  merge COMMON_HEADER_PROPERTIES and COMMON_HEADERS
-  developer Hub
-  add x-access-id to allowed cors header
-  Centralized Log Management Backend Development
-  patch/QPLY-1449-Error-functions-should-always-return-the-same-type
-  Backend Translation
-  Access Control
-  add SWAGGER_THEME environment variable and update related configurations
-  deprecation warning caused by invalid sqlite URL format
-  Plugin Fails to Load
-  Enhance autoloadModels to Support Unified Core Model Directory

#### New Features

-  refactor action and description properties and enhance unit tests   [`d2f9364`](https://bitbucket.org/aiodintech/wlsapi/commits/d2f9364221d831404e008465574ca9f0ed7890a3)
-  add translation for success and error response; update reveal 500 for dev env; add missing unit test   [`b3ea6fc`](https://bitbucket.org/aiodintech/wlsapi/commits/b3ea6fcd160beaba820befa4265aaab242111f21)
-  update i18n namespaces and defaultNS to 'api'; enhance validation error with translation key   [`f0cb03f`](https://bitbucket.org/aiodintech/wlsapi/commits/f0cb03fe4aaf3f7ed6d2706ecfbe0657d004a6c1)
-  update validation message util with helper; add unit tests   [`e931e92`](https://bitbucket.org/aiodintech/wlsapi/commits/e931e92ef75bf704b6aae174d156dcf2cf25e289)
-  i computeUpdateDiff util, enhance schema handling in onRoute hook, update cache util to use SCAN for retrieving keys with prefix   [`4cb715e`](https://bitbucket.org/aiodintech/wlsapi/commits/4cb715ed44c95234df8c62de6b1ce73e304e89e4)
-  enhance onRequest hook with language extraction and entity access ID handling; add unit tests for new functionalities   [`eea1d6e`](https://bitbucket.org/aiodintech/wlsapi/commits/eea1d6e871b761859f06a0c7efe80e857d2223ab)
-  minor update   [`d7625f2`](https://bitbucket.org/aiodintech/wlsapi/commits/d7625f213b5279aacd2cced26f7ffc9b4fb153fa)
-  implement common headers schema and update error handling for translation   [`a41b133`](https://bitbucket.org/aiodintech/wlsapi/commits/a41b133dd22ee96638254bc9d7bbec23b09c5847)
-  enhance i18next utility functions with validation keyword mapping and dropdown translation; add unit tests   [`161e197`](https://bitbucket.org/aiodintech/wlsapi/commits/161e1977a1f3f0dcee9ec89a798f4ea583016a0e)
-  update Swagger theme to include muted and classic options   [`6defc4b`](https://bitbucket.org/aiodintech/wlsapi/commits/6defc4b11a8373997c3057cc0afc9b7acaf44a84)
-  validate IP overlap before updating status   [`8385e0e`](https://bitbucket.org/aiodintech/wlsapi/commits/8385e0e3114b7cc2497fffbac32f26b38f7d6146)
-  update service, schema   [`2ebe9f6`](https://bitbucket.org/aiodintech/wlsapi/commits/2ebe9f6c16a433604b4b222161f5f443bcc59fac)
-  perform unique IP validation only if IP is modified   [`0a8d393`](https://bitbucket.org/aiodintech/wlsapi/commits/0a8d3930bb2673af0cb6d96ab84b078437dd690d)
-  index filter with entityId   [`300fc85`](https://bitbucket.org/aiodintech/wlsapi/commits/300fc8529dfb7f148dc6de2406a909975b16991f)
-  update handler, repository   [`52fe0f3`](https://bitbucket.org/aiodintech/wlsapi/commits/52fe0f3c8d05b722d633955dbe507d9fa7f00fd1)
-  update schema   [`819080c`](https://bitbucket.org/aiodintech/wlsapi/commits/819080c8db1f953c7c0215b7346f8d57a9a0b502)
-  update swagger configuration and documentation   [`63b5968`](https://bitbucket.org/aiodintech/wlsapi/commits/63b596859a9bad1b54ce828902b5bb990914cc11)
-  update event references   [`511db5b`](https://bitbucket.org/aiodintech/wlsapi/commits/511db5b277d8aeff09221b1a1c9891180990591a)
-  add new index for status and started_at, remove redundant index in down method   [`f37e06e`](https://bitbucket.org/aiodintech/wlsapi/commits/f37e06e5db1e996b98650044c67c37fe82dc20c8)
-  update models   [`c622be7`](https://bitbucket.org/aiodintech/wlsapi/commits/c622be7f1cb8cf0f5871ccccac6a2db11a102512)
-  allow to update application name   [`3980a93`](https://bitbucket.org/aiodintech/wlsapi/commits/3980a93a65b32ae97526655a2f683a5b49f5a0ee)
-  update api key validation - must has at least one number   [`6d06608`](https://bitbucket.org/aiodintech/wlsapi/commits/6d06608f8c0b6c37d3fba299844bbf96503c779f)
-  merge branch develop into feature QPLY-1112   [`55f680e`](https://bitbucket.org/aiodintech/wlsapi/commits/55f680ee7df54566f39e6edc68a9470865864a4a)
-  merge branch develop into feature QPLY-1112   [`4dbe2a7`](https://bitbucket.org/aiodintech/wlsapi/commits/4dbe2a792f5dffbaca98fba3a88de534547c1533)
-  refine the code based on the comments   [`81d0227`](https://bitbucket.org/aiodintech/wlsapi/commits/81d02276432f8d26d527b2475a3b69e5b81035fb)
-  add audit trail features   [`fccb6c0`](https://bitbucket.org/aiodintech/wlsapi/commits/fccb6c0748f42fda0cce84a8297f24f25ca19d52)
-  solve comments   [`a4c2730`](https://bitbucket.org/aiodintech/wlsapi/commits/a4c273006c463c7b6b34569a2d7c1feba1e05026)
-  refine audit trail features according to the standard   [`0c56818`](https://bitbucket.org/aiodintech/wlsapi/commits/0c5681852e3b913db8f1014fd915ca798af14d2d)
-  solve comments   [`33e8f53`](https://bitbucket.org/aiodintech/wlsapi/commits/33e8f5345b3e641a64bd48e954cae5561cd5585a)
-  developer hub migrations & api   [`4059863`](https://bitbucket.org/aiodintech/wlsapi/commits/40598635858825c25eb5532c7f0a455787820030)
-  access control api - index, create, view, edit, updateStatus   [`eff8598`](https://bitbucket.org/aiodintech/wlsapi/commits/eff859868b7f050e44cf22f2a8e78d1d7a69df7a)
-  add audit trail features   [`c586cc4`](https://bitbucket.org/aiodintech/wlsapi/commits/c586cc4b2e78e27228436f9cc0cb35224d783c93)
-  solve merge conflicts   [`a699579`](https://bitbucket.org/aiodintech/wlsapi/commits/a699579abb01645991cea7e2c5e9cfd474a0c295)
-  developer hub apis   [`c442b30`](https://bitbucket.org/aiodintech/wlsapi/commits/c442b3098beabb147bf4237baf2c1583d06e5df8)
-  add check ip address overlap validation   [`a9ed020`](https://bitbucket.org/aiodintech/wlsapi/commits/a9ed02096b5a4693d4b8aa9d1951391e74b51e13)
-  refine audit trail features according to the standard   [`6b2d3b4`](https://bitbucket.org/aiodintech/wlsapi/commits/6b2d3b459eab978fc14ce72bed3b4e53e3911aee)
-  solve comments   [`8be8946`](https://bitbucket.org/aiodintech/wlsapi/commits/8be89463ffafc7c1a15836c13605c1da8e42396f)
-  implement theme support for swagger ui with light and dark modes   [`f5933dc`](https://bitbucket.org/aiodintech/wlsapi/commits/f5933dc8eab8da476bf80bffae6ce9453eeb2be9)
-  update unique constraint from (parent_id, rule_type, ip_address) to (parent_id, ip_address)   [`d6d73b5`](https://bitbucket.org/aiodintech/wlsapi/commits/d6d73b5c42e429877c6d619b1d315e181f29e700)
-  update package.json with swagger-themes dependencies   [`6639484`](https://bitbucket.org/aiodintech/wlsapi/commits/663948442e763c48e60946db8adc237af8142cf5)
-  remove unused import   [`567461a`](https://bitbucket.org/aiodintech/wlsapi/commits/567461a9102cd7148a049866fe8983d17428f2a6)
-  add status filter to IP overlap check for active entries   [`cd9e77b`](https://bitbucket.org/aiodintech/wlsapi/commits/cd9e77b82a7a2727cfee2b2e34f4fb0a52a9c4ed)
-  solve merge conflicts   [`24d5753`](https://bitbucket.org/aiodintech/wlsapi/commits/24d575358ea60b9f981d11f03e3a0604f4743f2f)
-  solve merge conflicts   [`baac70e`](https://bitbucket.org/aiodintech/wlsapi/commits/baac70e1facc9021c8d125a2c4bbe3db77b20732)
-  add audit trail features   [`7544830`](https://bitbucket.org/aiodintech/wlsapi/commits/75448309b98cf857eaa180ee5798a6308203b962)

#### Bug Fixes

-  disable autoIndex when connecting to MongoDB to improve performance   [`bf59558`](https://bitbucket.org/aiodintech/wlsapi/commits/bf5955813d078233bf35685123336181a2c797a0)
-  use optional chaining to safely check results length in getCacheKeysWithPrefix function   [`3b57ca7`](https://bitbucket.org/aiodintech/wlsapi/commits/3b57ca75a5b50925a28dd3eac0afa67f9f5514f3)
-  fix theme variable casing to lowercase   [`8ed72be`](https://bitbucket.org/aiodintech/wlsapi/commits/8ed72beb9ef11c017b12391ace22cec6630e6bfd)
-  disable sonarjs rule for hardcoded passwords in test and config files   [`a126ceb`](https://bitbucket.org/aiodintech/wlsapi/commits/a126ceb26dad8d995da27410e73d49020491507a)
-  fix incorrect instancepath format in adaptvalidationerror   [`ee62b11`](https://bitbucket.org/aiodintech/wlsapi/commits/ee62b11023fd5be18bc8806d4f57d59b68539a15)
-  add eslint disable comments for hardcoded passwords in tests   [`413199d`](https://bitbucket.org/aiodintech/wlsapi/commits/413199d90aa2d66c1ec0635cd3c10d96c4b57661)
-  fix incorrect key for rate limit redis connection   [`f363868`](https://bitbucket.org/aiodintech/wlsapi/commits/f3638683b47790b64e0136dce9ce37f15c3e3a5d)
-  correct typo in comment for COMMON_MONGO_PROPERTIES   [`9619c8a`](https://bitbucket.org/aiodintech/wlsapi/commits/9619c8aa70e4cf02d974d62aac1e42c9aa2398fb)
-  disable eslint rule for hardcoded passwords in core constants   [`6b6fece`](https://bitbucket.org/aiodintech/wlsapi/commits/6b6fece028d4fe225fc0f64f2a9a88a02ff11a59)

#### Chores & Housekeeping

-  update unique column pattern   [`2b91363`](https://bitbucket.org/aiodintech/wlsapi/commits/2b91363a486dda84ff8eaaf2d50782a8a38e2918)
-  beautify migration code   [`b488a42`](https://bitbucket.org/aiodintech/wlsapi/commits/b488a421ffdbc0304ecdb10880a3a2b8b1125d17)

#### Refactoring and Updates

-  refactor code   [`73faa89`](https://bitbucket.org/aiodintech/wlsapi/commits/73faa8906018cdb4340d7383e85ac0c5652e8f1b)
-  optimize IP overlap check using Sequelize operators and literals   [`8b3ec5a`](https://bitbucket.org/aiodintech/wlsapi/commits/8b3ec5a1895128e8eac8d9bed1678eeff6ddc0cd)
-  change checkipoverlap function using ipaddr.js   [`6a80ecb`](https://bitbucket.org/aiodintech/wlsapi/commits/6a80ecb272c0f00467c4c288f1d8838c13070170)
-  split out translateDropdownItem function and update related tests   [`4eeeb7d`](https://bitbucket.org/aiodintech/wlsapi/commits/4eeeb7df2aedc6ffcba85b029d8db2c17c80f1f6)
-  use raw SQL query for IP overlap validation to improve performance   [`a6a5823`](https://bitbucket.org/aiodintech/wlsapi/commits/a6a582398f00749c04dfad72fb1bf03469935c89)
-  change transaction key format   [`e919add`](https://bitbucket.org/aiodintech/wlsapi/commits/e919add602a6e1c51806ab9033031ebecc362863)
-  remove redundant COMMON_HEADERS from access control, localisation, and setting schemas   [`851918a`](https://bitbucket.org/aiodintech/wlsapi/commits/851918aabb74bebaa51abeeea77bc5357a9737be)
-  update error message keys to use the new error namespace   [`f052e11`](https://bitbucket.org/aiodintech/wlsapi/commits/f052e1144c514ffdd47513add5059b4443142ed7)
-  minor update   [`ba621d3`](https://bitbucket.org/aiodintech/wlsapi/commits/ba621d3226ce5c2c2e9d5602ace33dc91d51a6d8)
-  simplify log message assignment for clarity   [`667b1ad`](https://bitbucket.org/aiodintech/wlsapi/commits/667b1ad59e253b27bac855958da97b391351224e)
-  simplify default parameter handling and normalization in prepareMongoSorting function   [`ad0875a`](https://bitbucket.org/aiodintech/wlsapi/commits/ad0875a70ef670f9f7407f778f5e1564a7c736cb)
-  update JSDoc comments for clarity in findById function   [`f5c8694`](https://bitbucket.org/aiodintech/wlsapi/commits/f5c8694265043214bb71efd70871baa722c3a4dd)
-  update swagger theme local storage variable follow naming convention   [`d44ef33`](https://bitbucket.org/aiodintech/wlsapi/commits/d44ef33e793f683d21eb0438269aab0023b50504)
-  update api key validation - must at least one upper, at least one lower, no special character, 32 length   [`6768896`](https://bitbucket.org/aiodintech/wlsapi/commits/6768896f2a410ce841f590e9553b3f54601b0e41)
-  optimize code   [`e15d365`](https://bitbucket.org/aiodintech/wlsapi/commits/e15d365da7fcef4a82685df926b29e9c942ee3be)
-  access control api   [`b485a6f`](https://bitbucket.org/aiodintech/wlsapi/commits/b485a6f83f661ae8c4fba50efcaceed159eb744e)
-  refactor index query and route guard   [`f7d140f`](https://bitbucket.org/aiodintech/wlsapi/commits/f7d140ff1327f7cc82c188a0385548e976889474)
-  update autoload model change loading path   [`a04f46b`](https://bitbucket.org/aiodintech/wlsapi/commits/a04f46bdcb2c29bdca487620d57a12a42343ef82)
-  optimize code and remove unused indexing   [`087136a`](https://bitbucket.org/aiodintech/wlsapi/commits/087136a27c7cfc5a3d5a51a6f8e2dab2c9abc623)
-  rename blacklist & whitelist   [`cac207c`](https://bitbucket.org/aiodintech/wlsapi/commits/cac207c6defeb12ba88c18d64287531966b82f16)
-  rename migration files   [`12b2616`](https://bitbucket.org/aiodintech/wlsapi/commits/12b2616b79ff7d7c76200283e024527dd1fb89f3)
-  optimize ip support subnet   [`43c03da`](https://bitbucket.org/aiodintech/wlsapi/commits/43c03dad20a396f3488edf4b1bb676c16b0b574c)
-  remove defaultValue from parent_id migration   [`1f3173c`](https://bitbucket.org/aiodintech/wlsapi/commits/1f3173cdb4a5b8ec23e29f8d5a9dd3ea45d5a7f5)
-  able to filter validity range   [`06f4d10`](https://bitbucket.org/aiodintech/wlsapi/commits/06f4d102644e19d1f79873f33e7f4808b6d8d37d)
-  remove validity date validation   [`420da19`](https://bitbucket.org/aiodintech/wlsapi/commits/420da194d5944389377f1029fdc63b4fb70fc2a1)
-  apply parentId filter in findAll using entityId   [`ecc557e`](https://bitbucket.org/aiodintech/wlsapi/commits/ecc557ed2905acf8805291daa694eb8c7cd13d5d)
-  optimize code   [`4fb5c12`](https://bitbucket.org/aiodintech/wlsapi/commits/4fb5c1232211a15fcf50037099de243f36a7fc24)
-  add indexing to ip_access_control and change remark to_tsvector   [`fbddda5`](https://bitbucket.org/aiodintech/wlsapi/commits/fbddda59437e3a3f91d9370c1773b8d325d35467)
-  update rate limit Redis timeout to dynamic value and update use env port   [`7ac8995`](https://bitbucket.org/aiodintech/wlsapi/commits/7ac899528595bcc03515f2eb4413b610b9d19314)
-  update rate limit env key   [`67c53b2`](https://bitbucket.org/aiodintech/wlsapi/commits/67c53b2ce335ceeec53342235be5d03a3a888522)
-  optimize code   [`df9692e`](https://bitbucket.org/aiodintech/wlsapi/commits/df9692e0b202546a42a11cb5380313c7668acb91)
-  remove unnecessary swagger theme css   [`a8c4c45`](https://bitbucket.org/aiodintech/wlsapi/commits/a8c4c4528cf2df00082a9d8d948596874194a83f)

#### Changes to Test Assests

-  update & improve test coverage   [`3cf2ffb`](https://bitbucket.org/aiodintech/wlsapi/commits/3cf2ffbca6e0141eb00d1464a6ab29551ac040e1)
-  add missing unit test for core, localisation and setting schema   [`b15f24d`](https://bitbucket.org/aiodintech/wlsapi/commits/b15f24d2c9b8c692c1a4fa1818540c20141319ae)
-  add missing unit test   [`c661ceb`](https://bitbucket.org/aiodintech/wlsapi/commits/c661cebaf3bb186a5eef393159b07aa68bc5113b)
-  developer hub, api rights unit tests   [`1668076`](https://bitbucket.org/aiodintech/wlsapi/commits/166807651c5e33c841de537bf136c138bbb3bcf3)
-  access control unit tests   [`3e6c0ad`](https://bitbucket.org/aiodintech/wlsapi/commits/3e6c0ad3ce937c70f7ea81068f9e5b9197f60f0e)

<!-- auto-changelog-above -->

### v0.0.7

- Dynamic Filter Parsing for Repository Query Handling
- Guard Setup for Route Using Centralised Hooks
- Settings

#### New Features

-  system settings api-index, update, info   [`38fb1a8`](https://bitbucket.org/aiodintech/wlsapi/commits/38fb1a8d939b0879241c8fff675b7eca14244387)
-  update settings module use dynamic filter parsing   [`182adec`](https://bitbucket.org/aiodintech/wlsapi/commits/182adec40bf654bdbacc5adc036fe4bbc42f7e3f)
-  add dynamic filter parsing feature   [`f45aba7`](https://bitbucket.org/aiodintech/wlsapi/commits/f45aba78d43d56b2626e7e501112bb2b5293c97f)
-  restructure the code to be more organised and removed hardcoded code   [`113e489`](https://bitbucket.org/aiodintech/wlsapi/commits/113e4890ef29d93e68bd17bfd530b74a1da320bc)
-  add jwt verifiication   [`59711de`](https://bitbucket.org/aiodintech/wlsapi/commits/59711de834319bebcf2675b5ab85c6beb01fa11b)
-  refine the access check to bypass swagger and add jwt to swagger ui   [`5ac66ae`](https://bitbucket.org/aiodintech/wlsapi/commits/5ac66ae077a3f7bb5e79bac533e61d7f113a1d6d)
-  implement route guard using centralised hooks   [`6e05dc7`](https://bitbucket.org/aiodintech/wlsapi/commits/6e05dc700e4b219c81adb00735c3cc29aab504e1)
-  refined the jwt verification to use @fastify/jwt   [`4190f59`](https://bitbucket.org/aiodintech/wlsapi/commits/4190f598e11fbe77191b3ece26e6ed2acdbcd023)
-  implement route guard using centralised hooks   [`fa09bd9`](https://bitbucket.org/aiodintech/wlsapi/commits/fa09bd91cfbdc754bb2f6a590051f5e19f5bac2a)
-  refine the access check to bypass swagger and add jwt to swagger ui   [`ddc7740`](https://bitbucket.org/aiodintech/wlsapi/commits/ddc774002a897a5b65fd7587d012d6258ba9b6ce)
-  update authentication handling to use authInfo instead of user across hooks and services   [`2f5c535`](https://bitbucket.org/aiodintech/wlsapi/commits/2f5c535b31e9a9f4cb8de8e968d0d77eae0adcb1)
-  add core error handling and refactor localisation errors   [`e8a5bbe`](https://bitbucket.org/aiodintech/wlsapi/commits/e8a5bbe89823b5bac81fd3a1c1dfad2bd24888e8)
-  enhance localisation schema with additional filter parameters and update user reference in update function   [`8fb41b3`](https://bitbucket.org/aiodintech/wlsapi/commits/8fb41b3d15ca828410eaca0b5aeeed9f36e354ca)
-  restructure the code to be more organised and removed hardcoded code   [`cdcd107`](https://bitbucket.org/aiodintech/wlsapi/commits/cdcd107377294751b2749f6f7b67e3eee9b25b07)
-  update audit fields and repository methods to use authInfoId instead of userId   [`35d1014`](https://bitbucket.org/aiodintech/wlsapi/commits/35d1014966e647e6fa6f4be484a47fe2e1357ebb)
-  restructure the code to be more organised and removed hardcoded code   [`1023b9c`](https://bitbucket.org/aiodintech/wlsapi/commits/1023b9cdbb1273c09d1b90fa7facad973b608656)
-  remove irrelevant test case   [`97c5368`](https://bitbucket.org/aiodintech/wlsapi/commits/97c53680d1159471f67834b798d42155b39d9928)
-  solve merge conflicts   [`bf9b248`](https://bitbucket.org/aiodintech/wlsapi/commits/bf9b24883c5c5b9e9eb22be4d2736d1da457ef3e)
-  solve merge conflict and update the test case   [`4c7bc08`](https://bitbucket.org/aiodintech/wlsapi/commits/4c7bc08326dd34c1e8b5513648ba3f746785cdc3)

#### Bug Fixes

-  remove sequalize beforeCount workaround code   [`c06a4c6`](https://bitbucket.org/aiodintech/wlsapi/commits/c06a4c6dbc97f9ec3e95fa152f877e795bee34e9)
-  enhance cache key generation to support user-specific caching   [`ffdc2a0`](https://bitbucket.org/aiodintech/wlsapi/commits/ffdc2a0f64b2b770b0bd27959a780faa07a5b7ed)
-  update rootUserId and request.user.id to correct values   [`cc9e01a`](https://bitbucket.org/aiodintech/wlsapi/commits/cc9e01aacdf2578a753d47e055b6ddb5ffc8d64d)
-  fix strict type issue of request params of page & limit   [`62108b0`](https://bitbucket.org/aiodintech/wlsapi/commits/62108b0158c6c3358da006d395783cb503417b6e)
-  fix unit test for setting handler   [`e7f0698`](https://bitbucket.org/aiodintech/wlsapi/commits/e7f0698e3f1163230da05604d1c0dc91bc9daaa0)

#### Chores & Housekeeping

-  add setting & custom-setting migration & seeder   [`cb04218`](https://bitbucket.org/aiodintech/wlsapi/commits/cb04218e8c0846cf3825c69db2da51f4d927ba59)
-  add scopes   [`7b491a0`](https://bitbucket.org/aiodintech/wlsapi/commits/7b491a0d0da3d3c75bb2c9a276eb527aa3a724a9)
-  remove unnecessary indexing   [`a3ae18d`](https://bitbucket.org/aiodintech/wlsapi/commits/a3ae18dc270faf9cfe562d4b7dd18898ae318fdd)
-  remove unused indexing   [`3343d5c`](https://bitbucket.org/aiodintech/wlsapi/commits/3343d5c0d1c58a2bbfc59d6c328804a99d035aa5)
-  update error code   [`9326b73`](https://bitbucket.org/aiodintech/wlsapi/commits/9326b73144324c8dbecea54c12eb7432c823308b)
-  set version default value to schema   [`4a8eefc`](https://bitbucket.org/aiodintech/wlsapi/commits/4a8eefc86f12d0920ae44c3f6b5956ecc4fdfe84)
-  minor update   [`b979138`](https://bitbucket.org/aiodintech/wlsapi/commits/b979138c9b650e6dd00a1d25025618a4722e89c0)
-  add 'general' into enum_settings_category   [`5d4a853`](https://bitbucket.org/aiodintech/wlsapi/commits/5d4a853d017703c27ee9a28395b98c8f2d98e057)
-  add sqlite3 to devDependencies for unit testing   [`ad2823a`](https://bitbucket.org/aiodintech/wlsapi/commits/ad2823a895009987c144060338f72eb09ba67357)

#### Documentation Changes

-  update jsdoc   [`03274b4`](https://bitbucket.org/aiodintech/wlsapi/commits/03274b4066b57038a47e6c43a7756d7e0fe7b137)

#### Refactoring and Updates

-  localisation logic   [`cb882b9`](https://bitbucket.org/aiodintech/wlsapi/commits/cb882b9f2f245ce22c3fb7b6bf7a4955781ab01f)
-  update all api based on the db changes   [`43438b1`](https://bitbucket.org/aiodintech/wlsapi/commits/43438b15e1dae7e76a4b8a29bdd50f2ba68cd8ae)
-  optimize code   [`7921eaf`](https://bitbucket.org/aiodintech/wlsapi/commits/7921eaf83711f6509f9b8c38aa34b484d52fa15b)
-  enhance system setting plugin and update test cases; remove unused schemas   [`235c916`](https://bitbucket.org/aiodintech/wlsapi/commits/235c916906b31010ba6cf7748349cafa973aedde)
-  fetch data relationship error & return correct res data   [`3700844`](https://bitbucket.org/aiodintech/wlsapi/commits/3700844fd5f3fe7b1539a0f35c0241ab4484d0b7)
-  update schema and rename the association customSettings   [`b51355c`](https://bitbucket.org/aiodintech/wlsapi/commits/b51355c1cb801c6bc9ae5f5198fd42bfd1ee57b5)
-  add systemSetting plugin (baseCurrency)   [`642319d`](https://bitbucket.org/aiodintech/wlsapi/commits/642319dc01c3d3aad44a49f3a75acdd8e44cf1dd)
-  resolve comments   [`c9e0c16`](https://bitbucket.org/aiodintech/wlsapi/commits/c9e0c16c15e5d692d4c0c4fa4b13f6c4645d8a1d)
-  optimize code   [`e1ffd71`](https://bitbucket.org/aiodintech/wlsapi/commits/e1ffd71327a789a80d85492004f26b2609b24a4e)
-  move field to settings table & update setting seeder   [`731e624`](https://bitbucket.org/aiodintech/wlsapi/commits/731e62440657cea7d9fb028f6dbf78f0f9da945d)
-  update variable naming   [`637350f`](https://bitbucket.org/aiodintech/wlsapi/commits/637350f1e3161fcd3a2deac29ca70d9a27294def)
-  remove commented code   [`35fe45b`](https://bitbucket.org/aiodintech/wlsapi/commits/35fe45b1a7524a087403f4f0a337234ac7a4e06b)
-  remove request interceptor for authorization header   [`799b9d7`](https://bitbucket.org/aiodintech/wlsapi/commits/799b9d77b25e6a5d67e2e6f46a7cddbf54424ae2)
-  improve versionConflict error message for clarity   [`b085506`](https://bitbucket.org/aiodintech/wlsapi/commits/b085506ed280bcd8682cb523bc3fb462e2bca6cf)
-  remove unused indexing   [`5465a4c`](https://bitbucket.org/aiodintech/wlsapi/commits/5465a4cef7741f41bf2b43b0ff01e1bf1f4279d1)
-  fix cache error   [`12ce8bf`](https://bitbucket.org/aiodintech/wlsapi/commits/12ce8bf54344ee408d630f6ea726b0c8f95c0832)
-  fix sequelize detect onchange value type issue   [`8a83238`](https://bitbucket.org/aiodintech/wlsapi/commits/8a832389995147b5a6383e77fb73488c284bc9f4)
-  remove TODO comment   [`4642651`](https://bitbucket.org/aiodintech/wlsapi/commits/4642651176366f7089ca11a878ba9a4580c31bb5)

#### Changes to Test Assests

-  localisation unit tests   [`857f617`](https://bitbucket.org/aiodintech/wlsapi/commits/857f61775e00a82b6b84e9f2173a5a4c1ea15e67)
-  setting unit tests   [`6d332d5`](https://bitbucket.org/aiodintech/wlsapi/commits/6d332d50d1eb00ea6b29aff38b79f366591b94f0)
-  add unit test for dynamic filter parsing   [`6772907`](https://bitbucket.org/aiodintech/wlsapi/commits/6772907b3a2b5bcabb176351f2d6d31d74d24596)
-  update unit tests   [`56de311`](https://bitbucket.org/aiodintech/wlsapi/commits/56de311c68d1794638a7de982d66c425ee04a7c1)
-  update unit tests   [`63f6268`](https://bitbucket.org/aiodintech/wlsapi/commits/63f62687d71b8070bd9a3802b8ef32860d7e8de7)
-  mixins   [`f759605`](https://bitbucket.org/aiodintech/wlsapi/commits/f759605c89616dc6c7e32036019ec062fd212ff2)
-  system setting plugin test   [`3fe2fff`](https://bitbucket.org/aiodintech/wlsapi/commits/3fe2fff308876c3ea74ac1e1d6f180ac7336537a)
-  improve coverage   [`eb04c54`](https://bitbucket.org/aiodintech/wlsapi/commits/eb04c54c9418092d00923e4021afd855bf8be3b3)

<!-- auto-changelog-above -->
### v0.0.6

-  request data structure changed after purify
-  add jsdom and dompurify to wlsapi
-  localisation
-  settings - initial setup

#### New Features

-  localisation setup   [`63b583c`](https://bitbucket.org/aiodintech/wlsapi/commits/63b583c79ec232724c0e01d7a974adc33d785b4d)
-  enhance error serialization and handling   [`5c0fc8c`](https://bitbucket.org/aiodintech/wlsapi/commits/5c0fc8c0c1fda701b0b3ad734bdda679a45223a9)
-  localisation setup   [`2f254ba`](https://bitbucket.org/aiodintech/wlsapi/commits/2f254ba060677d31b254ea739954f2ebf2a5a7e8)
-  security header setup   [`bd6bef1`](https://bitbucket.org/aiodintech/wlsapi/commits/bd6bef1d5ad1e37f2fcf7546d2b655d3868102ac)
-  update hook unit test case with new changes after merge branch   [`8bc30a2`](https://bitbucket.org/aiodintech/wlsapi/commits/8bc30a29cc01b9d9bb6df3e4f21e90edb8ef59d9)
-  add support for logging Sequelize PostgreSQL queries to Elasticsearch   [`28a39a8`](https://bitbucket.org/aiodintech/wlsapi/commits/28a39a8d62c2823e9eef1a5fe284df7177ad2069)
-  enhance localization setup and update README   [`d653b76`](https://bitbucket.org/aiodintech/wlsapi/commits/d653b764f594ce6697c72907dd5ad56d0f186bf1)
-  localisation setup   [`60c533e`](https://bitbucket.org/aiodintech/wlsapi/commits/60c533e1160c2f9b83fb6c4450432d5631fbdcd8)
-  add support for PostgreSQL read replicas in database factory   [`3c2c6aa`](https://bitbucket.org/aiodintech/wlsapi/commits/3c2c6aafc99913ed082449f94e0094186074fce4)
-  localisation setup   [`b478d4d`](https://bitbucket.org/aiodintech/wlsapi/commits/b478d4d4c553c046430100d6fe913e015a7dc068)
-  update .env.example and sequelize config for PostgreSQL user and logging   [`44a7c8b`](https://bitbucket.org/aiodintech/wlsapi/commits/44a7c8b947aa44b5271732d4c4fa3538cd25255e)
-  enhance commitlint configuration with custom rules and help URL   [`f1b5f3d`](https://bitbucket.org/aiodintech/wlsapi/commits/f1b5f3d7439f4f9075ff251aa2e4e22b23d276ce)
-  localisation setup   [`77a6449`](https://bitbucket.org/aiodintech/wlsapi/commits/77a6449f13ed79da3d8109e6671b30667364bfdf)
-  update test case split out fastify register step   [`f9fd24d`](https://bitbucket.org/aiodintech/wlsapi/commits/f9fd24d9853165fd6aa8c359dfa251ef1eebdda6)
-  localisation setup   [`bfa693c`](https://bitbucket.org/aiodintech/wlsapi/commits/bfa693c1178519075da70290158a647b1296548c)
-  security headers setup   [`238ffa2`](https://bitbucket.org/aiodintech/wlsapi/commits/238ffa2dce940be42e0be8efbf445c009a7711f5)
-  security header setup   [`0f86db8`](https://bitbucket.org/aiodintech/wlsapi/commits/0f86db88e5e1e1c60fb9f29eebd83374aae79b41)
-  security headers setup   [`fc65b0d`](https://bitbucket.org/aiodintech/wlsapi/commits/fc65b0d01e1c124ab8a9a56fdeccea588686f4db)
-  security header setup   [`c8c1775`](https://bitbucket.org/aiodintech/wlsapi/commits/c8c17757b28766eff08ad9202e758fbce8a704c6)
-  localisation setup   [`8b358db`](https://bitbucket.org/aiodintech/wlsapi/commits/8b358db4dbc5d8275f717249c0c8649ba65b0c05)
-  localisation setup   [`3a5866b`](https://bitbucket.org/aiodintech/wlsapi/commits/3a5866be62e9212c7bb43cc59174a8a4e29741ce)
-  complete 100% coverage for utils folder   [`7072a4e`](https://bitbucket.org/aiodintech/wlsapi/commits/7072a4ec0fd20855233aa5b2e76d1c0e2f8c631d)
-  utils unit test   [`d335ed1`](https://bitbucket.org/aiodintech/wlsapi/commits/d335ed1533bca3bd7d49c673fdf5539ee9773f02)
-  refine security header setup   [`77a73d8`](https://bitbucket.org/aiodintech/wlsapi/commits/77a73d8e3f0c676de02efc2c80d80adb0bf3ff38)
-  update typesense example (create collection, add document, find document)   [`7ef86a3`](https://bitbucket.org/aiodintech/wlsapi/commits/7ef86a360b055a133ec4abf66ce3ec5159d4ff13)
-  add index template configuration for Elasticsearch   [`015b7bf`](https://bitbucket.org/aiodintech/wlsapi/commits/015b7bf732aa172c8f6520cf9b858537b6766c40)
-  change nested fields to flattened type for application logs   [`55d190c`](https://bitbucket.org/aiodintech/wlsapi/commits/55d190c4f7430029bb655f4104a2b4652cd92e95)
-  utils unit test   [`30c7566`](https://bitbucket.org/aiodintech/wlsapi/commits/30c75661b0904a070e637a14e8d1fad6b4eeb7dc)
-  set up elastic stack   [`10afe77`](https://bitbucket.org/aiodintech/wlsapi/commits/10afe77139f56857eb17e2a5d196fd7d7275f1ee)
-  localisation setup   [`2993778`](https://bitbucket.org/aiodintech/wlsapi/commits/29937788a250474a4f2465a27a0093533246c9c2)
-  localisation setup   [`4a13c72`](https://bitbucket.org/aiodintech/wlsapi/commits/4a13c726317c6dc0315e5844d7220cda2ebd0964)
-  add customLog decorator to Fastify instance   [`e7d33c5`](https://bitbucket.org/aiodintech/wlsapi/commits/e7d33c5bbc0c183e26dec3664823fa301761d25b)
-  add function for clear cache with prefix   [`02f4ca5`](https://bitbucket.org/aiodintech/wlsapi/commits/02f4ca5b6b603a5a9d759f810e220441f80da279)
-  add schema for API route   [`f952a3f`](https://bitbucket.org/aiodintech/wlsapi/commits/f952a3f9975bfaf8f417b47598002360788a59be)
-  standardise error response   [`a1346aa`](https://bitbucket.org/aiodintech/wlsapi/commits/a1346aadeb4e0c789feef7bf4a615a3a7b9c73cf)
-  resolve multipart upload issue for swagger   [`856745a`](https://bitbucket.org/aiodintech/wlsapi/commits/856745acfdc4373d1edfda73b53cf83b19ad0996)
-  update schema error response   [`5a30161`](https://bitbucket.org/aiodintech/wlsapi/commits/5a301616a1e5082eb9cbba454d348b233472c88b)
-  add config into .env and add plugin into fastify   [`fec05e0`](https://bitbucket.org/aiodintech/wlsapi/commits/fec05e0b3b7e32ae1f00e746a91d7e62ba76a465)
-  add fetchFromCache function   [`55d36fd`](https://bitbucket.org/aiodintech/wlsapi/commits/55d36fd009e4b3c19c6ee12859d6b74c67e4d9f2)
-  security header setup   [`23b2f11`](https://bitbucket.org/aiodintech/wlsapi/commits/23b2f11509ea60874c17387964e73a2b99d88f3c)
-  remove example route for testing   [`7f92236`](https://bitbucket.org/aiodintech/wlsapi/commits/7f92236c9dfee6f9d50fff58150d0e704f2ad7ec)
-  update cache validation   [`427f78d`](https://bitbucket.org/aiodintech/wlsapi/commits/427f78d11a16ab793d85593f183abf83ee9475c1)
-  update return using sensible error   [`c83ffd5`](https://bitbucket.org/aiodintech/wlsapi/commits/c83ffd5b0a5d5bc684e23fcedbfceb0a10a9ec5b)
-  remove example route for testing   [`c2e707a`](https://bitbucket.org/aiodintech/wlsapi/commits/c2e707afc79c6d8413ea6567256bada3bc420e00)
-  change test package to vitest   [`2214124`](https://bitbucket.org/aiodintech/wlsapi/commits/2214124e173dead5faf7c5413db79170b4c2dbfc)
-  add crud structure: route, controller, repository, utils   [`198c8a2`](https://bitbucket.org/aiodintech/wlsapi/commits/198c8a2db231a6afe4dda423bddf8738aa85b773)
-  add NYC package to generate coverage reporting of tests   [`0a2ede9`](https://bitbucket.org/aiodintech/wlsapi/commits/0a2ede9472e39effb5912876d04a353718b6f734)
-  add mocha and sinon package   [`35a7e61`](https://bitbucket.org/aiodintech/wlsapi/commits/35a7e61ccf8ba947278efd5f2c5f086bc1e3ae45)

#### Bug Fixes

-  fix language and region metadata seeder format   [`0bb1614`](https://bitbucket.org/aiodintech/wlsapi/commits/0bb1614ec4d959a828f3675e34cc624587a38e42)
-  fix merged code changes issues caused   [`fd27d3f`](https://bitbucket.org/aiodintech/wlsapi/commits/fd27d3f79b9b6ee17a7e64551af8f05785d0047c)
-  fix code conflict issue caused and put back todo comment to code   [`d32940a`](https://bitbucket.org/aiodintech/wlsapi/commits/d32940a8470c64f7944e3f1c9998a86e780566d9)
-  fix conflict code issue   [`c8b1780`](https://bitbucket.org/aiodintech/wlsapi/commits/c8b17809d0681ffe8d7d9061760bcbfd518b6807)
-  reorder ENUM values and improve setEnumType function for better readability   [`4026e58`](https://bitbucket.org/aiodintech/wlsapi/commits/4026e58c1b819ea618e2ffe9f8f02b636b69e15d)
-  i18next plugin unit test issue   [`8e6ae36`](https://bitbucket.org/aiodintech/wlsapi/commits/8e6ae36ae0dc7cd82fd240e96be4788f4ca4df2f)
-  fix path issue   [`24494a1`](https://bitbucket.org/aiodintech/wlsapi/commits/24494a11282dfea1839d49c74aad37fc55e5fe9e)
-  i18next plugin unit test issue   [`6ba4d31`](https://bitbucket.org/aiodintech/wlsapi/commits/6ba4d31713a74a58938584a4c28e5c3dba288136)
-  change coerceTypes option to false in Ajv configuration   [`a2f3ee2`](https://bitbucket.org/aiodintech/wlsapi/commits/a2f3ee23c273b60384860bee1a9afedd9b505c16)
-  add exchangeRate as a required property in update schema   [`851f620`](https://bitbucket.org/aiodintech/wlsapi/commits/851f620e5e10527676729ab0495ec3b9a1f2d255)
-  fix env.example mongo root username different docker setup username   [`638f04f`](https://bitbucket.org/aiodintech/wlsapi/commits/638f04f2ee35f9977184a59c11b25a4b17a7df82)
-  update import path for graceful shutdown plugin in unit tests   [`5f19778`](https://bitbucket.org/aiodintech/wlsapi/commits/5f19778c13149c49a3b917127949912c4ce8abb9)
-  remove todo code merged from dev branch   [`c8cb364`](https://bitbucket.org/aiodintech/wlsapi/commits/c8cb364cb89e85b37f772248fecb7a2850434cbd)
-  remove conflict code   [`8c0b1ed`](https://bitbucket.org/aiodintech/wlsapi/commits/8c0b1ed9321e0537ab83deddea3099897df8296d)
-  remove conflict code   [`771bdff`](https://bitbucket.org/aiodintech/wlsapi/commits/771bdff5c88a1b8f5c9dbc759b8811adb69eec42)
-  fix issue where not all fields are properly populated in ELK for application logs   [`586ec25`](https://bitbucket.org/aiodintech/wlsapi/commits/586ec25978ae3449041ced894bb7e9a0bce0c639)
-  fix sequelize db connection dialect   [`b13a957`](https://bitbucket.org/aiodintech/wlsapi/commits/b13a9571e75127e6f772388237b7208365655674)
-  update swagger URL to use DOCKER_PORT and fallback error code in onError hook   [`e810c71`](https://bitbucket.org/aiodintech/wlsapi/commits/e810c71d9b6e7e160f7d324dc3dc52bbaabf2998)
-  fix conflict code   [`0ac6f63`](https://bitbucket.org/aiodintech/wlsapi/commits/0ac6f63ba093b7c22d45482f25e2d336cee5207f)
-  fix missing line in docker compose   [`2ed0b4f`](https://bitbucket.org/aiodintech/wlsapi/commits/2ed0b4f418f26d69b73bcf686c887d2351b3a026)
-  fix conflict code   [`0cd1907`](https://bitbucket.org/aiodintech/wlsapi/commits/0cd190780223aecabbe3af532b2def95920b7fae)
-  fix typo on imported file path during merge conflicts   [`ed3faed`](https://bitbucket.org/aiodintech/wlsapi/commits/ed3faedb0012da44bfe885836ecf41aded2bd3f9)
-  change prettier file path   [`0ff0378`](https://bitbucket.org/aiodintech/wlsapi/commits/0ff03780cb8b3e4d9cf1706a57e4467adb832f7f)
-  refinement on the feature   [`775af16`](https://bitbucket.org/aiodintech/wlsapi/commits/775af16283eb3ef2af1357260697f85bf005d92c)
-  change to use response util in for error response   [`a638d15`](https://bitbucket.org/aiodintech/wlsapi/commits/a638d15391d3ebf95573b800f9ffec3f53c0f2f7)
-  fix log level issue   [`1ad0b4d`](https://bitbucket.org/aiodintech/wlsapi/commits/1ad0b4df9675c4dc246e7247c772852641125903)
-  fix format of fastify postgres model instant   [`4643388`](https://bitbucket.org/aiodintech/wlsapi/commits/4643388964d4f2301fb43010be11f3382caa205c)
-  email to name in user schema properties   [`f6bc662`](https://bitbucket.org/aiodintech/wlsapi/commits/f6bc6623baf9ed65cfa723407ed47c97a105e099)
-  fix swagger properties object issue   [`0e91207`](https://bitbucket.org/aiodintech/wlsapi/commits/0e91207bbf8aaa4df3d3ee1e0f64eaf44cda266d)
-  fix user schema missing name in findAll api   [`cfdaef9`](https://bitbucket.org/aiodintech/wlsapi/commits/cfdaef93d6c6466bb24a43599b592e607f54bcf2)
-  fix user schema missing name in response properties   [`7648bb3`](https://bitbucket.org/aiodintech/wlsapi/commits/7648bb3598c3f32d765bcbf5ca7dd7f77b3e530e)
-  refine logging behavior and code   [`be67af0`](https://bitbucket.org/aiodintech/wlsapi/commits/be67af00843dccb670315691362202f494ae9901)
-  refinement on error logging and handling   [`3f39401`](https://bitbucket.org/aiodintech/wlsapi/commits/3f394011ddecc663dbfe6c4b9d276334365fc90f)
-  fix elk-config permission issue with non-root user   [`14035c0`](https://bitbucket.org/aiodintech/wlsapi/commits/14035c0b57525cd2143331dc2659da6d0c666646)
-  initialize sentry in seperate file and add sentry profiling   [`80d1955`](https://bitbucket.org/aiodintech/wlsapi/commits/80d1955f978c9b3c1121449ffec3eabbf57798f3)
-  prevent unintended script execution by enforcing --ignore-scripts in Dockerfile   [`f72aca7`](https://bitbucket.org/aiodintech/wlsapi/commits/f72aca7f35942d1671c47e49366887062a8b6a21)
-  fix image not found after built   [`af778a3`](https://bitbucket.org/aiodintech/wlsapi/commits/af778a38b8c457d0b29d7f1bdf3ee1124ab21d66)
-  fix invalid reference format   [`060d846`](https://bitbucket.org/aiodintech/wlsapi/commits/060d8466d4aaf157623abe7484c801f10fa2b680)
-  try to fix image not found error   [`5995baa`](https://bitbucket.org/aiodintech/wlsapi/commits/5995baa6df17e4ca8b007530300004f7b3785106)
-  try to fix unknown shorthand flag: 't' in -t error   [`cf86ba1`](https://bitbucket.org/aiodintech/wlsapi/commits/cf86ba1d3851bd32b2c948c89ee20ba284b841a8)
-  try to fix the error authorization denied by plugin pipelines   [`cc0fdec`](https://bitbucket.org/aiodintech/wlsapi/commits/cc0fdecd9fc3c63f8d87818a32e3bf375cd95a60)
-  try to fix the error authorization denied by plugin pipelines   [`f02b09a`](https://bitbucket.org/aiodintech/wlsapi/commits/f02b09a56c42bb0b90a8f7b17eb7f908a6364498)

#### Chores & Housekeeping

-  cleanup unused code and update redis connection   [`e00a2b4`](https://bitbucket.org/aiodintech/wlsapi/commits/e00a2b4c6e3c3b25eea1e480820eb96fb60bbd3c)
-  add dotstrings   [`d337047`](https://bitbucket.org/aiodintech/wlsapi/commits/d337047555571a8c4267f6571eefc646be715cab)
-  parking   [`2427a7a`](https://bitbucket.org/aiodintech/wlsapi/commits/2427a7a10981892d97c04c8093a83f3e48cfc0ca)
-  update the test after merged the dev branch   [`b4aa7a1`](https://bitbucket.org/aiodintech/wlsapi/commits/b4aa7a1ccd584e46c24457d7a3fa60d6eed76d43)
-  remove unused environment variables   [`f91f93b`](https://bitbucket.org/aiodintech/wlsapi/commits/f91f93be6c183160381e0717fe81679928604407)
-  fix conflict code issue   [`9eb1b28`](https://bitbucket.org/aiodintech/wlsapi/commits/9eb1b28087242103fbbb58eea8cec8a3cb6571fd)
-  minor update   [`5799057`](https://bitbucket.org/aiodintech/wlsapi/commits/579905722b56aab1cfe67b20f06172845dffdfd6)
-  minor update   [`5b42763`](https://bitbucket.org/aiodintech/wlsapi/commits/5b4276319bd4e144b8e4126fbfc470f9dc028b98)
-  minor update   [`7863d69`](https://bitbucket.org/aiodintech/wlsapi/commits/7863d69c616dfb7e466b025b0c4a0b56270b99a0)
-  add code to skeleton code   [`f4964d7`](https://bitbucket.org/aiodintech/wlsapi/commits/f4964d7d85e3b5683ae1b47a8b112747fbafc4e2)
-  remove unused code   [`bbd719a`](https://bitbucket.org/aiodintech/wlsapi/commits/bbd719a62ff1cdb9dc10920dc5f52bc404d40f5d)
-  update typesense api key and remove auto restart for filebeat   [`44745a3`](https://bitbucket.org/aiodintech/wlsapi/commits/44745a3be15068d39488e9e65cb62196cadbcda3)
-  fix conflict issue   [`04b5ec8`](https://bitbucket.org/aiodintech/wlsapi/commits/04b5ec8247ce4a80dd280f11f1780ffe47faeccb)
-  update skeleton code message   [`c17ecf9`](https://bitbucket.org/aiodintech/wlsapi/commits/c17ecf9b7e805a1919ae8b949144f03df98f6d8d)
-  put back todo comment from dev branch   [`65624c8`](https://bitbucket.org/aiodintech/wlsapi/commits/65624c8b1d73f62f783daef1aa7c6a40aa939eb6)
-  add missing volumes field and remove filebeat image field from compose file   [`2a28420`](https://bitbucket.org/aiodintech/wlsapi/commits/2a28420ee72866a0c18391a608fcd546fc46358c)
-  fine tune code and configurations   [`e4efe9f`](https://bitbucket.org/aiodintech/wlsapi/commits/e4efe9fe6152c25815107c6193219fc8b3691a21)
-  update sonar project key   [`7e92020`](https://bitbucket.org/aiodintech/wlsapi/commits/7e92020364750411dbfd1f334ae4060ae67d6cda)
-  update elastic stack version to 8.16.1   [`bde4a09`](https://bitbucket.org/aiodintech/wlsapi/commits/bde4a09f3906e5159d07da0fcc445884b3609c1e)
-  remove deprecated pino-multi-stream package and use multistream in core Pino   [`7972851`](https://bitbucket.org/aiodintech/wlsapi/commits/7972851462ae7a4fc89261d7a124f2160f37f8f2)
-  update user service docstring and remove unused code   [`94741a4`](https://bitbucket.org/aiodintech/wlsapi/commits/94741a4944e44b24dc1076515d45e4d2a7af494a)
-  remove metribeat   [`2267f56`](https://bitbucket.org/aiodintech/wlsapi/commits/2267f56fd55fb61b22ed5105d3f0380c16f73750)
-  implement lint-staged for pre-commit checks   [`16f8918`](https://bitbucket.org/aiodintech/wlsapi/commits/16f89187a061c78ca8faac457849ff8f9713ded2)
-  refactor route js   [`2c48851`](https://bitbucket.org/aiodintech/wlsapi/commits/2c48851d8119e6b53cea3c00180e60760ad72301)
-  kafka setup with typesense and upsert example   [`d7182cc`](https://bitbucket.org/aiodintech/wlsapi/commits/d7182cccc64e44cd5042f6948b9d401644dea986)
-  housekeep environment variables   [`64adbe1`](https://bitbucket.org/aiodintech/wlsapi/commits/64adbe174911ebf4d73ee553d422aa26eb279b18)
-  remove unnecessary codes   [`5101f19`](https://bitbucket.org/aiodintech/wlsapi/commits/5101f19b03f2436816cbd62f720e998e63a6cbfd)
-  change import path   [`9a05c3b`](https://bitbucket.org/aiodintech/wlsapi/commits/9a05c3b5e9c9e0df6688354f3eb89a3e1a0bc996)
-  housekeep configuration files   [`d4687fb`](https://bitbucket.org/aiodintech/wlsapi/commits/d4687fbbaac8af8d57b1c64e7d29b010d439852f)
-  revert bitbucket-pipelines.yml   [`caf2da2`](https://bitbucket.org/aiodintech/wlsapi/commits/caf2da236706eeee6299b94e698dcbcd3d9d6273)
-  revert sonarqube directories   [`b00eaf4`](https://bitbucket.org/aiodintech/wlsapi/commits/b00eaf481169cc60b2a1e5f9deebb2a0dc57c80e)
-  temporary hardcode eslint version to 9.14 to prevent error   [`32cb6d3`](https://bitbucket.org/aiodintech/wlsapi/commits/32cb6d3029521d30e2584187ecfeb8bda048eaae)
-  standardize folder paths and set up the configuration for the coverage report   [`b641746`](https://bitbucket.org/aiodintech/wlsapi/commits/b641746b640166192c8dc54cf39ad6d29f36aab8)
-  remove migration   [`a9a50ca`](https://bitbucket.org/aiodintech/wlsapi/commits/a9a50cad90da7f635acaca50258bca8f0b85bd4b)
-  remove unused compose.staging.yaml file   [`29b264b`](https://bitbucket.org/aiodintech/wlsapi/commits/29b264b1621998fae1b3a190cb6ac82032121a9b)
-  minor code refactor to use fastify decorator   [`ab424bb`](https://bitbucket.org/aiodintech/wlsapi/commits/ab424bbb7f30b6ac33831cafb6e9f86453532146)
-  add lcov reporter type to vitest for sonarqube purpose   [`0c584c4`](https://bitbucket.org/aiodintech/wlsapi/commits/0c584c47ef320de8acb16eb697992968672340bd)
-  remove unuse package   [`020aefd`](https://bitbucket.org/aiodintech/wlsapi/commits/020aefd2f2c1e05000946c036debaf9bfa30c4fd)
-  add staging compose file and configure Bitbucket pipeline for staging builds   [`26c4c67`](https://bitbucket.org/aiodintech/wlsapi/commits/26c4c6719736adf562ba1f3611f8812a941d4a32)
-  add services and update view user test   [`b847076`](https://bitbucket.org/aiodintech/wlsapi/commits/b84707655fc49fc3589fc6e474ffe6a2d20432a5)
-  add mongodb package and configs and plugins   [`c8a6660`](https://bitbucket.org/aiodintech/wlsapi/commits/c8a6660d6e6c72444dc2c1f9dd61bd46794e402a)
-  missing ECR Repo URL on tag   [`f86df27`](https://bitbucket.org/aiodintech/wlsapi/commits/f86df2710528bbfd91e27ecc2b450d1a15a43fde)
-  housekeep bitbucket-pipelines.yml and fix image not found error   [`7513b20`](https://bitbucket.org/aiodintech/wlsapi/commits/7513b2003f6cf5967c6f5d14b68e13fcada36b07)
-  update bitbucket pipeline script to use pipe to push image to ECR   [`41d4919`](https://bitbucket.org/aiodintech/wlsapi/commits/41d49195d3993a5076cd744223dafedcd608a773)
-  update bitbucket-pipelines.yml to list images, tag and push each service to ECR   [`5f8b533`](https://bitbucket.org/aiodintech/wlsapi/commits/5f8b533bb5e6f5b193b193861aa71a5f27fef227)
-  remove env_file in compose.staging.yaml   [`8c15f7f`](https://bitbucket.org/aiodintech/wlsapi/commits/8c15f7f7959c9ad2579f257ab15d72f81620fa15)
-  remove migration commands   [`9b915cf`](https://bitbucket.org/aiodintech/wlsapi/commits/9b915cf75ef86318606c114c80f4877b6deff97e)
-  revert sonar-project.properties   [`c1b727c`](https://bitbucket.org/aiodintech/wlsapi/commits/c1b727ce78c1ebe367660d5f687e5a4f186c438d)
-  change eslint to 9.13   [`df5bcc1`](https://bitbucket.org/aiodintech/wlsapi/commits/df5bcc13903a1c4b5a5e98d041f5cab7ffd247bf)
-  onSend to onResponse   [`f262a96`](https://bitbucket.org/aiodintech/wlsapi/commits/f262a9668ce79c1f313fd3997248544ac09f1acc)
-  miss out one env_file to remove   [`1aeb545`](https://bitbucket.org/aiodintech/wlsapi/commits/1aeb54524fdcb488c0e8079dba53ea2f05220b77)
-  update docker compose build command with specify image name   [`70c0f33`](https://bitbucket.org/aiodintech/wlsapi/commits/70c0f33dc9f05b201962301a1c0550a2e3e520ce)
-  remove redundant line   [`2c47bdf`](https://bitbucket.org/aiodintech/wlsapi/commits/2c47bdf4ae216d2b295468eaf2376f0d2f0b2c7e)

#### Documentation Changes

-  update changelog for v0.0.4   [`48b6109`](https://bitbucket.org/aiodintech/wlsapi/commits/48b61092d6e10155388e9b86ad4d1f938904f120)
-  generate CHANGELOG.md v0.0.5   [`c4daeba`](https://bitbucket.org/aiodintech/wlsapi/commits/c4daeba5cdf4dffa98936e9a5ca1236db617d62c)
-  add typesense explanation into readme   [`7182ce3`](https://bitbucket.org/aiodintech/wlsapi/commits/7182ce377af3a1cc35378e97b1b022c9b79f4079)
-  update README with new docker install instruction for core services   [`72d1c44`](https://bitbucket.org/aiodintech/wlsapi/commits/72d1c44ce90cc20b7381ee84911744757b942830)
-  update README formatting and indentation for steps part   [`f9ba3b8`](https://bitbucket.org/aiodintech/wlsapi/commits/f9ba3b842e6df87ab259026dd25a345c6b8fac0c)
-  update README with new env DOCKER_PORT   [`88f8e7f`](https://bitbucket.org/aiodintech/wlsapi/commits/88f8e7f0f960614809aac026fb2f7970922946fa)
-  update changelog v0.0.3   [`e495fa7`](https://bitbucket.org/aiodintech/wlsapi/commits/e495fa7292437177c8db7f92d3921adae4562452)
-  update upload route with swagger docs   [`ee9434f`](https://bitbucket.org/aiodintech/wlsapi/commits/ee9434ffe7a5e32cc3dc650c2118da47ed4fe7a1)

#### Refactoring and Updates

-  remove deprecated unit tests   [`f141ce4`](https://bitbucket.org/aiodintech/wlsapi/commits/f141ce41d01405025d7935cd42d92f25ce565cd7)
-  migration & seeder   [`9c85b28`](https://bitbucket.org/aiodintech/wlsapi/commits/9c85b28ebc4f9107ad48098e4cc5de35557f880f)
-  remove deprecated example tests   [`cabab6a`](https://bitbucket.org/aiodintech/wlsapi/commits/cabab6a31dce1dc91cf24c0946299c5a649d5ffa)
-  localisation and settings module code structure   [`a36ce16`](https://bitbucket.org/aiodintech/wlsapi/commits/a36ce16b872ced00e020428cac17cf5cb17c9b0d)
-  restructure developer hub module   [`b4eeeb1`](https://bitbucket.org/aiodintech/wlsapi/commits/b4eeeb160b418842c35ded7f3d3cfd924f189511)
-  refactor response success message and version control   [`4ea1a90`](https://bitbucket.org/aiodintech/wlsapi/commits/4ea1a90d28ebef8d106f872b6acd3ab87d0847a4)
-  remove deprecated example routes and schemas   [`c54671e`](https://bitbucket.org/aiodintech/wlsapi/commits/c54671e260a0ba50dcfb6d3982a9a0bfb7a5c653)
-  remove unused files and clean up configuration settings   [`a0a912a`](https://bitbucket.org/aiodintech/wlsapi/commits/a0a912ac5ff08e0350569e42b3d3ee6c8af838e8)
-  update ENUM types and improve constraints in migration files   [`4f96523`](https://bitbucket.org/aiodintech/wlsapi/commits/4f9652339a2e772ae78e6bf959864b10440f96c3)
-  restructure developer hub and access control   [`fbb60d9`](https://bitbucket.org/aiodintech/wlsapi/commits/fbb60d9813e451ac0b58223f42f3678490710ddd)
-  restructure access control module   [`fcfdda7`](https://bitbucket.org/aiodintech/wlsapi/commits/fcfdda72b3c7a294eb3f4d38212b673615c6bf71)
-  separate settings seeder into localisation and system settings   [`32ea1c5`](https://bitbucket.org/aiodintech/wlsapi/commits/32ea1c505950a6708b49b784bb6bba0e43238dc4)
-  update settings migration, enhance localisation and setting routes, and improve setting schema with custom fields   [`5ba12e0`](https://bitbucket.org/aiodintech/wlsapi/commits/5ba12e0c3d579427fb32fdcf9a446cd581d75246)
-  restructure localisation & setting   [`44c3027`](https://bitbucket.org/aiodintech/wlsapi/commits/44c3027f67968cc1b7cb5564c20c61d90a5fdfad)
-  enhance localisation handling with caching, improve response management, and update logging levels   [`795df13`](https://bitbucket.org/aiodintech/wlsapi/commits/795df13e1a24ee2562c7f5c81edd3e1374044859)
-  update ENUM values and enhance model associations and validations   [`f75b7ad`](https://bitbucket.org/aiodintech/wlsapi/commits/f75b7ad4d97d617cc422491c9c0baa513662bc30)
-  remove unused file   [`935cf6e`](https://bitbucket.org/aiodintech/wlsapi/commits/935cf6ef783b42add7cf93443d6f6d8045021d4b)
-  refactor typesense plugin to apply database factory and write unit test   [`c71863c`](https://bitbucket.org/aiodintech/wlsapi/commits/c71863c8f5173e8fe8cbebb2c0a73ee5ea34bc49)
-  refactor cache util and generate redis plugin unit test   [`8a63471`](https://bitbucket.org/aiodintech/wlsapi/commits/8a634710778dd6ca731c9847f67ee71ef4f52418)
-  refactor developer hub & access control create return data   [`7aad571`](https://bitbucket.org/aiodintech/wlsapi/commits/7aad57135ef7445915156d6545a1ac8deaefddf5)
-  restructure schemas constants   [`6e8d28c`](https://bitbucket.org/aiodintech/wlsapi/commits/6e8d28ccdb97cea1502b08450d0ae682e557048e)
-  restructure localisation module   [`f594173`](https://bitbucket.org/aiodintech/wlsapi/commits/f594173cd44dbd7d895b40586d6385484f427578)
-  baseColumn helpers:   [`2c1cca6`](https://bitbucket.org/aiodintech/wlsapi/commits/2c1cca6fecf4f5c4dec532559ae2184cf7568420)
-  update pagination and error response schemas for consistency across modules   [`5ddf2af`](https://bitbucket.org/aiodintech/wlsapi/commits/5ddf2af00e9e9b7f3f1bfd2226657d53be9fb641)
-  add remarks migration   [`040ed9b`](https://bitbucket.org/aiodintech/wlsapi/commits/040ed9b0b2a65b6961d96ffe7f727e40c35764bc)
-  refactor user module to clearly separate concerns between handlers and services   [`da8ef8d`](https://bitbucket.org/aiodintech/wlsapi/commits/da8ef8d807720617c8f0468f8cd9935bc7a36df5)
-  restructure system setting module   [`f5a68b4`](https://bitbucket.org/aiodintech/wlsapi/commits/f5a68b400d68994f42adac9a7910608fa4a49054)
-  add config path for maintainability & install depcheck to check unused packages   [`fa98099`](https://bitbucket.org/aiodintech/wlsapi/commits/fa980993018560e0b7763337db8576be45ff59c6)
-  update import paths and improve error handling in utilities   [`642f05a`](https://bitbucket.org/aiodintech/wlsapi/commits/642f05afc3a014cb5422953e67f5a9222b533449)
-  update constants import paths and enhance remark model with new properties   [`23827db`](https://bitbucket.org/aiodintech/wlsapi/commits/23827db9321c4782817af6ec6146559a2a2607f3)
-  standardize request, reply, and error variables   [`dabbaae`](https://bitbucket.org/aiodintech/wlsapi/commits/dabbaae5f4dd17b9eb01fb8e258697706c683572)
-  standardize UUID generation and improve base column options   [`f1cee2b`](https://bitbucket.org/aiodintech/wlsapi/commits/f1cee2b7eb713984061285798b4552d343f615eb)
-  clean up import paths and improve SQL query formatting in migration files   [`e661919`](https://bitbucket.org/aiodintech/wlsapi/commits/e661919b0a6d87a903cf068ceb3266b713110204)
-  refactor serializer util for better readable   [`cc0b6e4`](https://bitbucket.org/aiodintech/wlsapi/commits/cc0b6e42ff103efe2f0a53de14d9501020d6a6d9)
-  reorganize constants and implement remark repository with CRUD operations   [`62fcb3d`](https://bitbucket.org/aiodintech/wlsapi/commits/62fcb3d0447a6959d8269a728fb5c52e5f4ae9ad)
-  update `Swagger` configuration, enhance pagination and  success message   [`232fe44`](https://bitbucket.org/aiodintech/wlsapi/commits/232fe44f6d075b686fe961682587a17ba56738b8)
-  enhance base column options with better structure and documentation   [`7b162d7`](https://bitbucket.org/aiodintech/wlsapi/commits/7b162d70c0d5494a14ac60007503ef3c22e7b836)
-  restructure localisation & setting constants   [`0851b5c`](https://bitbucket.org/aiodintech/wlsapi/commits/0851b5c4b5c9c1434ef6e38c8d86dbf0dc9a7cc1)
-  error code   [`94847bc`](https://bitbucket.org/aiodintech/wlsapi/commits/94847bc8bb5d6737097639c9c381a80c9163703a)
-  rename route prefix and update docker container name use variable   [`325aae8`](https://bitbucket.org/aiodintech/wlsapi/commits/325aae85280b4c16d6da303b026e04d5c09a0057)
-  remove unused route imports and clean up route groups   [`bc41917`](https://bitbucket.org/aiodintech/wlsapi/commits/bc419176b9abb55a327434898e4755ce784a3e67)
-  update `toJSON` method to use customLocalisation values and enhance findById error handling   [`8c0d38f`](https://bitbucket.org/aiodintech/wlsapi/commits/8c0d38ff327071c083b4fd7a4d4aff2c59f48660)
-  rename sequezlie to dbInstance   [`52ecd90`](https://bitbucket.org/aiodintech/wlsapi/commits/52ecd9089e40265d3cc2ba26fa82d039ce140bbb)
-  replace withSoftDelete option with withParanoid in migration files   [`73af4f9`](https://bitbucket.org/aiodintech/wlsapi/commits/73af4f9ce72bd3f7fe8aac4c4aa8da6c6e4161e2)
-  move helper file to util   [`b9b3e2b`](https://bitbucket.org/aiodintech/wlsapi/commits/b9b3e2b2fb2ae7455a880fbcb6a2cd51c0fa2378)
-  refactor the dninstance more readable and update the database factor unit test   [`22625ab`](https://bitbucket.org/aiodintech/wlsapi/commits/22625ab800c752f07ace0fba98501057afc13fee)
-  add header-max-length rule to commitlint configuration   [`d554762`](https://bitbucket.org/aiodintech/wlsapi/commits/d554762d8412f7dce01ab93655cb55daf94f09ea)
-  create database factory and model loader   [`c57bd99`](https://bitbucket.org/aiodintech/wlsapi/commits/c57bd99d9c9229c2b6af030651e18a01dfc6eded)
-  database factory with load model helper   [`5bf9923`](https://bitbucket.org/aiodintech/wlsapi/commits/5bf9923a0aa4596ac6fcc35280184af0e6d0f3ad)
-  separate elastic stack into a new repository and housekeep environment variables   [`75ee8e1`](https://bitbucket.org/aiodintech/wlsapi/commits/75ee8e13e8b035565ec0d40953a0725d3368647c)
-  factory structure   [`9484431`](https://bitbucket.org/aiodintech/wlsapi/commits/9484431b07fbd9e20d592d5d13245dfa66034ab3)
-  remove testing code   [`aef7cb4`](https://bitbucket.org/aiodintech/wlsapi/commits/aef7cb481a3bff93bf15554335c5121056954869)
-  remove unnecessary log data utility function and unit test   [`6e13051`](https://bitbucket.org/aiodintech/wlsapi/commits/6e13051770a612f4d433d1d76c1bbf037ed8a137)
-  rename mongo db env   [`41e2b54`](https://bitbucket.org/aiodintech/wlsapi/commits/41e2b54cb17041aeec16b68984a9fef267cd9d39)
-  minor update   [`15fec35`](https://bitbucket.org/aiodintech/wlsapi/commits/15fec35525f5758db3540c9464ed4aaf8a751eea)
-  add tag to swagger config   [`46f5ae2`](https://bitbucket.org/aiodintech/wlsapi/commits/46f5ae26fe485027e65992aae37731ac8a08c612)
-  remove composer create mongo user file   [`4277266`](https://bitbucket.org/aiodintech/wlsapi/commits/4277266b69ca34cac4de6add9234cce209b64227)
-  add throw exception if didn't implement required function   [`45911b9`](https://bitbucket.org/aiodintech/wlsapi/commits/45911b9f72445ad7b2bec628558969e6eb4b0d40)
-  update base url env use docker and README typo   [`361b713`](https://bitbucket.org/aiodintech/wlsapi/commits/361b713e144f3fbe4d6a43b6d2c005c9c8ba4e16)
-  remove audit-log example   [`c149891`](https://bitbucket.org/aiodintech/wlsapi/commits/c14989167549ab1cffcc7c49b03caa2b5d124638)
-  refactor typesense example   [`ec796be`](https://bitbucket.org/aiodintech/wlsapi/commits/ec796be357942bf540c58587b899b20e756df96d)
-  add mongodb example   [`a00dae1`](https://bitbucket.org/aiodintech/wlsapi/commits/a00dae1f66c764e4b2a0ee3979ea8df3059db61c)
-  remove kafka setup because it will do in another services   [`9445d74`](https://bitbucket.org/aiodintech/wlsapi/commits/9445d74a20bbac1b5ca677a926e22774a627bc1d)
-  refactor route js   [`d379e05`](https://bitbucket.org/aiodintech/wlsapi/commits/d379e05f961eafc129f8e586968f5243a908f1d4)
-  optimize sequelize decorator with connection and models with 'psql'   [`dcbb5c8`](https://bitbucket.org/aiodintech/wlsapi/commits/dcbb5c8a518079b9f928181fa94a8d62939e2ad3)
-  change all function to arrow function and import file with '#'   [`e610b57`](https://bitbucket.org/aiodintech/wlsapi/commits/e610b576b5e7d9cf904f68b98a5c97e7a6b0131a)
-  refactor example route folder   [`0044c62`](https://bitbucket.org/aiodintech/wlsapi/commits/0044c62304c97af6d6933811a25fbfb235f696ca)
-  file renaming and modify import path   [`4d8fb14`](https://bitbucket.org/aiodintech/wlsapi/commits/4d8fb14dcccf5b68f0d2233acd3dd04a81dc5277)
-  refactor route cache setup folder   [`c5d3b45`](https://bitbucket.org/aiodintech/wlsapi/commits/c5d3b45ba91e3f2f9b32a3a6ad7c8cc963621c27)
-  optimize mongo decorator with connection and models with 'mongo'   [`9e4df5f`](https://bitbucket.org/aiodintech/wlsapi/commits/9e4df5f06a4fdf4e77bd7fc5bdd2137d47153f75)
-  update get hooks to arrow function   [`d28b11c`](https://bitbucket.org/aiodintech/wlsapi/commits/d28b11cf5c746a04674bdf5568329b778e3a3d14)
-  streamline paths, standardize response schemas, and unify routes   [`c4a8025`](https://bitbucket.org/aiodintech/wlsapi/commits/c4a8025e75e8216dbd7b5364d99c2d12779d847e)
-  standardize log format using the pino serializer   [`d00eb70`](https://bitbucket.org/aiodintech/wlsapi/commits/d00eb70cf7d829be9c7eb5ff87589c5efe10ae71)
-  migrate to file utility and request/response plugins   [`ca33443`](https://bitbucket.org/aiodintech/wlsapi/commits/ca33443a9bda9c78bc997e81b2880f5c36bf3d39)
-  resolved comments   [`400d959`](https://bitbucket.org/aiodintech/wlsapi/commits/400d959ffb074b50e132d4ea70eb0e9e476e8881)
-  change read file to use utils and change to arrow functions   [`986c562`](https://bitbucket.org/aiodintech/wlsapi/commits/986c56211f4c0381eb2fa066f9069d8ff6c74981)
-  fine tune logstash filter, log format, serializer and logger config   [`2ac4cf4`](https://bitbucket.org/aiodintech/wlsapi/commits/2ac4cf4481cfd438398c3c1953dccad62cfc4adc)
-  resolved comments   [`b31cc80`](https://bitbucket.org/aiodintech/wlsapi/commits/b31cc80db5aa0a3887f0d346b9ee596216fb6ac1)
-  change gracefulShutdownPlugin and sentryPlugin to arrow function   [`99a6df6`](https://bitbucket.org/aiodintech/wlsapi/commits/99a6df6ace399bc9b2c73d241e2555b0fd5b5a7d)
-  change all function to arrow function and remove merged used code   [`457e123`](https://bitbucket.org/aiodintech/wlsapi/commits/457e1237879c188d2f171a163e42770b58bc6cfc)
-  rearrange elk-config docker files   [`0e8e15e`](https://bitbucket.org/aiodintech/wlsapi/commits/0e8e15e9f7e6c36edc42f4b61205a0d672a4596d)
-  refactor fastify decorator of sequelize and mongo connection & models   [`2a0c5b2`](https://bitbucket.org/aiodintech/wlsapi/commits/2a0c5b21668559558bc658ce425fac425bb210c0)
-  fix integration test with model function that using fastify decorator pattern   [`ecebd72`](https://bitbucket.org/aiodintech/wlsapi/commits/ecebd727f4e7635b5be8c2c09c8a53604a4fbdab)
-  add fastify instant into request then able to do request.fastify.User   [`a491857`](https://bitbucket.org/aiodintech/wlsapi/commits/a49185786a23afa5cbc2717a7e90aff19f7e0ee7)
-  remove integration test and remove docker up & down during run test   [`cffc94d`](https://bitbucket.org/aiodintech/wlsapi/commits/cffc94dfe2ac66eeead30e6611a07a6692333dd0)

#### Changes to Test Assests

-  unit tests for localisation, settings, access control, developer hub   [`ce83e66`](https://bitbucket.org/aiodintech/wlsapi/commits/ce83e6641725fb7c33e60c7cfa1275befe899680)
-  plugin unit tests   [`36b11ab`](https://bitbucket.org/aiodintech/wlsapi/commits/36b11ab9fccd1c3eca3830c1b0ab68ab7fb80eac)
-  update unit tests of modules   [`c031de3`](https://bitbucket.org/aiodintech/wlsapi/commits/c031de3c73dd94e457e14cc2333408cb1cecfe0f)
-  update unit tests of modules   [`c013d8a`](https://bitbucket.org/aiodintech/wlsapi/commits/c013d8aa685b47842ee8bb285f34416d6d1377e0)
-  plugin unit test   [`8a4c07b`](https://bitbucket.org/aiodintech/wlsapi/commits/8a4c07b2b92fc93195571c10e7c81c1b30406b73)
-  add unit tests for various plugins and improve plugin registration logic   [`2a84ec7`](https://bitbucket.org/aiodintech/wlsapi/commits/2a84ec7517020de729aede4a7be30911446ed491)
-  test typesense unit test   [`63631f0`](https://bitbucket.org/aiodintech/wlsapi/commits/63631f0dc02f0049d8ec0582b1e0833228e30e02)
-  add unit tests for app initialization and plugin registration   [`91ceb85`](https://bitbucket.org/aiodintech/wlsapi/commits/91ceb85c1018a20eee66bc6eb624583552f1df42)
-  add unit test for custom errors of user module and refine import alias   [`2f9737e`](https://bitbucket.org/aiodintech/wlsapi/commits/2f9737e06a24728040422cbdfd355c1f01ba8721)
-  i18next plugin unit test issue   [`05ca376`](https://bitbucket.org/aiodintech/wlsapi/commits/05ca376e79352086272151dc40af612ec99d65b4)
-  conflict code issue   [`e6f8cdb`](https://bitbucket.org/aiodintech/wlsapi/commits/e6f8cdb4105aae5c4470a5ef1f5a2602a96d1c36)
-  add unit test for modules   [`ffd9b55`](https://bitbucket.org/aiodintech/wlsapi/commits/ffd9b55b60ed1d0aa6c6607aba0e4a88eee95b07)
-  plugin unit test   [`3be8f1c`](https://bitbucket.org/aiodintech/wlsapi/commits/3be8f1c5ff8dadefd5c1c550fab1e3055570081e)
-  add example route unit test   [`5059b70`](https://bitbucket.org/aiodintech/wlsapi/commits/5059b70b1b8e26b9e1c3bb059a215d3fe09f2d05)
-  add module unit test   [`0e233a2`](https://bitbucket.org/aiodintech/wlsapi/commits/0e233a23586051f333c0a9b222736e24a0c5dcf8)
-  add unit test for cache   [`2662199`](https://bitbucket.org/aiodintech/wlsapi/commits/26621990ca07f3f159c910a15b8811634a2d6ddb)
-  plugin unit test   [`35e7d99`](https://bitbucket.org/aiodintech/wlsapi/commits/35e7d9991a36fdd5c08e3dd9166e353860fdb7c4)
-  exclude plugins from coverage   [`c191e74`](https://bitbucket.org/aiodintech/wlsapi/commits/c191e74e864094de0f0a2050bb8a9b4f315e3311)
-  fix index js to increase test coverage   [`22a4c39`](https://bitbucket.org/aiodintech/wlsapi/commits/22a4c3991117bf3fc04672a90e40711b90ee9ca5)
-  fix example route import   [`630934d`](https://bitbucket.org/aiodintech/wlsapi/commits/630934d49673133301bb97e74b5e9e9827cda4df)
-  update util unit test   [`04574e9`](https://bitbucket.org/aiodintech/wlsapi/commits/04574e97649705525bc62e8a8d39414f5916d179)
-  swagger test done, typesense still in-progress   [`44b57ec`](https://bitbucket.org/aiodintech/wlsapi/commits/44b57ec98f92ee0460789d92c55dec78fa6f670d)
-  combine deploy and watchtower step due to restriction on bitbucket   [`5a7d818`](https://bitbucket.org/aiodintech/wlsapi/commits/5a7d818ea3eebdbd81e31577c804e900b13c117a)
-  test PR for ECR image auto-update with watchtower   [`facad97`](https://bitbucket.org/aiodintech/wlsapi/commits/facad97d0f3e97602174052706ac502ff10620fb)
-  test inject env through bitbucket pipelines   [`2f81be4`](https://bitbucket.org/aiodintech/wlsapi/commits/2f81be4893ea239b0699a58b1f69766d59daad8f)
-  utils unit test   [`e7daf39`](https://bitbucket.org/aiodintech/wlsapi/commits/e7daf39a2fd371406acf9b44444db1441f02390c)
-  comment deployment line due to error   [`2d29f8b`](https://bitbucket.org/aiodintech/wlsapi/commits/2d29f8b9293b86ab8aebffd348b25aaf3a0ebc5a)
-  test sonarqube   [`af672fd`](https://bitbucket.org/aiodintech/wlsapi/commits/af672fd961d21398d9331b9c6ee2ae03cc6b16c6)
-  enable feature branch push ecr image for testing watchtower   [`a00ea48`](https://bitbucket.org/aiodintech/wlsapi/commits/a00ea48af0f02152861ef242dc348879ead9377d)
-  test chain step for building   [`63a1ad0`](https://bitbucket.org/aiodintech/wlsapi/commits/63a1ad0c777a233ed2158412c3ae06c51d606044)
-  test chain step for building 2   [`e2de2c5`](https://bitbucket.org/aiodintech/wlsapi/commits/e2de2c559f66e94a16ba065f7d86393ae23dfc6c)
-  test chain step for building 4   [`79e04da`](https://bitbucket.org/aiodintech/wlsapi/commits/79e04da61aad9c6f4f8781414549c0bf0474dc34)
-  test chain step for building 3   [`052f011`](https://bitbucket.org/aiodintech/wlsapi/commits/052f011ac6f975609d3d941f08b601bf84cddfb9)
-  test ecr image auto update   [`c016121`](https://bitbucket.org/aiodintech/wlsapi/commits/c016121b35ca15c58f943700acb55559fc60a04b)
-  remove sonarqube step (ecr image for testing watchtower)   [`9b272a5`](https://bitbucket.org/aiodintech/wlsapi/commits/9b272a502eaafb9548c68592ca1232552b72e466)
-  add service test example (provided by KY)   [`1bc61a9`](https://bitbucket.org/aiodintech/wlsapi/commits/1bc61a9149754ac3f5f6bbd4bfcec14167148ce9)
-  test migration and seeder with docker compose   [`13fbb28`](https://bitbucket.org/aiodintech/wlsapi/commits/13fbb28813a98e57f46b06464c2d532eefa06a27)
-  undo pipeline testing code   [`feea4a9`](https://bitbucket.org/aiodintech/wlsapi/commits/feea4a9b80e21561d73838b6ae64eaf8b7935aa4)
-  rearrange pipeline steps that move test after build and push docker image   [`cc3d3c8`](https://bitbucket.org/aiodintech/wlsapi/commits/cc3d3c8485dc26646e8d53af58c76680f4d7c0c8)
-  remove testing code   [`3d69451`](https://bitbucket.org/aiodintech/wlsapi/commits/3d69451fac483bd9765738091e203bd9e193c309)
-  change the sonarqube settings   [`6041e8a`](https://bitbucket.org/aiodintech/wlsapi/commits/6041e8ae29cc7d95d7ccaf5f85d4403afb5dc68c)
-  add service docker into test step   [`569896a`](https://bitbucket.org/aiodintech/wlsapi/commits/569896abb7439a6b2ff77f81382391ddaa5afd76)
-  add npm run migrate and npm run seed into dockerfile and set sonarqube coverage path   [`d183277`](https://bitbucket.org/aiodintech/wlsapi/commits/d1832774cdf85d66da44890049235c5881155977)
-  fix coverage folder not found issue on sonarqube   [`debb1b0`](https://bitbucket.org/aiodintech/wlsapi/commits/debb1b02a400e85d6432f55e7bb8a66deef2d134)
-  temp uncomment trigger feature push image   [`9b09a31`](https://bitbucket.org/aiodintech/wlsapi/commits/9b09a31dd53bb85d4e63dda9171dc30ef4aa37a4)
-  test push new image   [`1f27246`](https://bitbucket.org/aiodintech/wlsapi/commits/1f2724647083148db80571cb4181746263ace76c)
-  test push new image   [`1ae5754`](https://bitbucket.org/aiodintech/wlsapi/commits/1ae57547c05df397a137aa9e7feeaf7ff5ca7e53)

<!-- auto-changelog-above -->

### v0.0.5 

- Plugins Unit Test
- i18next plugin unit test issue
- Multi user for sequelize
- Localisation setup
- Log PostgreSQL Queries with ELK

#### New Features

-  localisation setup   [`63b583c`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/63b583c79ec232724c0e01d7a974adc33d785b4d)
-  localisation setup   [`bfa693c`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/bfa693c1178519075da70290158a647b1296548c)
-  enhance error serialization and handling   [`5c0fc8c`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5c0fc8c0c1fda701b0b3ad734bdda679a45223a9)
-  localisation setup   [`2f254ba`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2f254ba060677d31b254ea739954f2ebf2a5a7e8)
-  localisation setup   [`2993778`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/29937788a250474a4f2465a27a0093533246c9c2)
-  add support for logging Sequelize PostgreSQL queries to Elasticsearch   [`28a39a8`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/28a39a8d62c2823e9eef1a5fe284df7177ad2069)
-  enhance localization setup and update README   [`d653b76`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d653b764f594ce6697c72907dd5ad56d0f186bf1)
-  localisation setup   [`4a13c72`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/4a13c726317c6dc0315e5844d7220cda2ebd0964)
-  update typesense example (create collection, add document, find document)   [`7ef86a3`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/7ef86a360b055a133ec4abf66ce3ec5159d4ff13)
-  add config into .env and add plugin into fastify   [`fec05e0`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/fec05e0b3b7e32ae1f00e746a91d7e62ba76a465)
-  localisation setup   [`60c533e`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/60c533e1160c2f9b83fb6c4450432d5631fbdcd8)
-  add support for PostgreSQL read replicas in database factory   [`3c2c6aa`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/3c2c6aafc99913ed082449f94e0094186074fce4)
-  localisation setup   [`b478d4d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b478d4d4c553c046430100d6fe913e015a7dc068)
-  update .env.example and sequelize config for PostgreSQL user and logging   [`44a7c8b`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/44a7c8b947aa44b5271732d4c4fa3538cd25255e)
-  localisation setup   [`77a6449`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/77a6449f13ed79da3d8109e6671b30667364bfdf)
-  localisation setup   [`8b358db`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/8b358db4dbc5d8275f717249c0c8649ba65b0c05)
-  localisation setup   [`3a5866b`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/3a5866be62e9212c7bb43cc59174a8a4e29741ce)

#### Bug Fixes

-  remove conflict code   [`8c0b1ed`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/8c0b1ed9321e0537ab83deddea3099897df8296d)
-  update import path for graceful shutdown plugin in unit tests   [`5f19778`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5f19778c13149c49a3b917127949912c4ce8abb9)
-  fix conflict code   [`0cd1907`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/0cd190780223aecabbe3af532b2def95920b7fae)
-  remove conflict code   [`771bdff`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/771bdff5c88a1b8f5c9dbc759b8811adb69eec42)
-  i18next plugin unit test issue   [`8e6ae36`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/8e6ae36ae0dc7cd82fd240e96be4788f4ca4df2f)
-  i18next plugin unit test issue   [`6ba4d31`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/6ba4d31713a74a58938584a4c28e5c3dba288136)
-  fix conflict code   [`0ac6f63`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/0ac6f63ba093b7c22d45482f25e2d336cee5207f)

#### Chores & Housekeeping

-  kafka setup with typesense and upsert example   [`d7182cc`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d7182cccc64e44cd5042f6948b9d401644dea986)
-  update the test after merged the dev branch   [`b4aa7a1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b4aa7a1ccd584e46c24457d7a3fa60d6eed76d43)
-  remove unused code   [`bbd719a`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/bbd719a62ff1cdb9dc10920dc5f52bc404d40f5d)
-  update typesense api key and remove auto restart for filebeat   [`44745a3`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/44745a3be15068d39488e9e65cb62196cadbcda3)
-  put back todo comment from dev branch   [`65624c8`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/65624c8b1d73f62f783daef1aa7c6a40aa939eb6)

#### Documentation Changes

-  update changelog for v0.0.4   [`48b6109`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/48b61092d6e10155388e9b86ad4d1f938904f120)
-  add typesense explanation into readme   [`7182ce3`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/7182ce377af3a1cc35378e97b1b022c9b79f4079)

#### Refactoring and Updates

-  refactor typesense example   [`ec796be`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/ec796be357942bf540c58587b899b20e756df96d)
-  remove kafka setup because it will do in another services   [`9445d74`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/9445d74a20bbac1b5ca677a926e22774a627bc1d)
-  remove unused file   [`935cf6e`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/935cf6ef783b42add7cf93443d6f6d8045021d4b)
-  refactor typesense plugin to apply database factory and write unit test   [`c71863c`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c71863c8f5173e8fe8cbebb2c0a73ee5ea34bc49)
-  refactor user module to clearly separate concerns between handlers and services   [`da8ef8d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/da8ef8d807720617c8f0468f8cd9935bc7a36df5)
-  update import paths and improve error handling in utilities   [`642f05a`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/642f05afc3a014cb5422953e67f5a9222b533449)

#### Changes to Test Assests

-  plugin unit tests   [`36b11ab`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/36b11ab9fccd1c3eca3830c1b0ab68ab7fb80eac)
-  plugin unit test   [`3be8f1c`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/3be8f1c5ff8dadefd5c1c550fab1e3055570081e)
-  plugin unit test   [`8a4c07b`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/8a4c07b2b92fc93195571c10e7c81c1b30406b73)
-  add unit tests for various plugins and improve plugin registration logic   [`2a84ec7`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2a84ec7517020de729aede4a7be30911446ed491)
-  test typesense unit test   [`63631f0`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/63631f0dc02f0049d8ec0582b1e0833228e30e02)
-  add unit tests for app initialization and plugin registration   [`91ceb85`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/91ceb85c1018a20eee66bc6eb624583552f1df42)
-  plugin unit test   [`35e7d99`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/35e7d9991a36fdd5c08e3dd9166e353860fdb7c4)
-  swagger test done, typesense still in-progress   [`44b57ec`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/44b57ec98f92ee0460789d92c55dec78fa6f670d)
-  exclude plugins from coverage   [`c191e74`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c191e74e864094de0f0a2050bb8a9b4f315e3311)
-  add unit test for custom errors of user module and refine import alias   [`2f9737e`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2f9737e06a24728040422cbdfd355c1f01ba8721)
-  i18next plugin unit test issue   [`05ca376`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/05ca376e79352086272151dc40af612ec99d65b4)

<!-- auto-changelog-above -->

### v0.0.4 -  30 December 2024 

- Add unit test file for hooks directory
- MongoDB Setup
- Elastic Stack Setup
- Route cache setup
- Fix create migration not working after refactoring folder structure
- Module Unit Test
- Security Headers Setup
- Staging Docker Setup
- Implement lint-staged for pre-commit checks
- Utils Unit Test
- Refactor plugins and hooks
- Refactor folder structure
- Add prettier config path in settings json

#### New Features

-  update test case split out fastify register step   [`f9fd24d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f9fd24d9853165fd6aa8c359dfa251ef1eebdda6)
-  security headers setup   [`238ffa2`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/238ffa2dce940be42e0be8efbf445c009a7711f5)
-  complete 100% coverage for utils folder   [`7072a4e`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/7072a4ec0fd20855233aa5b2e76d1c0e2f8c631d)
-  security header setup   [`bd6bef1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/bd6bef1d5ad1e37f2fcf7546d2b655d3868102ac)
-  utils unit test   [`d335ed1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d335ed1533bca3bd7d49c673fdf5539ee9773f02)
-  update hook unit test case with new changes after merge branch   [`8bc30a2`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/8bc30a29cc01b9d9bb6df3e4f21e90edb8ef59d9)
-  refine security header setup   [`77a73d8`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/77a73d8e3f0c676de02efc2c80d80adb0bf3ff38)
-  add index template configuration for Elasticsearch   [`015b7bf`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/015b7bf732aa172c8f6520cf9b858537b6766c40)
-  security header setup   [`0f86db8`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/0f86db88e5e1e1c60fb9f29eebd83374aae79b41)
-  security headers setup   [`fc65b0d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/fc65b0d01e1c124ab8a9a56fdeccea588686f4db)
-  change nested fields to flattened type for application logs   [`55d190c`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/55d190c4f7430029bb655f4104a2b4652cd92e95)
-  utils unit test   [`30c7566`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/30c75661b0904a070e637a14e8d1fad6b4eeb7dc)
-  security header setup   [`c8c1775`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c8c17757b28766eff08ad9202e758fbce8a704c6)
-  set up elastic stack   [`10afe77`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/10afe77139f56857eb17e2a5d196fd7d7275f1ee)
-  security header setup   [`23b2f11`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/23b2f11509ea60874c17387964e73a2b99d88f3c)
-  add fetchFromCache function   [`55d36fd`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/55d36fd009e4b3c19c6ee12859d6b74c67e4d9f2)
-  add customLog decorator to Fastify instance   [`e7d33c5`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/e7d33c5bbc0c183e26dec3664823fa301761d25b)
-  remove example route for testing   [`7f92236`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/7f92236c9dfee6f9d50fff58150d0e704f2ad7ec)
-  add function for clear cache with prefix   [`02f4ca5`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/02f4ca5b6b603a5a9d759f810e220441f80da279)
-  update cache validation   [`427f78d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/427f78d11a16ab793d85593f183abf83ee9475c1)
-  remove example route for testing   [`c2e707a`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c2e707afc79c6d8413ea6567256bada3bc420e00)

#### Bug Fixes

-  fix merged code changes issues caused   [`fd27d3f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/fd27d3f79b9b6ee17a7e64551af8f05785d0047c)
-  fix code conflict issue caused and put back todo comment to code   [`d32940a`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d32940a8470c64f7944e3f1c9998a86e780566d9)
-  fix issue where not all fields are properly populated in ELK for application logs   [`586ec25`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/586ec25978ae3449041ced894bb7e9a0bce0c639)
-  fix conflict code issue   [`c8b1780`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c8b17809d0681ffe8d7d9061760bcbfd518b6807)
-  fix sequelize db connection dialect   [`b13a957`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b13a9571e75127e6f772388237b7208365655674)
-  fix path issue   [`24494a1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/24494a11282dfea1839d49c74aad37fc55e5fe9e)
-  update swagger URL to use DOCKER_PORT and fallback error code in onError hook   [`e810c71`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/e810c71d9b6e7e160f7d324dc3dc52bbaabf2998)
-  fix env.example mongo root username different docker setup username   [`638f04f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/638f04f2ee35f9977184a59c11b25a4b17a7df82)
-  remove todo code merged from dev branch   [`c8cb364`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c8cb364cb89e85b37f772248fecb7a2850434cbd)
-  fix missing line in docker compose   [`2ed0b4f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2ed0b4f418f26d69b73bcf686c887d2351b3a026)
-  fix typo on imported file path during merge conflicts   [`ed3faed`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/ed3faedb0012da44bfe885836ecf41aded2bd3f9)
-  refinement on the feature   [`775af16`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/775af16283eb3ef2af1357260697f85bf005d92c)
-  fix format of fastify postgres model instant   [`4643388`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/4643388964d4f2301fb43010be11f3382caa205c)
-  email to name in user schema properties   [`f6bc662`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f6bc6623baf9ed65cfa723407ed47c97a105e099)
-  change prettier file path   [`0ff0378`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/0ff03780cb8b3e4d9cf1706a57e4467adb832f7f)
-  fix swagger properties object issue   [`0e91207`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/0e91207bbf8aaa4df3d3ee1e0f64eaf44cda266d)
-  fix user schema missing name in findAll api   [`cfdaef9`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/cfdaef93d6c6466bb24a43599b592e607f54bcf2)
-  fix user schema missing name in response properties   [`7648bb3`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/7648bb3598c3f32d765bcbf5ca7dd7f77b3e530e)

#### Chores & Housekeeping

-  cleanup unused code and update redis connection   [`e00a2b4`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/e00a2b4c6e3c3b25eea1e480820eb96fb60bbd3c)
-  remove unused environment variables   [`f91f93b`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f91f93be6c183160381e0717fe81679928604407)
-  fix conflict code issue   [`9eb1b28`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/9eb1b28087242103fbbb58eea8cec8a3cb6571fd)
-  add missing volumes field and remove filebeat image field from compose file   [`2a28420`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2a28420ee72866a0c18391a608fcd546fc46358c)
-  update user service docstring and remove unused code   [`94741a4`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/94741a4944e44b24dc1076515d45e4d2a7af494a)
-  remove metribeat   [`2267f56`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2267f56fd55fb61b22ed5105d3f0380c16f73750)
-  fine tune code and configurations   [`e4efe9f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/e4efe9fe6152c25815107c6193219fc8b3691a21)
-  refactor route js   [`2c48851`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2c48851d8119e6b53cea3c00180e60760ad72301)
-  remove deprecated pino-multi-stream package and use multistream in core Pino   [`7972851`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/7972851462ae7a4fc89261d7a124f2160f37f8f2)
-  update elastic stack version to 8.16.1   [`bde4a09`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/bde4a09f3906e5159d07da0fcc445884b3609c1e)
-  housekeep environment variables   [`64adbe1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/64adbe174911ebf4d73ee553d422aa26eb279b18)
-  remove unnecessary codes   [`5101f19`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5101f19b03f2436816cbd62f720e998e63a6cbfd)
-  add mongodb package and configs and plugins   [`c8a6660`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c8a6660d6e6c72444dc2c1f9dd61bd46794e402a)
-  remove migration   [`a9a50ca`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/a9a50cad90da7f635acaca50258bca8f0b85bd4b)
-  implement lint-staged for pre-commit checks   [`16f8918`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/16f89187a061c78ca8faac457849ff8f9713ded2)
-  remove migration commands   [`9b915cf`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/9b915cf75ef86318606c114c80f4877b6deff97e)
-  temporary hardcode eslint version to 9.14 to prevent error   [`32cb6d3`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/32cb6d3029521d30e2584187ecfeb8bda048eaae)
-  remove redundant line   [`2c47bdf`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2c47bdf4ae216d2b295468eaf2376f0d2f0b2c7e)

#### Documentation Changes

-  update README with new docker install instruction for core services   [`72d1c44`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/72d1c44ce90cc20b7381ee84911744757b942830)
-  update README formatting and indentation for steps part   [`f9ba3b8`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f9ba3b842e6df87ab259026dd25a345c6b8fac0c)
-  update README with new env DOCKER_PORT   [`88f8e7f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/88f8e7f0f960614809aac026fb2f7970922946fa)

#### Refactoring and Updates

-  create database factory and model loader   [`c57bd99`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c57bd99d9c9229c2b6af030651e18a01dfc6eded)
-  remove audit-log example   [`c149891`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c14989167549ab1cffcc7c49b03caa2b5d124638)
-  add mongodb example   [`a00dae1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/a00dae1f66c764e4b2a0ee3979ea8df3059db61c)
-  database factory with load model helper   [`5bf9923`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5bf9923a0aa4596ac6fcc35280184af0e6d0f3ad)
-  separate elastic stack into a new repository and housekeep environment variables   [`75ee8e1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/75ee8e13e8b035565ec0d40953a0725d3368647c)
-  refactor cache util and generate redis plugin unit test   [`8a63471`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/8a634710778dd6ca731c9847f67ee71ef4f52418)
-  factory structure   [`9484431`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/9484431b07fbd9e20d592d5d13245dfa66034ab3)
-  refactor route js   [`d379e05`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d379e05f961eafc129f8e586968f5243a908f1d4)
-  remove testing code   [`aef7cb4`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/aef7cb481a3bff93bf15554335c5121056954869)
-  remove unnecessary log data utility function and unit test   [`6e13051`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/6e13051770a612f4d433d1d76c1bbf037ed8a137)
-  add config path for maintainability & install depcheck to check unused packages   [`fa98099`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/fa980993018560e0b7763337db8576be45ff59c6)
-  optimize sequelize decorator with connection and models with 'psql'   [`dcbb5c8`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/dcbb5c8a518079b9f928181fa94a8d62939e2ad3)
-  standardize request, reply, and error variables   [`dabbaae`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/dabbaae5f4dd17b9eb01fb8e258697706c683572)
-  fine tune logstash filter, log format, serializer and logger config   [`2ac4cf4`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2ac4cf4481cfd438398c3c1953dccad62cfc4adc)
-  refactor serializer util for better readable   [`cc0b6e4`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/cc0b6e42ff103efe2f0a53de14d9501020d6a6d9)
-  change all function to arrow function and import file with '#'   [`e610b57`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/e610b576b5e7d9cf904f68b98a5c97e7a6b0131a)
-  refactor example route folder   [`0044c62`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/0044c62304c97af6d6933811a25fbfb235f696ca)
-  rename mongo db env   [`41e2b54`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/41e2b54cb17041aeec16b68984a9fef267cd9d39)
-  file renaming and modify import path   [`4d8fb14`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/4d8fb14dcccf5b68f0d2233acd3dd04a81dc5277)
-  refactor route cache setup folder   [`c5d3b45`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c5d3b45ba91e3f2f9b32a3a6ad7c8cc963621c27)
-  rename sequezlie to dbInstance   [`52ecd90`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/52ecd9089e40265d3cc2ba26fa82d039ce140bbb)
-  optimize mongo decorator with connection and models with 'mongo'   [`9e4df5f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/9e4df5f06a4fdf4e77bd7fc5bdd2137d47153f75)
-  minor update   [`15fec35`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/15fec35525f5758db3540c9464ed4aaf8a751eea)
-  move helper file to util   [`b9b3e2b`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b9b3e2b2fb2ae7455a880fbcb6a2cd51c0fa2378)
-  refactor the dninstance more readable and update the database factor unit test   [`22625ab`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/22625ab800c752f07ace0fba98501057afc13fee)
-  add tag to swagger config   [`46f5ae2`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/46f5ae26fe485027e65992aae37731ac8a08c612)
-  remove composer create mongo user file   [`4277266`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/4277266b69ca34cac4de6add9234cce209b64227)
-  add throw exception if didn't implement required function   [`45911b9`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/45911b9f72445ad7b2bec628558969e6eb4b0d40)
-  update base url env use docker and README typo   [`361b713`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/361b713e144f3fbe4d6a43b6d2c005c9c8ba4e16)
-  update get hooks to arrow function   [`d28b11c`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d28b11cf5c746a04674bdf5568329b778e3a3d14)
-  standardize log format using the pino serializer   [`d00eb70`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d00eb70cf7d829be9c7eb5ff87589c5efe10ae71)
-  resolved comments   [`400d959`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/400d959ffb074b50e132d4ea70eb0e9e476e8881)
-  change read file to use utils and change to arrow functions   [`986c562`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/986c56211f4c0381eb2fa066f9069d8ff6c74981)
-  refactor fastify decorator of sequelize and mongo connection & models   [`2a0c5b2`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2a0c5b21668559558bc658ce425fac425bb210c0)
-  resolved comments   [`b31cc80`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b31cc80db5aa0a3887f0d346b9ee596216fb6ac1)

#### Changes to Test Assests

-  add unit test for modules   [`ffd9b55`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/ffd9b55b60ed1d0aa6c6607aba0e4a88eee95b07)
-  update unit tests of modules   [`c031de3`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c031de3c73dd94e457e14cc2333408cb1cecfe0f)
-  update unit tests of modules   [`c013d8a`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c013d8aa685b47842ee8bb285f34416d6d1377e0)
-  add example route unit test   [`5059b70`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5059b70b1b8e26b9e1c3bb059a215d3fe09f2d05)
-  add module unit test   [`0e233a2`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/0e233a23586051f333c0a9b222736e24a0c5dcf8)
-  add unit test for cache   [`2662199`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/26621990ca07f3f159c910a15b8811634a2d6ddb)
-  update util unit test   [`04574e9`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/04574e97649705525bc62e8a8d39414f5916d179)
-  test PR for ECR image auto-update with watchtower   [`facad97`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/facad97d0f3e97602174052706ac502ff10620fb)
-  fix index js to increase test coverage   [`22a4c39`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/22a4c3991117bf3fc04672a90e40711b90ee9ca5)
-  combine deploy and watchtower step due to restriction on bitbucket   [`5a7d818`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5a7d818ea3eebdbd81e31577c804e900b13c117a)
-  test inject env through bitbucket pipelines   [`2f81be4`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2f81be4893ea239b0699a58b1f69766d59daad8f)
-  fix example route import   [`630934d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/630934d49673133301bb97e74b5e9e9827cda4df)
-  conflict code issue   [`e6f8cdb`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/e6f8cdb4105aae5c4470a5ef1f5a2602a96d1c36)
-  enable feature branch push ecr image for testing watchtower   [`a00ea48`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/a00ea48af0f02152861ef242dc348879ead9377d)
-  test chain step for building   [`63a1ad0`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/63a1ad0c777a233ed2158412c3ae06c51d606044)
-  utils unit test   [`e7daf39`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/e7daf39a2fd371406acf9b44444db1441f02390c)
-  test chain step for building 2   [`e2de2c5`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/e2de2c559f66e94a16ba065f7d86393ae23dfc6c)
-  test chain step for building 4   [`79e04da`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/79e04da61aad9c6f4f8781414549c0bf0474dc34)
-  test chain step for building 3   [`052f011`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/052f011ac6f975609d3d941f08b601bf84cddfb9)
-  test ecr image auto update   [`c016121`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c016121b35ca15c58f943700acb55559fc60a04b)
-  comment deployment line due to error   [`2d29f8b`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2d29f8b9293b86ab8aebffd348b25aaf3a0ebc5a)
-  remove sonarqube step (ecr image for testing watchtower)   [`9b272a5`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/9b272a502eaafb9548c68592ca1232552b72e466)

### v0.0.3 -  02 December 2024 

- Housekeep Configuration Files in Root Directory
- Add logging and general error handling feature
- Api documentation setup
- Vitest Setup
- Prevent unintended script execution by enforcing --ignore-scripts in Dockerfile
- Add Bitbucket Pipeline configuration to build and push a Docker image to AWS ECR

#### New Features

-  add schema for API route   [`f952a3f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f952a3f9975bfaf8f417b47598002360788a59be)
-  standardise error response   [`a1346aa`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/a1346aadeb4e0c789feef7bf4a615a3a7b9c73cf)
-  resolve multipart upload issue for swagger   [`856745a`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/856745acfdc4373d1edfda73b53cf83b19ad0996)
-  update schema error response   [`5a30161`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5a301616a1e5082eb9cbba454d348b233472c88b)
-  update return using sensible error   [`c83ffd5`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c83ffd5b0a5d5bc684e23fcedbfceb0a10a9ec5b)
-  change test package to vitest   [`2214124`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2214124e173dead5faf7c5413db79170b4c2dbfc)
-  add crud structure: route, controller, repository, utils   [`198c8a2`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/198c8a2db231a6afe4dda423bddf8738aa85b773)
-  add NYC package to generate coverage reporting of tests   [`0a2ede9`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/0a2ede9472e39effb5912876d04a353718b6f734)
-  add mocha and sinon package   [`35a7e61`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/35a7e61ccf8ba947278efd5f2c5f086bc1e3ae45)

#### Bug Fixes

-  refine logging behavior and code   [`be67af0`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/be67af00843dccb670315691362202f494ae9901)
-  change to use response util in for error response   [`a638d15`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/a638d15391d3ebf95573b800f9ffec3f53c0f2f7)
-  refinement on error logging and handling   [`3f39401`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/3f394011ddecc663dbfe6c4b9d276334365fc90f)
-  fix log level issue   [`1ad0b4d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/1ad0b4df9675c4dc246e7247c772852641125903)
-  initialize sentry in seperate file and add sentry profiling   [`80d1955`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/80d1955f978c9b3c1121449ffec3eabbf57798f3)
-  prevent unintended script execution by enforcing --ignore-scripts in Dockerfile   [`f72aca7`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f72aca7f35942d1671c47e49366887062a8b6a21)
-  fix image not found after built   [`af778a3`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/af778a38b8c457d0b29d7f1bdf3ee1124ab21d66)
-  fix invalid reference format   [`060d846`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/060d8466d4aaf157623abe7484c801f10fa2b680)
-  try to fix image not found error   [`5995baa`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5995baa6df17e4ca8b007530300004f7b3785106)
-  try to fix unknown shorthand flag: 't' in -t error   [`cf86ba1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/cf86ba1d3851bd32b2c948c89ee20ba284b841a8)
-  try to fix the error authorization denied by plugin pipelines   [`cc0fdec`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/cc0fdecd9fc3c63f8d87818a32e3bf375cd95a60)
-  try to fix the error authorization denied by plugin pipelines   [`f02b09a`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f02b09a56c42bb0b90a8f7b17eb7f908a6364498)

#### Chores & Housekeeping

-  remove unused compose.staging.yaml file   [`29b264b`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/29b264b1621998fae1b3a190cb6ac82032121a9b)
-  standardize folder paths and set up the configuration for the coverage report   [`b641746`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b641746b640166192c8dc54cf39ad6d29f36aab8)
-  minor code refactor to use fastify decorator   [`ab424bb`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/ab424bbb7f30b6ac33831cafb6e9f86453532146)
-  change import path   [`9a05c3b`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/9a05c3b5e9c9e0df6688354f3eb89a3e1a0bc996)
-  housekeep configuration files   [`d4687fb`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d4687fbbaac8af8d57b1c64e7d29b010d439852f)
-  revert bitbucket-pipelines.yml   [`caf2da2`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/caf2da236706eeee6299b94e698dcbcd3d9d6273)
-  revert sonarqube directories   [`b00eaf4`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b00eaf481169cc60b2a1e5f9deebb2a0dc57c80e)
-  revert sonar-project.properties   [`c1b727c`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c1b727ce78c1ebe367660d5f687e5a4f186c438d)
-  change eslint to 9.13   [`df5bcc1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/df5bcc13903a1c4b5a5e98d041f5cab7ffd247bf)
-  add lcov reporter type to vitest for sonarqube purpose   [`0c584c4`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/0c584c47ef320de8acb16eb697992968672340bd)
-  remove unuse package   [`020aefd`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/020aefd2f2c1e05000946c036debaf9bfa30c4fd)
-  add staging compose file and configure Bitbucket pipeline for staging builds   [`26c4c67`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/26c4c6719736adf562ba1f3611f8812a941d4a32)
-  add services and update view user test   [`b847076`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b84707655fc49fc3589fc6e474ffe6a2d20432a5)
-  missing ECR Repo URL on tag   [`f86df27`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f86df2710528bbfd91e27ecc2b450d1a15a43fde)
-  housekeep bitbucket-pipelines.yml and fix image not found error   [`7513b20`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/7513b2003f6cf5967c6f5d14b68e13fcada36b07)
-  update bitbucket pipeline script to use pipe to push image to ECR   [`41d4919`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/41d49195d3993a5076cd744223dafedcd608a773)
-  update bitbucket-pipelines.yml to list images, tag and push each service to ECR   [`5f8b533`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5f8b533bb5e6f5b193b193861aa71a5f27fef227)
-  remove env_file in compose.staging.yaml   [`8c15f7f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/8c15f7f7959c9ad2579f257ab15d72f81620fa15)
-  onSend to onResponse   [`f262a96`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f262a9668ce79c1f313fd3997248544ac09f1acc)
-  miss out one env_file to remove   [`1aeb545`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/1aeb54524fdcb488c0e8079dba53ea2f05220b77)
-  update docker compose build command with specify image name   [`70c0f33`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/70c0f33dc9f05b201962301a1c0550a2e3e520ce)

#### Documentation Changes

-  update upload route with swagger docs   [`ee9434f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/ee9434ffe7a5e32cc3dc650c2118da47ed4fe7a1)

#### Refactoring and Updates

-  streamline paths, standardize response schemas, and unify routes   [`c4a8025`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c4a8025e75e8216dbd7b5364d99c2d12779d847e)
-  migrate to file utility and request/response plugins   [`ca33443`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/ca33443a9bda9c78bc997e81b2880f5c36bf3d39)
-  change all function to arrow function and remove merged used code   [`457e123`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/457e1237879c188d2f171a163e42770b58bc6cfc)
-  fix integration test with model function that using fastify decorator pattern   [`ecebd72`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/ecebd727f4e7635b5be8c2c09c8a53604a4fbdab)
-  add fastify instant into request then able to do request.fastify.User   [`a491857`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/a49185786a23afa5cbc2717a7e90aff19f7e0ee7)
-  remove integration test and remove docker up & down during run test   [`cffc94d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/cffc94dfe2ac66eeead30e6611a07a6692333dd0)
-  change gracefulShutdownPlugin and sentryPlugin to arrow function   [`99a6df6`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/99a6df6ace399bc9b2c73d241e2555b0fd5b5a7d)

#### Changes to Test Assests

-  add service test example (provided by KY)   [`1bc61a9`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/1bc61a9149754ac3f5f6bbd4bfcec14167148ce9)
-  test migration and seeder with docker compose   [`13fbb28`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/13fbb28813a98e57f46b06464c2d532eefa06a27)
-  test sonarqube   [`af672fd`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/af672fd961d21398d9331b9c6ee2ae03cc6b16c6)
-  undo pipeline testing code   [`feea4a9`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/feea4a9b80e21561d73838b6ae64eaf8b7935aa4)
-  rearrange pipeline steps that move test after build and push docker image   [`cc3d3c8`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/cc3d3c8485dc26646e8d53af58c76680f4d7c0c8)
-  remove testing code   [`3d69451`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/3d69451fac483bd9765738091e203bd9e193c309)
-  change the sonarqube settings   [`6041e8a`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/6041e8ae29cc7d95d7ccaf5f85d4403afb5dc68c)
-  add service docker into test step   [`569896a`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/569896abb7439a6b2ff77f81382391ddaa5afd76)
-  add npm run migrate and npm run seed into dockerfile and set sonarqube coverage path   [`d183277`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d1832774cdf85d66da44890049235c5881155977)
-  fix coverage folder not found issue on sonarqube   [`debb1b0`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/debb1b02a400e85d6432f55e7bb8a66deef2d134)
-  temp uncomment trigger feature push image   [`9b09a31`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/9b09a31dd53bb85d4e63dda9171dc30ef4aa37a4)
-  test push new image   [`1f27246`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/1f2724647083148db80571cb4181746263ace76c)
-  test push new image   [`1ae5754`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/1ae57547c05df397a137aa9e7feeaf7ff5ca7e53)

### v0.0.2 -  16 November 2024 

- PostgreSQL Database Setup
- File upload and cloud storage setup
- Remove third-party plugin for import sorting and use ESLint's sort-imports option
- Configure ESLint and Prettier to auto-sort and organize imports
- Setup SonarQube Integration and Configure PR & CI Pipeline for Code Quality Analysis
- SonarQube Configuration Setup (For testing purposes)
- Migrate codebase from CommonJS to ES Modules
- Rename repository and update codebase for boilerplate setup
- Docker Setup and Configuration for Local Development
- Implement automated changelog generation

#### New Features

-  add skeleton code of User findAll   [`066ac46`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/066ac46fd654866101fe03a23174e5b1c289acf6)
-  resolved comments   [`1633755`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/163375566267d4d26f7a0dfbbf397ba15150f603)
-  update readme(migration and seeder), migration & seeder example   [`53acd49`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/53acd49555368050d47f540c03fc9d7e55d099d6)
-  update readme, add config db, add user model (skeleton code)   [`7482775`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/7482775f2496298b2141d438dade51a00ab8ba25)
-  resolved comments   [`d9c2c4d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d9c2c4d912a443a211e59b5fc7ca7945149ff5bb)
-  change model path and migration config file   [`4e8a290`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/4e8a290c814da52e3374668ed5bc1f28e8b2e82c)
-  set sequelizerc and rearrange file path   [`461e5f4`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/461e5f4b9b3ec34b8167cac61a5a372918910d80)
-  add cli for create migrate, run migrate, migrate rollback and update readme   [`0505b63`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/0505b631c035d8f7cfc80403219f841008e8100f)
-  install pg, pg-hstore, sequelize, sequelize-cli   [`5c285a6`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5c285a67516f80b972ccbb74cdad1a80aeeed692)
-  resolved comments   [`c41a27d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c41a27d6c354cd18c3c6367d5e7744a3b03b9148)
-  resolved comments   [`d12074d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/d12074dcb519dde7a7bfb3fc70e5232c22aa8fea)
-  resolved comments   [`15083ea`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/15083ea1df5c171e9705beed2f094b5e605d0404)
-  alignment   [`900d773`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/900d7734cd8239319594f169a6b64f6e24d04350)
-  minor update readme   [`65d03c1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/65d03c156f2accf670332d69fba87a79ebeb1681)
-  implement Docker setup and configuration for local development   [`b7135d0`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b7135d0244ee1d8f4892da6317cb7b7b09cd6568)

#### Bug Fixes

-  fix the configuration error by removing the inline comment   [`280d8a3`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/280d8a3eb9ead8ba196e95282857c6f2dd501840)
-  add forgotten file to the commit   [`ecf4191`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/ecf41914d88045a087d8a5342bed1a58dd55ae03)
-  fix pipeline using node image   [`279d1a1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/279d1a1d61d93155b2376ce9cd1c84814109545d)
-  fix pipeline env bug   [`f484078`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f4840782c80bbd0b0d01b804d6740b3d1c071fa1)
-  fix configuration error   [`a97bd56`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/a97bd562d225025b7880acd4bacb4f40ad8a6a72)
-  fix pipeline env not declare   [`084144d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/084144d766d63bba504d5a40c784923dc04167ec)
-  clear old modules   [`a433cae`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/a433cae3bf620a813edf1d9ad492ab18ada607d3)
-  fix DEBUG value should be a string   [`a37c576`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/a37c576593aa2e6c1ad6e6725a5ecba54f951f99)
-  fix pipeline npm ci command error   [`b164bc8`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b164bc89acd3e28dabb1d4d294b38c6ab196debd)
-  missing access token for clear cache pipe   [`5920227`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/592022775b921a554c237d05a509fdc958a015d3)
-  fix command error   [`2e3550e`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2e3550e957b601e9d9695880fe0237d104b6ca39)
-  fix YAML syntax error   [`5451587`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5451587c83f5019dde530e64745ff6e63ccff95c)
-  fix configuration error   [`c56cc5d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/c56cc5d670edb0da7461d3ccf4a9b833f4901447)
-  fix configuration error   [`7aabd6f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/7aabd6ff96ca65e62c0ec12e6e117a1aa9ac5ef5)
-  fix invalid pipeline configuration   [`1e95132`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/1e9513267affab77dca6781f2bdc53791670ce60)

#### Chores & Housekeeping

-  sort env key and add new key description to README.md   [`acab43e`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/acab43e9da65507c581c5181d48f07fafdb1ebfd)
-  resolved comments   [`37aa226`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/37aa2269ec6779f1625128218f9249bf619aaca8)
-  resolve comments   [`1473460`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/14734608e9c0f58102c723aafe3ecd19a4770e66)
-  remove commented code for logger.js   [`3b60e4f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/3b60e4f190676a2dc275eeefbde3385f699e02d9)
-  configure ESLint and Prettier to auto-sort and organize imports   [`4959a24`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/4959a248848ce1094bf61bd2e4ec9f26e582ed5e)
-  remove 3rd party plugin and use ESLint "sort-imports" option   [`4c3ab66`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/4c3ab66bd121e05d3674011961cef760c7f78907)
-  update seed all seeders to only seed the project default seeders   [`43525a7`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/43525a7a8dff0a36aa48259079010f73288f1387)
-  add more sonar properties and share connected mode configuration among the team   [`54b2e8e`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/54b2e8e6eee71567ec08378cd51cddd07a9f561a)
-  missing settings.json commit   [`dc32ec1`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/dc32ec15de5c713c85ee9f9ad977d7261bd85598)
-  include .vscode environment to project   [`de0195b`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/de0195b5035e3b410494b72c9b6746c3555fe3cc)
-  update readme   [`06dd9d0`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/06dd9d0365b23afe469157b34591f33fe11e95e5)
-  add more sonar project properties   [`ca9dd61`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/ca9dd61d2081c4822712990b2ee929da94797a41)
-  standardize filename and test path   [`6a18364`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/6a1836474bf1df36d26e0ffae1f3a40fed31a818)
-  migrate codebase from CommonJS to ES Modules   [`80afbd9`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/80afbd991cdec94d4cd47b95fc1b237d2ca44a8d)
-  clean up and organize imports, configs, and scripts   [`1d62413`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/1d62413e5f8dfd29f1c49539d2a1a629211f7f7b)
-  configure bitbucket pipeline for sonarqube analysis on push or pull request   [`84cbfb7`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/84cbfb7ad1bf015f3f3679e2f28976ad2bf85b8b)
-  update .env.example for easier setup   [`536fb3d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/536fb3df1516033d7ffad28e1dcbafe6c566c87a)
-  update bitbucket pipeline to include quality gate check on SobarQube   [`156610d`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/156610de3fcd33cb452ed7043cff4f08aff7ae8d)
-  add installation step in the bitbucket pipeline   [`f1bf372`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/f1bf3728a5f2d453e189cb15a318e16dabf51569)
-  enhance environment schema   [`fb08ccc`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/fb08ccc064f1c201a7f07abc29e7a049901f0ab6)
-  add .nvmrc and engines field for Node version consistency in dev and CI environment   [`5b6c823`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/5b6c82341b0cc808298f989aa6d03b9e92eb1de8)
-  configure sonar-project properties and move `/test` directory outside from `/src`   [`17dad22`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/17dad22820dcf3a29899036ace8f20d4eea3bd79)
-  update branches in bitbucket pipeline   [`2f52a50`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/2f52a504dd98c43f57fb81664e6236b5831c2759)
-  add clear cache step in bitbucket pipeline   [`680e0dc`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/680e0dc6c68099522effb19443ae1da84ed918bb)
-  remove feature branch analysis in the CI pipeline when push changes   [`380733c`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/380733c3272b5bfa609f98017cef3d25d8603d82)
-  update project version and description in sonar project properties   [`cded31f`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/cded31f2ae50a46f5ca53e4fd33daeaaa23c25b4)
-  update PostgreSQL and MongoDB username in .env.example   [`21ccec6`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/21ccec656a567947b45e09d9e2aa27956b94beb2)

#### Refactoring and Updates

-  rename repository and update codebase for boilerplate setup   [`932c404`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/932c404aa40f0f7536a8ce07a946c9ea03b4ba1d)

#### Changes to Test Assests

-  insert dummy code for testing SonarQube analysis   [`b05741b`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/b05741beab647c35a0783325e8975db3249516ca)
-  test PR pipeline for SonarQube analysis   [`3ac7f52`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/3ac7f52e69e3add203a2bea3e4147de7d0140cd0)
-  test PR pipeline for SonarQube analysis   [`7a31baf`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/7a31baf44977007b4ffc9df92925e5b45bdebd9f)
-  test PR pipeline for SonarQube analysis   [`cf9aca4`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/cf9aca4e71ffd78529566cb4b17d7062f3d24311)
-  add dummy code for testing purposes on SobarQube analysis   [`a1a3a73`](https://bitbucket.org/aiodintech/fastify-boilerplate/commits/a1a3a73f5a0b0767a49d8af36b6ffe0f638c5a83)

<!-- auto-changelog-above -->

## v0.0.1

- Environment Configuration Setup
- Code Formatting Linting and EditorConfig Setup
- Initial Fastify project setup

#### Bug Fixes

-  missing husky dependency   [`59a9464`](https://bitbucket.org/aiodintech/wlsapi/commits/59a94644e62d567a4c623b383a247ceb3e690160)
-  resolve merge conflicts from develop into feature branch   [`a3b5384`](https://bitbucket.org/aiodintech/wlsapi/commits/a3b5384cfd482b3de446d5f09448c35a8ec827b9)

#### Chores & Housekeeping

-  reset semantic versioning   [`c3e1975`](https://bitbucket.org/aiodintech/wlsapi/commits/c3e197529402bfdd95be5f9f0218e0b353109586)
-  modify package version and auto changelog command   [`a500905`](https://bitbucket.org/aiodintech/wlsapi/commits/a5009054a4fe7299caf75cccea5d105bee322ec8)
-  implement auto-changelog for automated changelog generation   [`bb165c4`](https://bitbucket.org/aiodintech/wlsapi/commits/bb165c4e37aed114e412ceba6320dc069fa2bd9d)
-  add Prettier configuration for code formatting   [`50e8d35`](https://bitbucket.org/aiodintech/wlsapi/commits/50e8d3581cf126a9d814f353d0d0552553222b11)
-  add Prettier format for JSON files   [`8f5d39f`](https://bitbucket.org/aiodintech/wlsapi/commits/8f5d39fe34fc3565967d25d560fd81a636f49a7b)
-  add pino-pretty logger for formatted logging with colorized output to the console   [`aceaa17`](https://bitbucket.org/aiodintech/wlsapi/commits/aceaa1737eeed595f5f97fc335abed47bc8e2cfd)
-  setup ESLint, SonarJS plugin, commitlint, EditorConfig, and Husky hooks   [`7167171`](https://bitbucket.org/aiodintech/wlsapi/commits/716717161a7bc7661161db8f45b846bf5134d9f3)
-  setup environment configuration with fastify-env and dotenv   [`35b7369`](https://bitbucket.org/aiodintech/wlsapi/commits/35b736923e7fa6ffb4fdc9926dd7d46271a792e4)
-  fine-tune and add some comments   [`2ff775f`](https://bitbucket.org/aiodintech/wlsapi/commits/2ff775f39eaa60c92e8e96c5a38d602d9e99422c)
-  update environment schema to enforce PORT as a number type   [`f3cb6cb`](https://bitbucket.org/aiodintech/wlsapi/commits/f3cb6cb680fc74e54a10f845ef19f78ef0d504f6)
-  update lint script in package.json   [`7540343`](https://bitbucket.org/aiodintech/wlsapi/commits/75403439d66e7d32a48a7bc7965382abf31b27c3)

#### Documentation Changes

-  enhance the README.md and package.json   [`36be712`](https://bitbucket.org/aiodintech/wlsapi/commits/36be712e8795ebb58ff07c6c70dcf4269f1a92e3)
-  update node.js version from v16+ to v20+ as Fastify v5 will only support Node.js v20+   [`ea3bbcb`](https://bitbucket.org/aiodintech/wlsapi/commits/ea3bbcbe98610c0514c7abd6233ea4059120de6c)
-  add changelog generation script in README.md   [`8952bd9`](https://bitbucket.org/aiodintech/wlsapi/commits/8952bd974bf10282c6598f2bd5ada9ea90ceaf02)
