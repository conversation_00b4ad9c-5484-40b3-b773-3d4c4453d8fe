import { beforeEach, describe, expect, it, vi } from 'vitest';

import { applyAuditFields } from '#src/mixins/auditable.mixin.js';

describe('Auditable Mixin', () => {
  let mockModel;
  let mockInstance;

  beforeEach(() => {
    mockInstance = {
      set: vi.fn(),
      changed: vi.fn(),
    };

    mockModel = {
      beforeCreate: vi.fn(),
      beforeUpdate: vi.fn(),
      beforeDestroy: vi.fn(),
      beforeBulkCreate: vi.fn(),
    };

    applyAuditFields(mockModel);
  });

  describe('beforeCreate hook', () => {
    it('should set createdBy and updatedBy when authInfoId is provided', () => {
      const beforeCreateHook = mockModel.beforeCreate.mock.calls[0][0];
      beforeCreateHook(mockInstance, { authInfoId: 'user123' });

      expect(mockInstance.set).toHaveBeenCalledTimes(2);
      expect(mockInstance.set).toHaveBeenCalledWith('createdBy', 'user123');
      expect(mockInstance.set).toHaveBeenCalledWith('updatedBy', 'user123');
    });

    it('should not set fields if authInfoId is not provided', () => {
      const beforeCreateHook = mockModel.beforeCreate.mock.calls[0][0];
      beforeCreateHook(mockInstance, {});

      expect(mockInstance.set).not.toHaveBeenCalled();
    });
  });

  describe('beforeBulkCreate hook', () => {
    it('should set createdBy and updatedBy for all instances when authInfoId is provided', () => {
      const mockInstances = [{ set: vi.fn() }, { set: vi.fn() }];

      const beforeBulkCreateHook = mockModel.beforeBulkCreate.mock.calls[0][0];
      beforeBulkCreateHook(mockInstances, { authInfoId: 'user123' });

      expect(mockInstances[0].set).toHaveBeenCalledTimes(2);
      expect(mockInstances[0].set).toHaveBeenCalledWith('createdBy', 'user123');
      expect(mockInstances[0].set).toHaveBeenCalledWith('updatedBy', 'user123');

      expect(mockInstances[1].set).toHaveBeenCalledTimes(2);
      expect(mockInstances[1].set).toHaveBeenCalledWith('createdBy', 'user123');
      expect(mockInstances[1].set).toHaveBeenCalledWith('updatedBy', 'user123');
    });

    it('should not set fields if authInfoId is not provided', () => {
      const mockInstances = [{ set: vi.fn() }, { set: vi.fn() }];

      const beforeBulkCreateHook = mockModel.beforeBulkCreate.mock.calls[0][0];
      beforeBulkCreateHook(mockInstances, {});

      expect(mockInstances[0].set).not.toHaveBeenCalled();
      expect(mockInstances[1].set).not.toHaveBeenCalled();
    });
  });

  describe('beforeUpdate hook', () => {
    it('should set isDirty to true and updatedBy when fields changed and authInfoId is present', () => {
      mockInstance.changed.mockReturnValue(['field']);
      const beforeUpdateHook = mockModel.beforeUpdate.mock.calls[0][0];
      beforeUpdateHook(mockInstance, { authInfoId: 'user456' });

      expect(mockInstance.changed).toHaveBeenCalledTimes(1);
      expect(mockInstance.isDirty).toBe(true);
      expect(mockInstance.set).toHaveBeenCalledWith('updatedBy', 'user456');
    });

    it('should set isDirty to false if no fields have changed', () => {
      mockInstance.changed.mockReturnValue([]);
      const beforeUpdateHook = mockModel.beforeUpdate.mock.calls[0][0];
      beforeUpdateHook(mockInstance, { authInfoId: 'user456' });

      expect(mockInstance.changed).toHaveBeenCalledTimes(1);
      expect(mockInstance.isDirty).toBe(false);
      expect(mockInstance.set).not.toHaveBeenCalled();
    });

    it('should set isDirty but not updatedBy if authInfoId is missing', () => {
      mockInstance.changed.mockReturnValue(['field']);
      const beforeUpdateHook = mockModel.beforeUpdate.mock.calls[0][0];
      beforeUpdateHook(mockInstance, {});

      expect(mockInstance.changed).toHaveBeenCalledTimes(1);
      expect(mockInstance.isDirty).toBe(true);
      expect(mockInstance.set).not.toHaveBeenCalled();
    });
  });

  describe('beforeDestroy hook', () => {
    it('should set deletedBy when authInfoId is provided', () => {
      const beforeDestroyHook = mockModel.beforeDestroy.mock.calls[0][0];
      beforeDestroyHook(mockInstance, { authInfoId: 'user789' });

      expect(mockInstance.set).toHaveBeenCalledTimes(1);
      expect(mockInstance.set).toHaveBeenCalledWith('deletedBy', 'user789');
    });

    it('should not set deletedBy when authInfoId is not provided', () => {
      const beforeDestroyHook = mockModel.beforeDestroy.mock.calls[0][0];
      beforeDestroyHook(mockInstance, {});

      expect(mockInstance.set).not.toHaveBeenCalled();
    });
  });

  it('should apply beforeCreate and beforeUpdate hooks', () => {
    expect(mockModel.beforeCreate).toHaveBeenCalledTimes(1);
    expect(mockModel.beforeUpdate).toHaveBeenCalledTimes(1);
  });
});
