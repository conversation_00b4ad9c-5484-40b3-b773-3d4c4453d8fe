import fastifyCookie from '@fastify/cookie';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import cookiePlugin from '#src/plugins/cookie.plugin.js';

vi.mock('@fastify/cookie', () => ({
  default: vi.fn(),
}));

describe('cookie.plugin.js', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      register: vi.fn(),
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should register the fastify-cookie plugin', async () => {
    await cookiePlugin(mockFastify);

    expect(mockFastify.register).toHaveBeenCalledTimes(1);
  });

  it('should register the fastify-cookie plugin with an empty options object', async () => {
    await cookiePlugin(mockFastify);

    expect(mockFastify.register).toHaveBeenCalledWith(fastifyCookie, {});
  });
});
