import { verify } from 'hcaptcha';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import hcaptchaPlugin from '#src/plugins/hcaptcha.plugin.js';

vi.mock('hcaptcha', () => ({
  verify: vi.fn(),
}));

describe('hCaptcha Plugin', () => {
  let mockFastify;
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    vi.resetAllMocks();

    mockFastify = {
      decorate: vi.fn(),
      log: {
        warn: vi.fn(),
        error: vi.fn(),
      },
    };

    mockRequest = {
      body: {},
      ip: '127.0.0.1',
    };
    mockReply = {};
  });

  it('should register the plugin correctly', async () => {
    await hcaptchaPlugin(mockFastify, {});
    expect(mockFastify.decorate).toHaveBeenCalledWith('hcaptcha', expect.any(Function));
  });

  it('should set high risk level when token is missing', async () => {
    await hcaptchaPlugin(mockFastify, {});

    const hcaptchaFunction = mockFastify.decorate.mock.calls[0][1];

    await hcaptchaFunction(mockRequest, mockReply);

    expect(mockRequest.riskLevel).toBe(CoreConstant.HCAPTCHA_RISK_LEVELS.HIGH);
    expect(mockFastify.log.warn).toHaveBeenCalledWith(
      'hCaptcha token is missing, proceeding with high risk assessment',
    );
    expect(verify).not.toHaveBeenCalled();
  });

  it('should maintain high risk level when verification is not successful', async () => {
    mockRequest.body = { 'h-captcha-response': 'invalid-token' };

    verify.mockResolvedValue({
      success: false,
    });

    await hcaptchaPlugin(mockFastify, {});
    const hcaptchaFunction = mockFastify.decorate.mock.calls[0][1];

    await hcaptchaFunction(mockRequest, mockReply);

    expect(mockRequest.riskLevel).toBe(CoreConstant.HCAPTCHA_RISK_LEVELS.HIGH);
  });

  it('should handle verification errors gracefully', async () => {
    mockRequest.body = { 'h-captcha-response': 'error-token' };

    const testError = new Error('Verification failed');
    verify.mockRejectedValue(testError);

    await hcaptchaPlugin(mockFastify, {});
    const hcaptchaFunction = mockFastify.decorate.mock.calls[0][1];

    await hcaptchaFunction(mockRequest, mockReply);

    expect(mockRequest.riskLevel).toBe(CoreConstant.HCAPTCHA_RISK_LEVELS.HIGH);
    expect(mockFastify.log.error).toHaveBeenCalledWith(
      'hCaptcha verification error: Verification failed',
    );
  });

  it('should set low risk level when verification is successful', async () => {
    mockRequest.body = { 'h-captcha-response': 'valid-token' };
    verify.mockResolvedValue({
      success: true,
    });

    await hcaptchaPlugin(mockFastify, {});
    const hcaptchaFunction = mockFastify.decorate.mock.calls[0][1];

    await hcaptchaFunction(mockRequest, mockReply);

    expect(mockRequest.riskLevel).toBe(CoreConstant.HCAPTCHA_RISK_LEVELS.LOW);
  });
});
