import { beforeEach, describe, expect, it, vi } from 'vitest';

import schemaPlugin from '#src/plugins/schema.plugin.js';
import { ErrorResponse } from '#src/schemas/error.schema.js';

// Mock fastify-plugin
vi.mock('fastify-plugin', () => ({
  default: vi.fn((fn) => fn),
}));

describe('Schema Plugin', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      addSchema: vi.fn(),
      log: {
        error: vi.fn(),
      },
    };
  });

  it('should add ErrorResponse schema to fastify instance', async () => {
    await schemaPlugin(mockFastify);

    expect(mockFastify.addSchema).toHaveBeenCalledWith(ErrorResponse);
  });

  it('should not throw an error when called with options', async () => {
    await expect(schemaPlugin(mockFastify, { someOption: true })).resolves.not.toThrow();
  });

  it('should be an async function', () => {
    expect(schemaPlugin.constructor.name).toBe('AsyncFunction');
  });

  it('should not overwrite existing schemas unless intended', async () => {
    mockFastify.addSchema.mockImplementationOnce((schema) => {
      if (schema === ErrorResponse) {
        throw new Error('Schema already exists');
      }
    });

    await expect(schemaPlugin(mockFastify)).rejects.toThrow('Schema already exists');
  });
});
