import { beforeEach, describe, expect, it, vi } from 'vitest';

import { LocalisationRepository } from '#src/modules/setting/repository/index.js';
import systemSettingPlugin from '#src/plugins/system-setting.plugin.js';

vi.mock('#src/modules/setting/repository/index.js', () => ({
  LocalisationRepository: {
    findBaseCurrency: vi.fn(),
  },
}));

// mock fastify-plugin to return the inner fn directly
vi.mock('fastify-plugin', () => ({
  default: (fn) => fn,
}));

describe('System Setting Plugin', () => {
  let mockFastify;

  beforeEach(() => {
    vi.resetAllMocks();
    mockFastify = {
      decorate: vi.fn(),
      log: {
        info: vi.fn(),
        error: vi.fn(),
      },
    };
  });

  it('loads base currency and decorates fastify with systemSetting API', async () => {
    const mockBaseCurrency = { code: 'USD' };
    LocalisationRepository.findBaseCurrency.mockResolvedValue(mockBaseCurrency);

    await systemSettingPlugin(mockFastify);

    expect(LocalisationRepository.findBaseCurrency).toHaveBeenCalledWith(mockFastify);
    expect(mockFastify.decorate).toHaveBeenCalledWith(
      'systemSetting',
      expect.objectContaining({
        get: expect.any(Function),
        getValue: expect.any(Function),
        keys: expect.any(Function),
        reload: expect.any(Function),
      }),
    );

    const api = mockFastify.decorate.mock.calls[0][1];
    expect(api.get()).toEqual({ baseCurrency: { code: 'USD' } });
    expect(api.getValue('baseCurrency')).toEqual({ code: 'USD' });
    expect(api.keys()).toEqual(['baseCurrency']);

    expect(mockFastify.log.info).toHaveBeenCalledWith(
      expect.stringContaining('System settings initialised:'),
    );
  });

  it('propagates loader errors and does not decorate on failure', async () => {
    const error = new Error('Base currency not found');
    LocalisationRepository.findBaseCurrency.mockRejectedValue(error);

    await expect(systemSettingPlugin(mockFastify)).rejects.toThrow(error);
    expect(mockFastify.decorate).not.toHaveBeenCalled();
    // In the arrow-function rewrite there is no error log on failure; assert none.
    expect(mockFastify.log.error).not.toHaveBeenCalled();
  });

  it('reload() replaces the cache and logs', async () => {
    // First load: USD
    LocalisationRepository.findBaseCurrency.mockResolvedValueOnce({ code: 'USD' });

    await systemSettingPlugin(mockFastify);
    const api = mockFastify.decorate.mock.calls[0][1];

    expect(api.getValue('baseCurrency')).toEqual({ code: 'USD' });

    // Next reload: EUR
    LocalisationRepository.findBaseCurrency.mockResolvedValueOnce({ code: 'EUR' });

    await api.reload();

    expect(api.getValue('baseCurrency')).toEqual({ code: 'EUR' });
    expect(api.get()).toEqual({ baseCurrency: { code: 'EUR' } });
    expect(api.keys()).toEqual(['baseCurrency']);
    expect(mockFastify.log.info).toHaveBeenCalledWith(
      expect.stringContaining('System settings reloaded:'),
    );
  });

  it('coalesces concurrent reloads (only calls loaders once)', async () => {
    // Initial load: USD
    LocalisationRepository.findBaseCurrency.mockResolvedValueOnce({ code: 'USD' });
    await systemSettingPlugin(mockFastify);
    const api = mockFastify.decorate.mock.calls[0][1];

    // Prepare a reload that completes after a tick to simulate concurrency
    let resolveReload;
    const reloadPromise = new Promise((res) => {
      resolveReload = res;
    });

    // Make the repo return a promise that we control
    LocalisationRepository.findBaseCurrency.mockImplementationOnce(() => reloadPromise);

    // Fire two reloads nearly simultaneously
    const p1 = api.reload();
    const p2 = api.reload();

    // Complete the underlying loader once
    resolveReload({ code: 'JPY' });

    await Promise.all([p1, p2]);

    // Loader should have been invoked exactly once for the concurrent pair
    // (total calls: 1 for initial load + 1 for coalesced reload)
    expect(LocalisationRepository.findBaseCurrency).toHaveBeenCalledTimes(2);

    expect(api.getValue('baseCurrency')).toEqual({ code: 'JPY' });
  });

  it('getValue() returns undefined for unknown key; keys() reflects actual keys', async () => {
    LocalisationRepository.findBaseCurrency.mockResolvedValue({ code: 'USD' });

    await systemSettingPlugin(mockFastify);
    const api = mockFastify.decorate.mock.calls[0][1];

    expect(api.getValue('nonexistent')).toBeUndefined();
    expect(api.keys()).toEqual(['baseCurrency']);
  });
});
