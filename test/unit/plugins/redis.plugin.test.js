import { fastifyRedis } from '@fastify/redis';
import Fastify from 'fastify';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import redisPlugin from '#src/plugins/redis.plugin.js';

vi.mock('@fastify/redis', () => ({
  fastifyRedis: vi.fn(),
}));

describe('Redis Plugin (with db3 namespace)', () => {
  let fastify;

  beforeEach(() => {
    fastify = Fastify();
    fastify.config = {
      REDIS_HOST: 'localhost',
      REDIS_PORT: 6379,
    };
    fastify.log = {
      error: vi.fn(),
      info: vi.fn(),
    };
    // Default mock: capture args only
    fastify.register = vi.fn();
    vi.clearAllMocks();
  });

  it('registers two Redis clients: default (db:2) and namespaced db3 (db:3)', async () => {
    await redisPlugin(fastify);

    // Called twice
    expect(fastify.register).toHaveBeenCalledTimes(2);

    // 1st call: default client (db 2)
    expect(fastify.register).toHaveBeenNthCalledWith(1, fastifyRedis, {
      host: 'localhost',
      port: 6379,
      db: 2,
    });

    // 2nd call: db3 client with namespace
    expect(fastify.register).toHaveBeenNthCalledWith(
      2,
      fastifyRedis,
      expect.objectContaining({
        host: 'localhost',
        port: 6379,
        db: 3,
        namespace: 'db3',
      }),
    );
  });

  it('logs success message mentioning both clients', async () => {
    await redisPlugin(fastify);

    expect(fastify.log.info).toHaveBeenCalledTimes(1);
    const msg = fastify.log.info.mock.calls[0][0];
    expect(msg).toMatch(/Redis connections established/i);
    // If your plugin message includes details, assert them too:
    // e.g., "default=db2, db3 namespace"
    // Adjust to match the exact string in your plugin.
  });

  it('passes the custom host/port to both registrations', async () => {
    fastify.config.REDIS_HOST = 'custom-host';
    fastify.config.REDIS_PORT = 1234;

    await redisPlugin(fastify);

    expect(fastify.register).toHaveBeenNthCalledWith(1, fastifyRedis, {
      host: 'custom-host',
      port: 1234,
      db: 2,
    });

    expect(fastify.register).toHaveBeenNthCalledWith(
      2,
      fastifyRedis,
      expect.objectContaining({
        host: 'custom-host',
        port: 1234,
        db: 3,
        namespace: 'db3',
      }),
    );
  });

  it('propagates an error if the second (db3) registration fails', async () => {
    // First call succeeds; second throws
    fastify.register
      .mockImplementationOnce(() => {}) // first registration OK
      .mockImplementationOnce(() => {
        throw new Error('db3 failed');
      });

    await expect(redisPlugin(fastify)).rejects.toThrow('db3 failed');

    // Should have tried both calls
    expect(fastify.register).toHaveBeenCalledTimes(2);

    // Error is logged
    expect(fastify.log.error).toHaveBeenCalledTimes(1);
    const [err, prefix] = fastify.log.error.mock.calls[0];
    expect(err).toBeInstanceOf(Error);
    expect(err.message).toBe('db3 failed');
    expect(prefix).toMatch(/Failed to initialize Redis plugin/i);
  });

  it('short-circuits on first registration failure (original behaviour preserved)', async () => {
    const error = new Error('default redis failed');
    fastify.register.mockImplementationOnce(() => {
      throw error;
    });

    await expect(redisPlugin(fastify)).rejects.toThrow('default redis failed');

    // Only one attempt made; second registration not attempted
    expect(fastify.register).toHaveBeenCalledTimes(1);

    expect(fastify.log.error).toHaveBeenCalledWith(error, 'Failed to initialize Redis plugin:');
  });
});
