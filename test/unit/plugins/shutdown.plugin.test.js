import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import gracefulShutdownPlugin from '#src/plugins/shutdown.plugin.js';

describe('Graceful Shutdown Plugin', () => {
  let mockFastify;
  let processOnSpy;
  let processExitSpy;

  beforeEach(async () => {
    mockFastify = {
      close: vi.fn().mockResolvedValue(),
    };

    processOnSpy = vi.spyOn(process, 'on');
    processExitSpy = vi.spyOn(process, 'exit').mockImplementation(() => {});
    await gracefulShutdownPlugin(mockFastify);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should register signal handlers for SIGINT and SIGTERM', () => {
    expect(processOnSpy).toHaveBeenCalledTimes(2);
    expect(processOnSpy).toHaveBeenCalledWith('SIGINT', expect.any(Function));
    expect(processOnSpy).toHaveBeenCalledWith('SIGTERM', expect.any(Function));
  });

  it('should close the server and exit process when receiving a signal', async () => {
    // Get the handler function for SIGINT
    const signalHandler = processOnSpy.mock.calls.find((call) => call[0] === 'SIGINT')[1];

    // Simulate receiving a SIGINT signal
    await signalHandler();

    expect(mockFastify.close).toHaveBeenCalledTimes(1);
    expect(processExitSpy).toHaveBeenCalledWith(0);
  });
});
