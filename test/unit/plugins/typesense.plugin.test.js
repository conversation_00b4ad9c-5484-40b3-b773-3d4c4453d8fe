import Typesense from 'typesense';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { TypesensePlugin } from '#src/plugins/typesense.plugin.js';

// Mock the DatabaseFactory
vi.mock('#src/factories/database.factory.js', () => ({
  DatabaseFactory: class {
    constructor(fastify) {
      this.fastify = fastify;
    }
    static createPlugin() {
      return () => {};
    }
  },
}));

// Mock Typesense
vi.mock('typesense', () => ({
  default: {
    Client: vi.fn(() => ({
      health: {
        retrieve: vi.fn().mockResolvedValue(true),
      },
    })),
  },
}));

describe('TypesensePlugin', () => {
  let typesensePlugin;
  let mockFastify;
  let mockTypesenseClient;

  beforeEach(async () => {
    mockFastify = {
      config: {
        TYPESENSE_API_KEY: 'test-api-key',
        TYPESENSE_HOST: 'localhost',
        TYPESENSE_PORT: '8108',
        TYPESENSE_PROTOCOL: 'http',
      },
      log: {
        info: vi.fn(),
      },
      decorate: vi.fn(),
    };

    mockTypesenseClient = {
      health: {
        retrieve: vi.fn().mockResolvedValue(true),
      },
    };

    vi.mocked(Typesense.Client).mockReturnValue(mockTypesenseClient);

    typesensePlugin = new TypesensePlugin(mockFastify);

    await typesensePlugin.connect();
  });

  it('should connect to Typesense successfully', () => {
    expect(typesensePlugin.typesense).toBeDefined();
    expect(mockTypesenseClient.health.retrieve).toHaveBeenCalled();
    expect(mockFastify.log.info).toHaveBeenCalledWith('Typesense connection established');
  });

  it('should create Typesense client with correct configuration', () => {
    expect(vi.mocked(Typesense.Client)).toHaveBeenCalledWith({
      nodes: [
        {
          host: 'localhost',
          port: '8108',
          protocol: 'http',
        },
      ],
      apiKey: 'test-api-key',
      connectionTimeoutSeconds: 2,
    });
  });

  it('should decorate fastify instance with typesense client', () => {
    typesensePlugin.decorate();

    expect(mockFastify.decorate).toHaveBeenCalledWith('typesense', typesensePlugin.typesense);
  });

  it('should create plugin with correct parameters', () => {
    const createPluginSpy = vi.spyOn(TypesensePlugin, 'createPlugin');

    TypesensePlugin.createPlugin('typesense', false);

    expect(createPluginSpy).toHaveBeenCalledWith('typesense', false);
  });
});
