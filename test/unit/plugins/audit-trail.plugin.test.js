import Fastify from 'fastify';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { VIEW_ACTION } from '#src/modules/core/constants/core.constant.js';
import auditTrailPlugin from '#src/plugins/audit-trail.plugin.js';
import * as auditUtil from '#src/utils/audit-trail.util.js';

describe('auditTrailPlugin', () => {
  let fastify;
  let request;

  beforeEach(async () => {
    fastify = Fastify();
    await fastify.register(auditTrailPlugin);
    request = {
      auditEntries: {
        details: {
          metrics: {},
        },
        action: {
          translationKey: 'auditTrail.updateUser.action',
        },
        description: {
          translationKey: null,
          translationParams: {},
        },
      },
      server: {
        log: {
          warn: vi.fn(),
        },
      },
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should decorate fastify with withAuditLogging', () => {
    expect(typeof fastify.withAuditLogging).toBe('function');
  });

  it('should call buildAuditTrailTarget when valid modelMapping is provided', async () => {
    const buildAuditTrailTargetSpy = vi.spyOn(auditUtil, 'buildAuditTrailTarget');

    const modelMapping = {
      User: {
        fieldsChanged: ['username', 'email'],
        beforeState: { username: 'oldUser', email: '<EMAIL>' },
        afterState: { username: 'newUser', email: '<EMAIL>' },
        referenceDetails: { label: 'User A' },
      },
    };

    await fastify.withAuditLogging({
      request,
      modelMapping,
      metrics: { duration: 120 },
      status: 200,
    });

    expect(buildAuditTrailTargetSpy).toHaveBeenCalledOnce();
    expect(buildAuditTrailTargetSpy).toHaveBeenCalledWith(request, {
      modelMapping,
      action: 'auditTrail.updateUser.action',
      metrics: { duration: 120 },
      status: 200,
    });

    buildAuditTrailTargetSpy.mockRestore();
  });

  it('should skip buildAuditTrailTarget and log warning if modelMapping is empty and action is not a view', async () => {
    request.auditEntries.action.translationKey = 'auditTrail.updateUser.action';

    const buildAuditTrailTargetSpy = vi.spyOn(auditUtil, 'buildAuditTrailTarget');

    await fastify.withAuditLogging({
      request,
      modelMapping: {},
    });

    expect(buildAuditTrailTargetSpy).not.toHaveBeenCalled();
    expect(request.server.log.warn).toHaveBeenCalledWith(
      'withAuditLogging called without modelMapping for non-view actions.',
    );

    buildAuditTrailTargetSpy.mockRestore();
  });

  it('should allow calling withAuditLogging for view action even if modelMapping is empty', async () => {
    request.auditEntries.action.translationKey = `auditTrail.${VIEW_ACTION.DETAILS_VIEWED}.action`;

    const buildAuditTrailTargetSpy = vi.spyOn(auditUtil, 'buildAuditTrailTarget');

    await fastify.withAuditLogging({
      request,
      modelMapping: {},
    });

    expect(buildAuditTrailTargetSpy).toHaveBeenCalled();

    buildAuditTrailTargetSpy.mockRestore();
  });

  it("Should default action to 'UNKNOWN_ACTION' when request.auditEntries is undefined", async () => {
    request.auditEntries = undefined;
    const buildAuditTrailTargetSpy = vi.spyOn(auditUtil, 'buildAuditTrailTarget');
    await fastify.withAuditLogging({
      request,
      modelMapping: {},
    });
    expect(buildAuditTrailTargetSpy).not.toHaveBeenCalled();
    expect(request.server.log.warn).toHaveBeenCalledWith(
      'withAuditLogging called without modelMapping for non-view actions.',
    );
    buildAuditTrailTargetSpy.mockRestore();
  });

  it("Should default action to 'UNKNOWN_ACTION' when request.auditEntries is null", async () => {
    request.auditEntries = null;
    const buildAuditTrailTargetSpy = vi.spyOn(auditUtil, 'buildAuditTrailTarget');
    await fastify.withAuditLogging({
      request,
      modelMapping: {},
    });
    expect(buildAuditTrailTargetSpy).not.toHaveBeenCalled();
    expect(request.server.log.warn).toHaveBeenCalledWith(
      'withAuditLogging called without modelMapping for non-view actions.',
    );
    buildAuditTrailTargetSpy.mockRestore();
  });
});
