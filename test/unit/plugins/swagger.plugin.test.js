import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { swaggerConfig, swaggerUiConfig } from '#config/swagger/swagger.config.js';
import swaggerPlugin from '#src/plugins/swagger.plugin.js';

// Mock the dependencies
vi.mock('fastify-plugin', () => ({
  default: vi.fn((fn) => fn),
}));

vi.mock('@fastify/swagger', () => ({ default: vi.fn() }));

vi.mock('@fastify/swagger-ui', () => ({ default: vi.fn() }));

vi.mock('#config/swagger.config.js', () => ({
  swaggerConfig: { swagger: 'config' },
  swaggerUiConfig: { swaggerUi: 'config' },
}));

describe('Swagger Plugin', () => {
  let mockFastify;

  beforeEach(async () => {
    mockFastify = {
      register: vi.fn().mockResolvedValue(),
      log: {
        info: vi.fn(),
      },
    };

    await swaggerPlugin(mockFastify);
  });

  it('should register swagger with correct config', () => {
    expect(mockFastify.register).toHaveBeenCalledWith(swagger, swaggerConfig);
  });

  it('should register swagger-ui with correct config', () => {
    expect(mockFastify.register).toHaveBeenCalledWith(swaggerUi, swaggerUiConfig);
  });

  it('should log info message after successful registration', () => {
    expect(mockFastify.log.info).toHaveBeenCalledWith('Swagger plugin registered successfully');
  });

  it('should handle errors during registration', async () => {
    const error = new Error('Registration failed');
    mockFastify.register.mockRejectedValueOnce(error);

    await expect(swaggerPlugin(mockFastify)).rejects.toThrow('Registration failed');
  });
});
