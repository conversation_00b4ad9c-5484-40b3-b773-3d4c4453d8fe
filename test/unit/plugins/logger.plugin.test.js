import { beforeEach, describe, expect, it, vi } from 'vitest';

import { pinoLogger } from '#config/pino-logger.config.js';

// Mock the dependencies
vi.mock('fastify-plugin', () => ({
  __esModule: true,
  default: vi.fn((fn) => fn),
}));

vi.mock('#config/pino-logger.config.js', () => ({
  pinoLogger: {
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

// Import the plugin after mocking dependencies
const loggerPlugin = (await import('#src/plugins/logger.plugin.js')).default;

describe('Logger Plugin', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      decorate: vi.fn(),
    };
    vi.clearAllMocks();
  });

  it('should decorate fastify instance with log property', async () => {
    await loggerPlugin(mockFastify);

    expect(mockFastify.decorate).toHaveBeenCalledWith('log', pinoLogger);
  });

  it('should return a function', () => {
    expect(typeof loggerPlugin).toBe('function');
  });

  it('should be an async function', () => {
    expect(loggerPlugin.constructor.name).toBe('AsyncFunction');
  });

  it('should not throw when called', async () => {
    await expect(loggerPlugin(mockFastify)).resolves.not.toThrow();
  });

  it('should ignore opts parameter', async () => {
    const opts = { someOption: 'value' };
    await loggerPlugin(mockFastify, opts);

    expect(mockFastify.decorate).toHaveBeenCalledWith('log', pinoLogger);
    // Ensure that opts are not used in any way
    expect(mockFastify.decorate).toHaveBeenCalledTimes(1);
  });

  it('should call logger methods without errors', () => {
    pinoLogger.info('info message');
    pinoLogger.error('error message');
    pinoLogger.debug('debug message');

    expect(pinoLogger.info).toHaveBeenCalledWith('info message');
    expect(pinoLogger.error).toHaveBeenCalledWith('error message');
    expect(pinoLogger.debug).toHaveBeenCalledWith('debug message');
  });

  it('should register the plugin with fastify', async () => {
    const registerSpy = vi.spyOn(mockFastify, 'decorate');
    await loggerPlugin(mockFastify);

    expect(registerSpy).toHaveBeenCalledWith('log', pinoLogger);
  });
});
