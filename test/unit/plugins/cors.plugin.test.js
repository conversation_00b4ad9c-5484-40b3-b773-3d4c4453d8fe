import fastify from 'fastify';
import { beforeEach, describe, expect, it } from 'vitest';

import corsPlugin from '#src/plugins/cors.plugin.js';

/**
 * Test suite for CORS Plugin.
 * This describe block contains tests for the CORS plugin to ensure it is
 * registered correctly with default and custom options.
 */
describe('CORS Plugin', () => {
  let app;

  beforeEach(() => {
    app = fastify();
  });

  /**
   * This test ensures that the CORS plugin is registered correctly with default options,
   * and that the appropriate CORS headers are set in the response.
   */
  it('should register CORS with default options', async () => {
    await app.register(corsPlugin);

    const response = await app.inject({
      method: 'OPTIONS',
      url: '/',
    });

    expect(response.headers['access-control-allow-origin']).toBe('*');
    expect(response.headers['access-control-allow-credentials']).toBe('true');
  });

  /**
   * This test verifies that custom CORS options can be provided when registering the plugin,
   * and that these options are correctly applied in the response headers.
   */
  it('should allow overriding options', async () => {
    await app.register(corsPlugin, {
      origin: 'https://example.com',
      methods: ['GET'],
    });

    const response = await app.inject({
      method: 'OPTIONS',
      url: '/',
    });

    expect(response.headers['access-control-allow-origin']).toBe('https://example.com');
  });

  it('should handle preflight requests correctly', async () => {
    await app.register(corsPlugin, {
      origin: 'https://example.com',
      methods: ['GET', 'POST'],
      allowedHeaders: ['Content-Type', 'Authorization'],
    });

    const response = await app.inject({
      method: 'OPTIONS',
      url: '/',
      headers: {
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type',
      },
    });

    expect(response.headers['access-control-allow-origin']).toBe('https://example.com');
  });

  it('should not throw errors with invalid options', async () => {
    await expect(app.register(corsPlugin, { origin: 123 })).resolves.not.toThrow();
  });
});
