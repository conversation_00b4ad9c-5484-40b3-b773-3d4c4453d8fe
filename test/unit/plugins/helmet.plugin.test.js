import fastify from 'fastify';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';

import helmetPlugin from '#src/plugins/helmet.plugin.js';

/**
 * Test suite for Helmet Plugin.
 * This suite tests the configuration and behavior of the Helmet security plugin.
 */
describe('Helmet Plugin', () => {
  let originalNodeEnv;
  let app;

  beforeEach(() => {
    originalNodeEnv = process.env.NODE_ENV;
    app = fastify();
  });

  afterEach(() => {
    process.env.NODE_ENV = originalNodeEnv;
  });

  /**
   * This test ensures that Helmet is properly configured with default security headers
   * when the application is running in production mode.
   */
  it('should register helmet with default options in production', async () => {
    process.env.NODE_ENV = 'production';

    await app.register(helmetPlugin);

    const response = await app.inject({
      method: 'GET',
      url: '/',
    });

    // expect(response.headers['content-security-policy']).toBeDefined(); Fix this assertion
    expect(response.headers['x-frame-options']).toBe('DENY');
    expect(response.headers['referrer-policy']).toBe('same-origin');
    expect(response.headers['x-xss-protection']).toBe('0');
    expect(response.headers['x-content-type-options']).toBe('nosniff');
  });

  /**
   * This test ensures that Helmet is properly configured with default security headers
   * when the application is running in production mode.
   */
  it('should allow overriding options', async () => {
    app.config = { NODE_ENV: 'production' };

    await app.register(helmetPlugin, {
      frameguard: { action: 'sameorigin' },
    });

    const response = await app.inject({
      method: 'GET',
      url: '/',
    });

    expect(response.headers['x-frame-options']).toBe('SAMEORIGIN');
  });
});
