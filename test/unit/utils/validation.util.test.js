import fastifyMultipart from '@fastify/multipart';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('ajv');
vi.mock('ajv-formats');
vi.mock('@fastify/multipart');

/**
 * Describes the test suite for the Validation Utility.
 * This suite tests the initialization and configuration of Ajv (Another JSON Schema Validator)
 * for use in validating data within the application.
 */
describe('Validation Utility', () => {
  let ajv;
  let mockAddFormat;
  let mockAjvInstance;

  beforeEach(async () => {
    vi.resetModules();
    mockAddFormat = vi.fn();
    mockAjvInstance = {
      addFormat: mockAddFormat,
    };
    vi.mocked(Ajv).mockImplementation(() => mockAjvInstance);

    vi.mocked(addFormats).mockImplementation((ajvInstance) => {
      ajvInstance.addFormat('date-time', () => true);
    });

    vi.mocked(fastifyMultipart.ajvFilePlugin).mockImplementation(() => {});
    const validationUtilModule = await import('#src/utils/validation.util.js');
    ajv = validationUtilModule.default;
  });

  it('should initialize Ajv with correct options', () => {
    expect(Ajv).toHaveBeenCalledWith({
      removeAdditional: true,
      useDefaults: true,
      coerceTypes: false,
      allErrors: true,
    });
  });

  it('should add standard formats to Ajv instance', () => {
    expect(addFormats).toHaveBeenCalledWith(mockAjvInstance);
  });

  it('should add custom file format and validate correctly', () => {
    expect(mockAddFormat).toHaveBeenCalledWith(
      'file',
      expect.objectContaining({
        type: 'object',
        validate: expect.any(Function),
      }),
    );

    expect(mockAddFormat.mock.calls.length).toBeGreaterThan(0);
    const fileFormatCall = mockAddFormat.mock.calls.find((call) => call[0] === 'file');
    expect(fileFormatCall).toBeDefined();

    const validateFile = fileFormatCall[1].validate;
    expect(validateFile({ toBuffer: () => {} })).toBe(true);
    expect(validateFile({})).toBe(false);
  });

  it('should integrate Ajv with Fastify Multipart', () => {
    expect(fastifyMultipart.ajvFilePlugin).toHaveBeenCalledWith(mockAjvInstance);
  });

  it('should export the configured Ajv instance', () => {
    expect(ajv).toBe(mockAjvInstance);
  });
});
