import { beforeEach, describe, expect, it, vi } from 'vitest';

import { sendTelegramMessage } from '#src/utils/telegram.util.js';

describe('sendTelegramMessage', () => {
  const token = '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11';
  const chatId = '987654321';
  const content = 'Hello Telegram!';
  let mockAxios;

  beforeEach(() => {
    vi.clearAllMocks();
    mockAxios = {
      post: vi.fn(),
      interceptors: { request: { use: vi.fn((fn) => fn({ headers: { 'X-Test': 'yes' } })) } },
    };
  });

  it('should return { data, requestHeaders } when Telegram API responds with ok: true', async () => {
    const mockResponse = {
      ok: true,
      result: { message_id: 1, chat: { id: chatId }, text: content },
    };

    mockAxios.post.mockResolvedValueOnce({ data: mockResponse });

    const result = await sendTelegramMessage({ token, chatId, content }, mockAxios);

    expect(result).toEqual({
      data: mockResponse,
      requestHeaders: { 'X-Test': 'yes' },
    });
    expect(mockAxios.post).toHaveBeenCalledWith(
      `https://api.telegram.org/bot${token}/sendMessage`,
      { chat_id: chatId, text: content },
    );
  });

  it('should throw object when Telegram API responds with ok: false and description', async () => {
    const mockResponse = { ok: false, description: 'Unauthorised' };
    mockAxios.post.mockResolvedValueOnce({ data: mockResponse });

    await expect(sendTelegramMessage({ token, chatId, content }, mockAxios)).rejects.toMatchObject({
      success: false,
      message: 'Unauthorised',
      requestHeaders: { 'X-Test': 'yes' },
    });
  });

  it('should throw object on Axios failure', async () => {
    mockAxios.post.mockRejectedValueOnce(new Error('Network error'));

    await expect(sendTelegramMessage({ token, chatId, content }, mockAxios)).rejects.toMatchObject({
      success: false,
      message: 'Network error',
      requestHeaders: { 'X-Test': 'yes' },
    });
  });
});
