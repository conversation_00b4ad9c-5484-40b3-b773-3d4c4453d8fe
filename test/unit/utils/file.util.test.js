// Import utility functions

// Import test utilities
import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';

import { describe, expect, it } from 'vitest';

import * as FileUtil from '#src/utils/file.util.js';

// Get the actual file path
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '../../..');

/**
 * Test suite for File Utility Functions.
 * This describe block contains tests for various file utility functions.
 */
describe('File Utility Functions', () => {
  it('getPackageJson should return a valid package.json object', () => {
    const packageJson = FileUtil.getPackageJson();
    expect(packageJson).toBeTypeOf('object');
    expect(packageJson).toHaveProperty('name');
    expect(packageJson).toHaveProperty('version');
  });

  it('getLogFilePath should return the correct log file path', () => {
    process.env.APP_NAME = 'test-app';
    const logPath = FileUtil.getLogFilePath();
    expect(logPath).toBe(join(projectRoot, 'logs', 'test-app'));
  });

  it('getPluginsPath should return the correct plugins path', () => {
    const pluginsPath = FileUtil.getPluginsPath();
    expect(pluginsPath).toBe(join(projectRoot, 'src', 'plugins'));
  });

  it('getModulesPath should return the correct modules path', () => {
    const modulesPath = FileUtil.getModulesPath();
    expect(modulesPath).toBe(join(projectRoot, 'src', 'modules'));
  });

  it('getHooksPath should return the correct hooks path', () => {
    const hooksPath = FileUtil.getHooksPath();
    expect(hooksPath).toBe(join(projectRoot, 'src', 'hooks'));
  });

  it('getModelPath should return the correct model path', () => {
    const modelPath = FileUtil.getModelPath();
    expect(modelPath).toBe(join(projectRoot, 'src', 'modules', 'core', 'models'));
  });
});
