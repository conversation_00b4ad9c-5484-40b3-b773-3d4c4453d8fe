import { afterAll, beforeAll, describe, expect, it } from 'vitest';

import { decrypt, encrypt } from '#src/utils/aes.util.js';

const ORIGINAL_ENV = process.env;

describe('AES-GCM Encryption Utility', () => {
  const sampleText = 'Sensitive data 123!@#';

  beforeAll(() => {
    process.env.AES_SECRET_KEY = 'a'.repeat(64);
  });
  afterAll(() => {
    process.env = ORIGINAL_ENV;
  });

  it('should encrypt and decrypt text correctly', () => {
    const encrypted = encrypt(sampleText);
    expect(typeof encrypted).toBe('string');

    const decrypted = decrypt(encrypted);
    expect(decrypted).toBe(sampleText);
  });

  it('should return different ciphertexts for same input due to random IV', () => {
    const encrypted1 = encrypt(sampleText);
    const encrypted2 = encrypt(sampleText);

    expect(encrypted1).not.toBe(encrypted2);
  });

  it('should throw an error on tampered ciphertext', () => {
    const encrypted = encrypt(sampleText);
    const tampered = encrypted.slice(0, -4) + 'abcd'; // corrupting end

    expect(() => decrypt(tampered)).toThrow();
  });

  it('should throw if AES_SECRET_KEY is missing or invalid', () => {
    process.env.AES_SECRET_KEY = ''; // blank key

    expect(() => encrypt(sampleText)).toThrow();
    expect(() => decrypt('')).toThrow();
  });
  it('should throw if AES_SECRET_KEY is not 32 bytes in hex', () => {
    process.env.AES_SECRET_KEY = 'deadbeef';
    expect(() => encrypt(sampleText)).toThrowError(/invalidEnv/);
    expect(() => decrypt('')).toThrowError(/invalidEnv/);
  });
});
