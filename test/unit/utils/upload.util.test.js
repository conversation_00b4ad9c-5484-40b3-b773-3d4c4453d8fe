import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreError } from '#src/modules/core/errors/index.js';
import { uploadToS3 } from '#src/utils/upload.util.js';

// Mock AWS S3
vi.mock('@aws-sdk/client-s3', () => ({
  S3Client: vi.fn(),
  PutObjectCommand: vi.fn(),
}));

// Mock crypto.randomBytes for predictable filenames
vi.mock('crypto', async () => {
  const actual = await vi.importActual('crypto');
  return {
    ...actual,
    randomBytes: vi.fn(() => Buffer.from('12345678', 'utf-8')),
  };
});

describe('uploadToS3', () => {
  let mockS3Instance;
  let credentials;
  let file;

  beforeEach(() => {
    vi.clearAllMocks();

    mockS3Instance = { send: vi.fn().mockResolvedValue({}) };
    S3Client.mockImplementation(() => mockS3Instance);

    credentials = {
      awsRegion: 'us-east-1',
      awsAccessKeyID: 'mock-access-key',
      awsSecretKey: 'mock-secret-key',
      awsBucketName: 'mock-bucket',
      baseUrl: 'https://bucket.s3.amazonaws.com',
    };

    file = {
      fileContent: Buffer.from('test file'),
      originalname: 'test.png',
      mimeType: 'image/png',
    };
  });

  it('uploads a valid file and returns the correct URL', async () => {
    const url = await uploadToS3({ credentials, file });

    expect(S3Client).toHaveBeenCalledWith({
      region: 'us-east-1',
      credentials: {
        accessKeyId: 'mock-access-key',
        secretAccessKey: 'mock-secret-key',
      },
    });

    expect(PutObjectCommand).toHaveBeenCalledWith(
      expect.objectContaining({
        Bucket: credentials.awsBucketName,
        Body: file.fileContent,
        ContentType: file.mimeType,
        ACL: 'public-read',
        Key: expect.stringMatching(/\.png$/),
      }),
    );

    expect(url).toContain(credentials.baseUrl);
    expect(url).toMatch(/\.png$/);
  });

  it('throws error when file size exceeds 5 MB', async () => {
    file.fileContent = Buffer.alloc(6 * 1024 * 1024); // 6MB

    await expect(uploadToS3({ credentials, file })).rejects.toThrow(
      CoreError.format({ attribute: 'test', format: 'size exceeds 5 MB limit' }),
    );
  });

  it('throws error when file type is invalid', async () => {
    file.mimeType = 'text/plain';

    await expect(uploadToS3({ credentials, file })).rejects.toThrow(
      CoreError.format({
        attribute: 'test',
        format: 'JPEG, JPG, PNG, PDF, PNG, GIF, PDF, CSV, MPEG, OGG, and WAV',
      }),
    );
  });

  it('generates unique filenames for different uploads', async () => {
    const url1 = await uploadToS3({ credentials, file });

    // Change system time to ensure filename changes
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2025-08-26T10:00:00Z'));

    const url2 = await uploadToS3({ credentials, file });

    expect(url1).not.toEqual(url2);

    vi.useRealTimers();
  });

  it('throws error if S3 upload fails', async () => {
    mockS3Instance.send.mockRejectedValueOnce(new Error('Upload failed'));

    await expect(uploadToS3({ credentials, file })).rejects.toThrow('Upload failed');
  });
});
