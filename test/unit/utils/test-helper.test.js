import { describe, expect, it } from 'vitest';

import { populateToJSON } from '#src/utils/test-helper.util.js';

describe('populateToJSON', () => {
  it('should add a toJSON method to the object', () => {
    const obj = { name: 'Alice', age: 25 };
    const result = populateToJSON(obj);

    expect(typeof result.toJSON).toBe('function');
  });

  it('should exclude toJSO<PERSON> from the result when stringified', () => {
    const obj = { name: '<PERSON>', age: 25 };
    const result = populateToJSON(obj);

    const json = JSON.stringify(result);
    const parsed = JSON.parse(json);

    expect(parsed.toJSON).toBeUndefined();
    expect(parsed.name).toBe('Alice');
    expect(parsed.age).toBe(25);
  });

  it('should return the object as is if it is not an object', () => {
    const nonObject = 'string';
    const result = populateToJSON(nonObject);
    expect(result).toBe(nonObject);
  });

  it('should handle null and undefined inputs gracefully', () => {
    const resultNull = populateToJSON(null);
    const resultUndefined = populateToJSON(undefined);

    expect(resultNull).toBeNull();
    expect(resultUndefined).toBeUndefined();
  });

  it('should return the object unchanged if it already has a toJSON method', () => {
    const obj = { name: 'Alice', age: 25, toJSON: () => ({ name: 'Bob' }) };
    const result = populateToJSON(obj);

    expect(result.toJSON()).toEqual({ name: 'Bob' });
  });
});
