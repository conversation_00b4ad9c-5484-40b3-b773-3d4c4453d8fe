import speakeasy from 'speakeasy';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { generate2FASecret, handle2FASetup, validateTOTPToken } from '#src/utils/twofa.util.js';

vi.mock('qrcode', () => ({
  toDataURL: vi.fn(),
}));

vi.mock('speakeasy', () => ({
  default: {
    generateSecret: vi.fn(),
    otpauthURL: vi.fn(),
    totp: {
      verify: vi.fn(),
    },
  },
}));

vi.mock('#src/modules/user/errors/index.js', () => ({
  AuthError: {
    invalid2FAToken: vi.fn(() => new Error('Invalid 2FA token')),
  },
}));

describe('2FA Utility Functions', () => {
  let mockUser;
  let mockExisting2FA;

  beforeEach(() => {
    mockUser = {
      id: 'user123',
      username: 'testuser',
      ua: [
        {
          entity: {
            name: 'Test Organization',
          },
        },
      ],
    };

    mockExisting2FA = {
      secretKey: 'JBSWY3DPEHPK3PXP',
    };

    vi.clearAllMocks();
  });

  describe('generate2FASecret', () => {
    it('should generate a 2FA secret with a base32 string', () => {
      speakeasy.generateSecret.mockReturnValue({
        base32: 'JBSWY3DPEHPK3PXP',
        otpauth_url: 'otpauth://totp/app?secret=JBSWY3DPEHPK3PXP',
      });

      const result = generate2FASecret('testapp');

      expect(speakeasy.generateSecret).toHaveBeenCalledWith({ name: 'testapp' });
      expect(result).toEqual({
        base32secret: 'JBSWY3DPEHPK3PXP',
      });
    });

    it('should use default label when none provided', () => {
      speakeasy.generateSecret.mockReturnValue({
        base32: 'JBSWY3DPEHPK3PXP',
        otpauth_url: 'otpauth://totp/app?secret=JBSWY3DPEHPK3PXP',
      });

      const result = generate2FASecret();

      expect(speakeasy.generateSecret).toHaveBeenCalledWith({ name: 'app' });
      expect(result).toEqual({
        base32secret: 'JBSWY3DPEHPK3PXP',
      });
    });
  });

  describe('handle2FASetup', () => {
    it('should generate QR code for 2FA setup successfully', async () => {
      const { toDataURL } = await import('qrcode');

      const mockOtpauthUrl =
        'otpauth://totp/Test%20Organization:testuser?secret=JBSWY3DPEHPK3PXP&issuer=Test%20Organization';
      const mockQrCodeUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...';

      speakeasy.otpauthURL.mockReturnValue(mockOtpauthUrl);
      toDataURL.mockResolvedValue(mockQrCodeUrl);

      const result = await handle2FASetup(mockUser, mockExisting2FA);

      expect(speakeasy.otpauthURL).toHaveBeenCalledWith({
        secret: mockExisting2FA.secretKey,
        label: mockUser.username,
        issuer: `QPLY2.0 ${mockUser.ua[0].entity.name}`,
        encoding: 'base32',
      });

      expect(toDataURL).toHaveBeenCalledWith(mockOtpauthUrl);

      expect(result).toEqual(mockQrCodeUrl);
    });

    it('should handle QR code generation failure', async () => {
      const { toDataURL } = await import('qrcode');

      const mockOtpauthUrl =
        'otpauth://totp/Test%20Organization:testuser?secret=JBSWY3DPEHPK3PXP&issuer=Test%20Organization';
      const qrError = new Error('QR code generation failed');

      speakeasy.otpauthURL.mockReturnValue(mockOtpauthUrl);
      toDataURL.mockRejectedValue(qrError);

      await expect(handle2FASetup(mockUser, mockExisting2FA)).rejects.toThrow(
        'QR code generation failed',
      );
    });
  });

  describe('validateTOTPToken', () => {
    it('should validate TOTP token successfully when token is valid', async () => {
      const { AuthConstant } = await import('#src/modules/user/constants/index.js');

      speakeasy.totp.verify.mockReturnValue(true);

      const secretKey = 'JBSWY3DPEHPK3PXP';
      const token = '123456';

      expect(() => validateTOTPToken(secretKey, token)).not.toThrow();

      expect(speakeasy.totp.verify).toHaveBeenCalledWith({
        secret: secretKey,
        encoding: 'base32',
        token,
        window: AuthConstant.SECURITY.TWO_FACTOR_AUTHENTICATION_WINDOW_DURATION,
      });
    });

    it('should throw error when TOTP token is invalid', async () => {
      const { AuthError } = await import('#src/modules/user/errors/index.js');

      speakeasy.totp.verify.mockReturnValue(false);

      const secretKey = 'JBSWY3DPEHPK3PXP';
      const token = '000000';

      expect(() => validateTOTPToken(secretKey, token)).toThrow('Invalid 2FA token');

      expect(AuthError.invalid2FAToken).toHaveBeenCalled();
    });

    it('should use correct parameters for TOTP verification', async () => {
      const { AuthConstant } = await import('#src/modules/user/constants/index.js');

      speakeasy.totp.verify.mockReturnValue(true);

      const secretKey = 'DIFFERENT_SECRET_KEY';
      const token = '654321';

      validateTOTPToken(secretKey, token);

      expect(speakeasy.totp.verify).toHaveBeenCalledWith({
        secret: secretKey,
        encoding: 'base32',
        token,
        window: AuthConstant.SECURITY.TWO_FACTOR_AUTHENTICATION_WINDOW_DURATION,
      });
    });

    it('should handle empty or null token', async () => {
      const { AuthError } = await import('#src/modules/user/errors/index.js');

      speakeasy.totp.verify.mockReturnValue(false);

      const secretKey = 'JBSWY3DPEHPK3PXP';

      expect(() => validateTOTPToken(secretKey, '')).toThrow('Invalid 2FA token');
      expect(() => validateTOTPToken(secretKey, null)).toThrow('Invalid 2FA token');

      expect(AuthError.invalid2FAToken).toHaveBeenCalledTimes(2);
    });

    it('should handle empty or null secret key', async () => {
      const { AuthError } = await import('#src/modules/user/errors/index.js');

      speakeasy.totp.verify.mockReturnValue(false);

      const token = '123456';

      expect(() => validateTOTPToken('', token)).toThrow('Invalid 2FA token');
      expect(() => validateTOTPToken(null, token)).toThrow('Invalid 2FA token');

      expect(AuthError.invalid2FAToken).toHaveBeenCalledTimes(2);
    });
  });
});
