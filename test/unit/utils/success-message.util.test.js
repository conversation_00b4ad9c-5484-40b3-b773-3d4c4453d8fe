import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';

describe('SuccessMessage Utility', () => {
  let SuccessMessage;
  let instance;
  let mockFastify;
  let mockTranslateMessage;

  beforeEach(async () => {
    // Clear the require cache and re-import to get fresh instance
    vi.resetModules();

    mockFastify = {
      t: vi.fn().mockImplementation((key, _opts = {}) => {
        return `t_${key}`;
      }),
    };

    mockTranslateMessage = vi.fn().mockImplementation((template, _opts, _fastify) => {
      return `translated_${template}`;
    });

    vi.doMock('#src/utils/i18next.util.js', () => ({
      translateMessage: mockTranslateMessage,
    }));

    const module = await import('#src/utils/success-message.util.js');
    SuccessMessage = module.default.constructor;
    instance = module.default;
  });

  describe('Singleton Pattern', () => {
    it('should create only one instance', () => {
      const instance1 = SuccessMessage.getInstance();
      const instance2 = SuccessMessage.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should initialize with message templates', () => {
      expect(instance.templates).toBeDefined();
      expect(typeof instance.templates[CoreConstant.MODULE_METHODS.INDEX]).toBe('string');
    });
  });

  describe('getTemplate()', () => {
    const module = 'user';
    const testCases = Object.keys(CoreConstant.MODULE_METHODS).map((key) => {
      return {
        type: CoreConstant.MODULE_METHODS[key],
        expected: `translated_common.sentence.${CoreConstant.MODULE_METHODS[key]}Success`,
      };
    });

    testCases.forEach(({ type, expected }) => {
      it(`should return correct message for ${type} operation`, () => {
        const result = instance.getTemplate(mockFastify, module, type);
        expect(result).toBe(expected);
      });
    });

    it('should return default message when type is not in templates', () => {
      const unknownType = 'unknownType';
      const result = instance.getTemplate(mockFastify, 'user', unknownType);

      expect(result).toBe('translated_common.sentence.actionSuccess');
      expect(mockTranslateMessage).toHaveBeenCalledWith(
        'common.sentence.actionSuccess',
        undefined,
        mockFastify,
      );
    });
  });
});
