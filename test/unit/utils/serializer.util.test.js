import { describe, expect, it } from 'vitest';

import { serializeError, serializeReply, serializeRequest } from '#src/utils/serializer.util.js';

describe('serializer', () => {
  describe('serializeRequest', () => {
    it('should correctly serialize all fields for a valid input object', () => {
      const mockInput = {
        headers: { 'content-type': 'application/json' },
        ip: '127.0.0.1',
        method: 'GET',
        params: { id: '123' },
        url: '/api/users',
        query: { page: '1' },
        body: { name: '<PERSON>' },
        id: 'req-001',
        user: { id: 'user-001' },
      };

      const result = serializeRequest(mockInput);

      expect(result).toEqual({
        headers: { 'content-type': 'application/json' },
        ipAddress: '127.0.0.1',
        method: 'GET',
        parameters: { id: '123' },
        path: '/api/users',
        query: { page: '1' },
        body: { name: '<PERSON>' },
        requestId: 'req-001',
        userId: 'user-001',
      });
    });

    it('should handle an empty input object gracefully', () => {
      const emptyInput = {};
      const result = serializeRequest(emptyInput);

      expect(result).toEqual({
        headers: {},
        ipAddress: '',
        method: '',
        parameters: undefined,
        path: '',
        query: undefined,
        body: undefined,
        requestId: '',
        userId: 'anonymous',
      });
    });

    it('should correctly set userId to "anonymous" when input.user is null', () => {
      const mockInput = {
        headers: { 'content-type': 'application/json' },
        ip: '127.0.0.1',
        method: 'POST',
        params: { id: '456' },
        url: '/api/items',
        query: { sort: 'desc' },
        body: { item: 'Example' },
        id: 'req-002',
        user: null,
      };

      const result = serializeRequest(mockInput);

      expect(result).toEqual({
        headers: { 'content-type': 'application/json' },
        ipAddress: '127.0.0.1',
        method: 'POST',
        parameters: { id: '456' },
        path: '/api/items',
        query: { sort: 'desc' },
        body: { item: 'Example' },
        requestId: 'req-002',
        userId: 'anonymous',
      });
    });

    it('should correctly set userId to "anonymous" when input.user is undefined', () => {
      const mockInput = {
        headers: { 'content-type': 'application/json' },
        ip: '127.0.0.1',
        method: 'GET',
        params: { id: '789' },
        url: '/api/products',
        query: { category: 'electronics' },
        body: { filter: 'new' },
        id: 'req-003',
        user: undefined,
      };

      const result = serializeRequest(mockInput);

      expect(result).toEqual({
        headers: { 'content-type': 'application/json' },
        ipAddress: '127.0.0.1',
        method: 'GET',
        parameters: { id: '789' },
        path: '/api/products',
        query: { category: 'electronics' },
        body: { filter: 'new' },
        requestId: 'req-003',
        userId: 'anonymous',
      });
    });

    it('should correctly serialize input with nested objects in headers or body', () => {
      const mockInput = {
        headers: {
          'content-type': 'application/json',
          'custom-header': {
            version: '1.0',
            encoding: 'utf-8',
          },
        },
        ip: '127.0.0.1',
        method: 'POST',
        params: { id: '123' },
        url: '/api/users',
        query: { filter: 'active' },
        body: {
          user: {
            name: 'John Doe',
            age: 30,
            address: {
              street: '123 Main St',
              city: 'Anytown',
            },
          },
        },
        id: 'req-nested-001',
        user: { id: 'user-nested-001' },
      };

      const result = serializeRequest(mockInput);

      expect(result).toEqual({
        headers: {
          'content-type': 'application/json',
          'custom-header': {
            version: '1.0',
            encoding: 'utf-8',
          },
        },
        ipAddress: '127.0.0.1',
        method: 'POST',
        parameters: { id: '123' },
        path: '/api/users',
        query: { filter: 'active' },
        body: {
          user: {
            name: 'John Doe',
            age: 30,
            address: {
              street: '123 Main St',
              city: 'Anytown',
            },
          },
        },
        requestId: 'req-nested-001',
        userId: 'user-nested-001',
      });
    });
  });

  describe('serializeReply', () => {
    it('should return undefined for statusCode when input.statusCode is undefined', () => {
      const mockInput = {
        request: {
          responsePayload: { data: 'test' },
          startTime: Date.now() - 100,
        },
      };

      const result = serializeReply(mockInput);
      expect(result).toEqual({
        payload: { data: 'test' },
        statusCode: 0,
        responseTimeMs: expect.stringMatching(/^\d+ms$/),
      });
      expect(result.statusCode).toBe(0);
    });

    it('should handle case when input.request.startTime is undefined', () => {
      const mockInput = {
        request: {
          responsePayload: { data: 'test' },
        },
        statusCode: 200,
      };

      const result = serializeReply(mockInput);

      expect(result).toEqual({
        payload: { data: 'test' },
        statusCode: 200,
        responseTimeMs: expect.stringMatching(/^\d+ms$/),
      });
      expect(parseInt(result.responseTimeMs)).toBe(0);
    });

    it('should handle case when input.request.responsePayload is undefined', () => {
      const mockInput = {
        request: {
          startTime: Date.now() - 100,
        },
        statusCode: 204,
      };

      const result = serializeReply(mockInput);

      expect(result).toEqual({
        payload: undefined,
        statusCode: 204,
        responseTimeMs: expect.stringMatching(/^\d+ms$/),
      });
      expect(parseInt(result.responseTimeMs)).toBeGreaterThanOrEqual(100);
      expect(parseInt(result.responseTimeMs)).toBeLessThan(115); // Allow for small variations in execution time
    });

    it('should handle non-object payloads (e.g., strings, numbers, arrays)', () => {
      const mockInputString = {
        request: {
          responsePayload: 'Hello, World!',
          startTime: Date.now() - 50,
        },
        statusCode: 200,
      };

      const mockInputNumber = {
        request: {
          responsePayload: 42,
          startTime: Date.now() - 30,
        },
        statusCode: 200,
      };

      const mockInputArray = {
        request: {
          responsePayload: [1, 2, 3],
          startTime: Date.now() - 40,
        },
        statusCode: 200,
      };

      const resultString = serializeReply(mockInputString);
      const resultNumber = serializeReply(mockInputNumber);
      const resultArray = serializeReply(mockInputArray);

      expect(resultString).toEqual({
        payload: 'Hello, World!',
        statusCode: 200,
        responseTimeMs: expect.stringMatching(/^\d+ms$/),
      });

      expect(resultNumber).toEqual({
        payload: 42,
        statusCode: 200,
        responseTimeMs: expect.stringMatching(/^\d+ms$/),
      });

      expect(resultArray).toEqual({
        payload: [1, 2, 3],
        statusCode: 200,
        responseTimeMs: expect.stringMatching(/^\d+ms$/),
      });

      expect(parseInt(resultString.responseTimeMs)).toBeGreaterThanOrEqual(50);
      expect(parseInt(resultNumber.responseTimeMs)).toBeGreaterThanOrEqual(30);
      expect(parseInt(resultArray.responseTimeMs)).toBeGreaterThanOrEqual(40);
    });

    it('should calculate responseTimeMs correctly when request.startTime is provided', () => {
      const mockStartTime = Date.now() - 1000; // 1 second ago
      const mockInput = {
        request: {
          responsePayload: { data: 'test' },
          startTime: mockStartTime,
        },
        statusCode: 200,
      };

      const result = serializeReply(mockInput);

      expect(result).toEqual({
        payload: { data: 'test' },
        statusCode: 200,
        responseTimeMs: expect.stringMatching(/^\d+ms$/),
      });

      const calculatedTime = parseInt(result.responseTimeMs);
      expect(calculatedTime).toBeGreaterThanOrEqual(1000);
      expect(calculatedTime).toBeLessThan(1100); // Allow for small variations in execution time
    });

    it('should correctly handle very large responsePayload objects', () => {
      const largePayload = {};
      for (let i = 0; i < 10000; i++) {
        largePayload[`key${i}`] = `value${i}`;
      }

      const mockInput = {
        request: {
          responsePayload: largePayload,
          startTime: Date.now() - 1000, // Simulate 1 second processing time
        },
        statusCode: 200,
      };

      const result = serializeReply(mockInput);

      expect(result).toEqual({
        payload: largePayload,
        statusCode: 200,
        responseTimeMs: expect.stringMatching(/^\d+ms$/),
      });
      expect(Object.keys(result.payload).length).toBe(10000);
      expect(parseInt(result.responseTimeMs)).toBeGreaterThanOrEqual(1000);
    });

    it('should properly serialize when input contains additional unexpected properties', () => {
      const mockInput = {
        request: {
          responsePayload: { data: 'test data' },
          startTime: Date.now() - 100,
        },
        statusCode: 201,
        unexpectedProp1: 'surprise',
        unexpectedProp2: 42,
      };

      const result = serializeReply(mockInput);

      expect(result).toEqual({
        payload: { data: 'test data' },
        statusCode: 201,
        responseTimeMs: expect.stringMatching(/^\d+ms$/),
      });
      expect(result).not.toHaveProperty('unexpectedProp1');
      expect(result).not.toHaveProperty('unexpectedProp2');
      expect(parseInt(result.responseTimeMs)).toBeGreaterThanOrEqual(100);
    });
  });

  describe('serializeError', () => {
    it('should serialize an instance of a custom error class with additional properties', () => {
      class CustomError extends Error {
        constructor(statusCode, message, code, moduleName) {
          super(message);
          this.name = `${moduleName}ModuleError`;
          this.code = code;
          this.statusCode = statusCode;
        }
      }

      const customError = new CustomError(
        400,
        'Custom error occurred',
        'CUSTOM_CODE_123',
        'Custom',
      );

      const result = serializeError(customError);

      expect(result).toEqual({
        type: 'CustomError',
        name: 'CustomModuleError',
        message: 'Custom error occurred',
        stack: expect.any(String),
        code: 'CUSTOM_CODE_123',
        statusCode: 400,
      });
    });

    it('should serialize an instance of a built-in error class', () => {
      const builtInError = new Error('Built-in error occurred');

      const result = serializeError(builtInError);

      expect(result).toEqual({
        type: 'Error',
        name: 'Error',
        message: 'Built-in error occurred',
        stack: expect.any(String),
        code: null,
        statusCode: 500,
      });
    });

    it('should serialize an instance of Error with all properties correctly', () => {
      const errorInstance = new Error('Test error message');
      errorInstance.type = 'TestErrorType';
      errorInstance.code = 'TEST_CODE';
      errorInstance.statusCode = 404;

      const result = serializeError(errorInstance);

      expect(result).toEqual({
        type: 'TestErrorType',
        name: 'Error',
        message: 'Test error message',
        stack: expect.any(String),
        code: 'TEST_CODE',
        statusCode: 404,
      });
    });

    it('should handle serialization of a plain object with no properties', () => {
      const plainObject = {};
      const result = serializeError(plainObject);

      expect(result).toEqual({
        type: 'Object',
        name: 'Error',
        message: 'Unknown error',
        stack: '',
        code: null,
        statusCode: 500,
      });
    });

    it('should return default values when the error object has missing fields', () => {
      const incompleteError = {
        message: 'Incomplete error object',
      };

      const result = serializeError(incompleteError);

      expect(result).toEqual({
        type: 'Object',
        name: 'Error',
        message: incompleteError.message,
        stack: '',
        code: null,
        statusCode: 500,
      });
    });

    it('should handle serialization of an error object with a non-string message', () => {
      const errorWithNonStringMessage = {
        name: 'NonStringMessageError',
        message: { error: 'complex error message' },
        stack: 'Error stack trace',
        code: 'ERR_NON_STRING_MESSAGE',
        statusCode: 500,
      };

      const result = serializeError(errorWithNonStringMessage);

      expect(result).toEqual({
        type: 'Object',
        name: 'NonStringMessageError',
        message: JSON.stringify(errorWithNonStringMessage.message),
        stack: errorWithNonStringMessage.stack,
        code: 'ERR_NON_STRING_MESSAGE',
        statusCode: 500,
      });
    });

    it('should serialize an error object with a stack property that is not a string', () => {
      const errorWithNonStringStack = {
        name: 'NonStringStackError',
        message: 'Error with non-string stack',
        stack: { line: 42, file: 'app.js' },
        code: 'ERR_NON_STRING_STACK',
        statusCode: 400,
      };

      const result = serializeError(errorWithNonStringStack);

      expect(result).toEqual({
        type: 'Object',
        name: 'NonStringStackError',
        message: errorWithNonStringStack.message,
        stack: JSON.stringify(errorWithNonStringStack.stack),
        code: 'ERR_NON_STRING_STACK',
        statusCode: 400,
      });
    });

    it('should handle serialization of an error object with a deeply nested structure', () => {
      const nestedError = {
        name: 'NestedError',
        message: 'An error with nested details',
        stack: 'Error stack trace',
        code: 'ERR_NESTED',
        statusCode: 500,
        details: {
          level1: {
            level2: {
              level3: {
                info: 'Deeply nested information',
              },
            },
          },
        },
      };

      const result = serializeError(nestedError);

      expect(result).toEqual({
        type: 'Object',
        name: 'NestedError',
        message: nestedError.message,
        stack: nestedError.stack,
        code: 'ERR_NESTED',
        statusCode: 500,
        details: nestedError.details,
      });
    });

    it('should handle serialization of an error object with unexpected properties', () => {
      const unexpectedError = {
        name: 'UnexpectedError',
        message: 'An error with unexpected properties',
        stack: 'Error stack trace',
        code: 'ERR_UNEXPECTED',
        statusCode: 500,
        unexpectedProp1: 'value1',
        unexpectedProp2: 'value2',
      };

      const result = serializeError(unexpectedError);

      expect(result).toEqual({
        type: 'Object',
        name: 'UnexpectedError',
        message: unexpectedError.message,
        stack: unexpectedError.stack,
        code: 'ERR_UNEXPECTED',
        statusCode: 500,
        unexpectedProp1: 'value1',
        unexpectedProp2: 'value2',
      });
    });
  });
});
