import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  createCustomError,
  createModuleErrors,
  createValidationError,
} from '#src/utils/error.util.js';

describe('createCustomError', () => {
  let mockFastify;

  beforeEach(() => {
    vi.clearAllMocks();

    mockFastify = { t: vi.fn().mockImplementation((key) => `t_${key}`) };
  });

  it('should create a custom error with the correct name format based on the module name', () => {
    const code = 'CUSTOM_ERROR';
    const message = 'An error occurred';
    const statusCode = 400;
    const moduleName = 'Test';

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError();

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.code).toBe('CUSTOM_ERROR');
    expect(errorInstance.name).toBe('TestModuleError');
    expect(errorInstance.message).toBe('An error occurred');
    expect(errorInstance.statusCode).toBe(statusCode);
    expect(errorInstance.tMessageFn(mockFastify)).toBe(`t_${message}`);
  });

  it('should include additional metadata when provided as additional arguments', () => {
    const code = 'CUSTOM_ERROR';
    const message = 'error.test.sentence.occurred';
    const statusCode = 400;
    const moduleName = 'Test';
    const translationParams = { name: 'Test error message' };
    const additionalData = { detail: 'Additional error details' };

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError(translationParams, additionalData);

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.name).toBe('TestModuleError');
    expect(errorInstance.statusCode).toBe(statusCode);
    expect(errorInstance.metaData).toEqual({
      translationParams,
      0: additionalData,
    });
    expect(errorInstance.tMessageFn(mockFastify)).toBe(`t_${message}`);
  });

  it('should handle multiple arguments in metaData', () => {
    const code = 'CUSTOM_ERROR';
    const message = 'error.test.sentence.multipleArgs';
    const statusCode = 400;
    const moduleName = 'Test';
    const translationParams = { name: 'Test error message' };
    const argument2 = 1;

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError(translationParams, argument2);

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.name).toBe('TestModuleError');
    expect(errorInstance.statusCode).toBe(statusCode);
    expect(errorInstance.metaData).toEqual({
      translationParams,
      0: argument2,
    });
    expect(errorInstance.tMessageFn(mockFastify)).toBe(`t_${message}`);
  });

  it('should correctly instantiate a custom error with translation parameters', () => {
    const code = 'TEMPLATE_ERROR';
    const message = 'error.template.sentence.occurred';
    const statusCode = 500;
    const moduleName = 'Template';
    const translationParams = { argument: 'Template argument' };

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError(translationParams);

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.name).toBe('TemplateModuleError');
    expect(errorInstance.statusCode).toBe(statusCode);
    expect(errorInstance.metaData).toEqual({
      translationParams,
    });
    expect(errorInstance.tMessageFn(mockFastify)).toBe(`t_${message}`);
  });
});

describe('createModuleErrors', () => {
  it('should create an object with error factory functions for all defined errors', () => {
    const moduleName = 'Test';
    const errorDefs = {
      notFound: ['10002', 'error.test.sentence.notFound', 404],
      unauthorised: ['10004', 'error.test.sentence.forbidden', 403],
    };

    const errors = createModuleErrors(moduleName, errorDefs);

    // Verify all error keys are present
    expect(Object.keys(errors)).toEqual(['notFound', 'unauthorised']);

    // Verify each error factory generates the correct error
    const notFoundError = errors.notFound({ id: 'UUID' });

    expect(notFoundError).toBeInstanceOf(Error);
    expect(notFoundError.code).toBe('10002');
    expect(notFoundError.statusCode).toBe(404);
    expect(notFoundError.name).toBe('TestModuleError');
    expect(notFoundError.metaData).toEqual({
      translationParams: { id: 'UUID' },
    });

    // Verify each error factory generates the correct error
    const unauthorisedError = errors.unauthorised();

    expect(unauthorisedError).toBeInstanceOf(Error);
    expect(unauthorisedError.code).toBe('10004');
    expect(unauthorisedError.statusCode).toBe(403);
    expect(unauthorisedError.name).toBe('TestModuleError');
    expect(unauthorisedError.metaData).toEqual({
      translationParams: {},
    });
  });
});

describe('createValidationError', () => {
  const mockValidationErrors = [
    {
      instancePath: [],
      schemaPath: '',
      keyword: '',
      params: {},
      message: '',
    },
  ];

  it('should create a validation error with default message', () => {
    const errorInstance = createValidationError(mockValidationErrors);

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.message).toBe('Validation Error');
    expect(errorInstance.statusCode).toBe(400);
    expect(errorInstance.code).toBe('VALIDATION_ERROR');
  });

  it('should create a validation error with custom message if provided', () => {
    const mockErrorMessage = 'Test error message';

    const errorInstance = createValidationError(mockValidationErrors, mockErrorMessage);

    expect(errorInstance.message).toBe(mockErrorMessage);
  });

  it('should handle single validation error object', () => {
    const errorInstance = createValidationError(mockValidationErrors[0]);

    expect(errorInstance.validation).toEqual(mockValidationErrors);
  });

  it('should handle array validation error object', () => {
    const errorInstance = createValidationError(mockValidationErrors);

    expect(errorInstance.validation).toEqual(mockValidationErrors);
  });
});
