import i18next from 'i18next';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import * as v from '#src/utils/validation-message.util.js';

vi.mock('i18next');

describe('Validation Message Utility', () => {
  const keyPrefix = 'error.validation.sentence.';

  beforeEach(() => {
    vi.clearAllMocks();

    i18next.t = vi.fn().mockImplementation((key, options) => {
      if (!options?.attribute) {
        return `${key.split('.').pop()}`;
      }

      const { attribute, ...other } = options;
      let message = `${key};${options.attribute};`;
      Object.keys(other).forEach((key) => {
        message += `${key}:${other[key]};`;
      });
      return message;
    });
  });

  describe('createAttributeMessage', () => {
    const { createAttributeMessage } = v.exportForUnitTest;

    it('should translate validation message with attribute', () => {
      const key = 'key';
      const attribute = 'attribute';

      const result = createAttributeMessage(i18next, key, attribute);

      expect(result).toEqual(`${keyPrefix}${key};${attribute};`);
    });

    it('should translate validation message with attribute and extra parameters', () => {
      const key = 'key';
      const attribute = 'attribute';
      const extra = { key: 'value' };

      const result = createAttributeMessage(i18next, key, attribute, extra);

      expect(result).toEqual(`${keyPrefix}${key};${attribute};key:value;`);
    });
  });

  describe('m_required', () => {
    it('should translated required validation message without extra parameters', () => {
      const key = 'required';
      const attribute = 'username';

      const result = v.m_required({ i18next, attribute });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};`);
    });
  });

  describe('m_enum', () => {
    it('should translate enum validation message with string values that are not numbers', () => {
      const key = 'enum';
      const attribute = 'status';
      const params = {
        allowedValues: ['active', 'inactive'],
      };

      const result = v.m_enum({ i18next, attribute, params });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};values:activecomma inactive;`);
      expect(i18next.t).toHaveBeenCalledWith('common.label.active');
      expect(i18next.t).toHaveBeenCalledWith('common.label.inactive');
    });

    it('should not translate numeric string values', () => {
      const key = 'enum';
      const attribute = 'priority';
      const params = {
        allowedValues: ['1', '2', '3'],
      };

      const result = v.m_enum({ i18next, attribute, params });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};values:1comma 2comma 3;`);
      // Should not call translation for numeric strings
      expect(i18next.t).not.toHaveBeenCalledWith('common.label.1');
      expect(i18next.t).not.toHaveBeenCalledWith('common.label.2');
      expect(i18next.t).not.toHaveBeenCalledWith('common.label.3');
    });

    it('should not translate non-string values', () => {
      const key = 'enum';
      const attribute = 'count';
      const params = {
        allowedValues: [1, 2, 3, true, false],
      };

      const result = v.m_enum({ i18next, attribute, params });

      expect(result).toEqual(
        `${keyPrefix}${key};${attribute};values:1comma 2comma 3comma truecomma false;`,
      );
      // Should not call translation for non-string values
      expect(i18next.t).not.toHaveBeenCalledWith('common.label.1');
      expect(i18next.t).not.toHaveBeenCalledWith('common.label.true');
      expect(i18next.t).not.toHaveBeenCalledWith('common.label.false');
    });

    it('should handle mixed value types correctly', () => {
      const key = 'enum';
      const attribute = 'mixed';
      const params = {
        allowedValues: ['active', '123', 456, 'inactive', true],
      };

      const result = v.m_enum({ i18next, attribute, params });

      expect(result).toEqual(
        `${keyPrefix}${key};${attribute};values:activecomma 123comma 456comma inactivecomma true;`,
      );
      // Should only translate non-numeric strings
      expect(i18next.t).toHaveBeenCalledWith('common.label.active');
      expect(i18next.t).toHaveBeenCalledWith('common.label.inactive');
      expect(i18next.t).not.toHaveBeenCalledWith('common.label.123');
      expect(i18next.t).not.toHaveBeenCalledWith('common.label.456');
      expect(i18next.t).not.toHaveBeenCalledWith('common.label.true');
    });
  });

  describe('m_format', () => {
    it('should translated format validation message with extra parameters', () => {
      const key = 'format';
      const attribute = 'id';
      const params = {
        format: 'uuid',
      };

      const result = v.m_format({ i18next, attribute, params });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};format:uuid;`);
    });
  });

  describe('m_type', () => {
    it('should translated type validation message with extra parameters', () => {
      const key = 'type';
      const attribute = 'amount';
      const params = {
        type: 'number',
      };

      const result = v.m_type({ i18next, attribute, params });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};type:number;`);
    });
  });

  describe('m_minLength', () => {
    it('should translated minLength validation message with extra parameters', () => {
      const key = 'minLength';
      const attribute = 'amount';
      const params = {
        limit: 10,
      };

      const result = v.m_minLength({ i18next, attribute, params });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};limit:10;`);
    });
  });

  describe('m_maxLength', () => {
    it('should translated maxLength validation message with extra parameters', () => {
      const key = 'maxLength';
      const attribute = 'amount';
      const params = {
        limit: 10,
      };

      const result = v.m_maxLength({ i18next, attribute, params });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};limit:10;`);
    });
  });

  describe('m_minimum', () => {
    it('should translated minimum validation message with extra parameters', () => {
      const key = 'minimum';
      const attribute = 'amount';
      const params = {
        limit: 10,
      };

      const result = v.m_minimum({ i18next, attribute, params });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};limit:10;`);
    });
  });

  describe('m_maximum', () => {
    it('should translated maximum validation message with extra parameters', () => {
      const key = 'maximum';
      const attribute = 'amount';
      const params = {
        limit: 10,
      };

      const result = v.m_maximum({ i18next, attribute, params });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};limit:10;`);
    });
  });

  describe('m_pattern', () => {
    it('should translated pattern validation message with extra parameters', () => {
      const key = 'pattern';
      const attribute = 'username';
      const params = {
        pattern: '^[A-Za-z0-9]+$',
      };

      const result = v.m_pattern({ i18next, attribute, params });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};pattern:^[A-Za-z0-9]+$;`);
    });
  });
});
