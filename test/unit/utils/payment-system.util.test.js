import { beforeEach, describe, expect, it, vi } from 'vitest';

import { paymentSystem } from '#src/utils/payment-system.util.js';

describe('paymentSystem.getMaintenance', () => {
  const mockParams = {
    secretCode: 'test-secret-code',
    secretPassword: 'test-secret-pass',
    accountId: 'test-account-id',
  };

  let mockAxios;

  beforeEach(() => {
    vi.clearAllMocks();
    mockAxios = {
      get: vi.fn(),
    };
  });

  it('should return { success, data, requestHeaders } when API responds successfully', async () => {
    const mockData = {
      success: true,
      data: {
        maintenance: true,
        message: 'System under maintenance',
      },
    };

    mockAxios.get.mockResolvedValueOnce({
      data: mockData,
      config: { headers: { 'X-Test': 'yes' } },
    });

    const result = await paymentSystem.getMaintenance(mockParams, mockAxios);

    expect(result).toEqual({
      success: true,
      data: mockData.data,
      requestHeaders: { 'X-Test': 'yes' },
    });

    expect(mockAxios.get).toHaveBeenCalledWith('https://psapi-qa.dt818w.com/api/maintenances', {
      headers: {
        'secret-code': mockParams.secretCode,
        'secret-password': mockParams.secretPassword,
        'account-id': mockParams.accountId,
      },
    });
  });

  it('should throw object if API responds with success: false', async () => {
    const mockData = { success: false, error: { info: 'Invalid credentials' } };

    mockAxios.get.mockResolvedValueOnce({
      data: mockData,
      config: { headers: { 'X-Test': 'yes' } },
    });

    await expect(paymentSystem.getMaintenance(mockParams, mockAxios)).rejects.toMatchObject({
      success: false,
      message: expect.stringContaining('Payment System API - GET /maintenances'),
      requestHeaders: {
        'account-id': 'test-account-id',
        'secret-code': 'test-secret-code',
        'secret-password': 'test-secret-pass',
      },
    });
  });

  it('should throw object if Axios rejects', async () => {
    const error = new Error('Network error');
    error.config = { headers: { 'X-Test': 'yes' } };

    mockAxios.get.mockRejectedValueOnce(error);

    await expect(paymentSystem.getMaintenance(mockParams, mockAxios)).rejects.toMatchObject({
      success: false,
      message: 'Payment System API - GET /maintenances: Network error',
      requestHeaders: { 'X-Test': 'yes' },
    });
  });
});
