import sgMail from '@sendgrid/mail';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { sendGrid } from '#src/utils/sendgrid.util.js';

vi.mock('@sendgrid/mail', () => ({
  default: {
    setApiKey: vi.fn(),
    send: vi.fn(),
  },
}));

describe('sendGrid', () => {
  const mockParams = {
    apiKey: 'SG.fake-api-key',
    to: '<EMAIL>',
    from: '<EMAIL>',
    subject: 'Test Subject',
    text: 'Test body',
    html: '<p>Test body</p>',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should send email successfully', async () => {
    const mockResponse = [{ statusCode: 200 }];
    sgMail.send.mockResolvedValueOnce(mockResponse);

    const result = await sendGrid(mockParams);

    expect(sgMail.setApiKey).toHaveBeenCalledWith(mockParams.apiKey);
    expect(sgMail.send).toHaveBeenCalledWith({
      to: mockParams.to,
      from: mockParams.from,
      subject: mockParams.subject,
      text: mockParams.text,
      html: mockParams.html,
    });
    expect(result).toBe(mockResponse[0]);
  });

  it('should throw error when sending fails', async () => {
    const errorMessage = 'The sender email is not verified.';
    const mockError = {
      response: {
        body: {
          errors: [{ message: errorMessage }],
        },
      },
    };

    sgMail.send.mockRejectedValueOnce(mockError);

    await expect(sendGrid(mockParams)).rejects.toThrow(`${errorMessage}`);
  });
});
