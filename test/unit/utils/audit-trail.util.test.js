/* eslint-disable sonarjs/no-hardcoded-ip */
import geoip from 'geoip-lite';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { VIEW_ACTION } from '#src/modules/core/constants/core.constant.js';
import {
  buildAuditTrailTarget,
  computeUpdateDiff,
  deepToJSON,
  finalizeAuditTrailEntry,
  formatEntityInfo,
  initializeAuditMeta,
  isNullish,
  prepareAuditTrailEntry,
} from '#src/utils/audit-trail.util.js';

const createMockRequest = (overrides = {}) => ({
  id: 'req-id',
  url: '/api/some-endpoint',
  method: 'POST',
  headers: {
    'x-forwarded-for': '*******',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    host: 'localhost:3000',
    origin: 'http://localhost',
  },
  query: { id: 123 },
  body: { data: 'value' },
  authInfo: {
    authAccess: 'user',
    id: 'user-123',
    username: 'tester',
    role: 'admin',
    department: 'IT',
    fingerprintId: 'abc123',
  },
  entity: {
    id: 'e-1',
    code: 'E1',
    prefix: 'ENT',
    name: 'Entity One',
    hierarchy: 'organisation',
  },
  entityAccessId: 'access-001',
  hierarchyLevel: 'level-1',
  parentEntity: null,
  responsePayload: {
    message: 'Some error occurred',
    errorCode: 'ERR001',
    meta: { detail: 'Detailed error' },
  },
  ...overrides,
});

const createMockFastify = () => ({
  config: { KAFKA: true },
  kafka: {
    producer: {
      send: vi.fn().mockResolvedValue(true),
    },
  },
  redis: {
    get: vi.fn().mockResolvedValue(null),
    setex: vi.fn().mockResolvedValue(true),
  },
  log: {
    error: vi.fn(),
    debug: vi.fn(),
  },
});

describe('Audit Trail Utility', () => {
  describe('prepareAuditTrailEntry', () => {
    it('should create auditEntries with correct defaults', () => {
      const fastify = {};
      const request = createMockRequest();

      prepareAuditTrailEntry(fastify, request);

      expect(request.auditEntries).toBeDefined();
      expect(request.auditEntries.actor.id).toBe('user-123');
      expect(request.auditEntries.context.ip).toBe('*******');
    });

    it('should fallback to UNKNOWN_LOCATION when geoip lookup fails', () => {
      const request = createMockRequest({ ip: '0.0.0.0' });
      vi.spyOn(geoip, 'lookup').mockReturnValueOnce(null);
      prepareAuditTrailEntry({}, request);
      expect(request.auditEntries.context.location).toBe('UNKNOWN_LOCATION');
    });

    it('should fallback to UNKNOWN_CITY and UNKNOWN_COUNTRY when geoip returns incomplete data', () => {
      const request = createMockRequest({ ip: '*******' });
      vi.spyOn(geoip, 'lookup').mockReturnValueOnce({});
      prepareAuditTrailEntry({}, request);
      expect(request.auditEntries.context.location).toBe('UNKNOWN_CITY, UNKNOWN_COUNTRY');
    });

    it('should fallback to unknown device info if user-agent is missing', () => {
      const request = createMockRequest({
        headers: { ...createMockRequest().headers, 'user-agent': '' },
      });
      prepareAuditTrailEntry({}, request);
      expect(request.auditEntries.context.device).toBe(
        'UNKNOWN_DEVICE - UNKNOWN_OS - UNKNOWN_BROWSER',
      );
    });

    it('should fallback to Desktop, UNKNOWN_OS, and UNKNOWN_BROWSER if UA parts are missing', () => {
      const request = createMockRequest({
        headers: { ...createMockRequest().headers, 'user-agent': 'unknown-agent' },
      });

      const parsedUA = {
        device: {},
        os: {},
        browser: {},
      };
      vi.doMock('ua-parser-js', () => {
        return vi.fn().mockReturnValue(parsedUA);
      });

      prepareAuditTrailEntry({}, request);

      expect(request.auditEntries.context.device).toBe('Desktop - UNKNOWN_OS - UNKNOWN_BROWSER');
    });

    it('should set organisation and merchant when hierarchy is merchant', () => {
      const fastify = {};
      const request = createMockRequest({
        entity: {
          hierarchy: 'merchant',
          id: 'merchant1',
          code: 'M1',
          name: 'Merchant One',
          prefix: 'MER',
        },
        parentEntity: {
          hierarchy: 'organisation',
          id: 'org1',
          name: 'Org One',
          code: 'O1',
          name: 'Organisation One',
          prefix: 'ORG',
        },
      });

      prepareAuditTrailEntry(fastify, request);

      expect(request.auditEntries).toBeDefined();
      expect(request.auditEntries.actor.organisation).toEqual({
        id: 'org1',
        name: 'Organisation One',
        code: 'O1',
        prefix: 'ORG',
      });

      expect(request.auditEntries.actor.merchant).toEqual({
        id: 'merchant1',
        name: 'Merchant One',
        code: 'M1',
        prefix: 'MER',
      });
    });

    it('should set organisation when hierarchy is organisation', () => {
      const fastify = {};
      const request = createMockRequest({
        entity: {
          hierarchy: 'organisation',
          id: 'org1',
          name: 'Org One',
          code: 'O1',
          name: 'Organisation One',
          prefix: 'ORG',
        },
      });

      prepareAuditTrailEntry(fastify, request);

      expect(request.auditEntries.actor.organisation).toEqual({
        id: 'org1',
        name: 'Organisation One',
        code: 'O1',
        prefix: 'ORG',
      });

      expect(request.auditEntries.actor.merchant).toBeNull();
      expect(request.auditEntries.actor.root).toBeNull();
    });

    it('should set root when hierarchy is root', () => {
      const fastify = {};
      const request = createMockRequest({
        entity: {
          hierarchy: 'root',
          id: 'root1',
          name: 'Root',
          code: 'RT1',
          name: 'Root One',
          prefix: 'RT',
        },
      });

      prepareAuditTrailEntry(fastify, request);

      expect(request.auditEntries.actor.root).toEqual({
        id: 'root1',
        name: 'Root One',
        code: 'RT1',
        prefix: 'RT',
      });

      expect(request.auditEntries.actor.organisation).toBeNull();
      expect(request.auditEntries.actor.merchant).toBeNull();
    });
  });

  describe('formatEntityInfo', () => {
    it('should return null when entity is undefined', () => {
      expect(formatEntityInfo(undefined)).toBeNull();
    });

    it('should return null when entity is null', () => {
      expect(formatEntityInfo(null)).toBeNull();
    });

    it('should fallback missing fields with default "UNKNOWN"', () => {
      const entity = {
        id: null,
      };

      expect(formatEntityInfo(entity)).toEqual({
        id: 'UNKNOWN_ID',
        code: 'UNKNOWN_CODE',
        prefix: 'UNKNOWN_PREFIX',
        name: 'UNKNOWN_NAME',
      });
    });
  });

  describe('isNullish function', () => {
    it('should return true for null', () => {
      expect(isNullish(null)).toBe(true);
    });

    it('should return true for undefined', () => {
      expect(isNullish(undefined)).toBe(true);
    });

    it('should return false for other falsy values', () => {
      expect(isNullish(false)).toBe(false);
      expect(isNullish(0)).toBe(false);
      expect(isNullish('')).toBe(false);
      expect(isNullish(NaN)).toBe(false);
    });

    it('should return false for non-nullish values', () => {
      expect(isNullish('hello')).toBe(false);
      expect(isNullish(123)).toBe(false);
      expect(isNullish({})).toBe(false);
      expect(isNullish([])).toBe(false);
    });
  });

  describe('deepToJSON', () => {
    class ToJSONMock {
      constructor(value) {
        this.value = value;
      }
      toJSON() {
        return { transformed: this.value };
      }
    }

    it('should return primitives as-is', () => {
      expect(deepToJSON(42)).toBe(42);
      expect(deepToJSON('hello')).toBe('hello');
      expect(deepToJSON(null)).toBe(null);
      expect(deepToJSON(undefined)).toBe(undefined);
      expect(deepToJSON(true)).toBe(true);
    });

    it('should handle objects with .toJSON()', () => {
      const obj = new ToJSONMock('abc');
      expect(deepToJSON(obj)).toEqual({ transformed: 'abc' });
    });

    it('should handle nested structures with .toJSON()', () => {
      const input = {
        a: new ToJSONMock(1),
        b: {
          c: new ToJSONMock(2),
          d: [new ToJSONMock(3)],
        },
      };

      expect(deepToJSON(input)).toEqual({
        a: { transformed: 1 },
        b: {
          c: { transformed: 2 },
          d: [{ transformed: 3 }],
        },
      });
    });

    it('should handle arrays with .toJSON()', () => {
      const arr = [new ToJSONMock('x'), 'raw'];
      expect(deepToJSON(arr)).toEqual([{ transformed: 'x' }, 'raw']);
    });

    it('should avoid circular references', () => {
      const obj = {};
      obj.self = obj;

      expect(deepToJSON(obj)).toEqual({ self: null });
    });

    it('should handle mixed data types', () => {
      const data = {
        num: 1,
        str: 'text',
        bool: false,
        undef: undefined,
        nil: null,
        obj: new ToJSONMock('val'),
        arr: [1, new ToJSONMock('deep')],
      };

      expect(deepToJSON(data)).toEqual({
        num: 1,
        str: 'text',
        bool: false,
        undef: undefined,
        nil: null,
        obj: { transformed: 'val' },
        arr: [1, { transformed: 'deep' }],
      });
    });
  });

  describe('computeUpdateDiff', () => {
    it('should compute diff between original and updates', () => {
      const original = { id: 1, name: 'John', age: 25 };
      const updates = { id: 1, name: 'Jane', age: 24 };
      const diff = computeUpdateDiff(original, updates);

      expect(diff).toEqual({
        afterState: updates,
        beforeState: original,
        fieldsChanged: ['name', 'age'],
      });
    });

    it('should not include keys in fieldsChanged if both the original and updated values are null or undefined', () => {
      const original = { id: 1, name: 'John', age: 25 };
      const updates = { id: 1, name: 'Jane', age: 25, gender: null };

      const diff = computeUpdateDiff(original, updates);

      expect(diff.fieldsChanged).not.includes('gender');
      expect(diff.fieldsChanged).includes('name');
    });

    it('should include keys in fieldChanged only if either the original or updated value is null or undefined, but not both', () => {
      const original = { id: 1, name: 'John', age: 25, hobby: 'reading' };
      const updates = { id: 1, name: 'Jane', age: 25, gender: 'female', hobby: null };
      const diff1 = computeUpdateDiff(original, updates);

      expect(diff1.fieldsChanged).includes('name');
      expect(diff1.fieldsChanged).includes('gender');
      expect(diff1.fieldsChanged).includes('hobby');
    });

    it('should not include keys in ignoredKeys', () => {
      const original = { id: 1, name: 'John', age: 25, hobby: 'reading' };
      const updates = { id: 2, name: 'Jane', age: 25, gender: 'female', hobby: null };
      const diff1 = computeUpdateDiff(original, updates, 'id');

      expect(diff1.fieldsChanged).not.includes('id');
    });
  });

  describe('initializeAuditMeta', () => {
    const baseMeta = {
      module: 'user',
    };

    it('should fallback to UNKNOWN_USER if username is missing', async () => {
      const mockRequest = {
        authInfo: {},
        entity: { hierarchy: 'merchant', name: 'Test Entity' },
      };

      await initializeAuditMeta(mockRequest, baseMeta, '001');

      expect(mockRequest.auditEntries.description.translationParams.username).toBe('UNKNOWN_USER');
    });

    it('should fallback to UNKNOWN_HIERARCHY if entity.hierarchy is missing', async () => {
      const mockRequest = {
        authInfo: { username: 'jane.doe' },
        entity: { name: 'Entity Name' },
      };

      await initializeAuditMeta(mockRequest, baseMeta, '002');

      expect(mockRequest.auditEntries.description.translationParams.hierarchy).toBe(
        'UNKNOWN_HIERARCHY',
      );
    });

    it('should fallback to UNKNOWN_ENTITY if entity.name is missing', async () => {
      const mockRequest = {
        authInfo: { username: 'jane.doe' },
        entity: { hierarchy: 'organisation' },
      };

      await initializeAuditMeta(mockRequest, baseMeta, '002');

      expect(mockRequest.auditEntries.description.translationParams.entityName).toBe(
        'UNKNOWN_ENTITY',
      );
    });

    it('should initialize auditEntries if not present', async () => {
      const mockRequest = {};

      await initializeAuditMeta(mockRequest, baseMeta);

      expect(mockRequest.auditEntries).toBeDefined();
      expect(mockRequest.auditEntries.description.translationParams.username).toBe('UNKNOWN_USER');
    });
  });

  describe('buildAuditTrailTarget', () => {
    let mockRequest;
    beforeEach(() => {
      mockRequest = createMockRequest();
      prepareAuditTrailEntry({}, mockRequest);
    });

    it('should construct audit trail targets for VIEW_ACTION and call buildViewTargets logic', () => {
      const model = 'User';
      const referenceId = 'user-abc';
      const referenceDetails = { name: 'Test User' };

      const modelMapping = {
        [model]: {
          beforeState: { id: referenceId, name: 'Test User' },
          afterState: { id: referenceId, name: 'Test User' },
          // For VIEW_ACTION, fieldsChanged can be empty or not provided, as changes are not tracked
          fieldsChanged: [],
          referenceDetails: referenceDetails,
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: VIEW_ACTION.DETAILS_VIEWED, // Use a specific VIEW_ACTION
        metrics: { duration: 100, dbQueries: 5 },
        status: 'Success',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target).toBeDefined();
      expect(mockRequest.auditEntries.target).toHaveLength(1);
      expect(mockRequest.auditEntries.target[0]).toEqual({
        model: model,
        referenceId: referenceId,
        referenceDetails: referenceDetails,
        changes: undefined, // View actions don't track specific field changes
      });
      expect(mockRequest.auditEntries.details.metrics).toEqual({ duration: 100, dbQueries: 5 });
      expect(mockRequest.auditEntries.description.translationParams.referenceIds).toBe(referenceId);
      expect(mockRequest.auditEntries.description.translationParams.status).toBe('Success');
    });

    it('should construct audit trail targets for single UPDATE action', () => {
      const model = 'Product';
      const referenceId = 'prod-xyz';
      const referenceDetails = { code: 'PROD001' };

      const modelMapping = {
        [model]: {
          beforeState: { id: referenceId, name: 'Old Product Name', price: 100, status: 'ACTIVE' },
          afterState: { id: referenceId, name: 'New Product Name', price: 100, status: 'ACTIVE' },
          fieldsChanged: ['name'],
          referenceDetails: referenceDetails,
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: 'UPDATED',
        metrics: {},
        status: 'Success',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target).toBeDefined();
      expect(mockRequest.auditEntries.target).toHaveLength(1);
      expect(mockRequest.auditEntries.target[0]).toEqual({
        model: model,
        referenceId: referenceId,
        referenceDetails: referenceDetails,
        changes: {
          beforeState: { name: 'Old Product Name' },
          afterState: { name: 'New Product Name' },
        },
      });
      expect(mockRequest.auditEntries.description.translationParams.referenceIds).toBe(referenceId);
      expect(mockRequest.auditEntries.description.translationParams.status).toBe('Success');
    });

    it('should construct audit trail targets for multiple UPDATE action and only include changed fields', () => {
      const modelMapping = {
        Model1: [
          {
            beforeState: {
              id: 'row1',
              field1: 'oldVal1',
              field2: 'oldVal2',
            },
            afterState: {
              id: 'row1',
              field1: 'newVal1',
              field2: 'oldVal2',
            },
            fieldsChanged: ['field1'],
          },
          {
            beforeState: {
              id: 'row2',
              field1: 'oldVal1',
              field2: 'oldVal2',
            },
            afterState: {
              id: 'row2',
              field1: 'oldVal1',
              field2: 'newVal2',
            },
            fieldsChanged: ['field2'],
          },
        ],
      };

      const options = {
        modelMapping: modelMapping,
        action: 'UPDATED',
        metrics: {},
        status: 'Success',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target).toBeDefined();
      expect(mockRequest.auditEntries.target).toHaveLength(2);
      expect(mockRequest.auditEntries.target).toEqual([
        {
          model: 'Model1',
          referenceId: 'row1',
          referenceDetails: undefined,
          changes: {
            beforeState: { field1: 'oldVal1' },
            afterState: { field1: 'newVal1' },
          },
        },
        {
          model: 'Model1',
          referenceId: 'row2',
          referenceDetails: undefined,
          changes: {
            beforeState: { field2: 'oldVal2' },
            afterState: { field2: 'newVal2' },
          },
        },
      ]);
      expect(mockRequest.auditEntries.description.translationParams.referenceIds).toBe(
        'row1, row2',
      );
      expect(mockRequest.auditEntries.description.translationParams.status).toBe('Success');
    });

    it('should handle target with missing referenceId for multi-update', () => {
      const modelMapping = {
        Model1: [
          {
            beforeState: {
              field1: 'oldVal1',
              field2: 'oldVal2',
            },
            afterState: {
              field1: 'newVal1',
              field2: 'oldVal2',
            },
            fieldsChanged: ['field1'],
          },
          {
            beforeState: {
              field1: 'oldVal1',
              field2: 'oldVal2',
            },
            afterState: {
              field1: 'oldVal1',
              field2: 'newVal2',
            },
            fieldsChanged: ['field2'],
          },
        ],
      };

      const options = {
        modelMapping: modelMapping,
        action: 'UPDATED',
        metrics: {},
        status: 'Success',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target[0].referenceId).toBe('UNKNOWN_ID');
      expect(mockRequest.auditEntries.target[1].referenceId).toBe('UNKNOWN_ID');
    });

    it('should handle object with dataValues for extractData', () => {
      const model = 'User';
      const referenceId = 'user-dataValues';
      const referenceDetails = { name: 'DataValues Test' };

      const modelMapping = {
        [model]: {
          beforeState: { dataValues: { id: referenceId, name: 'Old DataValues' } },
          afterState: { dataValues: { id: referenceId, name: 'New DataValues' } },
          fieldsChanged: ['name'],
          referenceDetails: referenceDetails,
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: 'UPDATED',
        metrics: {},
        status: 'Success',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target[0].changes.beforeState.name).toBe('Old DataValues');
      expect(mockRequest.auditEntries.target[0].changes.afterState.name).toBe('New DataValues');
      expect(mockRequest.auditEntries.target[0].referenceId).toBe(referenceId);
    });

    it('should derive changed fields from afterState for CREATE action', () => {
      const model = 'NewRecord';

      const modelMapping = {
        [model]: {
          afterState: { fieldA: 'valueA', fieldB: 'valueB' },
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: 'CREATED',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target).toHaveLength(1);
      expect(mockRequest.auditEntries.target[0].changes.beforeState).toEqual({});
      expect(mockRequest.auditEntries.target[0].changes.afterState).toEqual({
        fieldA: 'valueA',
        fieldB: 'valueB',
      });
    });

    it('should derive changed fields from beforeState for DELETE action', () => {
      const model = 'OldRecord';

      const modelMapping = {
        [model]: {
          beforeState: { id: 'old-rec-id', fieldX: 'valueX', fieldY: 'valueY' },
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: 'DELETED',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target).toHaveLength(1);
      expect(mockRequest.auditEntries.target[0].changes.beforeState).toEqual({
        id: 'old-rec-id',
        fieldX: 'valueX',
        fieldY: 'valueY',
      });
      expect(mockRequest.auditEntries.target[0].changes.afterState).toEqual({});
    });
  });

  describe('finalizeAuditTrailEntry', () => {
    it('should update status and send message to Kafka on success', async () => {
      const fastify = createMockFastify();
      const request = createMockRequest();
      const reply = { statusCode: 200 };

      prepareAuditTrailEntry(fastify, request);
      await finalizeAuditTrailEntry(fastify, request, reply);

      expect(request.auditEntries.status).toBe('Success');
      expect(fastify.kafka.producer.send).toHaveBeenCalled();
    });

    it('should mark audit as failed and include error details if response failed', async () => {
      const fastify = createMockFastify();
      const request = createMockRequest();
      const reply = { statusCode: 500 };

      prepareAuditTrailEntry(fastify, request);
      await finalizeAuditTrailEntry(fastify, request, reply);

      expect(request.auditEntries.status).toBe('Failed');
      expect(request.auditEntries.details.error.message).toBe('Some error occurred');
      expect(fastify.kafka.producer.send).toHaveBeenCalled();
    });

    it('should store message in Redis if Kafka send fails', async () => {
      const fastify = createMockFastify();
      fastify.kafka.producer.send.mockRejectedValue(new Error('Kafka error'));
      const request = createMockRequest();
      const reply = { statusCode: 200 };

      prepareAuditTrailEntry(fastify, request);
      await finalizeAuditTrailEntry(fastify, request, reply);

      expect(fastify.log.error).toHaveBeenCalledWith(expect.any(Error), 'Kafka send failed');
      expect(fastify.log.debug).toHaveBeenCalledWith(
        expect.any(Array),
        'Kafka message stored in Redis',
      );
    });
  });
});
