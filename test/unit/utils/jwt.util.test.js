import { describe, expect, it, vi } from 'vitest';

import { decodeJWT, signJWT } from '#src/utils/jwt.util.js';

describe('decodeJWT', () => {
  const mockDecoded = { id: 1, name: '<PERSON>' };

  it('should decode a valid token', async () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { verify: vi.fn().mockResolvedValue(mockDecoded) },
    };

    const token = 'valid.jwt.token';
    const result = await decodeJWT(token, mockFastify);

    expect(mockFastify.jwt.verify).toHaveBeenCalledWith(token);
    expect(result).toEqual(mockDecoded);
    expect(mockFastify.log.debug).not.toHaveBeenCalled();
  });

  it('should return null if no token is passed', async () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { verify: vi.fn() },
    };
    const result = await decodeJWT(undefined, mockFastify);

    expect(result).toBeNull();
    expect(mockFastify.log.debug).toHaveBeenCalledWith('No JWT token provided');
    expect(mockFastify.jwt.verify).not.toHaveBeenCalled();
  });

  it('should log and return undefined on decode error', async () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { verify: vi.fn().mockRejectedValue(new Error('Invalid token')) },
    };

    const result = await decodeJWT('invalid.token', mockFastify);

    expect(result).toBeNull();
    expect(mockFastify.jwt.verify).toHaveBeenCalledWith('invalid.token');
    expect(mockFastify.log.debug).toHaveBeenCalledWith(
      expect.any(Error),
      'Failed to verify JWT token',
    );
  });
});

describe('signJWT', () => {
  const mockPayload = { id: 1, name: 'John Doe' };
  const mockSignedToken = 'signed.jwt.token';

  it('should sign a valid payload', () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { sign: vi.fn().mockReturnValue(mockSignedToken) },
    };

    const result = signJWT(mockFastify, mockPayload);

    expect(mockFastify.jwt.sign).toHaveBeenCalledWith(mockPayload, {});
    expect(result).toBe(mockSignedToken);
    expect(mockFastify.log.debug).not.toHaveBeenCalled();
  });

  it('should sign a payload with options', () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { sign: vi.fn().mockReturnValue(mockSignedToken) },
    };
    const options = { expiresIn: '1h', audience: 'test-app' };

    const result = signJWT(mockFastify, mockPayload, options);

    expect(mockFastify.jwt.sign).toHaveBeenCalledWith(mockPayload, options);
    expect(result).toBe(mockSignedToken);
    expect(mockFastify.log.debug).not.toHaveBeenCalled();
  });

  it('should return null if payload is null', () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { sign: vi.fn() },
    };

    const result = signJWT(mockFastify, null);

    expect(result).toBeNull();
    expect(mockFastify.log.debug).toHaveBeenCalledWith('Invalid payload provided for JWT signing');
    expect(mockFastify.jwt.sign).not.toHaveBeenCalled();
  });

  it('should return null if payload is undefined', () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { sign: vi.fn() },
    };

    const result = signJWT(mockFastify, undefined);

    expect(result).toBeNull();
    expect(mockFastify.log.debug).toHaveBeenCalledWith('Invalid payload provided for JWT signing');
    expect(mockFastify.jwt.sign).not.toHaveBeenCalled();
  });

  it('should return null if payload is not an object', () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { sign: vi.fn() },
    };

    const result = signJWT(mockFastify, 'invalid-payload');

    expect(result).toBeNull();
    expect(mockFastify.log.debug).toHaveBeenCalledWith('Invalid payload provided for JWT signing');
    expect(mockFastify.jwt.sign).not.toHaveBeenCalled();
  });

  it('should log and return null on signing error', () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: {
        sign: vi.fn().mockImplementation(() => {
          throw new Error('Signing failed');
        }),
      },
    };

    const result = signJWT(mockFastify, mockPayload);

    expect(result).toBeNull();
    expect(mockFastify.jwt.sign).toHaveBeenCalledWith(mockPayload, {});
    expect(mockFastify.log.debug).toHaveBeenCalledWith(
      expect.any(Error),
      'Failed to sign JWT token',
    );
  });
});
