import { beforeEach, describe, expect, it, vi } from 'vitest';

import { getExchangeRates } from '#src/utils/exchange-rate.util.js';

describe('getExchangeRates', () => {
  let mockAxios;

  beforeEach(() => {
    mockAxios = {
      get: vi.fn(),
      interceptors: { request: { use: vi.fn((fn) => fn({ headers: { 'X-Test': 'yes' } })) } },
    };
    vi.clearAllMocks();
  });

  it('returns exchange data and request headers on success', async () => {
    const mockData = {
      success: true,
      base: 'USD',
      rates: { MYR: 4.7, EUR: 0.85 },
    };
    mockAxios.get.mockResolvedValueOnce({ data: mockData });

    const result = await getExchangeRates({ base: 'USD' }, mockAxios);

    expect(result).toEqual({
      data: mockData,
      requestHeaders: { 'X-Test': 'yes' },
    });
    expect(mockAxios.get).toHaveBeenCalledWith('https://api.exchangerate.host/live', {
      params: { base: 'USD' },
    });
  });

  it('should append access_key when token is provided', async () => {
    mockAxios.get.mockResolvedValueOnce({
      data: { success: true, rates: { MYR: 4.68 } },
    });

    const token = 'my-secret-token';
    await getExchangeRates({ base: 'USD', token }, mockAxios);

    const calledParams = mockAxios.get.mock.calls[0][1].params;
    expect(calledParams.access_key).toBe(token);
  });

  it('throws object if API responds with success: false', async () => {
    mockAxios.get.mockResolvedValueOnce({
      data: { success: false, error: { info: 'Invalid base' } },
    });

    await expect(getExchangeRates({ base: 'XYZ' }, mockAxios)).rejects.toMatchObject({
      success: false,
      message: 'Invalid base',
      requestHeaders: { 'X-Test': 'yes' },
    });
  });

  it('throws object with undefined message if API returns invalid data', async () => {
    mockAxios.get.mockResolvedValueOnce({ data: null });

    await expect(getExchangeRates({ base: 'USD' }, mockAxios)).rejects.toMatchObject({
      success: false,
      requestHeaders: { 'X-Test': 'yes' },
    });
  });

  it('throws object on Axios failure', async () => {
    mockAxios.get.mockRejectedValueOnce(new Error('Network error'));

    await expect(getExchangeRates({ base: 'USD' }, mockAxios)).rejects.toMatchObject({
      success: false,
      message: 'Network error',
      requestHeaders: { 'X-Test': 'yes' },
    });
  });
});
