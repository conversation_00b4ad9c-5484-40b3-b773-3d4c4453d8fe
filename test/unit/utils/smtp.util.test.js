import nodemailer from 'nodemailer';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { sendEmail } from '#src/utils/smtp.util.js';

vi.mock('nodemailer');

describe('sendEmail', () => {
  const mockSendMail = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    nodemailer.createTransport.mockReturnValue({
      sendMail: mockSendMail,
    });
  });

  it('should send email successfully', async () => {
    const mockResponse = { messageId: '12345' };
    mockSendMail.mockResolvedValue(mockResponse);

    const config = {
      host: 'smtp.test.com',
      port: 465,
      secure: true,
      user: '<EMAIL>',
      pass: 'password',
      from: 'App <<EMAIL>>',
      to: '<EMAIL>',
      subject: 'Test Subject',
      text: 'Test message',
      html: '<p>Test message</p>',
    };

    const result = await sendEmail(config);

    expect(nodemailer.createTransport).toHaveBeenCalledWith({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: { user: config.user, pass: config.pass },
    });

    expect(mockSendMail).toHaveBeenCalledWith({
      from: config.from,
      to: config.to,
      subject: config.subject,
      text: config.text,
      html: config.html,
    });

    expect(result).toEqual(mockResponse);
  });

  it('should throw error when sending fails', async () => {
    mockSendMail.mockRejectedValue(new Error('SMTP failure'));

    const config = {
      host: 'smtp.test.com',
      port: 465,
      secure: true,
      user: '<EMAIL>',
      pass: 'password',
      from: 'App <<EMAIL>>',
      to: '<EMAIL>',
      subject: 'Test Subject',
      text: 'Test message',
    };

    await expect(sendEmail(config)).rejects.toThrow('SMTP failure');
  });
});
