import { beforeEach, describe, expect, it, vi } from 'vitest';

import { decrypt } from '#src/utils/aes.util.js';
import { sendDiscordMessage } from '#src/utils/discord.util.js';
import { getExchangeRates } from '#src/utils/exchange-rate.util.js';
import { paymentSystem } from '#src/utils/payment-system.util.js';
import { sendGrid } from '#src/utils/sendgrid.util.js';
import { sendSlackMessage } from '#src/utils/slack.util.js';
import { sendEmail } from '#src/utils/smtp.util.js';
import { sendTelegramMessage } from '#src/utils/telegram.util.js';
import {
  decryptConfigByKey,
  testStrategy,
  validateRequiredFields,
} from '#src/utils/test-strategy.util.js';
import { uploadToS3 } from '#src/utils/upload.util.js';

vi.mock('#src/utils/aes.util.js', () => ({
  decrypt: vi.fn(),
}));
vi.mock('#src/utils/slack.util.js', () => ({
  sendSlackMessage: vi.fn(),
}));
vi.mock('#src/utils/telegram.util.js', () => ({
  sendTelegramMessage: vi.fn(),
}));
vi.mock('#src/utils/discord.util.js', () => ({
  sendDiscordMessage: vi.fn(),
}));
vi.mock('#src/utils/smtp.util.js', () => ({
  sendEmail: vi.fn(),
}));
vi.mock('#src/utils/sendgrid.util.js', () => ({
  sendGrid: vi.fn(),
}));

vi.mock('#src/utils/upload.util.js', () => ({
  uploadToS3: vi.fn(),
}));
vi.mock('#src/utils/exchange-rate.util.js', () => ({
  getExchangeRates: vi.fn(),
}));
vi.mock('#src/utils/payment-system.util.js', () => ({
  paymentSystem: {
    getMaintenance: vi.fn(),
  },
}));
vi.mock('#src/utils/error.util.js', () => ({
  createValidationError: vi.fn((...args) => {
    const error = new Error('Validation Error');
    error.name = 'ValidationError';
    error.statusCode = 400;
    error.code = 'VALIDATION_ERROR';
    error.validation = args;
    throw error;
  }),
}));
describe('validateRequiredFields', () => {
  it('should throw validation error for missing fields', () => {
    const fields = {
      Name: '',
      Email: null,
      Phone: undefined,
    };

    expect(() => validateRequiredFields(fields)).toThrowError('Validation Error');
  });
});

describe('decryptConfigByKey', () => {
  const configs = [
    { configKey: 'clientId', configValue: 'abc123' },
    { configKey: 'clientSecret', configValue: 'xyz456' },
  ];
  it('should return null if key does not exist', () => {
    const result = decryptConfigByKey('notFoundKey', configs);
    expect(result).toBeNull();
  });

  it('should call decrypt with correct value', () => {
    decryptConfigByKey('clientSecret', configs);
    expect(decrypt).toHaveBeenCalledWith('xyz456');
  });
});

describe('testStrategy.Slack', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return success response if Slack message sends successfully', async () => {
    sendSlackMessage.mockResolvedValue({
      message: 'Message sent',
    });

    const request = {
      webhook: '*****************************************************************************',
    };
    const configs = [];
    const result = await testStrategy.Slack(configs, request);

    expect(result).toEqual({
      success: true,
      data: 'Message sent',
      error: null,
      requestHeaders: null,
    });

    expect(sendSlackMessage).toHaveBeenCalledWith({
      webhook: '*****************************************************************************',
      content: '✅ This is a test message',
    });
  });

  it('should return failure response if Slack message fails', async () => {
    sendSlackMessage.mockRejectedValue(new Error('Slack API error'));
    const request = {
      webhook: '*****************************************************************************',
    };
    const configs = [];
    const result = await testStrategy.Slack(configs, request);

    expect(result).toEqual({
      success: false,
      data: null,
      error: 'Slack API error',
      requestHeaders: null,
    });
  });
});

describe('testStrategy.Discord', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return success response if Discord message sends successfully', async () => {
    sendDiscordMessage.mockResolvedValue({
      message: 'Message sent',
    });

    const request = {
      webhook: 'https://discord.com/api/webhooks/1234567890/abcdef',
    };
    const configs = [];
    const result = await testStrategy.Discord(configs, request);

    expect(result).toEqual({
      success: true,
      data: 'Message sent',
      error: null,
      requestHeaders: null,
    });

    expect(sendDiscordMessage).toHaveBeenCalledWith({
      webhook: 'https://discord.com/api/webhooks/1234567890/abcdef',
      content: '✅ This is a test message',
    });
  });

  it('should return failure response if Discord message fails', async () => {
    sendDiscordMessage.mockRejectedValue(new Error('Discord API error'));

    const request = {
      webhook: 'https://discord.com/api/webhooks/1234567890/abcdef',
    };
    const configs = [];
    const result = await testStrategy.Discord(configs, request);

    expect(result).toEqual({
      success: false,
      data: null,
      error: 'Discord API error',
      requestHeaders: null,
    });
  });
});
describe('testStrategy.Telegram', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return success response if Telegram message sends successfully', async () => {
    decrypt.mockReturnValue('mocked-telegram-token');
    sendTelegramMessage.mockResolvedValue({
      message: 'Message sent',
    });

    const configs = [{ configKey: 'telegramBotToken', configValue: 'encrypted-token' }];
    const request = { chatId: 'T123456' };
    const result = await testStrategy.Telegram(configs, request);

    expect(result).toEqual({
      success: true,
      data: 'Message sent',
      error: null,
      requestHeaders: null,
    });

    expect(sendTelegramMessage).toHaveBeenCalledWith({
      token: 'mocked-telegram-token',
      chatId: 'T123456',
      content: '✅ This is a test message',
    });
  });

  it('should return failure response if Telegram message fails', async () => {
    decrypt.mockReturnValue('mocked-telegram-token');
    sendTelegramMessage.mockRejectedValue(new Error('Telegram API error'));

    const configs = [{ configKey: 'telegramBotToken', configValue: 'encrypted-token' }];
    const request = { chatId: 'T123456' };
    const result = await testStrategy.Telegram(configs, request);

    expect(result).toEqual({
      success: false,
      data: null,
      error: 'Telegram API error',
      requestHeaders: null,
    });
  });
});
describe('testStrategy.SMTP', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return success response if email sends successfully via SMTP', async () => {
    decrypt
      .mockReturnValueOnce('smtp.example.com') // SMTP Host
      .mockReturnValueOnce('587') // SMTP Port
      .mockReturnValueOnce('<EMAIL>') // SMTP User
      .mockReturnValueOnce('password'); // SMTP Password

    sendEmail.mockResolvedValue('Email sent');

    const configs = [
      { configKey: 'smtpHost', configValue: 'encrypted-host' },
      { configKey: 'smtpPort', configValue: 'encrypted-port' },
      { configKey: 'smtpUser', configValue: 'encrypted-user' },
      { configKey: 'smtpPassword', configValue: 'encrypted-password' },
    ];

    const request = { email: '<EMAIL>' };

    const result = await testStrategy.SMTP(configs, request);

    expect(result).toEqual({
      success: true,
      data: 'Email sent',
      error: null,
      requestHeaders: null,
    });

    expect(sendEmail).toHaveBeenCalledWith({
      host: 'smtp.example.com',
      port: 587,
      secure: false,
      user: '<EMAIL>',
      pass: 'password',
      from: '"Notifier" <<EMAIL>>',
      to: '<EMAIL>',
      subject: '🎯 SMTP Email Test',
      text: 'Hello from your SMTP!',
      html: '<strong>Hello from your SMTP!</strong>',
    });
  });

  it('should return failure response if SMTP email sending fails', async () => {
    decrypt.mockReturnValue('smtp-value');
    sendEmail.mockRejectedValue(new Error('SMTP failure'));

    const configs = [
      { configKey: 'smtpHost', configValue: 'x' },
      { configKey: 'smtpPort', configValue: 'x' },
      { configKey: 'smtpUser', configValue: 'x' },
      { configKey: 'smtpPassword', configValue: 'x' },
    ];

    const request = { email: '<EMAIL>' };

    const result = await testStrategy.SMTP(configs, request);

    expect(result).toEqual({
      success: false,
      data: null,
      error: 'SMTP failure',
      requestHeaders: null,
    });
  });
});
describe('testStrategy.SendGrid', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  it('should return success response if email sends successfully via SendGrid', async () => {
    decrypt.mockReturnValueOnce('SENDGRID_API_KEY').mockReturnValueOnce('<EMAIL>');

    sendGrid.mockResolvedValue('SendGrid email sent');

    const configs = [
      { configKey: 'sendgridApiKey', configValue: 'encrypted-key' },
      { configKey: 'sendgridSender', configValue: 'encrypted-sender' },
    ];

    const request = { email: '<EMAIL>' };

    const result = await testStrategy.SendGrid(configs, request);

    expect(result).toEqual({
      success: true,
      data: 'SendGrid email sent',
      error: null,
      requestHeaders: null,
    });

    expect(sendGrid).toHaveBeenCalledWith({
      apiKey: 'SENDGRID_API_KEY',
      to: '<EMAIL>',
      from: '<EMAIL>',
      subject: '🎯 Send Grid Email Test',
      text: 'Hello from your Send Grid!',
      html: '<strong>Hello from your Send Grid!</strong>',
    });
  });

  it('should return failure response if SendGrid email sending fails', async () => {
    decrypt.mockReturnValue('mocked-value');
    sendGrid.mockRejectedValue(new Error('SendGrid failure'));

    const configs = [
      { configKey: 'sendgridApiKey', configValue: 'x' },
      { configKey: 'sendgridSender', configValue: 'x' },
    ];

    const request = { email: '<EMAIL>' };

    const result = await testStrategy.SendGrid(configs, request);

    expect(result).toEqual({
      success: false,
      data: null,
      error: 'SendGrid failure',
      requestHeaders: null,
    });
  });
});

describe('testStrategy.S3', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    decrypt
      .mockReturnValueOnce('mocked-region')
      .mockReturnValueOnce('mocked-access-key')
      .mockReturnValueOnce('mocked-secret-key')
      .mockReturnValueOnce('mocked-bucket')
      .mockReturnValueOnce('mocked-base-url');
  });
  const configs = [
    { configKey: 'awsRegion', configValue: 'x' },
    { configKey: 'awsAccessKeyID', configValue: 'x' },
    { configKey: 'awsSecretKey', configValue: 'x' },
    { configKey: 'awsBucketName', configValue: 'x' },
    { configKey: 'baseUrl', configValue: 'https://mock-bucket.s3.amazonaws.com' },
  ];

  const mockUpload = {
    filename: 'file.jpg',
    mimetype: 'image/jpeg',
    toBuffer: vi.fn().mockResolvedValue(Buffer.from('test file content')),
  };
  it('should return success response if file uploads successfully to S3', async () => {
    uploadToS3.mockResolvedValue('https://mock-bucket.s3.amazonaws.com/file.jpg');

    const result = await testStrategy['AWS S3'](configs, { upload: mockUpload });

    expect(result).toEqual({
      success: true,
      data: 'https://mock-bucket.s3.amazonaws.com/file.jpg',
      error: null,
      requestHeaders: null,
    });

    expect(mockUpload.toBuffer).toHaveBeenCalled();
    expect(uploadToS3).toHaveBeenCalledWith({
      credentials: {
        awsRegion: 'mocked-region',
        awsAccessKeyID: 'mocked-access-key',
        awsSecretKey: 'mocked-secret-key',
        awsBucketName: 'mocked-bucket',
        baseUrl: 'mocked-base-url',
      },
      file: {
        fileContent: undefined,
        originalname: 'file.jpg',
        mimeType: 'image/jpeg',
      },
    });
  });

  it('should return failure response if uploadToS3 throws error', async () => {
    uploadToS3.mockRejectedValue(new Error('Upload failed'));

    const result = await testStrategy['AWS S3'](configs, { upload: mockUpload });

    expect(result).toEqual({
      success: false,
      data: null,
      error: 'Upload failed',
      requestHeaders: null,
    });

    expect(mockUpload.toBuffer).toHaveBeenCalled();
    expect(uploadToS3).toHaveBeenCalled();
  });
});

describe('testStrategy.ExchangeRate Host', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return success response if exchange host is reachable', async () => {
    decrypt.mockReturnValueOnce('mocked-token');

    const mockRates = { rates: { MYR: 4.6, EUR: 0.9 }, base: 'USD' };
    vi.mocked(getExchangeRates).mockResolvedValueOnce(mockRates);

    const configs = [{ configKey: 'apiAccessKey', configValue: 'x' }];

    const result = await testStrategy['Exchangerate Host'](configs, {});

    expect(result).toEqual({
      success: true,
      data: null,
      error: null,
      requestHeaders: null,
    });

    expect(getExchangeRates).toHaveBeenCalled();
  });

  it('should return failed response if getExchangeRates throws an error', async () => {
    decrypt.mockReturnValueOnce('mocked-token');

    vi.mocked(getExchangeRates).mockRejectedValueOnce(new Error('Host unreachable'));

    const configs = [{ configKey: 'apiAccessKey', configValue: 'x' }];

    const result = await testStrategy['Exchangerate Host'](configs, {});

    expect(result).toEqual({
      success: false,
      data: null,
      error: expect.stringContaining('Host unreachable'),
      requestHeaders: null,
    });

    expect(getExchangeRates).toHaveBeenCalled();
  });
});

describe('testStrategy.Payment System', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return success response if payment system API is reachable', async () => {
    decrypt.mockReturnValueOnce('mockCode').mockReturnValueOnce('mockPass');

    paymentSystem.getMaintenance.mockResolvedValueOnce({ maintenance: false });

    const configs = [
      { configKey: 'secretCode', configValue: 'test' },
      { configKey: 'secretPassword', configValue: 'test' },
    ];

    const request = { accountId: '1' };

    const result = await testStrategy['Payment System'](configs, request);

    expect(result).toEqual({
      success: true,
      data: null,
      error: null,
      requestHeaders: null,
    });

    expect(paymentSystem.getMaintenance).toHaveBeenCalledWith({
      secretCode: 'mockCode',
      secretPassword: 'mockPass',
      accountId: '1',
    });
  });
  it('should return failed response if getMaintenance throws an error', async () => {
    decrypt.mockReturnValueOnce('mockCode').mockReturnValueOnce('mockPass');

    vi.mocked(paymentSystem.getMaintenance).mockRejectedValueOnce(new Error('API failure'));

    const configs = [
      { configKey: 'secretCode', configValue: 'test' },
      { configKey: 'secretPassword', configValue: 'test' },
    ];

    const request = { accountId: '1' };

    const result = await testStrategy['Payment System'](configs, request);

    expect(result).toEqual({
      success: false,
      data: null,
      error: expect.stringContaining('API failure'),
      requestHeaders: null,
    });

    expect(paymentSystem.getMaintenance).toHaveBeenCalled();
  });
});
