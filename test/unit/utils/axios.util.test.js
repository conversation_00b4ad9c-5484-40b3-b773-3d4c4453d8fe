import axios from 'axios';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { pinoLogger } from '#config/pino-logger.config.js';
import axiosInstance from '#src/utils/axios.util.js';

vi.mock('axios');
vi.mock('#config/pino-logger.config.js', () => ({
  pinoLogger: { error: vi.fn() },
}));

describe('axiosInstance util', () => {
  let mockUseRequest;
  let mockUseResponse;
  let mockInstance;

  beforeEach(() => {
    mockUseRequest = vi.fn();
    mockUseResponse = vi.fn();

    mockInstance = {
      interceptors: {
        request: { use: mockUseRequest },
        response: { use: mockUseResponse },
      },
    };

    axios.create.mockReturnValue(mockInstance);
    vi.clearAllMocks();
  });

  it('should create an axios instance with correct config and attach interceptors', () => {
    const instance = axiosInstance();

    expect(axios.create).toHaveBeenCalledWith({
      timeout: 10000,
      headers: { 'Content-Type': 'application/json' },
    });

    expect(mockUseRequest).toHaveBeenCalledTimes(1);
    expect(mockUseResponse).toHaveBeenCalledTimes(1);
    expect(instance).toBe(mockInstance);
  });

  it('should pass response through unchanged in response interceptor', () => {
    let successHandler;
    mockUseResponse.mockImplementation((success, fail) => {
      successHandler = success;
    });

    axiosInstance(); // registers interceptors

    const fakeResponse = { data: { message: 'ok' } };
    const result = successHandler(fakeResponse);

    expect(result).toBe(fakeResponse); // covers (response) => response
  });

  it('should log error and reject as Error when error is not an Error instance', async () => {
    let errorHandler;
    mockUseResponse.mockImplementation((success, fail) => {
      errorHandler = fail;
    });

    axiosInstance();

    const fakeError = { response: { data: { code: 500 } } }; // non-Error object

    await expect(errorHandler(fakeError)).rejects.toThrowError(JSON.stringify({ code: 500 }));
    expect(pinoLogger.error).toHaveBeenCalledWith('Axios Error:', { code: 500 });
  });

  it('should log error and reject as original Error when error is an Error instance', async () => {
    let errorHandler;
    mockUseResponse.mockImplementation((success, fail) => {
      errorHandler = fail;
    });

    axiosInstance();

    const realError = new Error('Network down');

    await expect(errorHandler(realError)).rejects.toThrowError('Network down');
    expect(pinoLogger.error).toHaveBeenCalledWith('Axios Error:', 'Network down');
  });

  it('should reject error in request interceptor', async () => {
    let requestFail;
    mockUseRequest.mockImplementation((success, fail) => {
      requestFail = fail;
    });

    axiosInstance();

    const reqError = new Error('Request failed');

    await expect(requestFail(reqError)).rejects.toThrowError('Request failed');
  });

  it('should wrap non-Error in request interceptor as Error', async () => {
    let requestFail;
    mockUseRequest.mockImplementation((success, fail) => {
      requestFail = fail;
    });

    axiosInstance();

    const nonError = 'Some string error';

    await expect(requestFail(nonError)).rejects.toThrowError('Some string error');
  });
});
