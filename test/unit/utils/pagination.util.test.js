import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  applyMongoFiltersCursorPagination,
  applyOffsetPagination,
} from '#src/utils/pagination.util.js';
import { prepareMongoFilters, prepareMongoSorting } from '#src/utils/query.util.js';
vi.mock('#src/utils/query.util.js', async () => {
  const actual = await vi.importActual('#src/utils/query.util.js');
  return {
    ...actual,
    prepareMongoFilters: vi.fn(),
    prepareMongoSorting: vi.fn(),
  };
});
describe('applyOffsetPagination', () => {
  const mockFastify = {
    // Mock any Fastify utilities used in prepareSortOrder
  };

  const mockModel = {
    unscoped: vi.fn().mockReturnThis(),
    scope: vi.fn().mockReturnThis(),
    findAndCountAll: vi.fn(),
  };

  const mockIncludes = [{ model: 'relatedModel' }];

  beforeEach(() => {
    vi.clearAllMocks();

    mockModel.unscoped.mockReturnValue(mockModel);

    mockModel.findAndCountAll.mockResolvedValue({
      count: 100,
      rows: Array(10).fill({ id: 1 }),
    });
  });

  it('should apply default pagination when no parameters are provided', async () => {
    const result = await applyOffsetPagination(mockFastify, mockModel, {}, {});

    expect(mockModel.findAndCountAll).toHaveBeenCalledWith({
      limit: 25,
      offset: 0,
      order: expect.anything(),
      where: {},
    });

    expect(result).toEqual({
      rows: expect.any(Array),
      pagination: {
        totalCount: 100,
        totalPages: 4,
        currentPage: 1,
        limit: 25,
      },
    });
  });

  it('should apply custom pagination parameters', async () => {
    const result = await applyOffsetPagination(
      mockFastify,
      mockModel,
      {
        page: 2,
        limit: 10,
      },
      {},
    );

    expect(mockModel.findAndCountAll).toHaveBeenCalledWith({
      limit: 10,
      offset: 10,
      order: expect.anything(),
      where: {},
    });

    expect(result.pagination).toEqual({
      totalCount: 100,
      totalPages: 10,
      currentPage: 2,
      limit: 10,
    });
  });

  it('should include related models when includes are provided', async () => {
    await applyOffsetPagination(mockFastify, mockModel, {}, {}, mockIncludes);

    expect(mockModel.findAndCountAll).toHaveBeenCalledWith({
      limit: 25,
      offset: 0,
      order: expect.anything(),
      where: {},
      include: mockIncludes,
    });
  });

  it('should handle zero results', async () => {
    mockModel.findAndCountAll.mockResolvedValueOnce({
      count: 0,
      rows: [],
    });

    const result = await applyOffsetPagination(mockFastify, mockModel, {});

    expect(result.pagination).toEqual({
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
      limit: 25,
    });
  });
});

describe('applyMongoFiltersCursorPagination', () => {
  // Define a mock model
  const mockModel = {
    find: vi.fn(),
    sort: vi.fn(),
    lean: vi.fn(),
    limit: vi.fn(),
    exec: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    mockModel.find.mockReturnValue(mockModel);
    mockModel.sort.mockReturnValue(mockModel);
    mockModel.lean.mockReturnValue(mockModel);
    mockModel.limit.mockReturnValue(mockModel);
    mockModel.exec.mockResolvedValue([]);
  });

  it('should apply filters, sorting, and pagination correctly', async () => {
    const mockQuery = {
      limit: 10,
      sortBy: 'name',
      cursor: 'abc123',
      name: 'Test',
    };

    const mockResult = [
      { _id: '1', name: 'Test 1' },
      { _id: '2', name: 'Test 2' },
    ];

    mockModel.exec.mockResolvedValue(mockResult);

    prepareMongoFilters.mockReturnValue({ name: 'Test' });
    prepareMongoSorting.mockReturnValue({
      sortField: 'name',
      sortOrder: 1,
      cursorOperator: '$gt',
    });

    const result = await applyMongoFiltersCursorPagination(mockModel, mockQuery, false);

    expect(result).toEqual({
      rows: mockResult,
      pagination: {
        limit: 10,
        nextCursor: '2',
      },
    });

    expect(mockModel.find).toHaveBeenCalledWith({ name: 'Test', _id: { $gt: 'abc123' } });
    expect(mockModel.sort).toHaveBeenCalledWith({ name: 1, _id: 1 });
    expect(mockModel.limit).toHaveBeenCalledWith(10);
  });

  it('should handle export case and skip pagination', async () => {
    const mockQuery = {
      limit: 10,
      sortBy: 'name',
      cursor: 'abc123',
    };

    const mockResult = [{ _id: '1', name: 'Test 1' }];

    mockModel.exec.mockResolvedValue(mockResult);

    prepareMongoFilters.mockReturnValue({ name: 'Test' });
    prepareMongoSorting.mockReturnValue({
      sortField: 'name',
      sortOrder: 1,
      cursorOperator: '$gt',
    });

    const result = await applyMongoFiltersCursorPagination(mockModel, mockQuery, true);

    expect(result).toEqual({
      rows: mockResult,
      pagination: {
        limit: null,
        nextCursor: '1',
      },
    });

    expect(mockModel.limit).not.toHaveBeenCalled();
  });

  it('should handle pagination with no results', async () => {
    const mockQuery = {
      limit: 10,
      sortBy: 'name',
      cursor: 'abc123',
    };

    const mockResult = [];

    mockModel.exec.mockResolvedValue(mockResult);

    prepareMongoFilters.mockReturnValue({ name: 'Test' });
    prepareMongoSorting.mockReturnValue({
      sortField: 'name',
      sortOrder: 1,
      cursorOperator: '$gt',
    });

    const result = await applyMongoFiltersCursorPagination(mockModel, mockQuery, false);

    expect(result).toEqual({
      rows: [],
      pagination: {
        limit: 10,
        nextCursor: null, // No next cursor since no results
      },
    });
  });

  it('should handle missing filters in query', async () => {
    const mockQuery = {
      limit: 10,
      sortBy: 'name',
    };

    const mockResult = [{ _id: '1', name: 'Test 1' }];

    mockModel.exec.mockResolvedValue(mockResult);

    prepareMongoFilters.mockReturnValue({});
    prepareMongoSorting.mockReturnValue({
      sortField: 'name',
      sortOrder: 1,
      cursorOperator: '$gt',
    });

    const result = await applyMongoFiltersCursorPagination(mockModel, mockQuery, false);

    expect(result).toEqual({
      rows: mockResult,
      pagination: {
        limit: 10,
        nextCursor: '1',
      },
    });
  });

  it('should handle invalid query parameters gracefully', async () => {
    const mockQuery = {
      limit: 'invalid', // Non-numeric limit
      sortBy: 'name',
    };

    const mockResult = [{ _id: '1', name: 'Test 1' }];

    mockModel.exec.mockResolvedValue(mockResult);

    prepareMongoFilters.mockReturnValue({});
    prepareMongoSorting.mockReturnValue({
      sortField: 'name',
      sortOrder: 1,
      cursorOperator: '$gt',
    });

    const result = await applyMongoFiltersCursorPagination(mockModel, mockQuery, false);

    expect(result).toEqual({
      rows: mockResult,
      pagination: {
        limit: 50, // Default limit is used
        nextCursor: '1',
      },
    });
  });
});
