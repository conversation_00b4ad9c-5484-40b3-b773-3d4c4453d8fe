import { describe, expect, it } from 'vitest';

import { comparePassword, hashPassword } from '#src/utils/hash.util.js';

describe('password utils', () => {
  const plainPassword = 'MySecret123!';

  it('should hash a password and return a string', async () => {
    const hashed = await hashPassword(plainPassword);

    expect(typeof hashed).toBe('string');
    expect(hashed.length).toBeGreaterThan(0);
    expect(hashed).not.toBe(plainPassword); // hashed value should be different
  });

  it('should correctly compare matching password', async () => {
    const hashed = await hashPassword(plainPassword);
    const result = await comparePassword(plainPassword, hashed);

    expect(result).toBe(true);
  });

  it('should correctly compare non-matching password', async () => {
    const hashed = await hashPassword(plainPassword);
    const result = await comparePassword('WrongPassword', hashed);

    expect(result).toBe(false);
  });
});
