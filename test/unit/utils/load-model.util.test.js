import { access, readdir } from 'node:fs/promises';
import { extname, join } from 'node:path';
import { pathToFileURL } from 'node:url';

import { Sequelize } from 'sequelize';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { getModelPath } from '#src/utils/file.util.js';
import { MODEL_TYPES } from '#src/utils/load-model.util.js';
import * as loadModelHelper from '#src/utils/load-model.util.js';

vi.mock('node:fs/promises');
vi.mock('node:path');
vi.mock('node:url');
vi.mock('#src/utils/file.util.js');
vi.mock('sequelize');

describe('load-model.helper', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      log: {
        error: vi.fn(),
        info: vi.fn(),
      },
      mongo: {},
      psql: {},
    };
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('MODEL_TYPES', () => {
    it('should validate and get name for mongo model', () => {
      const mockMongoModel = { modelName: 'User' };

      const isValid = MODEL_TYPES.mongo.isValidModel(mockMongoModel);
      const name = MODEL_TYPES.mongo.getModelName(mockMongoModel);

      expect(isValid).toBe(true);
      expect(name).toBe('User');
    });

    it('should invalidate mongo model without modelName', () => {
      const mockInvalidModel = {};

      const isValid = MODEL_TYPES.mongo.isValidModel(mockInvalidModel);
      expect(isValid).toBe(false);
    });

    it('should validate and get name for postgres model', () => {
      class PostgresModel extends Sequelize.Model {}
      const mockInstance = PostgresModel;

      const isValid = MODEL_TYPES.postgres.isValidModel(mockInstance);
      const name = MODEL_TYPES.postgres.getModelName(mockInstance);

      expect(isValid).toBe(true);
      expect(name).toBe('PostgresModel');
    });

    it('should invalidate postgres model if not subclass of Sequelize.Model', () => {
      class NotSequelizeModel {}
      const isValid = MODEL_TYPES.postgres.isValidModel(NotSequelizeModel);
      expect(isValid).toBe(false);
    });
  });

  describe('autoloadModels', () => {
    const modelPath = getModelPath();
    const modelType = 'mongo';

    beforeEach(() => {
      vi.clearAllMocks();

      readdir.mockResolvedValue([]);
    });

    it('should handle unsupported model type gracefully', async () => {
      await loadModelHelper.autoloadModels(fastifyMock, 'unsupported');

      expect(fastifyMock.log.error).toHaveBeenCalledWith('Unsupported model type: unsupported');
    });

    it('should load the model based on the model type.', async () => {
      readdir.mockResolvedValue([]);

      await loadModelHelper.autoloadModels(fastifyMock, modelType);

      expect(join).toHaveBeenCalledWith(modelPath, modelType);
      expect(fastifyMock.log.error).not.toHaveBeenCalled();
    });

    it('should handle ENOENT error when accessing model directory', async () => {
      getModelPath.mockReturnValue('/invalid/path');

      await loadModelHelper.autoloadModels(fastifyMock, modelType);

      expect(fastifyMock.log.error).not.toHaveBeenCalled();
    });

    it('should handle non-ENOENT errors when accessing model directory', async () => {
      const error = new Error('Mock error');
      access.mockRejectedValueOnce(error);

      await loadModelHelper.autoloadModels(fastifyMock, modelType);

      expect(fastifyMock.log.error).toHaveBeenCalledWith(error, 'Error autoloading models');
    });
  });

  describe('loadModels', () => {
    it('should load all models under model directory', async () => {
      const modelDir = '/path/to/modules';
      const type = 'mongo';
      const modelConfig = loadModelHelper.MODEL_TYPES[type];

      readdir.mockResolvedValue(['model1.js', 'model2.js']);

      await loadModelHelper.loadModels(fastifyMock, modelDir, null, modelConfig);

      expect(readdir).toHaveBeenCalledWith(modelDir);
    });

    it('should handle non-ENOENT errors when accessing model directory', async () => {
      const modelDir = '/path/to/modules';
      const type = 'mongo';
      const modelConfig = loadModelHelper.MODEL_TYPES[type];

      const mockError = new Error('Mock error');
      readdir.mockRejectedValue(mockError);

      await loadModelHelper.loadModels(fastifyMock, modelDir, null, modelConfig);

      expect(fastifyMock.log.error).toHaveBeenCalledWith(
        mockError,
        'Error accessing model directory',
      );
    });
  });

  describe('dynamicImport', () => {
    it('should import model file correctly', async () => {
      const file = await loadModelHelper.dynamicImport(
        'src/modules/core/models/postgres/localisation.model.js',
      );

      expect(file).toHaveProperty('default');
    });
  });

  describe('loadModelFile', () => {
    let mockDynamicImport = {};
    let mockValidModel = {};
    let mockModelConfig = {};
    const mockModelName = 'Mock';

    beforeEach(() => {
      vi.resetAllMocks();

      mockDynamicImport = vi.fn().mockResolvedValue({
        default: vi.fn().mockReturnValue({
          modelName: mockModelName,
          prototype: 'mock',
        }),
      });

      mockValidModel = vi.fn().mockReturnValue(true);
      mockModelConfig = {
        decoratorName: 'mongo',
        getModelName: vi.fn().mockReturnValue(mockModelName),
        isValidModel: mockValidModel,
      };

      extname.mockReturnValue('.js');
      pathToFileURL.mockReturnValue(new URL('file:///path/to/models/example.model.js'));
    });

    it('should handle non-JS files', async () => {
      extname.mockReturnValueOnce('.txt');

      await loadModelHelper.loadModelFile(
        fastifyMock,
        '/path/to/models',
        'model.txt',
        null,
        mockModelConfig,
      );

      expect(pathToFileURL).not.toHaveBeenCalled();
    });

    it('should handle errors when loading model file', async () => {
      const mockError = new Error('Mock error');
      const mockDynamicImport = vi.fn().mockRejectedValue(mockError);

      await loadModelHelper.loadModelFile(
        fastifyMock,
        '/path/to/models',
        'example.model.js',
        null,
        mockModelConfig,
        mockDynamicImport,
      );

      expect(fastifyMock.log.error).toHaveBeenCalledWith(
        mockError,
        'Error loading model example.model.js',
      );
    });

    it('should skip if not a js model file', async () => {
      mockDynamicImport.mockResolvedValueOnce({});

      await loadModelHelper.loadModelFile(
        fastifyMock,
        '/path/to/models',
        'example.model.js',
        null,
        mockModelConfig,
        mockDynamicImport,
      );

      expect(fastifyMock.log.info).not.toHaveBeenCalled();
    });

    it('should skip if not a valid model instance', async () => {
      mockValidModel.mockReturnValue(false);

      await loadModelHelper.loadModelFile(
        fastifyMock,
        '/path/to/models',
        'example.model.js',
        null,
        mockModelConfig,
        mockDynamicImport,
      );

      expect(fastifyMock.log.info).not.toHaveBeenCalled();
    });

    it('should load model successfully', async () => {
      await loadModelHelper.loadModelFile(
        fastifyMock,
        '/path/to/models',
        'example.model.js',
        null,
        mockModelConfig,
        mockDynamicImport,
      );

      expect(fastifyMock.log.info).toHaveBeenCalledWith(`Loaded model: ${mockModelName}`);
    });
  });
});
