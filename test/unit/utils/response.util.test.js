import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { translateDropdownItem, translateMessage } from '#src/utils/i18next.util.js';
import {
  formatDropdownResponse,
  formatSuccessResponse,
  handleServiceResponse,
} from '#src/utils/response.util.js';

// Mock the translateMessage function
vi.mock('#src/utils/i18next.util.js', () => ({
  translateMessage: vi.fn(),
  translateDropdownItem: vi.fn(),
}));

describe('Response Utility Functions', () => {
  describe('formatSuccessResponse', () => {
    it('should format a success response correctly', () => {
      translateMessage.mockReturnValue('Translated: Success message');

      const result = formatSuccessResponse(
        'Success message',
        { id: 1 },
        { total: 1 },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Success message',
        data: { id: 1 },
        meta: { total: 1 },
      });
    });

    it('should handle default parameters', () => {
      const result = formatSuccessResponse();
      expect(result).toEqual({
        message: '',
        data: [],
        meta: {},
      });
    });

    it('should correctly handle when meta.details is an empty string and not call translateMessage', () => {
      const result = formatSuccessResponse(
        'Success message',
        { id: 1 },
        { total: 1, details: '' },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Success message',
        data: { id: 1 },
        meta: {
          total: 1,
          details: '',
        },
      });
      expect(translateMessage).not.toHaveBeenCalled();
    });

    it('should translate meta.details when present in success response', () => {
      translateMessage.mockImplementation((details, params) => `Translated: ${details}`);

      const result = formatSuccessResponse(
        'Success message',
        { id: 1 },
        { total: 1, details: 'Some details' },
        { param: 'value' },
      );

      expect(result).toEqual({
        message: 'Success message',
        data: { id: 1 },
        meta: {
          total: 1,
          details: 'Translated: Some details',
        },
      });
      expect(translateMessage).toHaveBeenCalledWith('Some details', { param: 'value' });
    });
  });

  describe('formatDropdownResponse', () => {
    beforeEach(() => {
      translateDropdownItem.mockImplementation((data) =>
        // eslint-disable-next-line sonarjs/no-nested-functions
        data.map((item) => `Translated Dropdown: ${item}`),
      );
    });

    it('should format a dropdown response correctly', () => {
      const mockData = ['Option 1', 'Option 2'];
      translateMessage.mockImplementation((message, params) => `Translated: ${message}`);

      const result = formatDropdownResponse(
        'Dropdown message',
        mockData,
        { total: 2 },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Dropdown message',
        data: ['Translated Dropdown: Option 1', 'Translated Dropdown: Option 2'],
        meta: { total: 2 },
      });
      expect(translateMessage).toHaveBeenCalledWith('Dropdown message', { param: 'value' });
      expect(translateDropdownItem).toHaveBeenCalledWith(mockData);
    });

    it('should handle default parameters', () => {
      translateMessage.mockImplementation((message, params) => 'Translated: ');

      const result = formatDropdownResponse();
      expect(result).toEqual({
        message: 'Translated: ',
        data: [],
        meta: {},
      });
    });

    it('should translate meta.details when present', () => {
      translateMessage.mockImplementation((message, params) => `Translated: ${message}`);

      const result = formatDropdownResponse(
        'Dropdown message',
        ['Option 1', 'Option 2'],
        { total: 2, details: 'Some dropdown details' },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Dropdown message',
        data: ['Translated Dropdown: Option 1', 'Translated Dropdown: Option 2'],
        meta: {
          total: 2,
          details: 'Translated: Some dropdown details',
        },
      });
    });
  });

  describe('handleServiceResponse', () => {
    const mockRequest = {};
    const mockReply = {
      success: vi.fn(),
    };

    afterEach(() => {
      vi.clearAllMocks();
    });

    it('should handle index method and call reply.success with rows and pagination', async () => {
      const serviceFn = vi.fn().mockResolvedValue({
        rows: [{ id: 1 }],
        pagination: { total: 1 },
      });

      await handleServiceResponse({
        request: mockRequest,
        reply: mockReply,
        serviceFn,
        module: 'user',
        method: 'index',
      });

      expect(serviceFn).toHaveBeenCalledWith(mockRequest);
      expect(mockReply.success).toHaveBeenCalledWith('user', 'index', [{ id: 1 }], {
        pagination: { total: 1 },
      });
    });

    it('should handle non-index method and call reply.success with result only', async () => {
      const serviceFn = vi.fn().mockResolvedValue({ id: 1 });

      await handleServiceResponse({
        request: mockRequest,
        reply: mockReply,
        serviceFn,
        module: 'user',
        method: 'create',
      });

      expect(serviceFn).toHaveBeenCalledWith(mockRequest);
      expect(mockReply.success).toHaveBeenCalledWith('user', 'create', { id: 1 });
    });

    it('should populate request with audit if passed in', async () => {
      const serviceFn = vi.fn().mockResolvedValue({ id: 1 });

      await handleServiceResponse({
        request: mockRequest,
        reply: mockReply,
        serviceFn,
        module: 'user',
        method: 'create',
        audit: 'audit',
      });

      expect(serviceFn).toHaveBeenCalledWith(mockRequest);
      expect(mockRequest.audit).toBe('audit');
    });
  });
});
