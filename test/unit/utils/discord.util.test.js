import { beforeEach, describe, expect, it, vi } from 'vitest';

import { sendDiscordMessage } from '#src/utils/discord.util.js';

describe('sendDiscordMessage', () => {
  const webhook = 'https://discord.com/api/webhooks/1234567890/abcdef';
  const content = 'Hello, Discord!';
  let mockAxios;

  beforeEach(() => {
    mockAxios = {
      post: vi.fn(),
      interceptors: { request: { use: vi.fn((fn) => fn({ headers: { 'X-Test': 'yes' } })) } },
    };
  });

  it('should return success true and capture request headers if Discord responds with 204', async () => {
    mockAxios.post.mockResolvedValueOnce({ status: 204 });

    const result = await sendDiscordMessage({ webhook, content }, mockAxios);

    expect(result).toEqual({
      success: true,
      requestHeaders: { 'X-Test': 'yes' },
    });
    expect(mockAxios.post).toHaveBeenCalledWith(webhook, { content });
  });

  it('should throw an error object with message if <PERSON><PERSON><PERSON> rejects with response error', async () => {
    const error = { response: { data: { message: 'Invalid webhook' } }, message: 'Failed' };
    mockAxios.post.mockRejectedValueOnce(error);

    await expect(sendDiscordMessage({ webhook, content }, mockAxios)).rejects.toMatchObject({
      success: false,
      message: 'Invalid webhook',
      requestHeaders: { 'X-Test': 'yes' },
    });
  });

  it('should throw an error object with message if Axios rejects with generic error', async () => {
    const error = { message: 'Network error' };
    mockAxios.post.mockRejectedValueOnce(error);

    await expect(sendDiscordMessage({ webhook, content }, mockAxios)).rejects.toMatchObject({
      success: false,
      message: 'Network error',
      requestHeaders: { 'X-Test': 'yes' },
    });
  });

  it('should use default content when none is provided', async () => {
    mockAxios.post.mockResolvedValueOnce({ status: 204 });

    const result = await sendDiscordMessage({ webhook }, mockAxios);

    expect(result).toEqual({
      success: true,
      requestHeaders: { 'X-Test': 'yes' },
    });
    expect(mockAxios.post).toHaveBeenCalledWith(webhook, { content: 'No content provided.' });
  });
});
