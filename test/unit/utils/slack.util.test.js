import { beforeEach, describe, expect, it, vi } from 'vitest';

import { sendSlackMessage } from '#src/utils/slack.util.js';

describe('sendSlackMessage', () => {
  const webhook = '*****************************************************************************';
  const content = 'Test message to Slack';
  let mockAxios;

  beforeEach(() => {
    vi.clearAllMocks();
    mockAxios = {
      post: vi.fn(),
      interceptors: { request: { use: vi.fn((fn) => fn({ headers: { foo: 'bar' } })) } },
    };
  });

  it('should return success true and requestHeaders if Slack responds with 200 OK', async () => {
    mockAxios.post.mockResolvedValueOnce({ status: 200 });

    const result = await sendSlackMessage({ webhook, content }, mockAxios);

    expect(result).toEqual({ success: true, requestHeaders: { foo: 'bar' } });
    expect(mockAxios.post).toHaveBeenCalledWith(webhook, { text: content });
  });

  it('should throw error object if Slack responds with JSON error message', async () => {
    mockAxios.post.mockRejectedValueOnce({
      response: { data: { message: 'Invalid webhook URL' } },
    });

    await expect(sendSlackMessage({ webhook, content }, mockAxios)).rejects.toMatchObject({
      success: false,
      requestHeaders: { foo: 'bar' },
    });
  });

  it('should throw error object with undefined message if Slack responds with JSON without message', async () => {
    mockAxios.post.mockRejectedValueOnce({ response: { data: {} } });

    await expect(sendSlackMessage({ webhook, content }, mockAxios)).rejects.toMatchObject({
      success: false,
      requestHeaders: { foo: 'bar' },
    });
  });

  it('should throw error object with plain message if Axios fails without response', async () => {
    mockAxios.post.mockRejectedValueOnce(new Error('Network error'));

    await expect(sendSlackMessage({ webhook, content }, mockAxios)).rejects.toMatchObject({
      success: false,
      message: 'Network error',
      requestHeaders: { foo: 'bar' },
    });
  });

  it('should use default content if none is provided', async () => {
    mockAxios.post.mockResolvedValueOnce({ status: 200 });

    const result = await sendSlackMessage({ webhook }, mockAxios);

    expect(result).toEqual({ success: true, requestHeaders: { foo: 'bar' } });
    expect(mockAxios.post).toHaveBeenCalledWith(webhook, { text: 'No content provided.' });
  });
});
