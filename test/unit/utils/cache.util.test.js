import { createHash } from 'crypto';

import { beforeEach, describe, expect, it, vi } from 'vitest';

import { pinoLogger } from '#config/pino-logger.config.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  clearAllCache,
  clearCache,
  clearCacheWithPrefix,
  fetchFromCache,
  generateCacheKey,
  getCacheKeysWithPrefix,
  getCache,
  setCacheWithTTL,
} from '#src/utils/cache.util.js';

const {
  CACHE_KEY_SEGMENTS: { ENTITY, AUTH, AUDIT_TRAIL },
} = CoreConstant;

vi.mock('#config/pino-logger.config.js', () => ({
  pinoLogger: {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

describe('Cache Utility', () => {
  let mockRedis;

  beforeEach(() => {
    mockRedis = {
      get: vi.fn(),
      set: vi.fn(),
      setex: vi.fn(),
      del: vi.fn(),
      scan: vi.fn().mockResolvedValue(['0', []]),
      flushall: vi.fn(),
    };
  });

  describe('generateCacheKey', () => {
    it('should generate a cache key with ENTITY scope (default)', () => {
      const prefix = 'testPrefix';
      const request = {
        raw: { url: '/api/test-endpoint' },
        entity: { id: 'entity-123' },
      };
      const expectedRaw = JSON.stringify({
        url: '/api/test-endpoint',
        entityId: 'entity-123',
      });
      const expectedHash = createHash('sha256').update(expectedRaw).digest('hex');
      const expectedKey = `${prefix}:${request.entity.id}:${expectedHash}`;

      const result = generateCacheKey(prefix, request, { keyType: ENTITY });
      expect(result).toBe(expectedKey);
    });

    it('should generate a cache key with AUTH scope', () => {
      const prefix = 'testPrefix';
      const request = {
        raw: { url: '/api/personal-endpoint' },
        authInfo: { id: 'user-456' },
      };
      const expectedRaw = JSON.stringify({
        url: '/api/personal-endpoint',
        entityId: 'user-456',
      });
      const expectedHash = createHash('sha256').update(expectedRaw).digest('hex');
      const expectedKey = `${prefix}:${request.authInfo.id}:${expectedHash}`;

      const result = generateCacheKey(prefix, request, { keyType: AUTH });
      expect(result).toBe(expectedKey);
    });

    it('should generate a cache key with AUDIT_TRAIL scope (entityAccessId + authId)', () => {
      const prefix = 'testPrefix';
      const request = {
        raw: { url: '/api/audit-endpoint' },
        entityAccessId: 'access-789',
        authInfo: { id: 'user-456' },
      };
      const expectedRaw = JSON.stringify({
        url: '/api/audit-endpoint',
        entityId: 'access-789:user-456',
      });
      const expectedHash = createHash('sha256').update(expectedRaw).digest('hex');
      const expectedKey = `${prefix}:${request.entityAccessId}:${request.authInfo.id}:${expectedHash}`;

      const result = generateCacheKey(prefix, request, { keyType: AUDIT_TRAIL });
      expect(result).toBe(expectedKey);
    });

    it('should generate a cache key without entityId when excludeKey = true', () => {
      const prefix = 'testPrefix';
      const request = {
        raw: { url: '/api/no-entity' },
        entity: { id: 'entity-123' },
      };
      const expectedRaw = JSON.stringify({
        url: '/api/no-entity',
        entityId: 'entity-123',
      });
      const expectedHash = createHash('sha256').update(expectedRaw).digest('hex');
      const expectedKey = `${prefix}:${expectedHash}`;

      const result = generateCacheKey(prefix, request, { keyType: ENTITY, excludeKey: true });
      expect(result).toBe(expectedKey);
    });

    it('should fallback to no-entity if entityId is missing', () => {
      const prefix = 'testPrefix';
      const request = { raw: { url: '/api/fallback' } };
      const expectedRaw = JSON.stringify({
        url: '/api/fallback',
        entityId: 'no-entity',
      });
      const expectedHash = createHash('sha256').update(expectedRaw).digest('hex');
      const expectedKey = `${prefix}:no-entity:${expectedHash}`;

      const result = generateCacheKey(prefix, request);
      expect(result).toBe(expectedKey);
    });

    it('should throw error if prefix is invalid', () => {
      expect(() => generateCacheKey('', {})).toThrow('Cache key prefix must be a non-empty string');
    });

    it('uses "no-auth" when authInfo.id is missing for AUTH keys', () => {
      const mockRequest = { authInfo: {} };
      const prefix = 'test';

      const key = generateCacheKey(prefix, mockRequest, { keyType: AUTH });

      expect(key).toMatch(/no-auth/);
    });

    it('includes entityAccessId and falls back to "no-auth" for AUDIT_TRAIL keys when authInfo.id is missing', () => {
      const mockRequest = { entityAccessId: 'entity123', authInfo: {} };
      const prefix = 'test';

      const key = generateCacheKey(prefix, mockRequest, { keyType: AUDIT_TRAIL });

      expect(key).toMatch(/entity123:no-auth/);
    });

    it('should log and rethrow error when redis.scan fails', async () => {
      const prefix = 'broken:';
      const redisError = new Error('Redis scan failed');
      mockRedis.scan.mockRejectedValueOnce(redisError);

      await expect(getCacheKeysWithPrefix(mockRedis, prefix)).rejects.toThrow('Redis scan failed');

      expect(pinoLogger.error).toHaveBeenCalledWith(
        { prefix, err: redisError },
        'Redis error in getCacheKeysWithPrefix',
      );
    });
  });

  describe('fetchFromCache', () => {
    it('should return cached data if it exists', async () => {
      const cacheKey = 'test_key';
      const cachedData = JSON.stringify({ foo: 'bar' });
      mockRedis.get.mockResolvedValue(cachedData);

      const result = await fetchFromCache(mockRedis, cacheKey, () => {});

      expect(result).toEqual({ foo: 'bar' });
      expect(mockRedis.get).toHaveBeenCalledWith(cacheKey);
    });

    it('should call resolver and cache result if data is not in cache', async () => {
      const cacheKey = 'test_key';
      const newData = { foo: 'baz' };
      mockRedis.get.mockResolvedValue(null);
      const resolver = vi.fn().mockResolvedValue(newData);

      const result = await fetchFromCache(mockRedis, cacheKey, resolver);

      expect(result).toEqual(newData);
      expect(resolver).toHaveBeenCalled();
      expect(mockRedis.setex).toHaveBeenCalledWith(cacheKey, 86400, JSON.stringify(newData));
    });

    it('should throw an error for invalid key', async () => {
      await expect(fetchFromCache(mockRedis, '', () => {})).rejects.toThrow(
        'Cache key must be a non-empty string',
      );
    });

    it('should set value in cache if provided and no resolver', async () => {
      const cacheKey = 'test-key';
      const resolver = { foo: 'set-direct' };
      mockRedis.get.mockResolvedValue(null);

      const result = await fetchFromCache(mockRedis, cacheKey, resolver);

      expect(result).toEqual(resolver);
      expect(mockRedis.setex).toHaveBeenCalledWith(cacheKey, 86400, JSON.stringify(resolver));
    });

    it('should handle corrupted cached data gracefully', async () => {
      const cacheKey = 'test-corrupt';
      mockRedis.get.mockResolvedValue('not-json');

      await expect(fetchFromCache(mockRedis, cacheKey, () => {})).rejects.toThrow();
    });
  });

  describe('clearCache', () => {
    it('should clear cache for a specific key and log accordingly', async () => {
      const cacheKey = 'test_key';
      mockRedis.del.mockResolvedValue(1);

      await clearCache(mockRedis, cacheKey);
      expect(mockRedis.del).toHaveBeenCalledWith(cacheKey);
    });

    it('should log when no cache key found to clear', async () => {
      const cacheKey = 'missing_key';
      mockRedis.del.mockResolvedValue(0);

      await clearCache(mockRedis, cacheKey);
      expect(mockRedis.del).toHaveBeenCalledWith(cacheKey);
    });

    it('should log error and throw if Redis fails', async () => {
      const err = new Error('Redis down');
      mockRedis.del.mockRejectedValue(err);

      await expect(clearCache(mockRedis, 'testKey')).rejects.toThrow(err);

      expect(pinoLogger.error).toHaveBeenCalledWith(
        { key: 'testKey', err },
        'Redis error in clearCache',
      );
    });
  });

  describe('clearCacheWithPrefix', () => {
    it('should clear cache with a specific prefix', async () => {
      const prefix = 'test';
      const keys = ['test_index:1', 'test_view:2'];
      mockRedis.scan.mockResolvedValueOnce(['0', keys]);

      await clearCacheWithPrefix(mockRedis, prefix);
      expect(mockRedis.scan).toHaveBeenCalledWith('0', 'MATCH', `${prefix}*:*`, 'COUNT', 1000);
      expect(mockRedis.del).toHaveBeenCalledWith(keys);
    });

    it('should clear cache with specific entityId', async () => {
      const prefix = 'test:';
      const entityId = '123';
      const keys = ['test:1231', 'test:1232'];
      mockRedis.scan.mockResolvedValueOnce(['0', keys]);

      await clearCacheWithPrefix(mockRedis, prefix, entityId);
      expect(mockRedis.scan).toHaveBeenCalledWith(
        '0',
        'MATCH',
        `${prefix}*:${entityId}:*`,
        'COUNT',
        1000,
      );
      expect(mockRedis.del).toHaveBeenCalledWith(keys);
    });

    it('should not call del if no keys found', async () => {
      const prefix = 'test:';
      mockRedis.scan.mockResolvedValueOnce(['0', []]);

      await clearCacheWithPrefix(mockRedis, prefix);
      expect(mockRedis.del).not.toHaveBeenCalled();
    });
    it('logs and rethrows error if redis fails', async () => {
      const mockError = new Error('Redis failure');
      const mockPrefix = 'testPrefix';
      const mockEntityId = '123';

      mockRedis.scan.mockRejectedValueOnce(mockError);

      await expect(clearCacheWithPrefix(mockRedis, mockPrefix, mockEntityId)).rejects.toThrow(
        'Redis failure',
      );

      expect(pinoLogger.error).toHaveBeenCalledWith(
        { prefix: mockPrefix, entityId: mockEntityId, err: mockError },
        'Error clearing cache with prefix',
      );
    });
  });

  describe('clearAllCache', () => {
    it('should clear all cache', async () => {
      await clearAllCache(mockRedis);
      expect(mockRedis.flushall).toHaveBeenCalled();
    });
    it('should log error and throw if flushall fails', async () => {
      const err = new Error('Redis flush failed');
      mockRedis.flushall.mockRejectedValue(err);

      await expect(clearAllCache(mockRedis)).rejects.toThrow(err);

      expect(pinoLogger.error).toHaveBeenCalledWith({ err }, 'Redis error in clearAllCache');
    });
  });

  describe('getCacheKeysWithPrefix', () => {
    it('should return all keys matching the prefix across multiple scan calls', async () => {
      const prefix = 'test:';
      mockRedis.scan
        .mockResolvedValueOnce(['1', ['test:1', 'test:2']])
        .mockResolvedValueOnce(['0', ['test:3']]);

      const result = await getCacheKeysWithPrefix(mockRedis, prefix);

      expect(mockRedis.scan).toHaveBeenCalledWith('0', 'MATCH', `${prefix}*`, 'COUNT', 1000);
      expect(mockRedis.scan).toHaveBeenCalledWith('1', 'MATCH', `${prefix}*`, 'COUNT', 1000);
      expect(result).toEqual(['test:1', 'test:2', 'test:3']);
    });

    it('should return an empty array if no keys are found', async () => {
      const prefix = 'no:match';
      mockRedis.scan.mockResolvedValueOnce(['0', []]);

      const result = await getCacheKeysWithPrefix(mockRedis, prefix);

      expect(mockRedis.scan).toHaveBeenCalledWith('0', 'MATCH', `${prefix}*`, 'COUNT', 1000);
      expect(result).toEqual([]);
    });

    it('should handle empty scan results in intermediate calls', async () => {
      const prefix = 'some_prefix_';
      mockRedis.scan
        .mockResolvedValueOnce(['1', []])
        .mockResolvedValueOnce(['0', ['some_prefix_1']]);

      const result = await getCacheKeysWithPrefix(mockRedis, prefix);
      expect(result).toEqual(['some_prefix_1']);
    });
  });

  describe('setCacheWithTTL', () => {
    it('should set cache with TTL and return the key', async () => {
      const key = 'test-key';
      const data = { foo: 'bar' };
      const ttlSeconds = 3600;

      const result = await setCacheWithTTL(mockRedis, key, data, ttlSeconds);

      expect(mockRedis.setex).toHaveBeenCalledWith(key, ttlSeconds, JSON.stringify(data));
      expect(result).toBe(key);
    });

    it('should log error and throw when redis.setex fails', async () => {
      const key = 'test-key';
      const data = { foo: 'bar' };
      const ttlSeconds = 3600;
      const redisError = new Error('Redis setex failed');

      mockRedis.setex.mockRejectedValueOnce(redisError);

      await expect(setCacheWithTTL(mockRedis, key, data, ttlSeconds)).rejects.toThrow(
        'Redis setex failed',
      );

      expect(pinoLogger.error).toHaveBeenCalledWith(
        { key, err: redisError },
        'Failed to cache data', // updated log message
      );
    });

    it('should throw if key is invalid', async () => {
      await expect(setCacheWithTTL(mockRedis, '', { foo: 'bar' }, 3600)).rejects.toThrow(
        'Cache key must be a non-empty string',
      );
    });

    it('should throw if ttlSeconds is invalid', async () => {
      await expect(setCacheWithTTL(mockRedis, 'test-key', { foo: 'bar' }, -10)).rejects.toThrow(
        'TTL must be a positive integer',
      );
    });
  });

  describe('getCache', () => {
    it('should return parsed cached data when cache hit occurs', async () => {
      const key = 'test-key';
      const cachedData = JSON.stringify({ foo: 'bar' });
      mockRedis.get.mockResolvedValue(cachedData);

      const result = await getCache(mockRedis, key);

      expect(mockRedis.get).toHaveBeenCalledWith(key);
      expect(pinoLogger.debug).toHaveBeenCalledWith(`Cache hit for key: ${key}`);
      expect(result).toEqual({ foo: 'bar' });
    });

    it('should return null when cache miss occurs', async () => {
      const key = 'test-key';
      mockRedis.get.mockResolvedValue(null);

      const result = await getCache(mockRedis, key);

      expect(mockRedis.get).toHaveBeenCalledWith(key);
      expect(pinoLogger.debug).toHaveBeenCalledWith(`Cache miss for key: ${key}`);
      expect(result).toBeNull();
    });

    it('should throw error for invalid key', async () => {
      await expect(getCache(mockRedis, '')).rejects.toThrow('Cache key must be a non-empty string');

      await expect(getCache(mockRedis, null)).rejects.toThrow(
        'Cache key must be a non-empty string',
      );

      await expect(getCache(mockRedis, '   ')).rejects.toThrow(
        'Cache key must be a non-empty string',
      );
    });

    it('should log error and throw when redis.get fails', async () => {
      const key = 'test-key';
      const redisError = new Error('Redis get failed');

      mockRedis.get.mockRejectedValueOnce(redisError);

      await expect(getCache(mockRedis, key)).rejects.toThrow('Redis get failed');

      expect(pinoLogger.error).toHaveBeenCalledWith(
        { key, err: redisError },
        'Redis error in getCache',
      );
    });

    it('should handle corrupted JSON data gracefully', async () => {
      const key = 'test-key';
      mockRedis.get.mockResolvedValue('invalid-json');

      await expect(getCache(mockRedis, key)).rejects.toThrow();
    });
  });
});
