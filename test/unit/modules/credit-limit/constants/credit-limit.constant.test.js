import { describe, expect, it } from 'vitest';

import { CreditLimitConstant } from '#src/modules/credit-limit/index.js';

describe('Credit Limit Constants', () => {
  it('should have the correct value for OPTIMISTIC_RETRY', () => {
    expect(CreditLimitConstant.OPTIMISTIC_RETRY).toEqual(3);
  });

  it('should have the correct value for CREDIT_LIMIT_TRANSACTION_TYPES', () => {
    expect(CreditLimitConstant.CREDIT_LIMIT_TRANSACTION_TYPES).toEqual({
      CREDIT: 'credit',
      DEBIT: 'debit',
    });
  });
});
