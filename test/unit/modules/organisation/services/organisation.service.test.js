import crypto from 'crypto';

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreError } from '#src/modules/core/errors/index.js';
import {
  AddressRepository,
  EntityRepository,
  LinkRepository,
} from '#src/modules/core/repository/index.js';
import { OrganisationError } from '#src/modules/organisation/errors/index.js';
import {
  create,
  getThemes,
  index,
  options,
  organisationDropdown,
  updateBasicInformation,
  updateContactInformation,
  updateOrganisationLink,
  updateStatus,
  updateThemes,
  view,
} from '#src/modules/organisation/services/organisation.service.js';
import { LOCALISATION_CATEGORIES } from '#src/modules/setting/constants/localisation.constant.js';
import {
  CustomLocalisationRepository,
  LocalisationRepository,
} from '#src/modules/setting/repository/index.js';
import { LocalisationService } from '#src/modules/setting/services/index.js';

vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    COMMON_STATUSES: {
      ACTIVE: 'ACTIVE',
      INACTIVE: 'INACTIVE',
    },
    HIERARCHY: {
      ORGANISATION: 'ORGANISATION',
    },
    CACHE_KEY_SEGMENTS: {
      ENTITY: 'entity',
      AUTH: 'auth',
      AUDIT_TRAIL: 'auditTrail',
    },
    CACHE_SECOND: {
      SHORT: 10, // For rapidly changing data or debounce-type caching
      MEDIUM: 30, // 30 second – suitable for moderately volatile data
      STANDARD: 60, // 1 minute – good default for most general cache
      LONG: 3600, // 1 hour – stable data that changes infrequently
      DAILY: 86400, // 24 hours – rarely changing reference data
      WEEKLY: 604800, // 7 days – archive-type or external lookup cache
      NEVER: 0,
    },
  },
  LinkConstant: {
    TYPE: {
      EMAIL_DOMAIN: 'email_domain',
    },
  },
  EntityConstant: {
    IMAGE_TYPE: {
      LOGO_DESKTOP: 'logoDesktop',
      LOGO_MOBILE: 'logoMobile',
      FAVICON: 'favicon',
    },
  },
}));

vi.mock('#src/modules/core/repository/index.js', () => ({
  EntityRepository: {
    findAll: vi.fn(),
    findById: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    count: vi.fn(),
  },
  LinkRepository: {
    create: vi.fn(),
    findOrCreate: vi.fn(),
  },
  AddressRepository: {
    create: vi.fn(),
    findOrCreate: vi.fn(),
  },
  MediaAssetRepository: {
    findByPk: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/repository/index.js', () => ({
  LocalisationRepository: {
    count: vi.fn(),
  },
  CustomLocalisationRepository: {
    create: vi.fn(),
    findOrCreate: vi.fn().mockImplementation((options) => {
      return Promise.resolve([
        {
          id: 'cl-mock-id',
          update: vi.fn().mockResolvedValue({}),
        },
        false,
      ]);
    }),
    findAll: vi.fn(),
    update: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/constants/localisation.constant.js', () => ({
  LOCALISATION_CATEGORIES: {
    CURRENCY: 'CURRENCY',
  },
}));

vi.mock('#src/modules/organisation/errors/index.js', () => ({
  OrganisationError: {
    organisationNotFound: vi.fn((id) => new Error(`Organisation not found: ${id}`)),
    organisationCreationFailed: vi.fn(() => new Error('Organisation creation failed')),
    organisationUpdateFailed: vi.fn(() => new Error('Organisation update failed')),
    noCurrenciesProvided: vi.fn(() => new Error('No currencies provided')),
    invalidCurrencies: vi.fn(() => new Error('Invalid currencies')),
  },
}));

vi.mock('#src/modules/core/errors/index.js', () => ({
  CoreError: {
    dataNotFound: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/services/index.js', () => ({
  LocalisationService: {
    generateDropdown: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/constants/localisation.constant.js', () => ({
  LOCALISATION_CATEGORIES: {
    LANGUAGE: 'LANGUAGE',
    REGION: 'REGION',
    CURRENCY: 'CURRENCY',
  },
}));

vi.mock('#src/modules/media/constants/media-asset.constant.js', () => ({}));

vi.mock('crypto', () => ({
  default: {
    randomBytes: vi.fn(),
  },
}));

vi.mock('#src/modules/media/services/media.service.js', () => ({
  uploadMedia: vi.fn().mockImplementation(async (options, server) => {
    return {
      id: `mock-media-id-${Date.now()}`,
      filePath: `path/to/uploaded/${options.filename || 'file'}`,
      file_type: options.fileContent ? 'image' : 'document',
      mime_type: options.mimeType || 'image/png',
    };
  }),
}));

const createMediaAssetMock = () => {
  const assetMap = {
    'logo-desktop-id': {
      id: 'logo-desktop-id',
      filePath: '/path/to/desktop-logo.png',
      toJSON: function () {
        return { id: this.id, filePath: this.filePath };
      },
    },
    'logo-mobile-id': {
      id: 'logo-mobile-id',
      filePath: '/path/to/mobile-logo.png',
      toJSON: function () {
        return { id: this.id, filePath: this.filePath };
      },
    },
    'favicon-id': {
      id: 'favicon-id',
      filePath: '/path/to/favicon.ico',
      toJSON: function () {
        return { id: this.id, filePath: this.filePath };
      },
    },
  };

  return {
    create: vi.fn(),
    findByPk: vi.fn((id) => Promise.resolve(assetMap[id])),
  };
};

describe('Organisation Service', () => {
  let mockServer;
  let mockRequest;
  let mockTransaction;
  let mediaAssetMock;

  beforeEach(() => {
    vi.clearAllMocks();

    mediaAssetMock = createMediaAssetMock();

    mockTransaction = {
      commit: vi.fn(),
      rollback: vi.fn(),
    };

    mockServer = {
      psql: {
        connection: {
          transaction: vi.fn((callback) => callback(mockTransaction)),
          Sequelize: {
            Op: {
              in: Symbol('in'),
              ne: Symbol('ne'),
            },
          },
          models: {
            CustomLocalisation: {
              create: vi.fn(),
            },
          },
        },
        Link: {
          create: vi.fn(),
          findOrCreate: vi
            .fn()
            .mockImplementation(() =>
              Promise.resolve([{ update: vi.fn().mockResolvedValue({}) }, false]),
            ),
        },
        Address: {
          create: vi.fn(),
          findOrCreate: vi
            .fn()
            .mockImplementation(() =>
              Promise.resolve([{ update: vi.fn().mockResolvedValue({}) }, false]),
            ),
        },
        Localisation: {
          count: vi.fn(),
        },
        Entity: {
          count: vi.fn(),
        },
        CustomLocalisation: {
          findAll: vi.fn(),
          update: vi.fn(),
          findOrCreate: vi.fn().mockImplementation(() => {
            return Promise.resolve([
              {
                id: 'cl-mock-id',
                update: vi.fn().mockResolvedValue({}),
              },
              false,
            ]);
          }),
        },
        MediaAsset: mediaAssetMock,
      },
    };

    mockRequest = {
      entity: { id: 'entity-123' },
      server: mockServer,
      authInfo: { id: 'user-123' },
      user: { id: 'user-123' },
      query: {},
      params: {},
      body: {},
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('index', () => {
    it('should retrieve all organisation entries for a given entity', async () => {
      const mockResult = { rows: [{ id: '1', name: 'Org 1' }] };
      EntityRepository.findAll.mockResolvedValue(mockResult);

      mockRequest.query = { limit: 10 };

      const result = await index(mockRequest);

      expect(EntityRepository.findAll).toHaveBeenCalledWith(mockServer, {
        limit: 10,
        filter_parentId_eq: 'entity-123',
      });
      expect(result).toEqual(mockResult);
    });

    it('should map currency information from custom_localisations in the index function', async () => {
      const mockOrgs = [
        {
          id: 'org-123',
          name: 'Test Org',
          toJSON: () => ({
            id: 'org-123',
            name: 'Test Org',
            custom_localisations: [
              {
                localisation: { name: 'US Dollar', code: 'USD' },
              },
              {
                localisation: { name: 'Euro', code: 'EUR' },
              },
            ],
          }),
        },
        {
          id: 'org-456',
          name: 'Another Org',
          toJSON: () => ({
            id: 'org-456',
            name: 'Another Org',
            custom_localisations: null,
          }),
        },
      ];

      EntityRepository.findAll.mockResolvedValue({
        rows: mockOrgs,
        totalRows: 2,
        totalPages: 1,
        currentPage: 1,
      });

      const result = await index(mockRequest);

      expect(result.rows).toEqual([
        {
          id: 'org-123',
          name: 'Test Org',
          custom_localisations: [
            {
              localisation: { name: 'US Dollar', code: 'USD' },
            },
            {
              localisation: { name: 'Euro', code: 'EUR' },
            },
          ],
          currencies: [
            { name: 'US Dollar', code: 'USD' },
            { name: 'Euro', code: 'EUR' },
          ],
        },
        {
          id: 'org-456',
          name: 'Another Org',
          custom_localisations: null,
          currencies: [],
        },
      ]);
    });

    it('should handle empty query parameters', async () => {
      const mockResult = { rows: [] };
      EntityRepository.findAll.mockResolvedValue(mockResult);

      const result = await index(mockRequest);

      expect(EntityRepository.findAll).toHaveBeenCalledWith(mockServer, {
        filter_parentId_eq: 'entity-123',
      });
      expect(result).toEqual(mockResult);
    });
  });

  describe('view', () => {
    it('should retrieve a specific organisation by ID with full information', async () => {
      const mockOrganisation = {
        id: 'org-123',
        name: 'Test Org',
        toJSON: vi.fn(() => ({ id: 'org-123', name: 'Test Org' })),
        addresses: {
          toJSON: vi.fn(() => ({ street: '123 Main St' })),
        },
        links: [{ url: '<EMAIL>' }],
        custom_localisations: [
          {
            localisation: { name: 'US Dollar', code: 'USD' },
          },
        ],
      };

      EntityRepository.findById.mockResolvedValue(mockOrganisation);
      mockRequest.params = { id: 'org-123' };

      const result = await view(mockRequest);

      expect(EntityRepository.findById).toHaveBeenCalledWith(mockServer, {
        filter_id_eq: 'org-123',
        filter_parentId_eq: 'entity-123',
      });

      expect(result).toEqual({
        id: 'org-123',
        name: 'Test Org',
        address: { street: '123 Main St' },
        emailDomain: '<EMAIL>',
        currencies: [{ name: 'US Dollar', code: 'USD' }],
      });
    });

    it('should handle organisation with no addresses, links, or localisations', async () => {
      const mockOrganisation = {
        id: 'org-123',
        name: 'Test Org',
        toJSON: vi.fn(() => ({ id: 'org-123', name: 'Test Org' })),
        addresses: null,
        links: [],
        custom_localisations: null,
      };

      EntityRepository.findById.mockResolvedValue(mockOrganisation);
      mockRequest.params = { id: 'org-123' };

      const result = await view(mockRequest);

      expect(result).toEqual({
        id: 'org-123',
        name: 'Test Org',
        address: null,
        emailDomain: null,
        currencies: [],
      });
    });

    it('should throw error when organisation not found', async () => {
      EntityRepository.findById.mockResolvedValue(null);
      mockRequest.params = { id: 'non-existent' };

      await expect(view(mockRequest)).rejects.toThrow(
        CoreError.dataNotFound(mockRequest.params.id),
      );
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.organisation',
        attribute: 'ID',
        value: mockRequest.params.id,
      });
    });
  });

  describe('create', () => {
    beforeEach(() => {
      crypto.randomBytes.mockReturnValue(Buffer.from([0x12, 0x34]));

      vi.spyOn(Date, 'now').mockReturnValue(1640995200000);

      LocalisationRepository.count.mockImplementation((server, query) => {
        if (query && query.category === 'CURRENCY') {
          return Promise.resolve(query.id[mockServer.psql.connection.Sequelize.Op.in].length);
        }
        return Promise.resolve(1);
      });
      EntityRepository.count.mockResolvedValue(0);
    });

    it('should create a new organisation with all optional fields', async () => {
      const mockNewOrganisation = { id: 'new-org-123' };
      EntityRepository.create.mockResolvedValue(mockNewOrganisation);

      mockRequest.body = {
        name: 'New Organisation',
        prefix: 'NEW',
        code: 'NEWORG',
        emailDomain: '<EMAIL>',
        address: { street: '456 New St' },
        currencyIds: ['currency-1', 'currency-2'],
        language: 'lang-1',
        region: 'region-1',
      };

      const result = await create(mockRequest);

      expect(EntityRepository.count).toHaveBeenCalledTimes(2);
      expect(LocalisationRepository.count).toHaveBeenCalledWith(mockServer, {
        id: { [mockServer.psql.connection.Sequelize.Op.in]: ['currency-1', 'currency-2'] },
        category: 'CURRENCY',
      });

      expect(EntityRepository.create).toHaveBeenCalledWith(
        mockServer,
        'entity-123',
        expect.objectContaining({
          name: 'New Organisation',
          hierarchy: 'ORGANISATION',
          prefix: 'NEW',
          code: 'NEWORG',
          accessId: '801640995200',
          status: 'ACTIVE',
        }),
        { transaction: mockTransaction, authInfoId: 'user-123' },
      );

      expect(LinkRepository.create).toHaveBeenCalledWith(
        mockServer,
        {
          entity_id: 'new-org-123',
          type: 'email_domain',
          url: '<EMAIL>',
        },
        { transaction: mockTransaction, authInfoId: 'user-123' },
      );

      expect(AddressRepository.create).toHaveBeenCalledWith(
        mockServer,
        {
          entity_id: 'new-org-123',
          street: '456 New St',
        },
        { transaction: mockTransaction, authInfoId: 'user-123' },
      );

      expect(CustomLocalisationRepository.create).toHaveBeenCalledTimes(4);

      expect(result).toEqual(mockNewOrganisation);
    });

    it('should create organisation with minimal required fields', async () => {
      const mockNewOrganisation = { id: 'new-org-123' };
      EntityRepository.create.mockResolvedValue(mockNewOrganisation);
      LocalisationRepository.count.mockResolvedValue(1);

      mockRequest.body = {
        name: 'Minimal Org',
        prefix: 'MIN',
        code: 'MINORG',
        currencyIds: ['currency-1'],
      };

      const result = await create(mockRequest);

      expect(EntityRepository.create).toHaveBeenCalled();
      expect(LinkRepository.create).not.toHaveBeenCalled();
      expect(AddressRepository.create).not.toHaveBeenCalled();
      expect(CustomLocalisationRepository.create).toHaveBeenCalledTimes(1);
      expect(result).toEqual(mockNewOrganisation);
    });

    // it('should throw error when prefix is not unique', async () => {
    //   EntityRepository.count.mockResolvedValueOnce(1);

    //   mockRequest.body = {
    //     name: 'Test Org',
    //     prefix: 'EXISTING',
    //     code: 'TESTORG',
    //     currencyIds: ['currency-1'],
    //   };

    //   await expect(create(mockRequest)).rejects.toThrow(OrganisationError.prefixNotUnique());
    //   expect(OrganisationError.prefixNotUnique).toHaveBeenCalledWith();
    // });

    // it('should throw error when code is not unique', async () => {
    //   EntityRepository.count.mockResolvedValueOnce(0).mockResolvedValueOnce(1);

    //   mockRequest.body = {
    //     name: 'Test Org',
    //     prefix: 'TEST',
    //     code: 'EXISTING',
    //     currencyIds: ['currency-1'],
    //   };

    //   await expect(create(mockRequest)).rejects.toThrow(OrganisationError.codeNotUnique());
    //   expect(OrganisationError.codeNotUnique).toHaveBeenCalledWith();
    // });

    it('should throw error when organisation creation fails', async () => {
      EntityRepository.create.mockResolvedValue(null);

      mockRequest.body = {
        name: 'Test Org',
        prefix: 'TEST',
        code: 'TESTORG',
        currencyIds: ['currency-1'],
      };

      await expect(create(mockRequest)).rejects.toThrow(
        OrganisationError.organisationCreationFailed(),
      );
      expect(OrganisationError.organisationCreationFailed).toHaveBeenCalled();
    });
  });

  describe('updateBasicInformation', () => {
    beforeEach(() => {
      LocalisationRepository.count.mockResolvedValue(2);
      CustomLocalisationRepository.findAll.mockResolvedValue([]);
      CustomLocalisationRepository.findOrCreate = vi
        .fn()
        .mockImplementation((server, query, options) => {
          return Promise.resolve([
            {
              id: `cl-${query.where.parentId || 'mock-id'}`,
              update: vi.fn().mockResolvedValue({}),
            },
            false,
          ]);
        });
    });

    it('should update basic information of an organisation', async () => {
      const mockExistingOrganisation = { id: 'org-123' };
      const mockUpdatedOrganisation = {
        id: 'org-123',
        name: 'Updated Org',
        description: 'Updated description',
      };

      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);
      EntityRepository.update.mockResolvedValue(mockUpdatedOrganisation);

      CustomLocalisationRepository.findOrCreate.mockImplementation((_server, options) => {
        return Promise.resolve([
          {
            id: `cl-${options.where.parentId}`,
            update: vi.fn().mockResolvedValue({}),
          },
          false,
        ]);
      });

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        currencyIds: ['currency-1', 'currency-2'],
        description: 'Updated description',
        version: 2,
        logoDesktop: 'desktop-logo.png',
        logoMobile: 'mobile-logo.png',
        favicon: 'favicon.ico',
      };
      mockRequest.authInfo = { id: 'user-123' };

      const result = await updateBasicInformation(mockRequest);

      expect(EntityRepository.findById).toHaveBeenCalledWith(mockServer, {
        filter_id_eq: 'org-123',
        filter_parentId_eq: 'entity-123',
      });

      expect(EntityRepository.update).toHaveBeenCalledWith(
        mockExistingOrganisation,
        expect.objectContaining({
          description: 'Updated description',
          updated_by: 'user-123',
          version: 2,
          logoDesktop: 'desktop-logo.png',
          logoMobile: 'mobile-logo.png',
          favicon: 'favicon.ico',
        }),
        { transaction: mockTransaction, authInfoId: 'user-123' },
      );

      expect(result).toEqual(mockUpdatedOrganisation);
    });

    it('should throw error when organisation not found for basic information update', async () => {
      EntityRepository.findById.mockResolvedValue(null);

      mockRequest.params = { id: 'non-existent' };
      mockRequest.body = {
        currencyIds: ['currency-1', 'currency-2'],
        description: 'Test description',
      };

      await expect(updateBasicInformation(mockRequest)).rejects.toThrow(
        CoreError.dataNotFound(mockRequest.params.id),
      );
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.organisation',
        attribute: 'ID',
        value: mockRequest.params.id,
      });
    });

    it('should throw error when basic information update fails', async () => {
      const mockExistingOrganisation = { id: 'org-123' };
      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);
      EntityRepository.update.mockResolvedValue(null);

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        currencyIds: ['currency-1', 'currency-2'],
        description: 'Test description',
      };

      await expect(updateBasicInformation(mockRequest)).rejects.toThrow(
        OrganisationError.organisationUpdateFailed(),
      );
      expect(OrganisationError.organisationUpdateFailed).toHaveBeenCalled();
    });

    it('should update existing currency localisations to inactive before creating new ones', async () => {
      const mockExistingOrganisation = { id: 'org-123' };
      const mockUpdatedOrganisation = {
        id: 'org-123',
        name: 'Updated Org',
        description: 'Updated description',
      };

      const mockCurrencyCustomLocalisations = [
        { id: 'cl-1', parentId: 'old-currency-1' },
        { id: 'cl-2', parentId: 'old-currency-2' },
      ];

      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);
      EntityRepository.update.mockResolvedValue(mockUpdatedOrganisation);

      CustomLocalisationRepository.findAll.mockResolvedValue(mockCurrencyCustomLocalisations);

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        currencyIds: ['currency-1', 'currency-2'],
        description: 'Updated description',
        version: 2,
      };
      mockRequest.authInfo = { id: 'user-123' };

      const result = await updateBasicInformation(mockRequest);

      expect(CustomLocalisationRepository.findAll).toHaveBeenCalledWith(mockServer, {
        where: { entityId: 'org-123' },
        include: [
          {
            model: mockServer.psql.Localisation,
            as: 'localisation',
            where: { category: 'CURRENCY' },
            required: true,
          },
        ],
        transaction: mockTransaction,
      });

      expect(CustomLocalisationRepository.update).toHaveBeenCalledWith(
        mockServer,
        {
          status: 'INACTIVE',
          updated_by: 'user-123',
        },
        {
          id: { [mockServer.psql.connection.Sequelize.Op.in]: ['cl-1', 'cl-2'] },
        },
        { transaction: mockTransaction },
      );

      expect(CustomLocalisationRepository.findOrCreate).toHaveBeenCalledTimes(2);

      expect(result).toEqual(mockUpdatedOrganisation);
    });
  });

  describe('updateContactInformation', () => {
    it('should update contact information of an organisation', async () => {
      const mockExistingOrganisation = { id: 'org-123' };
      const mockUpdatedOrganisation = {
        id: 'org-123',
        name: 'Updated Org',
        phone: '+1234567890',
        email: '<EMAIL>',
      };

      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);
      EntityRepository.update.mockResolvedValue(mockUpdatedOrganisation);

      AddressRepository.findOrCreate.mockImplementation(() => {
        return Promise.resolve([{ update: vi.fn().mockResolvedValue({}) }, false]);
      });

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        phone: '+1234567890',
        email: '<EMAIL>',
        address: { street: '789 Updated St' },
      };
      mockRequest.authInfo = { id: 'user-123' };

      const result = await updateContactInformation(mockRequest);

      expect(EntityRepository.findById).toHaveBeenCalledWith(mockServer, {
        filter_id_eq: 'org-123',
        filter_parentId_eq: 'entity-123',
      });

      expect(EntityRepository.update).toHaveBeenCalledWith(
        mockExistingOrganisation,
        expect.objectContaining({
          phone: '+1234567890',
          email: '<EMAIL>',
          updated_by: 'user-123',
        }),
        { transaction: mockTransaction, authInfoId: 'user-123' },
      );

      expect(AddressRepository.findOrCreate).toHaveBeenCalledWith(
        mockServer,
        {
          where: {
            entity_id: 'org-123',
          },
          defaults: {
            street: '789 Updated St',
          },
        },
        { transaction: mockTransaction, authInfoId: 'user-123' },
      );

      expect(result).toEqual(mockUpdatedOrganisation);
    });

    it('should update contact information without address', async () => {
      const mockExistingOrganisation = { id: 'org-123' };
      const mockUpdatedOrganisation = {
        id: 'org-123',
        phone: '+1234567890',
        email: '<EMAIL>',
      };

      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);
      EntityRepository.update.mockResolvedValue(mockUpdatedOrganisation);

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        phone: '+1234567890',
        email: '<EMAIL>',
      };
      mockRequest.authInfo = { id: 'user-123' };

      const result = await updateContactInformation(mockRequest);

      expect(EntityRepository.update).toHaveBeenCalledWith(
        mockExistingOrganisation,
        expect.objectContaining({
          phone: '+1234567890',
          email: '<EMAIL>',
          updated_by: 'user-123',
        }),
        { transaction: mockTransaction, authInfoId: 'user-123' },
      );

      expect(AddressRepository.findOrCreate).not.toHaveBeenCalled();
      expect(result).toEqual(mockUpdatedOrganisation);
    });

    it('should throw error when organisation not found for contact information update', async () => {
      EntityRepository.findById.mockResolvedValue(null);

      mockRequest.params = { id: 'non-existent' };
      mockRequest.body = {
        phone: '+1234567890',
        email: '<EMAIL>',
      };

      await expect(updateContactInformation(mockRequest)).rejects.toThrow();
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.organisation',
        attribute: 'ID',
        value: mockRequest.params.id,
      });
    });

    it('should throw error when contact information update fails', async () => {
      const mockExistingOrganisation = { id: 'org-123' };
      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);
      EntityRepository.update.mockResolvedValue(null);

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        phone: '+1234567890',
        email: '<EMAIL>',
      };

      await expect(updateContactInformation(mockRequest)).rejects.toThrow();
      expect(OrganisationError.organisationUpdateFailed).toHaveBeenCalled();
    });
  });

  describe('updateOrganisationLink', () => {
    it('should update organisation email domain', async () => {
      const mockExistingOrganisation = { id: 'org-123', name: 'Test Org' };

      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);

      LinkRepository.findOrCreate.mockImplementation(() => {
        return Promise.resolve([
          {
            update: vi.fn().mockResolvedValue({}),
          },
          false,
        ]);
      });

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        emailDomain: '<EMAIL>',
      };
      mockRequest.authInfo = { id: 'user-123' };

      const result = await updateOrganisationLink(mockRequest);

      expect(EntityRepository.findById).toHaveBeenCalledWith(mockServer, {
        filter_id_eq: 'org-123',
        filter_parentId_eq: 'entity-123',
      });

      expect(LinkRepository.findOrCreate).toHaveBeenCalledWith(
        mockServer,
        {
          where: {
            entity_id: 'org-123',
            type: 'email_domain',
          },
          defaults: {
            url: '<EMAIL>',
          },
        },
        { transaction: mockTransaction, authInfoId: 'user-123' },
      );

      expect(result).toEqual(mockExistingOrganisation);
    });

    it('should create new email domain link if it does not exist', async () => {
      const mockExistingOrganisation = { id: 'org-123', name: 'Test Org' };

      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);

      LinkRepository.findOrCreate.mockImplementation(() => {
        return Promise.resolve([{ id: 'link-123', url: '<EMAIL>' }, true]);
      });

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        emailDomain: '<EMAIL>',
      };

      const result = await updateOrganisationLink(mockRequest);

      expect(LinkRepository.findOrCreate).toHaveBeenCalledWith(
        mockServer,
        {
          where: {
            entity_id: 'org-123',
            type: 'email_domain',
          },
          defaults: {
            url: '<EMAIL>',
          },
        },
        { transaction: mockTransaction, authInfoId: 'user-123' },
      );

      expect(result).toEqual(mockExistingOrganisation);
    });

    it('should throw error when organisation not found', async () => {
      EntityRepository.findById.mockResolvedValue(null);

      mockRequest.params = { id: 'non-existent' };
      mockRequest.body = {
        emailDomain: '<EMAIL>',
      };

      await expect(updateOrganisationLink(mockRequest)).rejects.toThrow();
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.organisation',
        attribute: 'ID',
        value: mockRequest.params.id,
      });
    });

    it('should not update link if emailDomain is undefined', async () => {
      const mockExistingOrganisation = { id: 'org-123', name: 'Test Org' };

      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {};

      const result = await updateOrganisationLink(mockRequest);

      expect(LinkRepository.findOrCreate).not.toHaveBeenCalled();
      expect(result).toEqual(mockExistingOrganisation);
    });
  });

  describe('updateStatus', () => {
    it('should update organisation status successfully', async () => {
      const mockOrganisation = { id: 'org-123', status: 'ACTIVE' };
      const mockUpdatedOrganisation = { id: 'org-123', status: 'INACTIVE' };

      EntityRepository.findById.mockResolvedValue(mockOrganisation);
      EntityRepository.update.mockResolvedValue(mockUpdatedOrganisation);

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = { status: 'INACTIVE' };

      const result = await updateStatus(mockRequest);

      expect(EntityRepository.findById).toHaveBeenCalledWith(mockServer, {
        filter_id_eq: 'org-123',
      });
      expect(EntityRepository.update).toHaveBeenCalledWith(
        mockOrganisation,
        { status: 'INACTIVE' },
        mockRequest.user,
      );
      expect(result).toEqual(mockUpdatedOrganisation);
    });

    it('should throw error when organisation not found for status update', async () => {
      EntityRepository.findById.mockResolvedValue(null);

      mockRequest.params = { id: 'non-existent' };
      mockRequest.body = { status: 'INACTIVE' };

      await expect(updateStatus(mockRequest)).rejects.toThrow(
        'Organisation not found: non-existent',
      );
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.organisation',
        attribute: 'ID',
        value: mockRequest.params.id,
      });
    });
  });

  describe('organisationDropdown', () => {
    it('should return sorted list of organisations for dropdown', async () => {
      const mockOrganisations = {
        rows: [
          { code: 'ORG2', name: 'Zebra Organisation', accessId: 'access-2' },
          { code: 'ORG1', name: 'Alpha Organisation', accessId: 'access-1' },
          { code: 'ORG3', name: 'Beta Organisation', accessId: 'access-3' },
        ],
      };

      EntityRepository.findAll.mockResolvedValue(mockOrganisations);

      const result = await organisationDropdown(mockRequest);

      expect(result).toEqual([
        { code: 'ORG1', name: 'Alpha Organisation', accessId: 'access-1' },
        { code: 'ORG3', name: 'Beta Organisation', accessId: 'access-3' },
        { code: 'ORG2', name: 'Zebra Organisation', accessId: 'access-2' },
      ]);
    });

    it('should handle empty organisation list', async () => {
      EntityRepository.findAll.mockResolvedValue({ rows: [] });

      const result = await organisationDropdown(mockRequest);

      expect(result).toEqual([]);
    });
  });

  describe('options', () => {
    beforeEach(() => {
      vi.resetAllMocks();
    });

    it('should retrieve localisation options for drooptionspdown', async () => {
      LocalisationService.generateDropdown
        .mockResolvedValueOnce([{ id: 'en', name: 'English' }])
        .mockResolvedValueOnce([{ id: 'US', name: 'United States' }])
        .mockResolvedValueOnce([{ id: 'USD', name: 'US Dollar' }]);

      const result = await options(mockRequest);

      expect(LocalisationService.generateDropdown).toHaveBeenCalledTimes(3);
      expect(LocalisationService.generateDropdown).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({ 'filter_localisation.category_eq': 'LANGUAGE' }),
        }),
        true,
      );
      expect(LocalisationService.generateDropdown).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({ 'filter_localisation.category_eq': 'REGION' }),
        }),
        true,
      );
      expect(LocalisationService.generateDropdown).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({ 'filter_localisation.category_eq': 'CURRENCY' }),
        }),
        true,
      );

      expect(result).toEqual({
        [LOCALISATION_CATEGORIES.LANGUAGE]: [{ id: 'en', name: 'English' }],
        [LOCALISATION_CATEGORIES.REGION]: [{ id: 'US', name: 'United States' }],
        [LOCALISATION_CATEGORIES.CURRENCY]: [{ id: 'USD', name: 'US Dollar' }],
      });
    });

    it('should handle empty localisation options', async () => {
      LocalisationService.generateDropdown
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([])
        .mockResolvedValueOnce([]);

      const result = await options(mockRequest);

      expect(LocalisationService.generateDropdown).toHaveBeenCalledTimes(3);
      expect(result).toEqual({
        [LOCALISATION_CATEGORIES.LANGUAGE]: [],
        [LOCALISATION_CATEGORIES.REGION]: [],
        [LOCALISATION_CATEGORIES.CURRENCY]: [],
      });
    });

    it('should handle errors from LocalisationService', async () => {
      LocalisationService.generateDropdown.mockRejectedValue(new Error('Service error'));

      await expect(options(mockRequest)).rejects.toThrow('Service error');
      expect(LocalisationService.generateDropdown).toHaveBeenCalledTimes(3);
    });
  });

  describe('getThemes', () => {
    it('should retrieve theme settings for an organization', async () => {
      const mockOrganisation = {
        id: 'org-123',
        logoDesktop: 'logo-desktop-id',
        logoMobile: 'logo-mobile-id',
        favicon: 'favicon-id',
      };

      EntityRepository.findById.mockResolvedValue(mockOrganisation);

      mockRequest.params = { id: 'org-123' };

      const result = await getThemes(mockRequest);

      expect(EntityRepository.findById).toHaveBeenCalledWith(mockServer, {
        filter_id_eq: 'org-123',
      });

      expect(mockServer.psql.MediaAsset.findByPk).toHaveBeenCalledTimes(3);
      expect(mockServer.psql.MediaAsset.findByPk).toHaveBeenCalledWith('logo-desktop-id', {});
      expect(mockServer.psql.MediaAsset.findByPk).toHaveBeenCalledWith('logo-mobile-id', {});
      expect(mockServer.psql.MediaAsset.findByPk).toHaveBeenCalledWith('favicon-id', {});

      // Verify that the file paths are correctly assigned to the theme data
      expect(result).toEqual({
        logoDesktop: '/path/to/desktop-logo.png',
        logoMobile: '/path/to/mobile-logo.png',
        favicon: '/path/to/favicon.ico',
      });
    });

    it('should handle missing media assets gracefully', async () => {
      const mockOrganisation = {
        id: 'org-123',
        logoDesktop: 'logo-desktop-id',
        logoMobile: null,
        favicon: 'favicon-id',
      };

      EntityRepository.findById.mockResolvedValue(mockOrganisation);

      mockRequest.params = { id: 'org-123' };

      await getThemes(mockRequest);

      expect(mockServer.psql.MediaAsset.findByPk).toHaveBeenCalledTimes(2);
    });

    it('should throw error when organisation not found', async () => {
      EntityRepository.findById.mockResolvedValue(null);
      mockRequest.params = { id: 'non-existent' };

      await expect(getThemes(mockRequest)).rejects.toThrow(
        CoreError.dataNotFound(mockRequest.params.id),
      );
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.organisation',
        attribute: 'ID',
        value: mockRequest.params.id,
      });
    });

    it('should handle errors when fetching media assets', async () => {
      const mockOrganisation = {
        id: 'org-123',
        logoDesktop: 'logo-desktop-id',
        logoMobile: 'logo-mobile-id',
        favicon: 'favicon-id',
      };

      EntityRepository.findById.mockResolvedValue(mockOrganisation);

      mockServer.psql.MediaAsset.findByPk = vi.fn().mockRejectedValue(new Error('Database error'));

      mockRequest.params = { id: 'org-123' };

      await expect(getThemes(mockRequest)).rejects.toThrow('Database error');
    });
  });

  describe('updateThemes', () => {
    it('should update theme assets for an organization', async () => {
      const mockExistingOrganisation = { id: 'org-123' };
      const mockUpdatedOrganisation = {
        id: 'org-123',
        logoDesktop: 'new-desktop-id',
        logoMobile: 'new-mobile-id',
        favicon: 'new-favicon-id',
      };

      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);
      EntityRepository.update.mockResolvedValue(mockUpdatedOrganisation);

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        logoDesktop: 'new-desktop-id',
        logoMobile: 'new-mobile-id',
        favicon: 'new-favicon-id',
      };
      mockRequest.authInfo = { id: 'user-123' };

      const result = await updateThemes(mockRequest);

      expect(EntityRepository.findById).toHaveBeenCalledWith(mockServer, {
        filter_id_eq: 'org-123',
      });

      expect(EntityRepository.update).toHaveBeenCalledWith(
        mockExistingOrganisation,
        {
          logoDesktop: 'new-desktop-id',
          logoMobile: 'new-mobile-id',
          favicon: 'new-favicon-id',
        },
        { authInfoId: 'user-123' },
      );

      expect(result).toEqual({
        logoDesktop: 'new-desktop-id',
        logoMobile: 'new-mobile-id',
        favicon: 'new-favicon-id',
      });
    });

    it('should handle partial theme asset updates', async () => {
      const mockExistingOrganisation = { id: 'org-123' };
      const mockUpdatedOrganisation = {
        id: 'org-123',
        logoDesktop: 'new-desktop-id',
        logoMobile: null,
        favicon: null,
      };

      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);
      EntityRepository.update.mockResolvedValue(mockUpdatedOrganisation);

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        logoDesktop: 'new-desktop-id',
        logoMobile: null,
        favicon: null,
      };
      mockRequest.authInfo = { id: 'user-123' };

      const result = await updateThemes(mockRequest);

      expect(EntityRepository.update).toHaveBeenCalledWith(
        mockExistingOrganisation,
        {
          logoDesktop: 'new-desktop-id',
          logoMobile: null,
          favicon: null,
        },
        { authInfoId: 'user-123' },
      );

      expect(result).toEqual({
        logoDesktop: 'new-desktop-id',
        logoMobile: null,
        favicon: null,
      });
    });

    it('should throw error when organisation not found for theme update', async () => {
      EntityRepository.findById.mockResolvedValue(null);

      mockRequest.params = { id: 'non-existent' };
      mockRequest.body = {
        logoDesktop: 'new-desktop-id',
      };

      await expect(updateThemes(mockRequest)).rejects.toThrow(
        CoreError.dataNotFound(mockRequest.params.id),
      );
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.organisation',
        attribute: 'ID',
        value: mockRequest.params.id,
      });
    });

    it('should throw error when theme update fails', async () => {
      const mockExistingOrganisation = { id: 'org-123' };
      EntityRepository.findById.mockResolvedValue(mockExistingOrganisation);
      EntityRepository.update.mockResolvedValue(null);

      mockRequest.params = { id: 'org-123' };
      mockRequest.body = {
        logoDesktop: 'new-desktop-id',
      };

      await expect(updateThemes(mockRequest)).rejects.toThrow(
        OrganisationError.organisationUpdateFailed(),
      );
      expect(OrganisationError.organisationUpdateFailed).toHaveBeenCalled();
    });
  });
});
