import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { index, updateStatus, view } from '#src/modules/organisation/handlers/currency.handler.js';
import { LocalisationService } from '#src/modules/setting/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/setting/services/index.js');
describe('Currency Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      query: {},
      server: {
        redis: {},
        systemSetting: {
          baseCurrency: {
            code: 'USD',
          },
        },
        psql: {
          CustomLocalisation: {
            findAll: vi.fn(),
            associations: {
              localisation: {
                target: {},
              },
            },
            rawAttributes: {
              category: {},
            },
          },
        },
      },
      entity: {
        id: 'test-entity-id',
      },
    };
    mockReply = {};
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should append category query, generate cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      expect(mockRequest.query['filter_localisation.category_eq']).toBe('currency');

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ORGANISATION}_setting_currency_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      // Call serviceFn to verify cache fallback
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should call LocalisationService.index if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((_redis, _key, cb) => cb());

      await index(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(LocalisationService.index).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('view', () => {
    it('should generate cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'currency_view_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await view(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ORGANISATION}_setting_currency_view`,
        mockRequest,
      );

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.VIEW,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });
    it('should call LocalisationService.view on cache miss', async () => {
      generateCacheKey.mockReturnValue('currency_view_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await view(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(LocalisationService.view).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with LocalisationService.updateStatus', async () => {
      await updateStatus(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: LocalisationService.updateStatus,
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: 'updateStatus',
      });
    });
  });
});
