import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  create,
  getThemes,
  index,
  options,
  organisationDropdown,
  updateBasicInformation,
  updateContactInformation,
  updateOrganisationLink,
  updateStatus,
  updateThemes,
  view,
} from '#src/modules/organisation/handlers/organisation.handler.js';
import { OrganisationService } from '#src/modules/organisation/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/organisation/services/index.js');

describe('Organisation Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      server: {
        redis: {},
      },
    };
    mockReply = {};

    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ORGANISATION}_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should call OrganisationService.index if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await index(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(OrganisationService.index).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('view', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await view(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ORGANISATION}_${CoreConstant.MODULE_METHODS.VIEW}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.VIEW,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should call OrganisationService.view if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await view(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(OrganisationService.view).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await create(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: OrganisationService.create,
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.CREATE,
      });
    });
  });

  describe('updateBasicInformation', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateBasicInformation(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: OrganisationService.updateBasicInformation,
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.UPDATE,
      });
    });
  });

  describe('updateContactInformation', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateContactInformation(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: OrganisationService.updateContactInformation,
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.UPDATE,
      });
    });
  });

  describe('updateOrganisationLink', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateOrganisationLink(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: OrganisationService.updateOrganisationLink,
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.UPDATE,
      });
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateStatus(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: OrganisationService.updateStatus,
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.UPDATE_STATUS,
      });
    });
  });

  describe('organisationDropdown', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await organisationDropdown(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: OrganisationService.organisationDropdown,
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.OPTION,
      });
    });
  });

  describe('options', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await options(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: OrganisationService.options,
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.OPTION,
      });
    });
  });

  describe('getThemes', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await getThemes(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: OrganisationService.getThemes,
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.VIEW_THEMES,
      });
    });
  });

  describe('updateThemes', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateThemes(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: OrganisationService.updateThemes,
        module: CoreConstant.MODULE_NAMES.ORGANISATION,
        method: CoreConstant.MODULE_METHODS.UPDATE_THEMES,
      });
    });
  });
});
