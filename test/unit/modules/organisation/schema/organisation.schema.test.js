import { describe, expect, it } from 'vitest';

import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { EntityConstant } from '#src/modules/core/constants/index.js';
import * as organisationSchema from '#src/modules/organisation/schemas/organisation.schema.js';

const { ORGANISATION } = MODULE_NAMES;

describe('Organisation Schema', () => {
  describe('index schema', () => {
    it('should have correct tags and summary', () => {
      expect(organisationSchema.index.tags).toEqual([
        'BO / Organisation Management / Organisation',
      ]);
      expect(organisationSchema.index.summary).toBe(`Get a list of ${ORGANISATION}`);
    });

    it('should have correct querystring properties', () => {
      const { properties } = organisationSchema.index.querystring;
      expect(properties).toHaveProperty('filter_id_eq');
      expect(properties).toHaveProperty('filter_code_eq');
      expect(properties).toHaveProperty('filter_name_eq');
      expect(properties).toHaveProperty('filter_prefix_eq');
      expect(properties).toHaveProperty('filter_accessId_eq');
      expect(properties).toHaveProperty('filter_status_in');
      expect(properties).toHaveProperty('sortBy');
    });

    it('should have correct response schema', () => {
      const { properties } = organisationSchema.index.response[200];
      expect(properties).toHaveProperty('message');
      expect(properties).toHaveProperty('data');
      expect(properties).toHaveProperty('meta');
    });
  });

  describe('view schema', () => {
    it('should have correct tags and summary', () => {
      expect(organisationSchema.view.tags).toEqual(['BO / Organisation Management / Organisation']);
      expect(organisationSchema.view.summary).toBe(`View a ${ORGANISATION}`);
    });

    it('should have params', () => {
      expect(organisationSchema.view).toHaveProperty('params');
    });

    it('should have response schema', () => {
      expect(organisationSchema.view).toHaveProperty('response');
    });
  });

  describe('create schema', () => {
    it('should have correct tags and summary', () => {
      expect(organisationSchema.create.tags).toEqual([
        'BO / Organisation Management / Organisation',
      ]);
      expect(organisationSchema.create.summary).toBe(`Create a ${ORGANISATION}`);
    });

    it('should have correct body schema', () => {
      const { properties, required } = organisationSchema.create.body;
      expect(properties).toHaveProperty('name');
      expect(properties).toHaveProperty('prefix');
      expect(properties).toHaveProperty('code');
      expect(properties).toHaveProperty('currencyIds');
      expect(properties).toHaveProperty('description');
      expect(properties).toHaveProperty('phone');
      expect(properties).toHaveProperty('email');
      expect(properties).toHaveProperty('address');
      expect(properties).toHaveProperty('emailDomain');
      expect(properties).toHaveProperty('logoDesktop');
      expect(properties).toHaveProperty('logoMobile');
      expect(properties).toHaveProperty('favicon');
      expect(properties).toHaveProperty('language');
      expect(properties).toHaveProperty('region');
      expect(properties).toHaveProperty('timeFormat');
      expect(properties).toHaveProperty('dateFormat');

      expect(required).toContain('prefix');
      expect(required).toContain('code');
      expect(required).toContain('name');
      expect(required).toContain('currencyIds');
      expect(required).toContain('region');
      expect(required).toContain('language');
      expect(required).toContain('timeFormat');
      expect(required).toContain('dateFormat');
    });

    it('should have response schema', () => {
      expect(organisationSchema.create).toHaveProperty('response');
    });
  });

  describe('updateBasicInformation schema', () => {
    it('should have correct tags and summary', () => {
      expect(organisationSchema.updateBasicInformation.tags).toEqual([
        'BO / Organisation Management / Organisation',
      ]);
      expect(organisationSchema.updateBasicInformation.summary).toBe(
        `Update basic information of a ${ORGANISATION}`,
      );
    });

    it('should have params', () => {
      expect(organisationSchema.updateBasicInformation).toHaveProperty('params');
    });

    it('should have correct body schema', () => {
      const { properties, required } = organisationSchema.updateBasicInformation.body;
      expect(properties).toHaveProperty('description');
      expect(properties).toHaveProperty('currencyIds');

      expect(required).toContain('currencyIds');
    });

    it('should have correct currencyIds array schema', () => {
      const currencyIds = organisationSchema.updateBasicInformation.body.properties.currencyIds;
      expect(currencyIds.type).toBe('array');
      expect(currencyIds.items.type).toBe('string');
      expect(currencyIds.items.format).toBe('uuid');
    });

    it('should have response schema', () => {
      expect(organisationSchema.updateBasicInformation).toHaveProperty('response');
    });
  });

  describe('updateContactInformation schema', () => {
    it('should have correct tags and summary', () => {
      expect(organisationSchema.updateContactInformation.tags).toEqual([
        'BO / Organisation Management / Organisation',
      ]);
      expect(organisationSchema.updateContactInformation.summary).toBe(
        `Update contact information of a ${ORGANISATION}`,
      );
    });

    it('should have params', () => {
      expect(organisationSchema.updateContactInformation).toHaveProperty('params');
    });

    it('should have correct body schema', () => {
      const { properties } = organisationSchema.updateContactInformation.body;
      expect(properties).toHaveProperty('phone');
      expect(properties).toHaveProperty('email');
      expect(properties).toHaveProperty('address');
    });

    it('should have correct address object schema', () => {
      const address = organisationSchema.updateContactInformation.body.properties.address;
      expect(address.type).toBe('object');

      const addressProps = address.properties;
      expect(addressProps).toHaveProperty('street');
      expect(addressProps).toHaveProperty('city');
      expect(addressProps).toHaveProperty('state');
      expect(addressProps).toHaveProperty('postalCode');
      expect(addressProps).toHaveProperty('country');
    });

    it('should have response schema', () => {
      expect(organisationSchema.updateContactInformation).toHaveProperty('response');
    });
  });

  describe('updateOrganisationLink schema', () => {
    it('should have correct tags and summary', () => {
      expect(organisationSchema.updateOrganisationLink.tags).toEqual([
        'BO / Organisation Management / Organisation',
      ]);
      expect(organisationSchema.updateOrganisationLink.summary).toBe(
        `Update email domain of a ${ORGANISATION}`,
      );
    });

    it('should have params', () => {
      expect(organisationSchema.updateOrganisationLink).toHaveProperty('params');
    });

    it('should have correct body schema', () => {
      const { properties, required } = organisationSchema.updateOrganisationLink.body;
      expect(properties).toHaveProperty('emailDomain');
      expect(required).toContain('emailDomain');
    });

    it('should have response schema', () => {
      expect(organisationSchema.updateOrganisationLink).toHaveProperty('response');
    });
  });

  describe('updateStatus schema', () => {
    it('should have correct tags and summary', () => {
      expect(organisationSchema.updateStatus.tags).toEqual([
        'BO / Organisation Management / Organisation',
      ]);
      expect(organisationSchema.updateStatus.summary).toBe(`Update a ${ORGANISATION} status`);
    });

    it('should have params', () => {
      expect(organisationSchema.updateStatus).toHaveProperty('params');
    });

    it('should have correct body schema', () => {
      const { properties, required } = organisationSchema.updateStatus.body;
      expect(properties).toHaveProperty('status');
      expect(required).toContain('status');
    });

    it('should have correct status enum', () => {
      const { status } = organisationSchema.updateStatus.body.properties;
      expect(status.enum).toEqual(Object.values(EntityConstant.ENTITY_STATUSES));
    });

    it('should have response schema', () => {
      expect(organisationSchema.updateStatus).toHaveProperty('response');
    });
  });

  describe('dropdown schema', () => {
    it('should have correct tags and summary', () => {
      expect(organisationSchema.organisationDropdown.tags).toEqual([
        'BO / Organisation Management / Organisation',
      ]);
      expect(organisationSchema.organisationDropdown.summary).toBe(
        `Get a dropdown list of ${ORGANISATION}`,
      );
    });

    it('should have correct response schema', () => {
      const { properties } = organisationSchema.organisationDropdown.response[200];
      expect(properties).toHaveProperty('message');
      expect(properties).toHaveProperty('data');

      const dataItems = organisationSchema.organisationDropdown.response[200].properties.data.items;
      expect(dataItems.properties).toHaveProperty('code');
      expect(dataItems.properties).toHaveProperty('name');
      expect(dataItems.properties).toHaveProperty('accessId');
    });
  });
  describe('dropdown schema', () => {
    it('should have correct tags and summary', () => {
      expect(organisationSchema.organisationDropdown.tags).toEqual([
        'BO / Organisation Management / Organisation',
      ]);
      expect(organisationSchema.organisationDropdown.summary).toBe(
        `Get a dropdown list of ${ORGANISATION}`,
      );
    });

    it('should have correct response schema', () => {
      const { properties } = organisationSchema.organisationDropdown.response[200];
      expect(properties).toHaveProperty('message');
      expect(properties).toHaveProperty('data');

      const dataItems = organisationSchema.organisationDropdown.response[200].properties.data.items;
      expect(dataItems.properties).toHaveProperty('code');
      expect(dataItems.properties).toHaveProperty('name');
      expect(dataItems.properties).toHaveProperty('accessId');
    });
  });

  describe('settings dropdown schema', () => {
    it('should have correct tags and summary', () => {
      expect(organisationSchema.options.tags).toEqual([
        'BO / Organisation Management / Organisation',
      ]);
      expect(organisationSchema.options.summary).toBe(
        `Get a options list of ${ORGANISATION} settings`,
      );
    });

    it('should have correct response schema structure', () => {
      const { properties } = organisationSchema.options.response[200];
      expect(properties).toHaveProperty('message');
      expect(properties).toHaveProperty('data');

      const dataProps = properties.data.properties;
      expect(dataProps).toHaveProperty('language');
      expect(dataProps).toHaveProperty('region');
      expect(dataProps).toHaveProperty('currency');
    });

    it('should have correct language array schema', () => {
      const languageArray =
        organisationSchema.options.response[200].properties.data.properties.language;
      expect(languageArray.type).toBe('array');

      const languageItem = languageArray.items;
      expect(languageItem.properties).toHaveProperty('id');
      expect(languageItem.properties).toHaveProperty('name');
      expect(languageItem.properties.id.format).toBe('uuid');
    });

    it('should have correct region array schema', () => {
      const regionArray =
        organisationSchema.options.response[200].properties.data.properties.region;
      expect(regionArray.type).toBe('array');

      const regionItem = regionArray.items;
      expect(regionItem.properties).toHaveProperty('id');
      expect(regionItem.properties).toHaveProperty('name');
      expect(regionItem.properties.id.format).toBe('uuid');
    });

    it('should have correct currency array schema', () => {
      const currencyArray =
        organisationSchema.options.response[200].properties.data.properties.currency;
      expect(currencyArray.type).toBe('array');

      const currencyItem = currencyArray.items;
      expect(currencyItem.properties).toHaveProperty('id');
      expect(currencyItem.properties).toHaveProperty('name');
      expect(currencyItem.properties.id.format).toBe('uuid');
    });
  });
});
