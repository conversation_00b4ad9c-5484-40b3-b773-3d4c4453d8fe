import { describe, expect, it } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import * as CurrencySchema from '#src/modules/organisation/schemas/currency.schema.js';

describe('Currency Schema', () => {
  describe('index schema', () => {
    it('should have expected querystring properties', () => {
      const query = CurrencySchema.index.querystring.properties;

      expect(query).toHaveProperty('filter_localisation.code_in');
      expect(query).toHaveProperty('filter_status_in');
      expect(query).toHaveProperty('sortBy');

      expect(query.sortBy).toMatchObject({
        oneOf: expect.any(Array),
        default: 'localisation.name:asc',
      });
    });

    it('should define correct 200 response structure', () => {
      const res200 = CurrencySchema.index.response[200];
      expect(res200).toBeDefined();
      expect(res200.type).toBe('object');
      expect(res200.properties).toHaveProperty('message');
      expect(res200.properties).toHaveProperty('data');
      expect(res200.properties).toHaveProperty('meta');

      expect(res200.properties.data.type).toBe('array');
      expect(res200.properties.data.items.type).toBe('object');

      const currencyFields = res200.properties.data.items.properties;
      expect(currencyFields).toHaveProperty('id');
      expect(currencyFields).toHaveProperty('name');
      expect(currencyFields).toHaveProperty('code');
      expect(currencyFields).toHaveProperty('status');
    });
  });

  describe('view schema', () => {
    it('should define UUID params and view response', () => {
      const schema = CurrencySchema.view;
      const params = schema.params;

      expect(params).toHaveProperty('properties');
      expect(params.properties).toHaveProperty('id');
      expect(params.properties.id.format).toBe('uuid');

      const dataProps = schema.response[200].properties.data.properties;

      expect(dataProps).toHaveProperty('id');
      expect(dataProps).toHaveProperty('exchangeRate');
      expect(dataProps.exchangeRate.type).toBe('number');
    });
  });

  describe('updateStatus schema', () => {
    it('should require status in body', () => {
      const body = CurrencySchema.updateStatus.body;
      expect(body.type).toBe('object');
      expect(body.properties).toHaveProperty('status');
      expect(body.required).toContain('status');

      const statusEnum = Object.values(CoreConstant.COMMON_STATUSES);
      expect(body.properties.status.enum).toEqual(statusEnum);
    });

    it('should define UUID in params', () => {
      const params = CurrencySchema.updateStatus.params;

      expect(params).toHaveProperty('type', 'object');
      expect(params).toHaveProperty('properties');
      expect(params.properties).toHaveProperty('id');
      expect(params.properties.id).toMatchObject({
        type: 'string',
        format: 'uuid',
      });
    });

    it('should have update response defined', () => {
      expect(CurrencySchema.updateStatus.response).toBeDefined();
    });
  });
});
