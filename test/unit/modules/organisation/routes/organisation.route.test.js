import { beforeEach, describe, expect, it, vi } from 'vitest';

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OrganisationHandler } from '#src/modules/organisation/handlers/index.js';
import OrganisationRoute from '#src/modules/organisation/routes/organisation.route.js';
import { CurrencySchema, OrganisationSchema } from '#src/modules/organisation/schemas/index.js';

vi.mock('#src/modules/organisation/handlers/index.js', () => ({
  OrganisationHandler: {
    index: vi.fn(),
    view: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    updateStatus: vi.fn(),
    dropdown: vi.fn(),
  },
  CurrencyHandler: {
    index: vi.fn(),
    view: vi.fn(),
    updateStatus: vi.fn(),
  },
}));

vi.mock('#src/modules/organisation/schemas/index.js', () => ({
  OrganisationSchema: {
    index: { schema: 'index' },
    view: { schema: 'view' },
    create: { schema: 'create' },
    update: { schema: 'update' },
    updateStatus: { schema: 'updateStatus' },
    dropdown: { schema: 'dropdown' },
  },
  CurrencySchema: {
    index: { schema: 'currencyIndex' },
    view: { schema: 'currencyView' },
    updateStatus: { schema: 'currencyUpdateStatus' },
  },
}));

describe('Organisation Route', () => {
  let fastify;

  beforeEach(() => {
    fastify = {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      patch: vi.fn(),
    };

    vi.resetAllMocks();
  });

  it('should register all routes correctly', async () => {
    await OrganisationRoute(fastify, {});

    const commonAccessConfig = {
      user: true,
      member: false,
      webhook: true,
      public: false,
      ipWhitelist: ['127.0.0.1'],
    };

    expect(fastify.get).toHaveBeenCalledWith('/', {
      schema: OrganisationSchema.index,
      config: { name: 'organisation.list', access: commonAccessConfig },
      handler: OrganisationHandler.index,
    });

    expect(fastify.get).toHaveBeenCalledWith('/:id', {
      schema: OrganisationSchema.view,
      config: { name: 'organisation.view', access: commonAccessConfig },
      handler: OrganisationHandler.view,
    });

    expect(fastify.post).toHaveBeenCalledWith('/', {
      schema: OrganisationSchema.create,
      config: {
        name: 'organisation.create',
        access: commonAccessConfig,
        sanitiseHtml: {
          ignoreFields: [''],
          options: {
            ADD_ALLOWED_TAGS: ['a'],
            ADD_ALLOWED_ATTR: ['a'],
          },
        },
      },
      handler: OrganisationHandler.create,
    });

    expect(fastify.patch).toHaveBeenCalledWith('/:id/basic-information', {
      schema: OrganisationSchema.updateBasicInformation,
      config: { name: 'organisation.updateBasicInformation', access: commonAccessConfig },
      handler: OrganisationHandler.updateBasicInformation,
    });

    expect(fastify.patch).toHaveBeenCalledWith('/:id/contact-information', {
      schema: OrganisationSchema.updateContactInformation,
      config: { name: 'organisation.updateContactInformation', access: commonAccessConfig },
      handler: OrganisationHandler.updateContactInformation,
    });

    expect(fastify.patch).toHaveBeenCalledWith('/:id/organisation-link', {
      schema: OrganisationSchema.updateOrganisationLink,
      config: { name: 'organisation.updateOrganisationLink', access: commonAccessConfig },
      handler: OrganisationHandler.updateOrganisationLink,
    });

    expect(fastify.patch).toHaveBeenCalledWith('/:id/status', {
      schema: OrganisationSchema.updateStatus,
      config: { name: 'organisation.updateStatus', access: commonAccessConfig },
      handler: OrganisationHandler.updateStatus,
    });

    expect(fastify.get).toHaveBeenCalledWith('/options', {
      schema: OrganisationSchema.options,
      config: { name: 'organisation.options', access: commonAccessConfig },
      handler: OrganisationHandler.options,
    });
    expect(fastify.get).toHaveBeenCalledWith('/setting/currency', {
      schema: CurrencySchema.index,
      config: { name: 'organisation.setting.currency.index', access: commonAccessConfig },
      handler: CurrencyHandler.index,
    });

    expect(fastify.get).toHaveBeenCalledWith('/setting/currency/:id', {
      schema: CurrencySchema.view,
      config: { name: 'organisation.setting.currency.view', access: commonAccessConfig },
      handler: CurrencyHandler.view,
    });

    expect(fastify.patch).toHaveBeenCalledWith('/setting/currency/:id/status', {
      schema: CurrencySchema.updateStatus,
      config: { name: 'organisation.setting.currency.updateStatus', access: commonAccessConfig },
      handler: CurrencyHandler.updateStatus,
    });

    expect(fastify.get).toHaveBeenCalledTimes(7);
    expect(fastify.post).toHaveBeenCalledTimes(1);
    expect(fastify.patch).toHaveBeenCalledTimes(5);
  });

  it('should use the correct prefix for ID-based routes', async () => {
    await OrganisationRoute(fastify, {});

    const idPrefix = '/:id';

    expect(fastify.get).toHaveBeenCalledWith(idPrefix, expect.any(Object));
    expect(fastify.patch).toHaveBeenCalledWith(`${idPrefix}/basic-information`, expect.any(Object));
    expect(fastify.patch).toHaveBeenCalledWith(
      `${idPrefix}/contact-information`,
      expect.any(Object),
    );
    expect(fastify.patch).toHaveBeenCalledWith(`${idPrefix}/organisation-link`, expect.any(Object));
    expect(fastify.patch).toHaveBeenCalledWith(`${idPrefix}/status`, expect.any(Object));
  });

  it('should configure sanitiseHtml only for the create route', async () => {
    await OrganisationRoute(fastify, {});

    const postCallConfig = fastify.post.mock.calls[0][1].config;
    const patchCallConfig = fastify.patch.mock.calls[0][1].config;

    expect(postCallConfig).toHaveProperty('sanitiseHtml');
    expect(patchCallConfig).not.toHaveProperty('sanitiseHtml');

    expect(postCallConfig.sanitiseHtml).toEqual({
      ignoreFields: [''],
      options: {
        ADD_ALLOWED_TAGS: ['a'],
        ADD_ALLOWED_ATTR: ['a'],
      },
    });
  });
});
