import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreError } from '#src/modules/core/errors/index.js';
import * as validation from '#src/modules/core/validations/user.validation.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import { UserRepository } from '#src/modules/user/repository/index.js';

vi.mock('#src/modules/user/repository/index.js');

describe('User Validation', () => {
  let mockServer;

  beforeEach(() => {
    mockServer = {};
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('validateEmail', () => {
    it('should pass if email is unique', async () => {
      UserRepository.findUser.mockResolvedValue(null);
      const email = await validation.validateEmail(mockServer, '<EMAIL>');
      expect(email).toBe('<EMAIL>');
    });

    it('should pass if email belongs to same user', async () => {
      UserRepository.findUser.mockResolvedValue({ id: 'same-user-id' });
      const email = await validation.validateEmail(mockServer, '<EMAIL>', 'same-user-id');
      expect(email).toBe('<EMAIL>');
    });

    it('should throw error if email is taken by another user', async () => {
      UserRepository.findUser.mockResolvedValue({ id: 'other-user-id' });
      await expect(
        validation.validateEmail(mockServer, '<EMAIL>', 'different-id'),
      ).rejects.toThrow(
        CoreError.alreadyExists({ attribute: 'common.label.email', value: '<EMAIL>' }),
      );
    });
  });

  describe('validateUsername', () => {
    it('should pass if username is unique for normal user', async () => {
      UserRepository.findUser.mockResolvedValue(null);

      const result = await validation.validateUsername(mockServer, 'johndoe', 'normal', null);

      expect(result).toBe('johndoe');
      expect(UserRepository.findUser).toHaveBeenCalledWith(mockServer, {
        filter_username_eq: 'johndoe',
      });
    });

    it('should format and validate sub-account username', async () => {
      UserRepository.findUser.mockResolvedValue(null);
      const parentUser = { username: 'parentUser' };

      const result = await validation.validateUsername(
        mockServer,
        'child',
        UserConstant.USER_TYPES.SUB_ACCOUNT,
        parentUser,
      );

      expect(result).toBe('parentUser_child'); // preserve case if not normalized
      expect(UserRepository.findUser).toHaveBeenCalledWith(mockServer, {
        filter_username_eq: 'parentUser_child',
      });
    });
    it('should throw error if formatted sub-account username exists', async () => {
      UserRepository.findUser.mockResolvedValue({ id: 'conflict-id' });
      const parentUser = { username: 'parentUser' };

      await expect(
        validation.validateUsername(
          mockServer,
          'child',
          UserConstant.USER_TYPES.SUB_ACCOUNT,
          parentUser,
        ),
      ).rejects.toThrowError('error.validation.sentence.exists');
    });
  });
});
