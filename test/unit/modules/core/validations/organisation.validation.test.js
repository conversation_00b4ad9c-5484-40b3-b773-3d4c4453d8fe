import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreError } from '#src/modules/core/errors/index.js';
import { EntityRepository } from '#src/modules/core/repository/index.js';
import { validateOrganisationFields } from '#src/modules/core/validations/organisation.validation.js';

vi.mock('#src/modules/core/repository/index.js', () => ({
  EntityRepository: {
    count: vi.fn(),
  },
}));

vi.mock('#src/modules/core/errors/index.js', () => ({
  CoreError: {
    alreadyExists: vi.fn(),
  },
}));

vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    HIERARCHY: {
      ORGANISATION: 'ORGANISATION',
    },
  },
}));

describe('Organisation Validation', () => {
  let mockServer;

  beforeEach(() => {
    vi.clearAllMocks();

    mockServer = {
      psql: {
        connection: {
          Sequelize: {
            Op: {
              ne: Symbol('ne'),
            },
          },
        },
      },
    };
  });

  describe('validateOrganisationFields', () => {
    it('should validate prefix and code successfully when both are unique', async () => {
      EntityRepository.count.mockResolvedValueOnce(0).mockResolvedValueOnce(0);

      const result = await validateOrganisationFields(mockServer, {
        prefix: 'TEST',
        code: 'TESTORG',
      });

      expect(EntityRepository.count).toHaveBeenCalledTimes(2);
      expect(EntityRepository.count).toHaveBeenNthCalledWith(1, mockServer, {
        prefix: 'TEST',
        hierarchy: 'ORGANISATION',
      });
      expect(EntityRepository.count).toHaveBeenNthCalledWith(2, mockServer, {
        code: 'TESTORG',
        hierarchy: 'ORGANISATION',
      });
      expect(result).toBe(true);
    });

    it('should throw error when prefix is not unique', async () => {
      EntityRepository.count.mockResolvedValueOnce(1);

      await expect(
        validateOrganisationFields(mockServer, {
          prefix: 'EXISTING',
          code: 'TESTORG',
        }),
      ).rejects.toThrow('error.validation.sentence.exists');

      expect(EntityRepository.count).toHaveBeenCalledTimes(1);
      expect(CoreError.alreadyExists).toHaveBeenCalledWith({
        attribute: 'common.label.prefix',
        value: 'EXISTING',
      });
    });

    it('should throw error when code is not unique', async () => {
      EntityRepository.count.mockResolvedValueOnce(0).mockResolvedValueOnce(1);

      await expect(
        validateOrganisationFields(mockServer, {
          prefix: 'TEST',
          code: 'EXISTING',
        }),
      ).rejects.toThrow('error.validation.sentence.exists');

      expect(EntityRepository.count).toHaveBeenCalledTimes(2);
      expect(CoreError.alreadyExists).toHaveBeenCalledWith({
        attribute: 'common.label.code',
        value: 'EXISTING',
      });
    });

    it('should exclude the provided ID when validating prefix', async () => {
      EntityRepository.count.mockResolvedValueOnce(0).mockResolvedValueOnce(0);

      await validateOrganisationFields(mockServer, {
        prefix: 'TEST',
        code: 'TESTORG',
        excludeId: 'org-123',
      });

      expect(EntityRepository.count).toHaveBeenNthCalledWith(1, mockServer, {
        prefix: 'TEST',
        hierarchy: 'ORGANISATION',
        id: { [mockServer.psql.connection.Sequelize.Op.ne]: 'org-123' },
      });
    });

    it('should exclude the provided ID when validating code', async () => {
      EntityRepository.count.mockResolvedValueOnce(0).mockResolvedValueOnce(0);

      await validateOrganisationFields(mockServer, {
        prefix: 'TEST',
        code: 'TESTORG',
        excludeId: 'org-123',
      });

      expect(EntityRepository.count).toHaveBeenNthCalledWith(2, mockServer, {
        code: 'TESTORG',
        hierarchy: 'ORGANISATION',
        id: { [mockServer.psql.connection.Sequelize.Op.ne]: 'org-123' },
      });
    });

    it('should skip prefix validation when prefix is not provided', async () => {
      EntityRepository.count.mockResolvedValueOnce(0);

      await validateOrganisationFields(mockServer, {
        code: 'TESTORG',
      });

      expect(EntityRepository.count).toHaveBeenCalledTimes(1);
      expect(EntityRepository.count).toHaveBeenCalledWith(mockServer, {
        code: 'TESTORG',
        hierarchy: 'ORGANISATION',
      });
    });

    it('should skip code validation when code is not provided', async () => {
      EntityRepository.count.mockResolvedValueOnce(0);

      await validateOrganisationFields(mockServer, {
        prefix: 'TEST',
      });

      expect(EntityRepository.count).toHaveBeenCalledTimes(1);
      expect(EntityRepository.count).toHaveBeenCalledWith(mockServer, {
        prefix: 'TEST',
        hierarchy: 'ORGANISATION',
      });
    });
  });
});
