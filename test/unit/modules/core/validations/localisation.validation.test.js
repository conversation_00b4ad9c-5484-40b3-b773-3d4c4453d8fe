import { beforeEach, describe, expect, it, vi } from 'vitest';

import { validateLocalisation } from '#src/modules/core/validations/localisation.validation.js';
import { LocalisationError } from '#src/modules/setting/errors/index.js';
import { LocalisationRepository } from '#src/modules/setting/repository/index.js';

vi.mock('#src/modules/setting/repository/index.js', () => ({
  LocalisationRepository: {
    count: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/errors/index.js', () => ({
  LocalisationError: {
    invalidLocalisation: vi.fn((category) => new Error(`Invalid ${category} localisation`)),
  },
}));

describe('Localisation Validation', () => {
  let mockServer;

  beforeEach(() => {
    vi.clearAllMocks();

    mockServer = {
      psql: {
        connection: {
          Sequelize: {
            Op: {
              in: Symbol('in'),
            },
          },
        },
      },
    };
  });

  describe('validateLocalisation', () => {
    it('should validate a single localisation ID successfully', async () => {
      LocalisationRepository.count.mockResolvedValue(1);

      const result = await validateLocalisation(mockServer, 'loc-123', 'CURRENCY');

      expect(LocalisationRepository.count).toHaveBeenCalledWith(mockServer, {
        id: { [mockServer.psql.connection.Sequelize.Op.in]: ['loc-123'] },
        category: 'CURRENCY',
      });
      expect(result).toBe(true);
    });

    it('should validate multiple localisation IDs successfully', async () => {
      LocalisationRepository.count.mockResolvedValue(3);

      const result = await validateLocalisation(
        mockServer,
        ['loc-123', 'loc-456', 'loc-789'],
        'CURRENCY',
      );

      expect(LocalisationRepository.count).toHaveBeenCalledWith(mockServer, {
        id: { [mockServer.psql.connection.Sequelize.Op.in]: ['loc-123', 'loc-456', 'loc-789'] },
        category: 'CURRENCY',
      });
      expect(result).toBe(true);
    });

    it('should throw error when a single localisation ID is invalid', async () => {
      LocalisationRepository.count.mockResolvedValue(0);

      await expect(validateLocalisation(mockServer, 'invalid-loc', 'LANGUAGE')).rejects.toThrow(
        'Invalid LANGUAGE localisation',
      );

      expect(LocalisationRepository.count).toHaveBeenCalledWith(mockServer, {
        id: { [mockServer.psql.connection.Sequelize.Op.in]: ['invalid-loc'] },
        category: 'LANGUAGE',
      });
      expect(LocalisationError.invalidLocalisation).toHaveBeenCalledWith('LANGUAGE');
    });

    it('should throw error when some of multiple localisation IDs are invalid', async () => {
      LocalisationRepository.count.mockResolvedValue(2);

      await expect(
        validateLocalisation(mockServer, ['loc-123', 'loc-456', 'invalid-loc'], 'REGION'),
      ).rejects.toThrow('Invalid REGION localisation');

      expect(LocalisationRepository.count).toHaveBeenCalledWith(mockServer, {
        id: { [mockServer.psql.connection.Sequelize.Op.in]: ['loc-123', 'loc-456', 'invalid-loc'] },
        category: 'REGION',
      });
      expect(LocalisationError.invalidLocalisation).toHaveBeenCalledWith('REGION');
    });

    it('should handle different localisation categories', async () => {
      LocalisationRepository.count.mockResolvedValue(1);
      await validateLocalisation(mockServer, 'loc-123', 'CURRENCY');
      expect(LocalisationRepository.count).toHaveBeenCalledWith(mockServer, {
        id: { [mockServer.psql.connection.Sequelize.Op.in]: ['loc-123'] },
        category: 'CURRENCY',
      });
      LocalisationRepository.count.mockResolvedValue(1);
      await validateLocalisation(mockServer, 'loc-456', 'LANGUAGE');
      expect(LocalisationRepository.count).toHaveBeenCalledWith(mockServer, {
        id: { [mockServer.psql.connection.Sequelize.Op.in]: ['loc-456'] },
        category: 'LANGUAGE',
      });
      LocalisationRepository.count.mockResolvedValue(1);
      await validateLocalisation(mockServer, 'loc-789', 'REGION');
      expect(LocalisationRepository.count).toHaveBeenCalledWith(mockServer, {
        id: { [mockServer.psql.connection.Sequelize.Op.in]: ['loc-789'] },
        category: 'REGION',
      });
    });
  });
});
