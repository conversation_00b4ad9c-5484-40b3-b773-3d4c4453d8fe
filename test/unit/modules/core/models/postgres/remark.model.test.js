import { DataTypes, Model, Op, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { auditableMixin } from '#src/mixins/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import RemarkModel from '#src/modules/core/models/postgres/remark.model.js';

// Mock the auditableMixin
vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
}));

describe('Remark Model', () => {
  let mockFastify;
  let Remark;

  beforeEach(() => {
    vi.resetAllMocks();
    // Mock Sequelize methods
    Model.init = vi.fn();
    // Mock Fastify instance
    mockFastify = {
      psql: {
        connection: {},
      },
    };
    // Initialize the model
    Remark = RemarkModel(mockFastify);
  });

  it('should initialize Remark model', () => {
    expect(Remark).toBeDefined();
    expect(Model.init).toHaveBeenCalled();
  });

  describe('Model initialization', () => {
    let fields;
    let initOptions;

    beforeEach(() => {
      [fields, initOptions] = Model.init.mock.calls[0];
    });

    it('should set correct model name and table name', () => {
      expect(initOptions.modelName).toBe('Remark');
      expect(initOptions.tableName).toBe('remarks');
    });

    it('should set underscored, timestamps, and paranoid to true', () => {
      expect(initOptions.underscored).toBe(true);
      expect(initOptions.timestamps).toBe(true);
      expect(initOptions.paranoid).toBe(true);
    });

    it('should set the correct sequelize instance', () => {
      expect(initOptions.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct indexes', () => {
      expect(initOptions.indexes).toEqual([
        {
          fields: ['remarkable_id', 'remarkable_type', 'status'],
          name: 'idx_remarks_poly_status',
        },
        {
          fields: ['type', 'status'],
          name: 'idx_remarks_type_status',
        },
        {
          fields: ['deleted_at'],
          name: 'idx_remarks_deleted_at',
          where: {
            deleted_at: {
              [Op.ne]: null,
            },
          },
        },
      ]);
    });

    it('should define the correct fields', () => {
      expect(fields).toEqual({
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        remarkableId: {
          type: DataTypes.UUID,
          allowNull: false,
          comment: 'UUID of the entity this remark is associated with',
        },
        remarkableType: {
          type: DataTypes.ENUM(Object.values(CoreConstant.REMARKABLE_TYPE)),
          allowNull: false,
          field: 'remarkable_type',
          comment: 'Type of entity this remark is associated with',
        },
        type: {
          type: DataTypes.ENUM(Object.values(CoreConstant.REMARK_TYPE)),
          allowNull: false,
          defaultValue: CoreConstant.REMARK_TYPE.NOTE,
        },
        content: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        status: {
          type: DataTypes.ENUM(Object.values(CoreConstant.REMARK_STATUSES)),
          allowNull: false,
          defaultValue: CoreConstant.REMARK_STATUSES.ACTIVE,
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        updatedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        deletedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      });
    });
  });

  it('should have beforeCreate hook', () => {
    const hooks = Model.init.mock.calls[0][1].hooks;
    expect(hooks.beforeCreate).toBeDefined();
    expect(typeof hooks.beforeCreate).toBe('function');
  });

  it('should call auditableMixin.applyAuditFields', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(Remark);
  });

  describe('beforeCreate hook', () => {
    it('should update existing active remarks to archived', async () => {
      const hooks = Model.init.mock.calls[0][1].hooks;
      const beforeCreateHook = hooks.beforeCreate;

      const mockUpdate = vi.fn().mockResolvedValue([1]);
      Remark.update = mockUpdate;

      const mockRemark = {
        remarkableId: 'uuid-1',
        remarkableType: CoreConstant.REMARKABLE_TYPE.MEMBER,
        type: CoreConstant.REMARK_TYPE.NOTE,
        status: CoreConstant.REMARK_STATUSES.ACTIVE,
      };

      await beforeCreateHook(mockRemark, { transaction: {} });

      expect(mockUpdate).toHaveBeenCalledWith(
        { status: CoreConstant.REMARK_STATUSES.ARCHIVED },
        {
          where: {
            remarkableId: 'uuid-1',
            remarkableType: CoreConstant.REMARKABLE_TYPE.MEMBER,
            type: CoreConstant.REMARK_TYPE.NOTE,
            status: CoreConstant.REMARK_STATUSES.ACTIVE,
            deletedAt: null,
          },
          transaction: {},
        },
      );
    });

    it('should not update remarks if status is not active', async () => {
      const hooks = Model.init.mock.calls[0][1].hooks;
      const beforeCreateHook = hooks.beforeCreate;

      const mockUpdate = vi.fn();
      Remark.update = mockUpdate;

      const mockRemark = {
        status: CoreConstant.REMARK_STATUSES.ARCHIVED,
      };

      await beforeCreateHook(mockRemark, { transaction: {} });

      expect(mockUpdate).not.toHaveBeenCalled();
    });
  });
});
