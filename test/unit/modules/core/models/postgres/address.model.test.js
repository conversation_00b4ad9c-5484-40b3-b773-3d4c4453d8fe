import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import AddressModel from '#src/modules/core/models/postgres/address.model.js';

vi.mock('sequelize', () => {
  const actualSequelize = vi.importActual('sequelize');
  return {
    ...actualSequelize,
    DataTypes: {
      UUID: 'UUID',
      STRING: vi.fn((length) => `STRING(${length})`),
      DATE: 'DATE',
    },
    Model: class MockModel {
      static init() {
        return this;
      }
      static belongsTo() {}
    },
    Sequelize: {
      literal: vi.fn().mockReturnValue('uuid_generate_v1mc()'),
    },
  };
});

describe('Address Model', () => {
  let mockFastify;
  let mockModels;
  let spyInit;
  let spyBelongsTo;

  beforeEach(() => {
    vi.clearAllMocks();

    spyInit = vi.spyOn(Model, 'init');
    spyBelongsTo = vi.spyOn(Model, 'belongsTo');

    mockFastify = {
      psql: {
        connection: {
          define: vi.fn().mockReturnValue({}),
        },
      },
    };

    mockModels = {
      Entity: {},
    };
    AddressModel(mockFastify);
  });

  vi.mock('#src/mixins/index.js', () => ({
    auditableMixin: {
      applyAuditFields: vi.fn(),
    },
    versionedMixin: {
      applyVersioning: vi.fn(),
    },
  }));

  it('should initialize the Address model with correct attributes', () => {
    expect(spyInit).toHaveBeenCalledWith(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        entity_id: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        street: {
          type: DataTypes.STRING(100),
          allowNull: false,
          validate: {
            len: [0, 100],
          },
        },
        city: {
          type: DataTypes.STRING(50),
          allowNull: false,
          validate: {
            len: [0, 50],
          },
        },
        state: {
          type: DataTypes.STRING(50),
          allowNull: false,
          validate: {
            len: [0, 50],
          },
        },
        postalCode: {
          type: DataTypes.STRING(20),
          allowNull: false,
          validate: {
            len: [0, 20],
          },
        },
        country: {
          type: DataTypes.STRING(50),
          allowNull: false,
          validate: {
            len: [0, 50],
          },
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        updatedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      },
      {
        sequelize: mockFastify.psql.connection,
        modelName: 'Address',
        tableName: 'addresses',
        underscored: true,
        timestamps: true,
      },
    );
  });

  it('should define correct associations', () => {
    const Address = AddressModel(mockFastify);

    Address.associate(mockModels);

    expect(spyBelongsTo).toHaveBeenCalledWith(mockModels.Entity, {
      foreignKey: 'entity_id',
      as: 'entity',
    });
  });

  it('should return the Address model', () => {
    const Address = AddressModel(mockFastify);
    expect(Address).toBeDefined();
    expect(Address).toBeInstanceOf(Function);
  });

  it('should use STRING with correct lengths for address fields', () => {
    AddressModel(mockFastify);

    expect(DataTypes.STRING).toHaveBeenCalledWith(100); // street
    expect(DataTypes.STRING).toHaveBeenCalledWith(50); // city
    expect(DataTypes.STRING).toHaveBeenCalledWith(50); // state
    expect(DataTypes.STRING).toHaveBeenCalledWith(20); // postalCode
    expect(DataTypes.STRING).toHaveBeenCalledWith(50); // country
  });

  it('should set all address fields as non-nullable', () => {
    AddressModel(mockFastify);

    const initCall = spyInit.mock.calls[0][0];

    expect(initCall.street.allowNull).toBe(false);
    expect(initCall.city.allowNull).toBe(false);
    expect(initCall.state.allowNull).toBe(false);
    expect(initCall.postalCode.allowNull).toBe(false);
    expect(initCall.country.allowNull).toBe(false);
  });

  it('should set timestamps and underscored to true', () => {
    AddressModel(mockFastify);

    expect(spyInit).toHaveBeenCalledWith(
      expect.any(Object),
      expect.objectContaining({
        timestamps: true,
        underscored: true,
      }),
    );
  });

  it('should set the correct table name', () => {
    AddressModel(mockFastify);

    expect(spyInit).toHaveBeenCalledWith(
      expect.any(Object),
      expect.objectContaining({
        tableName: 'addresses',
      }),
    );
  });
});
