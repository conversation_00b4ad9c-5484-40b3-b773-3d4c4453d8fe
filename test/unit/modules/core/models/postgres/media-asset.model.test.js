import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { auditableMixin } from '#src/mixins/index.js';
import MediaAssetModel from '#src/modules/core/models/postgres/media-asset.model.js';

vi.mock('sequelize', () => {
  const actualSequelize = vi.importActual('sequelize');
  return {
    ...actualSequelize,
    DataTypes: {
      UUID: 'UUID',
      ENUM: vi.fn((...values) => `ENUM(${values.join(',')})`),
      STRING: vi.fn((length) => `STRING(${length})`),
      JSONB: 'JSONB',
      DATE: 'DATE',
    },
    Model: class MockModel {
      static init() {
        return this;
      }
      static belongsTo() {}
    },
    Sequelize: {
      literal: vi.fn().mockReturnValue('uuid_generate_v1mc()'),
    },
  };
});

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
}));

describe('MediaAsset Model', () => {
  let mockFastify;
  let spyInit;
  let MediaAsset;

  beforeEach(() => {
    vi.clearAllMocks();

    spyInit = vi.spyOn(Model, 'init');

    mockFastify = {
      psql: {
        connection: {
          define: vi.fn().mockReturnValue({}),
        },
      },
    };

    MediaAsset = MediaAssetModel(mockFastify);
  });

  it('should initialize the MediaAsset model with correct attributes', () => {
    expect(spyInit).toHaveBeenCalledWith(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        entityId: {
          type: DataTypes.UUID,
          allowNull: false,
          comment: 'The entity ID this media belongs to',
        },
        fileType: {
          type: DataTypes.ENUM('IMAGE', 'AUDIO', 'DOCUMENT'),
          allowNull: false,
          comment: 'Type of media file (image, audio, or document)',
        },
        filePath: {
          type: DataTypes.STRING(2083),
          allowNull: false,
          comment: 'Path to the stored file on disk or cloud storage',
          validate: {
            len: [1, 2083],
          },
        },
        mimeType: {
          type: DataTypes.STRING(255),
          allowNull: false,
          comment: 'MIME type of the file (e.g., image/jpeg, audio/mp3)',
          validate: {
            len: [1, 255],
          },
        },
        fileSize: {
          type: DataTypes.INTEGER,
          allowNull: false,
          comment: 'Size of the file in bytes or formatted size',
          validate: {
            min: 0,
          },
        },
        dimension: {
          type: DataTypes.STRING(20),
          allowNull: true,
          comment: 'Dimensions of the media (e.g., "1920x1080" for images)',
          validate: {
            len: [0, 20],
          },
        },
        thumbnail: {
          type: DataTypes.STRING(2083),
          allowNull: true,
          comment: 'Path to thumbnail version of the media if applicable',
          validate: {
            len: [0, 2083],
          },
        },
        metadata: {
          type: DataTypes.JSONB,
          allowNull: false,
          defaultValue: {},
          comment: 'Additional metadata about the media in JSON format',
        },
        altText: {
          type: DataTypes.STRING(150),
          allowNull: true,
          comment: 'Alternative text for accessibility purposes',
          validate: {
            len: [0, 150],
          },
        },
        visibility: {
          type: DataTypes.ENUM('PUBLIC', 'PRIVATE'),
          allowNull: false,
          comment: 'Whether the media is publicly accessible or private',
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        updatedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      },
      {
        sequelize: mockFastify.psql.connection,
        modelName: 'MediaAsset',
        tableName: 'media_assets',
        underscored: true,
        timestamps: true,
        paranoid: true,
      },
    );
  });

  it('should return the MediaAsset model', () => {
    expect(MediaAsset).toBeDefined();
    expect(MediaAsset).toBeInstanceOf(Function);
  });

  it('should use STRING with correct lengths for various fields', () => {
    expect(DataTypes.STRING).toHaveBeenCalledWith(255);
    expect(DataTypes.STRING).toHaveBeenCalledWith(2083);
    expect(DataTypes.STRING).toHaveBeenCalledWith(20);
    expect(DataTypes.STRING).toHaveBeenCalledWith(255);
    expect(DataTypes.STRING).toHaveBeenCalledWith(150);
  });

  it('should use ENUM with correct values for file_type and visibility', () => {
    expect(DataTypes.ENUM).toHaveBeenCalledWith(['image', 'audio', 'document']);
    expect(DataTypes.ENUM).toHaveBeenCalledWith(['public', 'private']);
  });

  it('should set timestamps, underscored, and paranoid to true', () => {
    expect(spyInit).toHaveBeenCalledWith(
      expect.any(Object),
      expect.objectContaining({
        timestamps: true,
        underscored: true,
        paranoid: true,
      }),
    );
  });

  it('should apply auditable mixin', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(MediaAsset);
  });

  it('should set the correct table name', () => {
    expect(spyInit).toHaveBeenCalledWith(
      expect.any(Object),
      expect.objectContaining({
        tableName: 'media_assets',
      }),
    );
  });

  it('should set the correct model name', () => {
    expect(spyInit).toHaveBeenCalledWith(
      expect.any(Object),
      expect.objectContaining({
        modelName: 'MediaAsset',
      }),
    );
  });

  it('should set required fields as non-nullable', () => {
    const initCall = spyInit.mock.calls[0][0];
    expect(initCall.fileType.allowNull).toBe(false);
    expect(initCall.filePath.allowNull).toBe(false);
    expect(initCall.mimeType.allowNull).toBe(false);
    expect(initCall.fileSize.allowNull).toBe(false);
    expect(initCall.visibility.allowNull).toBe(false);
    expect(initCall.metadata.allowNull).toBe(false);
  });

  it('should set optional fields as nullable', () => {
    const initCall = spyInit.mock.calls[0][0];
    expect(initCall.dimension.allowNull).toBe(true);
    expect(initCall.thumbnail.allowNull).toBe(true);
    expect(initCall.altText.allowNull).toBe(true);
    expect(initCall.createdBy.allowNull).toBe(true);
    expect(initCall.updatedBy.allowNull).toBe(true);
  });

  it('should set default values for metadata', () => {
    const initCall = spyInit.mock.calls[0][0];
    expect(initCall.metadata.defaultValue).toEqual({});
  });

  it('should set validation rules for string fields', () => {
    const initCall = spyInit.mock.calls[0][0];
    expect(initCall.filePath.validate).toEqual({ len: [1, 2083] });
    expect(initCall.mimeType.validate).toEqual({ len: [1, 255] });
    expect(initCall.fileSize.validate).toEqual({ min: 0 });
    expect(initCall.dimension.validate).toEqual({ len: [0, 20] });
    expect(initCall.thumbnail.validate).toEqual({ len: [0, 2083] });
    expect(initCall.altText.validate).toEqual({ len: [0, 150] });
  });
});
