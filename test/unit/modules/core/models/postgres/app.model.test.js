import { Model } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import AppModel from '#src/modules/core/models/postgres/app.model.js';

vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('App Model', () => {
  let mockFastify;
  let App;

  beforeEach(() => {
    vi.resetAllMocks();

    Model.init = vi.fn();
    Model.hasMany = vi.fn();

    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };

    App = AppModel(mockFastify);
  });

  it('should define associations correctly', () => {
    const mockModels = {
      EntityApp: {},
    };

    App.associate(mockModels);

    expect(Model.hasMany).toHaveBeenCalledWith(mockModels.EntityApp, {
      foreignKey: 'appId',
      as: 'entityApp',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  describe('Model initialization', () => {
    let fields, options;

    beforeEach(() => {
      [fields, options] = Model.init.mock.calls[0];
    });

    it('should set correct model and table names', () => {
      expect(options.modelName).toBe('App');
      expect(options.tableName).toBe('apps');
      expect(options.underscored).toBe(true);
      expect(options.timestamps).toBe(true);
    });

    it('should define the correct fields with validations', () => {
      expect(fields).toHaveProperty('id');
      expect(fields.id.type.key).toBe('UUID');

      expect(fields).toHaveProperty('icon');
      expect(fields.icon.allowNull).toBe(true);

      expect(fields).toHaveProperty('category');
      expect(fields.category.allowNull).toBe(false);
      expect(fields.category.validate).toMatchObject({
        len: [1, 50],
        notEmpty: true,
      });

      expect(fields).toHaveProperty('name');
      expect(fields.name.allowNull).toBe(false);
      expect(fields.name.validate).toMatchObject({
        len: [1, 100],
        notEmpty: true,
      });

      expect(fields).toHaveProperty('description');
      expect(fields.description.allowNull).toBe(false);

      expect(fields).toHaveProperty('apiUrl');
      expect(fields.apiUrl.allowNull).toBe(true);

      expect(fields).toHaveProperty('testFeature');
      expect(fields.testFeature.allowNull).toBe(false);
      expect(fields.testFeature.defaultValue).toBe(false);

      expect(fields).toHaveProperty('createdBy');
      expect(fields).toHaveProperty('updatedBy');
    });

    it('should define unique index on name and category', () => {
      expect(options.indexes).toEqual([
        {
          unique: true,
          fields: ['name', 'category'],
          name: 'uniq_apps_name_category',
        },
      ]);
    });

    it('should apply auditable mixin', async () => {
      const { auditableMixin, __mocks } = await import('#src/mixins/index.js');
      expect(auditableMixin.applyAuditFields).toBeDefined();

      auditableMixin.applyAuditFields(App);
      expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(App);
    });
  });
});
