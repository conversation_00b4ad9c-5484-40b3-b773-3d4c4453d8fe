import { DataTypes, Model } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { auditableMixin } from '#src/mixins/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import ModuleModel from '#src/modules/core/models/postgres/module.model.js';
import { DepartmentConstant } from '#src/modules/user/constants/index.js';

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
}));

const { HIERARCHY } = CoreConstant;
const { NAVIGATION_TYPES } = DepartmentConstant;

describe('Module Model', () => {
  let mockFastify;
  let Module;

  beforeEach(() => {
    vi.resetAllMocks();
    // Mock Sequelize methods
    Model.init = vi.fn();
    Model.hasOne = vi.fn();
    Model.belongsTo = vi.fn();
    Model.hasMany = vi.fn();
    Model.belongsToMany = vi.fn();
    // Mock Fastify instance
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };
    // Initialize the model
    Module = ModuleModel(mockFastify);
  });

  it('should define correct associations', () => {
    const mockModels = {
      Policy: {},
      Module: Module,
      DepartmentModule: {},
      Department: {},
    };
    Module.associate(mockModels);
    expect(Model.hasOne).toHaveBeenCalledWith(mockModels.Policy, {
      foreignKey: 'parentId',
      as: 'policy',
    });
    expect(Model.belongsTo).toHaveBeenCalledWith(Module, {
      foreignKey: 'parentId',
      as: 'parent',
    });
    expect(Model.hasMany).toHaveBeenCalledWith(Module, {
      foreignKey: 'parentId',
      as: 'children',
    });
    expect(Model.belongsToMany).toHaveBeenCalledWith(mockModels.Department, {
      through: mockModels.DepartmentModule,
      as: 'departments',
      foreignKey: 'moduleId',
    });
  });

  it('should initialize with correct attributes', () => {
    const initCall = Model.init.mock.calls[0];
    const attributes = initCall[0];
    const options = initCall[1];

    // Check attributes
    expect(attributes.id.type).toBe(DataTypes.UUID);
    expect(attributes.id.primaryKey).toBe(true);
    expect(attributes.parentId.type).toBe(DataTypes.UUID);
    expect(attributes.parentId.allowNull).toBe(true);
    expect(attributes.name.type).toBeInstanceOf(DataTypes.STRING);
    expect(attributes.name.type._length).toBe(100);
    expect(attributes.name.allowNull).toBe(false);
    expect(attributes.hierarchy.type).toBeInstanceOf(DataTypes.ENUM);
    expect(attributes.hierarchy.type.values).toEqual(Object.values(HIERARCHY));
    expect(attributes.navigationType.type.type.values).toEqual(Object.values(NAVIGATION_TYPES));
    expect(attributes.navigationPosition.type).toBe(DataTypes.INTEGER);
    expect(attributes.navigationUrl.type).toBeInstanceOf(DataTypes.STRING);
    expect(attributes.navigationUrl.type._length).toBe(100);

    // Check options
    expect(options.modelName).toBe('Module');
    expect(options.tableName).toBe('modules');
    expect(options.underscored).toBe(true);
    expect(options.timestamps).toBe(true);
    expect(options.sequelize).toBe(mockFastify.psql.connection);

    // Check indexes
    expect(options.indexes).toContainEqual({
      unique: true,
      fields: ['hierarchy', 'name', 'level'],
    });
    expect(options.indexes).toContainEqual({
      fields: ['parent_id'],
      name: 'idx_modules_parent',
    });
    expect(options.indexes).toContainEqual({
      fields: ['hierarchy'],
      name: 'idx_modules_hierarchy',
    });
  });

  it('should apply auditable mixin', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(Module);
  });
});
