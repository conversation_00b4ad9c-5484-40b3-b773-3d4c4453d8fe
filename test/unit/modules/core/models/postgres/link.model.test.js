import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import LinkModel from '#src/modules/core/models/postgres/link.model.js';

vi.mock('sequelize', () => {
  const actualSequelize = vi.importActual('sequelize');
  return {
    ...actualSequelize,
    DataTypes: {
      UUID: 'UUID',
      ENUM: vi.fn().mockReturnValue('ENUM'),
      STRING: vi.fn().mockReturnValue('STRING'),
      DATE: 'DATE',
    },
    Model: class MockModel {
      static init() {
        return this;
      }
      static belongsTo() {}
    },
    Sequelize: {
      literal: vi.fn().mockReturnValue('uuid_generate_v1mc()'),
    },
  };
});

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
  versionedMixin: {
    applyVersioning: vi.fn(),
  },
}));

describe('Link Model', () => {
  let mockFastify;
  let mockModels;
  let spyInit;
  let spyBelongsTo;

  beforeEach(() => {
    vi.clearAllMocks();

    spyInit = vi.spyOn(Model, 'init');
    spyBelongsTo = vi.spyOn(Model, 'belongsTo');

    mockFastify = {
      psql: {
        connection: {
          define: vi.fn().mockReturnValue({}),
        },
      },
    };

    mockModels = {
      Entity: {},
    };
    LinkModel(mockFastify);
  });

  it('should initialize the Link model with correct attributes', () => {
    expect(spyInit).toHaveBeenCalledWith(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        entity_id: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        type: {
          type: DataTypes.ENUM('EMAIL_DOMAIN', 'SIGNIN'),
          allowNull: false,
        },
        url: {
          type: DataTypes.STRING(2083),
          allowNull: false,
          validate: {
            len: [1, 2083],
            isUrl: true,
          },
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        updatedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      },
      {
        sequelize: mockFastify.psql.connection,
        modelName: 'Link',
        tableName: 'links',
        underscored: true,
        timestamps: true,
      },
    );
  });

  it('should define correct associations', () => {
    const Link = LinkModel(mockFastify);

    Link.associate(mockModels);

    expect(spyBelongsTo).toHaveBeenCalledWith(mockModels.Entity, {
      foreignKey: 'entity_id',
      as: 'entity',
    });
  });

  it('should return the Link model', () => {
    const Link = LinkModel(mockFastify);
    expect(Link).toBeDefined();
    expect(Link).toBeInstanceOf(Function);
  });

  it('should use ENUM for type field with correct values', () => {
    LinkModel(mockFastify);

    expect(DataTypes.ENUM).toHaveBeenCalledWith('email_domain', 'signin');
  });

  it('should use STRING with correct length for url field', () => {
    LinkModel(mockFastify);

    expect(DataTypes.STRING).toHaveBeenCalledWith(2083);
  });

  it('should set timestamps and underscored to true', () => {
    LinkModel(mockFastify);

    expect(spyInit).toHaveBeenCalledWith(
      expect.any(Object),
      expect.objectContaining({
        timestamps: true,
        underscored: true,
      }),
    );
  });
});
