import { DataTypes, Model, Sequelize } from 'sequelize';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { auditableMixin } from '#src/mixins/index.js';
import createRoleModuleModel from '#src/modules/core/models/postgres/role-module.model.js';

vi.mock('sequelize', () => {
  const actualSequelize = vi.importActual('sequelize');
  return {
    ...actualSequelize,
    DataTypes: {
      UUID: 'UUID',
      STRING: vi.fn().mockReturnValue('STRING'),
    },
    Model: class MockModel {
      static init() {
        return this;
      }
      static hasOne() {}
      static belongsTo() {}
      static getAttributes() {
        return {
          id: { type: 'UUID', primaryKey: true },
          roleId: { type: 'UUID', allowNull: false },
          moduleId: { type: 'UUID', allowNull: false },
          createdBy: { type: 'UUID', allowNull: true },
          updatedBy: { type: 'UUID', allowNull: true },
        };
      }
    },
    Sequelize: {
      literal: vi.fn().mockReturnValue('uuid_generate_v1mc()'),
    },
  };
});

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
}));

describe('RoleModule Model', () => {
  let mockFastify;
  let mockModels;
  let RoleModule;
  let spyInit;
  let spyHasOne;
  let spyBelongsTo;

  beforeEach(() => {
    vi.clearAllMocks();

    spyInit = vi.spyOn(Model, 'init');
    spyHasOne = vi.spyOn(Model, 'hasOne');
    spyBelongsTo = vi.spyOn(Model, 'belongsTo');

    mockFastify = {
      psql: {
        connection: {},
      },
    };

    mockModels = {
      Policy: {},
      Module: {},
      Role: {},
    };

    RoleModule = createRoleModuleModel(mockFastify);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize the RoleModule model correctly', () => {
    expect(RoleModule).toBeDefined();
  });

  it('should apply auditable mixin correctly', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(RoleModule);
  });

  it('should define the correct attributes', () => {
    expect(spyInit).toHaveBeenCalledWith(
      expect.objectContaining({
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        roleId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'roles',
            key: 'id',
          },
        },
        moduleId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'modules',
            key: 'id',
          },
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        updatedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      }),
      expect.objectContaining({
        modelName: 'RoleModule',
        tableName: 'role_modules',
        underscored: true,
        timestamps: true,
      }),
    );
  });

  it('should define the correct associations', () => {
    RoleModule.associate(mockModels);

    expect(spyHasOne).toHaveBeenCalledWith(mockModels.Policy, {
      foreignKey: 'parentId',
      as: 'policy',
    });

    expect(spyBelongsTo).toHaveBeenCalledWith(mockModels.Module, {
      foreignKey: 'moduleId',
      as: 'module',
    });

    expect(spyBelongsTo).toHaveBeenCalledWith(mockModels.Role, {
      foreignKey: 'roleId',
      as: 'role',
    });
  });
});
