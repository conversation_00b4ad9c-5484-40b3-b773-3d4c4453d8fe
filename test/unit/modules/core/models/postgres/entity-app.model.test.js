import { Model } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import EntityAppModel from '#src/modules/core/models/postgres/entity-app.model.js';
import { AppConstant } from '#src/modules/setting/constants/index.js';

vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('EntityApp Model', () => {
  let mockFastify;
  let EntityApp;

  beforeEach(() => {
    vi.resetAllMocks();

    Model.init = vi.fn();
    Model.hasMany = vi.fn();
    Model.belongsTo = vi.fn();

    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };

    EntityApp = EntityAppModel(mockFastify);
  });

  it('should define associations correctly', () => {
    const mockModels = {
      TestResult: {},
      AppConfig: {},
      App: {},
    };

    EntityApp.associate(mockModels);

    expect(Model.hasMany).toHaveBeenCalledWith(mockModels.TestResult, {
      foreignKey: 'testableId',
      as: 'testResult',
      constraints: false,
      scope: {
        testable_type: 'EntityApp',
      },
    });
    expect(Model.hasMany).toHaveBeenCalledWith(mockModels.AppConfig, {
      foreignKey: 'entityAppId',
      as: 'appConfig',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.App, {
      foreignKey: 'appId',
      as: 'app',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  describe('Model initialization', () => {
    let fields, options;

    beforeEach(() => {
      [fields, options] = Model.init.mock.calls[0];
    });

    it('should set correct model and table names', () => {
      expect(options.modelName).toBe('EntityApp');
      expect(options.tableName).toBe('entity_apps');
      expect(options.underscored).toBe(true);
      expect(options.timestamps).toBe(true);
      expect(options.paranoid).toBe(true);
    });

    it('should define the correct fields with validations', () => {
      expect(fields).toHaveProperty('id');
      expect(fields.id.type.key).toBe('UUID');

      expect(fields).toHaveProperty('entityId');
      expect(fields.entityId.allowNull).toBe(false);

      expect(fields).toHaveProperty('appId');
      expect(fields.appId.allowNull).toBe(false);

      expect(fields).toHaveProperty('status');
      expect(fields.status.allowNull).toBe(false);
      expect(fields.status.type.values).toEqual(Object.values(AppConstant.ENTITY_APP_STATUSES));
      expect(fields.status.defaultValue).toBe(AppConstant.ENTITY_APP_STATUSES.UNINSTALLED);

      expect(fields).toHaveProperty('createdBy');
      expect(fields).toHaveProperty('updatedBy');
      expect(fields).toHaveProperty('deletedBy');
    });

    it('should apply auditable mixin', async () => {
      const { auditableMixin, __mocks } = await import('#src/mixins/index.js');
      auditableMixin.applyAuditFields(EntityApp);
      expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(EntityApp);
    });
  });
});
