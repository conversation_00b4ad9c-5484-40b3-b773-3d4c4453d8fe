import { Model } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import UserAssociationCurrencyModel from '#src/modules/core/models/postgres/user-association-currency.model.js';

vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('UserAssociationCurrency Model', () => {
  let mockFastify;
  let UserAssociationCurrency;

  beforeEach(() => {
    vi.resetAllMocks();

    Model.init = vi.fn();
    Model.belongsTo = vi.fn();

    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };

    UserAssociationCurrency = UserAssociationCurrencyModel(mockFastify);
  });

  it('should define associations correctly', () => {
    const mockModels = {
      UserAssociation: {},
      CustomLocalisation: {},
    };

    UserAssociationCurrency.associate(mockModels);

    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.UserAssociation, {
      foreignKey: 'userAssociationId',
      as: 'ua',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });

    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.CustomLocalisation, {
      foreignKey: 'currencyId',
      as: 'cl',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  describe('Model initialization', () => {
    let options;
    let fields;

    beforeEach(() => {
      [fields, options] = Model.init.mock.calls[0];
    });

    it('should set correct model name and table name', () => {
      expect(options.modelName).toBe('UserAssociationCurrency');
      expect(options.tableName).toBe('user_assoc_currencies');
      expect(options.underscored).toBe(true);
      expect(options.timestamps).toBe(true);
      expect(options.paranoid).toBe(false);
    });

    it('should define the correct fields', () => {
      expect(fields).toHaveProperty('id');
      expect(fields.id.type.key).toBe('UUID');

      expect(fields).toHaveProperty('userAssociationId');
      expect(fields.userAssociationId.allowNull).toBe(false);

      expect(fields).toHaveProperty('currencyId');
      expect(fields.currencyId.allowNull).toBe(false);

      expect(fields).toHaveProperty('currencyCode');

      expect(fields).toHaveProperty('createdBy');
      expect(fields.createdBy.allowNull).toBe(true);

      expect(fields).toHaveProperty('updatedBy');
      expect(fields.updatedBy.allowNull).toBe(true);
    });
    it('should define unique index on userAssociationId and currencyId', () => {
      expect(options.indexes).toEqual([
        {
          unique: true,
          fields: ['userAssociationId', 'currencyId'],
        },
      ]);
    });

    it('should have auditable and versioned mixins available', async () => {
      const { auditableMixin, __mocks } = await import('#src/mixins/index.js');
      expect(auditableMixin.applyAuditFields).toBeDefined();

      auditableMixin.applyAuditFields(UserAssociationCurrency);
      expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(UserAssociationCurrency);
    });
  });

  describe('Virtual field: currencyCode', () => {
    it('should return code from associated cl model', () => {
      const instance = {
        cl: { code: 'USD' },
      };

      const getter = Model.init.mock.calls[0][0].currencyCode.get;
      const result = getter.call(instance);

      expect(result).toBe('USD');
    });
  });
});
