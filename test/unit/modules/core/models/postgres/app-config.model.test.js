import { Model } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import AppConfigModel from '#src/modules/core/models/postgres/app-config.model.js';

vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('AppConfig Model', () => {
  let mockFastify;
  let AppConfig;

  beforeEach(() => {
    vi.resetAllMocks();

    Model.init = vi.fn();
    Model.belongsTo = vi.fn();

    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };

    AppConfig = AppConfigModel(mockFastify);
  });

  it('should define associations correctly', () => {
    const mockModels = {
      EntityApp: {},
    };

    AppConfig.associate(mockModels);

    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.EntityApp, {
      foreignKey: 'entityAppId',
      as: 'entityApp',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  describe('Model initialization', () => {
    let fields, options;

    beforeEach(() => {
      [fields, options] = Model.init.mock.calls[0];
    });

    it('should set correct model name and table name', () => {
      expect(options.modelName).toBe('AppConfig');
      expect(options.tableName).toBe('app_configs');
      expect(options.underscored).toBe(true);
      expect(options.timestamps).toBe(true);
      expect(options.paranoid).toBe(true);
    });

    it('should define the correct fields with validations', () => {
      expect(fields).toHaveProperty('id');
      expect(fields.id.type.key).toBe('UUID');

      expect(fields).toHaveProperty('entityAppId');
      expect(fields.entityAppId.allowNull).toBe(false);

      expect(fields).toHaveProperty('configKey');
      expect(fields.configKey.allowNull).toBe(false);
      expect(fields.configKey.validate).toMatchObject({
        len: [1, 100],
        notEmpty: true,
      });

      expect(fields).toHaveProperty('configValue');
      expect(fields.configValue.type.key).toBe('TEXT');

      expect(fields).toHaveProperty('createdBy');
      expect(fields).toHaveProperty('updatedBy');
      expect(fields).toHaveProperty('deletedBy');
    });

    it('should apply audit fields', async () => {
      const { auditableMixin, __mocks } = await import('#src/mixins/index.js');
      expect(auditableMixin.applyAuditFields).toBeDefined();

      auditableMixin.applyAuditFields(AppConfig);
      expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(AppConfig);
    });
  });
});
