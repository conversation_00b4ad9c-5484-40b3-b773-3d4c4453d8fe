import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import UserInvitationModel from '#src/modules/core/models/postgres/user-invitation.model.js';

vi.mock('#src/modules/user/constants/index.js', () => ({
  UserConstant: {
    USER_INVITATION_STATUSES: {
      PENDING: 'pending',
      APPROVED: 'approved',
      CANCELLED: 'cancelled',
    },
  },
}));

vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('UserInvitation Model', () => {
  let mockFastify;
  let UserInvitation;

  beforeEach(() => {
    vi.resetAllMocks();
    Model.init = vi.fn();
    Model.belongsTo = vi.fn();

    mockFastify = {
      psql: {
        connection: {},
      },
    };

    UserInvitation = UserInvitationModel(mockFastify);
  });

  it('should define associations correctly', () => {
    const mockModels = {
      UserAssociation: {},
    };

    UserInvitation.associate(mockModels);

    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.UserAssociation, {
      foreignKey: 'userAssociationId',
      as: 'ua',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  describe('Model Initialization', () => {
    let fields;
    let options;

    beforeEach(() => {
      [fields, options] = Model.init.mock.calls[0];
    });

    it('should define correct model and table names', () => {
      expect(options.modelName).toBe('UserInvitation');
      expect(options.tableName).toBe('user_invitations');
    });

    it('should enable timestamps, underscored naming, and disable paranoid', () => {
      expect(options.timestamps).toBe(true);
      expect(options.underscored).toBe(true);
      expect(options.paranoid).toBe(false);
    });

    it('should use the correct sequelize connection', () => {
      expect(options.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct fields', () => {
      expect(fields.id.type).toBe(DataTypes.UUID);
      expect(fields.id.primaryKey).toBe(true);
      expect(fields.id.defaultValue).toEqual(Sequelize.literal('uuid_generate_v1mc()'));

      expect(fields.userAssociationId.allowNull).toBe(false);
      expect(fields.invitedBy.allowNull).toBe(false);

      expect(fields.invitedDate.allowNull).toBe(false);
      expect(fields.invitedDate.defaultValue).toBe(Sequelize.NOW);

      expect(fields.acceptedDate.allowNull).toBe(true);
      expect(fields.cancelledDate.allowNull).toBe(true);

      expect(fields.status.type.constructor.name).toBe('ENUM');
      expect(fields.status.defaultValue).toBe('pending');
    });

    it('should allow null for createdBy and updatedBy', () => {
      expect(fields.createdBy.allowNull).toBe(true);
      expect(fields.updatedBy.allowNull).toBe(true);
    });
    it('should apply auditableMixin', async () => {
      const { auditableMixin, __mocks } = await import('#src/mixins/index.js');
      expect(auditableMixin.applyAuditFields).toBeDefined();
      expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(UserInvitation);
    });
  });
});
