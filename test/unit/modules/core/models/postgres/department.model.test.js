import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { auditableMixin } from '#src/mixins/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import DepartmentModel from '#src/modules/core/models/postgres/department.model.js';

vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    HIERARCHY: {
      ROOT: 'root',
      ORGANISATION: 'organisation',
      MERCHANT: 'merchant',
    },
    COMMON_STATUSES: {
      ACTIVE: 'active',
      INACTIVE: 'inactive',
    },
  },
}));

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
}));

vi.mock('#src/modules/core/errors/index.js', () => ({
  CoreError: {
    alreadyExists: vi.fn(),
  },
}));

describe('Department Model', () => {
  let mockFastify;
  let Department;

  beforeEach(() => {
    vi.resetAllMocks();
    Model.init = vi.fn();
    Model.hasMany = vi.fn();
    Model.findOne = vi.fn();
    Model.belongsTo = vi.fn();
    Model.belongsToMany = vi.fn();
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };
    Department = DepartmentModel(mockFastify);
  });

  it('should define correct associations', () => {
    const mockModels = {
      DepartmentModule: {},
      Entity: {},
      Module: {},
    };
    Department.associate(mockModels);
    expect(Model.hasMany).toHaveBeenCalledWith(mockModels.DepartmentModule, {
      foreignKey: 'departmentId',
      as: 'modulePolicies',
    });
    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.Entity, {
      foreignKey: 'entityId',
      as: 'entity',
    });
    expect(Model.belongsToMany).toHaveBeenCalledWith(mockModels.Module, {
      foreignKey: 'departmentId',
      through: mockModels.DepartmentModule,
      as: 'modules',
    });
  });

  it('should call auditableMixin.applyAuditFields', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(Department);
  });

  describe('Model initialization', () => {
    let fields;
    let initOptions;

    beforeEach(() => {
      [fields, initOptions] = Model.init.mock.calls[0];
    });

    it('should set correct model name and table name', () => {
      expect(initOptions.modelName).toBe('Department');
      expect(initOptions.tableName).toBe('departments');
    });

    it('should set underscored, timestamps, and paranoid to true', () => {
      expect(initOptions.underscored).toBe(true);
      expect(initOptions.timestamps).toBe(true);
      expect(initOptions.paranoid).toBe(true);
    });

    it('should set the correct sequelize instance', () => {
      expect(initOptions.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct fields', () => {
      expect(fields).toEqual({
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        entityId: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        name: {
          type: DataTypes.STRING(50),
          allowNull: false,
          validate: {
            len: [1, 50],
            notEmpty: true,
          },
        },
        hierarchy: {
          type: DataTypes.ENUM(Object.values(CoreConstant.HIERARCHY)),
          allowNull: false,
        },
        template: {
          type: DataTypes.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        description: {
          type: DataTypes.STRING(100),
          allowNull: true,
          validate: {
            len: [1, 100],
            notEmpty: false,
          },
        },
        status: {
          type: DataTypes.ENUM(Object.values(CoreConstant.COMMON_STATUSES)),
          allowNull: false,
          defaultValue: CoreConstant.COMMON_STATUSES.ACTIVE,
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        updatedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        deletedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      });
    });

    it('should define the correct indexes', () => {
      expect(initOptions.indexes).toEqual([
        {
          fields: ['entity_id'],
          name: 'idx_departments_entity',
        },
        {
          fields: ['entity_id', 'hierarchy'],
          name: 'idx_departments_entity_hierarchy',
        },
        {
          fields: ['status'],
          name: 'idx_departments_status',
        },
      ]);
    });
  });

  describe('Model hooks', () => {
    let hooks;

    beforeEach(() => {
      hooks = Model.init.mock.calls[0][1].hooks;
    });

    it('should define beforeCreate and beforeUpdate hooks', () => {
      expect(hooks.beforeCreate).toBeDefined();
      expect(hooks.beforeUpdate).toBeDefined();
    });

    it('should call checkDuplicateDepartment in beforeCreate hook', async () => {
      const department = { entityId: 'entity1', name: 'Test Department' };
      const options = { transaction: {} };

      Model.findOne.mockResolvedValue(null);

      await hooks.beforeCreate(department, options);

      expect(Model.findOne).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            entityId: 'entity1',
            name: 'Test Department',
            id: expect.any(Object),
          }),
          transaction: options.transaction,
        }),
      );
    });

    it('should call checkDuplicateDepartment in beforeUpdate hook', async () => {
      const department = { id: 'dept1', entityId: 'entity1', name: 'Test Department' };
      const options = { transaction: {} };

      Model.findOne.mockResolvedValue(null);

      await hooks.beforeUpdate(department, options);

      expect(Model.findOne).toHaveBeenCalledWith({
        where: {
          entityId: 'entity1',
          name: 'Test Department',
          id: { [Sequelize.Op.ne]: 'dept1' },
        },
        transaction: options.transaction,
      });
    });

    it('should throw DepartmentError if duplicate department is found', async () => {
      const department = { entityId: 'entity1', name: 'Test Department' };
      const options = { transaction: {} };

      Model.findOne.mockResolvedValue({ id: 'existingDept' });

      await expect(hooks.beforeCreate(department, options)).rejects.toThrow(
        'Department already exists',
      );

      expect(CoreError.alreadyExists).toHaveBeenCalledWith({
        attribute: 'common.label.department',
        value: 'Test Department',
      });
    });
  });
});
