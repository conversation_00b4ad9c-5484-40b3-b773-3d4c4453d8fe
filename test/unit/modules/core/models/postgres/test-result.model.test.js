import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import TestResultModel from '#src/modules/core/models/postgres/test-result.model.js';

vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('TestResult Model', () => {
  let mockFastify;
  let TestResult;

  beforeEach(() => {
    vi.resetAllMocks();
    Model.init = vi.fn();
    Model.belongsTo = vi.fn();
    Model.hasMany = vi.fn();

    mockFastify = {
      psql: {
        connection: {},
      },
    };

    TestResult = TestResultModel(mockFastify);
  });

  describe('Model initialization', () => {
    let fields, options;

    beforeEach(() => {
      [fields, options] = Model.init.mock.calls[0];
    });

    it('should set correct model and table names', () => {
      expect(options.modelName).toBe('TestResult');
      expect(options.tableName).toBe('test_results');
      expect(options.underscored).toBe(true);
      expect(options.timestamps).toBe(true);
    });

    it('should define associations correctly', () => {
      const mockModels = {
        EntityApp: {},
      };

      TestResult.associate(mockModels);

      expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.EntityApp, {
        foreignKey: 'testableId',
        constraints: false,
        as: 'testable',
      });
    });
    it('should define all expected fields with correct properties', () => {
      expect(fields).toHaveProperty('id');
      expect(fields.id.type).toBe(DataTypes.UUID);
      expect(fields.id.defaultValue).toEqual(Sequelize.literal('uuid_generate_v1mc()'));

      expect(fields.testableId.allowNull).toBe(false);

      expect(fields.testableType.allowNull).toBe(false);
      expect(fields.testableType.type.values).toEqual(Object.values(CoreConstant.TESTABLE_TYPES));

      expect(fields.requestHeader.type).toBe(DataTypes.JSONB);
      expect(fields.requestBody.type).toBe(DataTypes.JSONB);
      expect(fields.response.type).toBe(DataTypes.JSONB);

      expect(fields.status.allowNull).toBe(false);
      expect(fields.status.type.values).toEqual(Object.values(CoreConstant.TEST_RESULT_STATUSES));

      expect(fields).toHaveProperty('createdBy');
      expect(fields).toHaveProperty('updatedBy');
    });

    it('should apply auditableMixin to the model', async () => {
      const { __mocks } = await import('#src/mixins/index.js');
      expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(TestResult);
    });

    it('should define an associate method on the model', () => {
      expect(typeof TestResult.associate).toBe('function');
    });
  });
});
