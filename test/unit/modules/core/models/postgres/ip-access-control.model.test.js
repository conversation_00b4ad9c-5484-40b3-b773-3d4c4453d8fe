/* eslint-disable sonarjs/no-nested-functions */
/* eslint-disable sonarjs/no-hardcoded-ip */
import { Model } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { auditableMixin } from '#src/mixins/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import IpAccessControlModel from '#src/modules/core/models/postgres/ip-access-control.model.js';
import { AccessControlConstant } from '#src/modules/setting/constants/index.js';
import { IpAccessControlRepository } from '#src/modules/setting/repository/index.js';

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/repository/index.js', () => ({
  IpAccessControlRepository: {
    checkOverlap: vi.fn(),
  },
}));

vi.mock('sequelize', () => ({
  DataTypes: {
    UUID: 'UUID',
    ENUM: vi.fn(),
    INET: 'INET',
    DATE: vi.fn(() => 'DATE'),
    VIRTUAL: 'VIRTUAL',
  },
  Model: class MockModel {
    static init() {}
    static belongsTo() {}
  },
  Sequelize: {
    literal: vi.fn(),
  },
}));

describe('IpAccessControl Model', () => {
  let mockFastify;
  let IpAccessControl;

  beforeEach(() => {
    vi.resetAllMocks();
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };
    IpAccessControl = IpAccessControlModel(mockFastify);
  });

  it('should define correct associations', () => {
    const mockModels = {
      Remark: {},
    };
    vi.spyOn(Model, 'belongsTo');
    IpAccessControl.associate(mockModels);
    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.Remark, {
      foreignKey: 'id',
      targetKey: 'remarkableId',
      constraints: false,
      as: 'activeRemark',
      scope: {
        remarkable_type: 'ip_access_control',
        status: CoreConstant.REMARK_STATUSES.ACTIVE,
      },
    });
  });

  it('should call auditableMixin.applyAuditFields', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(IpAccessControl);
  });

  describe('Model initialization', () => {
    let initSpy;
    let initArgs;

    beforeEach(() => {
      initSpy = vi.spyOn(Model, 'init');
      IpAccessControlModel(mockFastify);
      initArgs = initSpy.mock.calls[0];
    });

    it('should set correct model name and table name', () => {
      expect(initArgs[1].modelName).toBe('IpAccessControl');
      expect(initArgs[1].tableName).toBe('ip_access_controls');
    });

    it('should set underscored and timestamps to true', () => {
      expect(initArgs[1].underscored).toBe(true);
      expect(initArgs[1].timestamps).toBe(true);
    });

    it('should set the correct sequelize instance', () => {
      expect(initArgs[1].sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct indexes', () => {
      expect(initArgs[1].indexes).toHaveLength(6);
      expect(initArgs[1].indexes[0]).toEqual({
        unique: true,
        fields: ['parent_id', 'ip_address'],
        name: 'idx_ipac_unique_parent_ip',
      });
    });

    it('should define the correct fields', () => {
      const fields = initArgs[0];
      expect(fields).toHaveProperty('id');
      expect(fields).toHaveProperty('parentId');
      expect(fields).toHaveProperty('ruleType');
      expect(fields).toHaveProperty('ipAddress');
      expect(fields).toHaveProperty('validityDate');
      expect(fields).toHaveProperty('status');
      expect(fields).toHaveProperty('remark');
      expect(fields).toHaveProperty('createdBy');
      expect(fields).toHaveProperty('updatedBy');
    });

    it('should define the virtual field "remark" correctly', () => {
      const remarkGetter = initArgs[0].remark.get;
      const instance = { activeRemark: { content: 'Test remark' } };
      expect(remarkGetter.call(instance)).toBe('Test remark');
    });
  });

  describe('Hooks', () => {
    let hooks;
    let mockSequelize;

    beforeEach(() => {
      const initSpy = vi.spyOn(Model, 'init');
      IpAccessControlModel(mockFastify);
      hooks = initSpy.mock.calls[0][1].hooks;
      mockSequelize = {
        models: {
          Remark: {
            destroy: vi.fn(),
          },
        },
      };
    });

    describe('afterFind hook', () => {
      it('should handle a single record', () => {
        const singleRecord = {
          validityDate: new Date(Date.now() - 86400000),
          status: AccessControlConstant.STATUSES.ACTIVE,
        };
        hooks.afterFind(singleRecord);
        expect(singleRecord.status).toBe(AccessControlConstant.STATUSES.EXPIRED);
      });

      it('should handle an array of records', () => {
        const records = [
          {
            validityDate: new Date(Date.now() - 86400000),
            status: AccessControlConstant.STATUSES.ACTIVE,
          },
          {
            validityDate: new Date(Date.now() + 86400000),
            status: AccessControlConstant.STATUSES.ACTIVE,
          },
        ];
        hooks.afterFind(records);
        expect(records[0].status).toBe(AccessControlConstant.STATUSES.EXPIRED);
        expect(records[1].status).toBe(AccessControlConstant.STATUSES.ACTIVE);
      });

      it('should not modify records without validityDate', () => {
        const records = [
          { status: AccessControlConstant.STATUSES.ACTIVE },
          { validityDate: null, status: AccessControlConstant.STATUSES.ACTIVE },
        ];
        hooks.afterFind(records);
        expect(records[0].status).toBe(AccessControlConstant.STATUSES.ACTIVE);
        expect(records[1].status).toBe(AccessControlConstant.STATUSES.ACTIVE);
      });
    });

    describe('beforeCreate hook', () => {
      it('should throw an error if a duplicate record is found', async () => {
        const instance = {
          constructor: IpAccessControl,
          ipAddress: '***********',
          parentId: 'parent-id',
        };
        IpAccessControlRepository.checkOverlap.mockResolvedValue(true);
        await expect(hooks.beforeCreate(instance)).rejects.toThrow(
          CoreError.alreadyExists({
            attribute: 'common.label.ipAddress',
            value: '***********',
          }),
        );
      });

      it('should not throw an error if no duplicate record is found', async () => {
        const instance = {
          constructor: IpAccessControl,
          ipAddress: '***********',
          parentId: 'parent-id',
        };
        IpAccessControlRepository.checkOverlap.mockResolvedValue(false);
        await expect(hooks.beforeCreate(instance)).resolves.not.toThrow();
      });
    });

    describe('beforeUpdate hook', () => {
      let hooks;
      let mockServer;

      beforeEach(() => {
        const initSpy = vi.spyOn(Model, 'init');
        IpAccessControlModel(mockFastify);
        hooks = initSpy.mock.calls[0][1].hooks;
        mockServer = {
          psql: {
            IpAccessControl: IpAccessControl,
          },
        };
      });

      it('should check for overlap if ipAddress is changed', async () => {
        const instance = {
          constructor: IpAccessControl,
          id: 'current-id',
          ipAddress: '***********',
          parentId: 'parent-id',
          changed: () => ['ipAddress'],
        };
        IpAccessControlRepository.checkOverlap.mockResolvedValue(null);
        await expect(hooks.beforeUpdate(instance)).resolves.not.toThrow();
        expect(IpAccessControlRepository.checkOverlap).toHaveBeenCalledWith(
          mockServer,
          'parent-id',
          '***********',
          'current-id',
        );
      });

      it('should check for overlap if status is changed to ACTIVE', async () => {
        const instance = {
          constructor: IpAccessControl,
          id: 'current-id',
          ipAddress: '***********',
          parentId: 'parent-id',
          status: AccessControlConstant.STATUSES.ACTIVE,
          changed: () => ['status'],
        };
        IpAccessControlRepository.checkOverlap.mockResolvedValue(null);
        await expect(hooks.beforeUpdate(instance)).resolves.not.toThrow();
        expect(IpAccessControlRepository.checkOverlap).toHaveBeenCalledWith(
          mockServer,
          'parent-id',
          '***********',
          'current-id',
        );
      });

      it('should throw an error if ipAddress is changed and a duplicate record is found', async () => {
        const instance = {
          constructor: IpAccessControl,
          id: 'current-id',
          ipAddress: '***********',
          parentId: 'parent-id',
          changed: () => ['ipAddress'],
        };
        IpAccessControlRepository.checkOverlap.mockResolvedValue({ id: 'different-id' });
        await expect(hooks.beforeUpdate(instance)).rejects.toThrow(
          CoreError.alreadyExists({
            attribute: 'common.label.ipAddress',
            value: '***********',
          }),
        );
      });

      it('should not check for overlap if neither ipAddress nor status is changed', async () => {
        const instance = {
          constructor: IpAccessControl,
          id: 'current-id',
          ipAddress: '***********',
          parentId: 'parent-id',
          changed: () => ['validityDate'],
        };
        await expect(hooks.beforeUpdate(instance)).resolves.not.toThrow();
        expect(IpAccessControlRepository.checkOverlap).not.toHaveBeenCalled();
      });

      it('should not check for overlap if status is changed but not to ACTIVE', async () => {
        const instance = {
          constructor: IpAccessControl,
          id: 'current-id',
          ipAddress: '***********',
          parentId: 'parent-id',
          status: AccessControlConstant.STATUSES.INACTIVE,
          changed: () => ['status'],
        };
        await expect(hooks.beforeUpdate(instance)).resolves.not.toThrow();
        expect(IpAccessControlRepository.checkOverlap).not.toHaveBeenCalled();
      });

      it('should not throw an error if the found record is the same as the current instance', async () => {
        const instance = {
          constructor: IpAccessControl,
          id: 'current-id',
          ipAddress: '***********',
          parentId: 'parent-id',
          changed: () => ['ipAddress'],
        };
        IpAccessControlRepository.checkOverlap.mockResolvedValue({ id: 'current-id' });
        await expect(hooks.beforeUpdate(instance)).resolves.not.toThrow();
      });
    });

    describe('afterDestroy hook', () => {
      it('should destroy associated Remark records', async () => {
        const instance = {
          id: 'test-id',
          sequelize: mockSequelize,
        };
        const options = {
          transaction: 'mock-transaction',
        };

        await hooks.afterDestroy(instance, options);

        expect(mockSequelize.models.Remark.destroy).toHaveBeenCalledWith({
          where: {
            remarkableId: 'test-id',
          },
          transaction: 'mock-transaction',
        });
      });
    });
  });
});
