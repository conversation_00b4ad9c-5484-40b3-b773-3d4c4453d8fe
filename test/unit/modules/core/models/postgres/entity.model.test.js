import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { auditableMixin, versionedMixin } from '#src/mixins/index.js';
import EntityModel from '#src/modules/core/models/postgres/entity.model.js';
import { clearCacheWithPrefix } from '#src/utils/cache.util.js';

vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    HIERARCHY: {
      ROOT: 'root',
      ORGANISATION: 'organisation',
      MERCHANT: 'merchant',
      USER: 'user',
    },
    MODULE_NAMES: {
      ORGANISATION: 'organisation',
    },
  },
  EntityConstant: {
    ENTITY_STATUSES: {
      ACTIVE: 'active',
      INACTIVE: 'inactive',
      SUSPENDED: 'suspended',
      ARCHIVED: 'archived',
    },
  },
}));

vi.mock('sequelize', () => {
  const actualSequelize = vi.importActual('sequelize');
  return {
    ...actualSequelize,
    DataTypes: {
      UUID: 'UUID',
      ENUM: vi.fn().mockReturnValue('ENUM'),
      STRING: vi.fn().mockReturnValue('STRING'),
      TEXT: 'TEXT',
      BIGINT: 'BIGINT',
      BOOLEAN: 'BOOLEAN',
      ARRAY: vi.fn().mockReturnValue('ARRAY'),
    },
    Model: class MockModel {
      static init() {
        return this;
      }
      static hasMany() {}
      static hasOne() {}
      static belongsTo() {}
    },
    Sequelize: {
      literal: vi.fn().mockReturnValue('uuid_generate_v1mc()'),
    },
  };
});

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
  versionedMixin: {
    applyVersioning: vi.fn(),
  },
}));

vi.mock('#src/utils/cache.util.js', () => ({
  clearCacheWithPrefix: vi.fn(),
}));

describe('Entity Model', () => {
  let mockFastify;
  let mockModels;
  let spyInit;
  let spyHasMany;
  let spyHasOne;
  let Entity;
  let mockHooks;

  beforeEach(() => {
    vi.clearAllMocks();

    spyInit = vi.spyOn(Model, 'init');
    spyHasMany = vi.spyOn(Model, 'hasMany');
    spyHasOne = vi.spyOn(Model, 'hasOne');

    mockHooks = {
      afterCreate: expect.any(Function),
      afterUpdate: expect.any(Function),
    };

    mockFastify = {
      psql: {
        connection: {
          define: vi.fn().mockReturnValue({}),
        },
      },
      redis: {
        del: vi.fn(),
        keys: vi.fn().mockResolvedValue([]),
      },
    };

    mockModels = {
      Link: {},
      Address: {},
      CustomLocalisation: {},
    };
    Entity = EntityModel(mockFastify);
  });

  it('should initialize the Entity model with correct attributes', () => {
    expect(spyInit).toHaveBeenCalledWith(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        parentId: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        accessId: {
          type: DataTypes.STRING(12),
          unique: true,
          allowNull: false,
        },
        hierarchy: {
          type: DataTypes.ENUM('ROOT', 'ORGANISATION', 'MERCHANT'),
          allowNull: false,
        },
        code: {
          type: DataTypes.STRING(50),
          allowNull: false,
          unique: true,
          validate: {
            len: [1, 50],
            notEmpty: true,
          },
        },
        prefix: {
          type: DataTypes.STRING(10),
          allowNull: false,
          validate: {
            len: [1, 10],
            notEmpty: true,
          },
        },
        name: {
          type: DataTypes.STRING(50),
          allowNull: false,
          unique: true,
          validate: {
            len: [1, 50],
            notEmpty: true,
          },
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        status: {
          type: DataTypes.ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED', 'ARCHIVED'),
          defaultValue: 'active',
          allowNull: false,
        },
        phone: {
          type: DataTypes.STRING(20),
          allowNull: true,
          validate: {
            len: [0, 20],
          },
        },
        email: {
          type: DataTypes.STRING(50),
          allowNull: true,
          validate: {
            len: [0, 50],
            isEmail: true,
          },
        },
        logoDesktop: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        logoMobile: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        favicon: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        version: {
          type: DataTypes.BIGINT,
          allowNull: false,
          defaultValue: 1,
          validate: {
            min: 1,
            notNull: { msg: 'version is required' },
          },
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        updatedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      },
      expect.objectContaining({
        modelName: 'Entity',
        tableName: 'entities',
        underscored: true,
        timestamps: true,
        sequelize: mockFastify.psql.connection,
        hooks: mockHooks,
      }),
    );
  });

  it('should define correct associations', () => {
    Entity.associate(mockModels);

    expect(spyHasMany).toHaveBeenCalledWith(mockModels.Link, {
      foreignKey: 'entity_id',
      as: 'links',
    });

    expect(spyHasOne).toHaveBeenCalledWith(mockModels.Address, {
      foreignKey: 'entity_id',
      as: 'addresses',
    });

    expect(spyHasMany).toHaveBeenCalledWith(mockModels.CustomLocalisation, {
      foreignKey: 'entity_id',
      as: 'custom_localisations',
    });
  });

  it('should return the Entity model', () => {
    expect(Entity).toBeDefined();
    expect(Entity).toBeInstanceOf(Function);
  });

  it('should use STRING with correct lengths for various fields', () => {
    expect(DataTypes.STRING).toHaveBeenCalledWith(12); // accessId
    expect(DataTypes.STRING).toHaveBeenCalledWith(50); // code
    expect(DataTypes.STRING).toHaveBeenCalledWith(10); // prefix
    expect(DataTypes.STRING).toHaveBeenCalledWith(50); // name
    expect(DataTypes.STRING).toHaveBeenCalledWith(20); // phone
    expect(DataTypes.STRING).toHaveBeenCalledWith(50); // email
  });

  it('should set timestamps and underscored to true', () => {
    expect(spyInit).toHaveBeenCalledWith(
      expect.any(Object),
      expect.objectContaining({
        timestamps: true,
        underscored: true,
      }),
    );
  });

  it('should apply auditable and versioned mixins', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(Entity);
    expect(versionedMixin.applyVersioning).toHaveBeenCalledWith(Entity);
  });

  it('should define hooks for cache clearing', () => {
    const options = spyInit.mock.calls[0][1];

    expect(options.hooks).toBeDefined();
    expect(options.hooks.afterCreate).toBeDefined();
    expect(options.hooks.afterUpdate).toBeDefined();

    options.hooks.afterCreate();
    expect(clearCacheWithPrefix).toHaveBeenCalledWith(mockFastify.redis, 'organisation');

    clearCacheWithPrefix.mockClear();

    options.hooks.afterUpdate();
    expect(clearCacheWithPrefix).toHaveBeenCalledWith(mockFastify.redis, 'organisation');
  });
});
