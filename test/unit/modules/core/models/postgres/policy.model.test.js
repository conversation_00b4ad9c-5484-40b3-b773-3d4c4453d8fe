import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { auditableMixin } from '#src/mixins/index.js';
import PolicyModel from '#src/modules/core/models/postgres/policy.model.js';

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
}));

describe('Policy Model', () => {
  let mockFastify;
  let Policy;

  beforeEach(() => {
    vi.resetAllMocks();
    // Mock Sequelize methods
    Model.init = vi.fn();
    Sequelize.literal = vi.fn().mockReturnValue('MOCKED_UUID_FUNCTION');
    // Mock Fastify instance
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };
    // Initialize the model
    Policy = PolicyModel(mockFastify);
  });

  it('should initialize with correct attributes', () => {
    const initCall = Model.init.mock.calls[0];
    const attributes = initCall[0];
    const options = initCall[1];

    // Check attributes
    expect(attributes.id.type).toBe(DataTypes.UUID);
    expect(attributes.id.primaryKey).toBe(true);
    expect(attributes.id.defaultValue).toBe('MOCKED_UUID_FUNCTION');

    expect(attributes.parentId.type).toBe(DataTypes.UUID);
    expect(attributes.parentId.allowNull).toBe(false);
    expect(attributes.parentId.defaultValue).toBe('MOCKED_UUID_FUNCTION');
    expect(attributes.parentId.comment).toBe(
      'Reference to the parents (e.g., modules, department_module_policies)',
    );

    const booleanFields = [
      'canView',
      'canCreate',
      'canEdit',
      'canImport',
      'canExport',
      'canManage',
      'canMasking',
      'canOverwrite',
      'canVerify',
    ];
    booleanFields.forEach((field) => {
      expect(attributes[field].type).toBe(DataTypes.BOOLEAN);
      expect(attributes[field].allowNull).toBe(false);
      expect(attributes[field].defaultValue).toBe(false);
      const commentField = field.replace('can', '').toLowerCase();
      expect(attributes[field].comment).toBe(`The ${commentField} policy`);
    });

    expect(attributes.createdBy.type).toBe(DataTypes.UUID);
    expect(attributes.createdBy.allowNull).toBe(true);

    expect(attributes.updatedBy.type).toBe(DataTypes.UUID);
    expect(attributes.updatedBy.allowNull).toBe(true);

    // Check options
    expect(options.modelName).toBe('Policy');
    expect(options.tableName).toBe('policies');
    expect(options.underscored).toBe(true);
    expect(options.timestamps).toBe(true);
    expect(options.sequelize).toBe(mockFastify.psql.connection);

    // Check indexes
    expect(options.indexes).toEqual([
      {
        unique: true,
        fields: ['parent_id'],
      },
    ]);

    // Check scopes
    expect(options.scopes).toEqual({});
  });

  it('should apply auditable mixin', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(Policy);
  });

  it('should return the Policy class', () => {
    expect(Policy).toBeDefined();
    expect(Policy.prototype instanceof Model).toBe(true);
  });

  it('should call Model.init with correct parameters', () => {
    expect(Model.init).toHaveBeenCalledTimes(1);
    expect(Model.init).toHaveBeenCalledWith(expect.any(Object), expect.any(Object));
  });

  it('should use Sequelize.literal for UUID default values', () => {
    expect(Sequelize.literal).toHaveBeenCalledTimes(2);
    expect(Sequelize.literal).toHaveBeenCalledWith('uuid_generate_v1mc()');
  });
});
