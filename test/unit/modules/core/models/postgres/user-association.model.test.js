import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import UserAssociationModel from '#src/modules/core/models/postgres/user-association.model.js';

vi.mock('#src/modules/user/constants/index.js', () => ({
  UserConstant: {
    USER_ORIGINS: {
      INTERNAL: 'internal',
      EXTERNAL: 'external',
    },
  },
}));

vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('UserAssociation Model', () => {
  let mockFastify;
  let UserAssociation;

  beforeEach(() => {
    vi.resetAllMocks();

    Model.init = vi.fn();
    Model.hasMany = vi.fn();
    Model.belongsTo = vi.fn();
    Model.hasOne = vi.fn();

    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };

    UserAssociation = UserAssociationModel(mockFastify);
  });

  it('should define associations correctly', () => {
    const mockModels = {
      UserAssociationCurrency: {},
    };

    UserAssociation.associate(mockModels);

    expect(Model.hasMany).toHaveBeenCalledWith(mockModels.UserAssociationCurrency, {
      foreignKey: 'userAssociationId',
      as: 'uac',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });

    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });

    expect(Model.hasOne).toHaveBeenCalledWith(mockModels.UserInvitation, {
      foreignKey: 'userAssociationId',
      as: 'ui',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  describe('Model initialization', () => {
    let fields;
    let options;

    beforeEach(() => {
      [fields, options] = Model.init.mock.calls[0];
    });

    it('should set correct model and table names', () => {
      expect(options.modelName).toBe('UserAssociation');
      expect(options.tableName).toBe('user_associations');
    });

    it('should enable timestamps and use underscored naming', () => {
      expect(options.timestamps).toBe(true);
      expect(options.underscored).toBe(true);
      expect(options.paranoid).toBe(false);
    });

    it('should use the correct sequelize connection', () => {
      expect(options.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct fields', () => {
      expect(fields).toHaveProperty('id');
      expect(fields).toHaveProperty('entityId');
      expect(fields).toHaveProperty('userId');
      expect(fields).toHaveProperty('origin');
      expect(fields).toHaveProperty('roleId');
      expect(fields).toHaveProperty('createdBy');
      expect(fields).toHaveProperty('updatedBy');
    });

    it('should define id field correctly', () => {
      expect(fields.id.type).toBe(DataTypes.UUID);
      expect(fields.id.defaultValue).toEqual(Sequelize.literal('uuid_generate_v1mc()'));
      expect(fields.id.primaryKey).toBe(true);
    });

    it('should require entityId, userId, roleId', () => {
      expect(fields.entityId.allowNull).toBe(false);
      expect(fields.userId.allowNull).toBe(false);
      expect(fields.roleId.allowNull).toBe(false);
    });

    it('should define origin field with correct enum and default', () => {
      expect(fields.origin.type).toBeInstanceOf(DataTypes.ENUM);
      expect(fields.origin.allowNull).toBe(false);
      expect(fields.origin.defaultValue).toBe('internal');
    });

    it('should allow null for createdBy and updatedBy', () => {
      expect(fields.createdBy.allowNull).toBe(true);
      expect(fields.updatedBy.allowNull).toBe(true);
    });

    it('should define unique index on userId and entityId', () => {
      expect(options.indexes).toEqual([
        {
          unique: true,
          fields: ['userId', 'entityId'],
        },
      ]);
    });

    describe('Virtual field: currencyCodes', () => {
      it('should return currency codes from associated uac', () => {
        const instance = {
          getDataValue: vi.fn().mockReturnValue([{ currencyCode: 'USD' }, { currencyCode: 'EUR' }]),
        };

        const codeGetter = Model.init.mock.calls[0][0].currencyCodes.get;
        const result = codeGetter.call(instance);

        expect(instance.getDataValue).toHaveBeenCalledWith('uac');
        expect(result).toEqual(['USD', 'EUR']);
      });

      it('should return empty array when uac is undefined or empty', () => {
        const noUacInstance = {
          getDataValue: vi.fn().mockReturnValue(undefined),
        };

        const emptyUacInstance = {
          getDataValue: vi.fn().mockReturnValue([]),
        };

        const codeGetter = Model.init.mock.calls[0][0].currencyCodes.get;

        expect(codeGetter.call(noUacInstance)).toEqual([]);
        expect(codeGetter.call(emptyUacInstance)).toEqual([]);
      });
    });

    it('should have auditable and versioned mixins available', async () => {
      const { auditableMixin, __mocks } = await import('#src/mixins/index.js');
      expect(auditableMixin.applyAuditFields).toBeDefined();

      auditableMixin.applyAuditFields(UserAssociation);
      expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(UserAssociation);
    });
  });
});
