import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import UserMfaSettingModel from '#src/modules/core/models/postgres/user-mfa-setting.model.js';

vi.mock('#src/modules/user/constants/index.js', () => ({
  UserConstant: {
    USER_MFA_STATUSES: {
      ENABLED: 'enabled',
      DISABLED: 'disabled',
    },
    USER_MFA_SETUP_STATUSES: {
      ACTIVE: 'active',
      INACTIVE: 'inactive',
    },
  },
}));

vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('UserMfaSetting Model', () => {
  let mockFastify;
  let UserMfaSetting;

  beforeEach(() => {
    vi.resetAllMocks();
    Model.init = vi.fn();
    Model.belongsTo = vi.fn();

    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };

    UserMfaSetting = UserMfaSettingModel(mockFastify);
  });

  it('should define associations correctly', () => {
    const mockModels = { User: {} };

    UserMfaSetting.associate(mockModels);

    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.User, {
      foreignKey: 'userId',
      as: 'mfa',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  describe('Model initialization', () => {
    let fields, options;

    beforeEach(() => {
      [fields, options] = Model.init.mock.calls[0];
    });

    it('should define the correct model and table names', () => {
      expect(options.modelName).toBe('UserMfaSetting');
      expect(options.tableName).toBe('user_mfa_settings');
    });

    it('should use timestamps and underscored option', () => {
      expect(options.timestamps).toBe(true);
      expect(options.underscored).toBe(true);
      expect(options.paranoid).toBe(false);
    });

    it('should use the correct sequelize connection', () => {
      expect(options.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct fields', () => {
      expect(fields).toHaveProperty('id');
      expect(fields).toHaveProperty('userId');
      expect(fields).toHaveProperty('secretKey');
      expect(fields).toHaveProperty('status');
      expect(fields).toHaveProperty('createdBy');
      expect(fields).toHaveProperty('updatedBy');
    });

    it('should define id as UUID with default value and primary key', () => {
      expect(fields.id.type).toBe(DataTypes.UUID);
      expect(fields.id.defaultValue).toEqual(Sequelize.literal('uuid_generate_v1mc()'));
      expect(fields.id.primaryKey).toBe(true);
    });

    it('should define secretKey field with constraints', () => {
      expect(fields.secretKey.allowNull).toBe(false);
      expect(fields.secretKey.unique).toBe(true);
      expect(fields.secretKey.validate.notEmpty).toBe(true);
    });

    it('should define status as enum and required', () => {
      expect(fields.status.type).toBeInstanceOf(DataTypes.ENUM);
      expect(fields.status.allowNull).toBe(false);
    });

    it('should define setup status as enum and required', () => {
      expect(fields.setupStatus.type).toBeInstanceOf(DataTypes.ENUM);
      expect(fields.setupStatus.allowNull).toBe(false);
    });

    it('should allow null for createdBy and updatedBy', () => {
      expect(fields.createdBy.allowNull).toBe(true);
      expect(fields.updatedBy.allowNull).toBe(true);
    });

    it('should call applyAuditFields mixin', async () => {
      const { auditableMixin, __mocks } = await import('#src/mixins/index.js');
      expect(auditableMixin.applyAuditFields).toBeDefined();
      auditableMixin.applyAuditFields(UserMfaSetting);
      expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(UserMfaSetting);
    });
  });
});
