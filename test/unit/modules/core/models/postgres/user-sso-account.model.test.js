import { Model } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import defineUserSSOAccount from '#src/modules/core/models/postgres/user-sso-account.model.js';

vi.mock('#src/modules/user/constants/index.js', () => ({
  UserConstant: {
    USER_SSO_STATUSES: {
      PENDING: 'pending',
      COMPLETED: 'completed',
      CANCELLED: 'cancelled',
    },
  },
}));

vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('UserSSOAccount Model', () => {
  let mockFastify;
  let UserSSOAccount;

  beforeEach(() => {
    vi.resetAllMocks();

    Model.init = vi.fn();
    Model.belongsTo = vi.fn();

    mockFastify = {
      psql: {
        connection: {},
      },
    };

    UserSSOAccount = defineUserSSOAccount(mockFastify);
  });

  it('should define associations correctly', () => {
    const mockModels = {
      User: {},
    };

    UserSSOAccount.associate(mockModels);

    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  describe('Model Initialization', () => {
    let fields, options;

    beforeEach(() => {
      [fields, options] = Model.init.mock.calls[0];
    });

    it('should define correct model/table config', () => {
      expect(options.modelName).toBe('UserSSOAccount');
      expect(options.tableName).toBe('user_sso_accounts');
      expect(options.timestamps).toBe(true);
      expect(options.paranoid).toBe(false);
      expect(options.underscored).toBe(true);
      expect(options.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define all required fields', () => {
      expect(fields).toHaveProperty('id');
      expect(fields).toHaveProperty('userId');
      expect(fields).toHaveProperty('email');
      expect(fields).toHaveProperty('sub');
      expect(fields).toHaveProperty('status');
      expect(fields).toHaveProperty('completedDate');
      expect(fields).toHaveProperty('cancelledDate');
    });

    it('should enforce constraints properly', () => {
      expect(fields.email.allowNull).toBe(false);
      expect(fields.email.unique).toBe(true);

      expect(fields.sub.allowNull).toBe(false);
      expect(fields.sub.unique).toBe(true);

      expect(fields.status.defaultValue).toBe('pending');
      expect(fields.status.type.values).toEqual(['pending', 'completed', 'cancelled']);
    });

    it('should have auditable mixin applied', async () => {
      const { auditableMixin, __mocks } = await import('#src/mixins/index.js');
      expect(auditableMixin.applyAuditFields).toBeDefined();
      auditableMixin.applyAuditFields(UserSSOAccount);
      expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(UserSSOAccount);
    });
  });
});
