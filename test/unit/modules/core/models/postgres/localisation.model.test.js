import { Model } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import LocalisationModel from '#src/modules/core/models/postgres/localisation.model.js';

describe('Localisation Model', () => {
  let mockFastify;
  let Localisation;

  beforeEach(() => {
    vi.resetAllMocks();
    // Mock Sequelize methods
    Model.init = vi.fn();
    Model.hasMany = vi.fn();
    // Mock Fastify instance
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };
    // Initialize the model
    Localisation = LocalisationModel(mockFastify);
  });

  it('should define correct associations', () => {
    const mockModels = {
      CustomLocalisation: {},
    };
    Localisation.associate(mockModels);
    expect(Model.hasMany).toHaveBeenCalledWith(mockModels.CustomLocalisation, {
      foreignKey: 'parentId',
      as: 'customLocalisation',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  });
});
