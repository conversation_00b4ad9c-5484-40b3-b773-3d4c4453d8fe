import { Model } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import UserModel from '#src/modules/core/models/postgres/user.model.js';
vi.mock('#src/modules/user/constants/index.js', () => ({
  UserConstant: {
    USER_TYPES: { NORMAL: 'normal', ADMIN: 'admin' },
    USER_STATUSES: { ACTIVE: 'active', INACTIVE: 'inactive' },
  },
}));

vi.mock('#src/modules/user/errors/index.js', () => ({
  CoreError: {
    futureDateOnly: (field) => new Error(`${field} must be in the future`),
  },
}));

vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('User Model', () => {
  let mockFastify;
  let User;

  beforeEach(() => {
    vi.resetAllMocks();

    Model.init = vi.fn();
    Model.hasMany = vi.fn();
    Model.hasOne = vi.fn();
    Model.belongsTo = vi.fn();

    mockFastify = {
      psql: {
        connection: {},
      },
    };

    User = UserModel(mockFastify);
  });

  it('should define associations correctly', () => {
    const mockModels = {
      UserAssociation: {},
      UserMfaSetting: {},
      User: {},
    };

    User.associate(mockModels);

    expect(Model.hasMany).toHaveBeenCalledWith(mockModels.UserAssociation, {
      foreignKey: 'userId',
      as: 'ua',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });

    expect(Model.hasOne).toHaveBeenCalledWith(mockModels.UserMfaSetting, {
      foreignKey: 'userId',
      as: 'mfa',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });

    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.User, {
      foreignKey: 'parentId',
      as: 'parent',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  describe('Model Initialization', () => {
    let fields, options;

    beforeEach(() => {
      [fields, options] = Model.init.mock.calls[0];
    });

    it('should define correct model/table config', () => {
      expect(options.modelName).toBe('User');
      expect(options.tableName).toBe('users');
      expect(options.timestamps).toBe(true);
      expect(options.paranoid).toBe(false);
      expect(options.underscored).toBe(true);
      expect(options.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define all required fields', () => {
      expect(fields).toHaveProperty('id');
      expect(fields).toHaveProperty('username');
      expect(fields).toHaveProperty('password');
      expect(fields).toHaveProperty('email');
      expect(fields).toHaveProperty('type');
      expect(fields).toHaveProperty('status');
      expect(fields).toHaveProperty('validityDate');
    });

    it('should enforce required and unique constraints properly', () => {
      expect(fields.username.allowNull).toBe(false);
      expect(fields.username.unique).toBe(true);

      expect(fields.password.allowNull).toBe(false);

      expect(fields.email.allowNull).toBe(true);
      expect(fields.email.unique).toBe(true);
    });

    it('should define ENUMs with correct defaults', () => {
      expect(fields.type.defaultValue).toBe('normal');
      expect(fields.status.defaultValue).toBe('active');
    });

    describe('Virtual field: userAssociation', () => {
      it('should return parent.ua if parent exists', () => {
        const instance = {
          parent: {
            ua: 'parent-association',
          },
          ua: 'self-association',
        };

        const getter = Model.init.mock.calls[0][0].userAssociation.get;
        const result = getter.call(instance);

        expect(result).toBe('parent-association');
      });

      it('should return ua if parent does not exist', () => {
        const instance = {
          parent: null,
          ua: 'self-association',
        };

        const getter = Model.init.mock.calls[0][0].userAssociation.get;
        const result = getter.call(instance);

        expect(result).toBe('self-association');
      });
    });

    it('should set up the hook: beforeCreate - future validityDate only', async () => {
      const hook = options.hooks.beforeCreate;

      const futureDate = new Date(Date.now() + 86400000);
      const pastDate = new Date(Date.now() - 86400000);

      await expect(hook({ validityDate: futureDate }, {})).resolves.toBeUndefined();

      await expect(hook({ validityDate: pastDate }, {})).rejects.toThrow(
        'error.validation.sentence.futureDateOnly',
      );
    });

    it('should set up the hook: beforeUpdate - future validityDate only', async () => {
      const hook = options.hooks.beforeUpdate;

      const futureDate = new Date(Date.now() + 86400000);
      const pastDate = new Date(Date.now() - 86400000);

      await expect(hook({ validityDate: futureDate }, {})).resolves.toBeUndefined();

      await expect(hook({ validityDate: pastDate }, {})).rejects.toThrow(
        'error.validation.sentence.futureDateOnly',
      );
    });

    it('should have auditable and versioned mixins available', async () => {
      const { auditableMixin, __mocks } = await import('#src/mixins/index.js');
      expect(auditableMixin.applyAuditFields).toBeDefined();

      auditableMixin.applyAuditFields(User);
      expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(User);
    });
  });
});
