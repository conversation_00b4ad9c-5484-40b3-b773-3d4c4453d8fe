import { beforeEach, describe, expect, it, vi } from 'vitest';

import { AddressRepository } from '#src/modules/core/repository/index.js';

describe('Address Repository', () => {
  let mockServer;

  beforeEach(() => {
    mockServer = {
      psql: {
        Address: {
          create: vi.fn().mockResolvedValue({ id: 1, street: '123 Main St' }),
          findOrCreate: vi.fn().mockResolvedValue([{ id: 1, street: '123 Main St' }, true]),
        },
      },
    };

    vi.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new address record', async () => {
      const addressData = { street: '123 Main St', city: 'Test City', country: 'Test Country' };
      const options = { transaction: 'mockTransaction' };
      const result = await AddressRepository.create(mockServer, addressData, options);

      expect(mockServer.psql.Address.create).toHaveBeenCalledWith(addressData, options);
      expect(result).toEqual({ id: 1, street: '123 Main St' });
    });

    it('should create a new address record with default options', async () => {
      const addressData = { street: '123 Main St', city: 'Test City', country: 'Test Country' };
      const result = await AddressRepository.create(mockServer, addressData);

      expect(mockServer.psql.Address.create).toHaveBeenCalledWith(addressData, {});
      expect(result).toEqual({ id: 1, street: '123 Main St' });
    });
  });

  describe('findOrCreate', () => {
    it('should find or create an address record', async () => {
      const query = {
        where: { street: '123 Main St' },
        defaults: { city: 'Test City', country: 'Test Country' },
      };
      const options = { transaction: 'mockTransaction' };
      const result = await AddressRepository.findOrCreate(mockServer, query, options);

      expect(mockServer.psql.Address.findOrCreate).toHaveBeenCalledWith({
        ...query,
        ...options,
      });
      expect(result).toEqual([{ id: 1, street: '123 Main St' }, true]);
    });

    it('should find or create an address record with default options', async () => {
      const query = {
        where: { street: '123 Main St' },
        defaults: { city: 'Test City', country: 'Test Country' },
      };
      const result = await AddressRepository.findOrCreate(mockServer, query);

      expect(mockServer.psql.Address.findOrCreate).toHaveBeenCalledWith({
        ...query,
      });
      expect(result).toEqual([{ id: 1, street: '123 Main St' }, true]);
    });
  });
});
