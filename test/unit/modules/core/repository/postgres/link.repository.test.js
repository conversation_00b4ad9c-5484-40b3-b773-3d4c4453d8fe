import { beforeEach, describe, expect, it, vi } from 'vitest';

import { LinkRepository } from '#src/modules/core/repository/index.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/query.util.js');
describe('Link Repository', () => {
  let mockServer;
  const mockWhereFilter = { mock: 'where' };
  const mockIncludeFilter = [{ mock: 'include' }];

  beforeEach(() => {
    mockServer = {
      psql: {
        Link: {
          create: vi.fn().mockResolvedValue({ id: 1, url: 'https://example.com' }),
          findOrCreate: vi.fn().mockResolvedValue([{ id: 1, url: 'https://example.com' }, true]),
          findOne: vi
            .fn()
            .mockResolvedValue({ id: 1, url: 'https://example.com', type: 'external' }),
        },
      },
    };
    buildWhereFromFilters.mockReturnValue({
      where: mockWhereFilter,
      include: mockIncludeFilter,
    });

    vi.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new link record', async () => {
      const linkData = { url: 'https://example.com', title: 'Example Link', type: 'external' };
      const options = { transaction: 'mockTransaction' };
      const result = await LinkRepository.create(mockServer, linkData, options);

      expect(mockServer.psql.Link.create).toHaveBeenCalledWith(linkData, options);
      expect(result).toEqual({ id: 1, url: 'https://example.com' });
    });

    it('should create a new link record with default options', async () => {
      const linkData = { url: 'https://example.com', title: 'Example Link', type: 'external' };
      const result = await LinkRepository.create(mockServer, linkData);

      expect(mockServer.psql.Link.create).toHaveBeenCalledWith(linkData, {});
      expect(result).toEqual({ id: 1, url: 'https://example.com' });
    });
  });

  describe('findOrCreate', () => {
    it('should find or create a link record', async () => {
      const query = {
        where: { url: 'https://example.com' },
        defaults: { title: 'Example Link', type: 'external' },
      };
      const options = { transaction: 'mockTransaction' };
      const result = await LinkRepository.findOrCreate(mockServer, query, options);

      expect(mockServer.psql.Link.findOrCreate).toHaveBeenCalledWith({
        ...query,
        ...options,
      });
      expect(result).toEqual([{ id: 1, url: 'https://example.com' }, true]);
    });

    it('should find or create a link record with default options', async () => {
      const query = {
        where: { url: 'https://example.com' },
        defaults: { title: 'Example Link', type: 'external' },
      };
      const result = await LinkRepository.findOrCreate(mockServer, query);

      expect(mockServer.psql.Link.findOrCreate).toHaveBeenCalledWith({
        ...query,
      });
      expect(result).toEqual([{ id: 1, url: 'https://example.com' }, true]);
    });
  });

  describe('findOne', () => {
    it('should find a single link record', async () => {
      const query = { filter_type_eq: 'external' };
      const options = { raw: true };

      const result = await LinkRepository.findOne(mockServer, query, options);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(query, mockServer.psql.Link);
      expect(mockServer.psql.Link.findOne).toHaveBeenCalledWith({
        where: mockWhereFilter,
        include: mockIncludeFilter,
        raw: true,
      });
      expect(result).toEqual({ id: 1, url: 'https://example.com', type: 'external' });
    });

    it('should find a single link record with default options', async () => {
      const query = { filter_type_eq: 'email_domain' };

      const result = await LinkRepository.findOne(mockServer, query);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(query, mockServer.psql.Link);
      expect(mockServer.psql.Link.findOne).toHaveBeenCalledWith({
        where: mockWhereFilter,
        include: mockIncludeFilter,
      });
      expect(result).toEqual({ id: 1, url: 'https://example.com', type: 'external' });
    });

    it('should return null when no link is found', async () => {
      const query = { filter_type_eq: 'not_found' };
      mockServer.psql.Link.findOne.mockResolvedValueOnce(null);

      const result = await LinkRepository.findOne(mockServer, query);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(query, mockServer.psql.Link);
      expect(mockServer.psql.Link.findOne).toHaveBeenCalledWith({
        where: mockWhereFilter,
        include: mockIncludeFilter,
      });
      expect(result).toBeNull();
    });
  });
});
