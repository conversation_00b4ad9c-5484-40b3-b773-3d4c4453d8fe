import { Op } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import * as moduleRepository from '#src/modules/core/repository/postgres/module.repository.js';

describe('Module Repository', () => {
  let mockServer;

  beforeEach(() => {
    mockServer = {
      psql: {
        Module: {
          findByPk: vi.fn(),
          findAll: vi.fn(),
        },
        Policy: {},
      },
    };
  });

  describe('findById', () => {
    it('should call findByPk with correct parameters', async () => {
      const id = '123';
      const options = { include: ['someAssociation'] };

      await moduleRepository.findById(mockServer, id, options);

      expect(mockServer.psql.Module.findByPk).toHaveBeenCalledWith(id, options);
    });

    it('should use empty options if not provided', async () => {
      await moduleRepository.findById(mockServer, '123');

      expect(mockServer.psql.Module.findByPk).toHaveBeenCalledWith('123', {});
    });
  });

  describe('findByIds', () => {
    it('should call findAll with correct parameters', async () => {
      const moduleIds = ['id1', 'id2', 'id3'];
      const options = { attributes: ['id', 'name'] };

      mockServer.psql.Module.findAll = vi.fn().mockResolvedValue([
        { id: 'id1', name: 'Module 1' },
        { id: 'id3', name: 'Module 3' },
      ]);

      const result = await moduleRepository.findByIds(mockServer, moduleIds, options);

      expect(mockServer.psql.Module.findAll).toHaveBeenCalledWith({
        where: {
          id: {
            [Op.in]: moduleIds,
          },
        },
        ...options,
      });
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('id1');
      expect(result[1].id).toBe('id3');
    });

    it('should return empty array when moduleIds is empty', async () => {
      const result = await moduleRepository.findByIds(mockServer, []);

      expect(mockServer.psql.Module.findAll).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it('should return empty array when moduleIds is null', async () => {
      const result = await moduleRepository.findByIds(mockServer, null);

      expect(mockServer.psql.Module.findAll).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });
  });

  describe('findModulePolicies', () => {
    it('should call findAll with correct parameters', async () => {
      const hierarchies = ['ROOT', 'ORGANISATION'];

      await moduleRepository.findModulePolicies(mockServer, hierarchies);

      expect(mockServer.psql.Module.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            parentId: null,
            hierarchy: {
              [Op.in]: hierarchies,
            },
          },
          order: expect.any(Array),
          include: expect.any(Array),
        }),
      );
    });

    it('should include Policy and nested Modules', async () => {
      await moduleRepository.findModulePolicies(mockServer, ['ROOT']);

      const callArgs = mockServer.psql.Module.findAll.mock.calls[0][0];

      expect(callArgs.include).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ as: 'policy' }),
          expect.objectContaining({
            as: 'children',
            include: expect.arrayContaining([
              expect.objectContaining({ as: 'policy' }),
              expect.objectContaining({ as: 'children' }),
            ]),
          }),
        ]),
      );
    });
  });

  describe('findAll', () => {
    it('should call findAll with correct parameters', async () => {
      const options = { where: { someField: 'someValue' } };

      await moduleRepository.findAll(mockServer, options);

      expect(mockServer.psql.Module.findAll).toHaveBeenCalledWith({
        ...options,
        include: [
          {
            model: mockServer.psql.Policy,
            as: 'policy',
            required: false,
          },
        ],
        order: [['navigationPosition', 'ASC']],
      });
    });

    it('should use empty options if not provided', async () => {
      await moduleRepository.findAll(mockServer);

      expect(mockServer.psql.Module.findAll).toHaveBeenCalledWith({
        include: [
          {
            model: mockServer.psql.Policy,
            as: 'policy',
            required: false,
          },
        ],
        order: [['navigationPosition', 'ASC']],
      });
    });
  });
});
