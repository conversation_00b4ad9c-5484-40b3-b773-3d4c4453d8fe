import { beforeEach, describe, expect, it, vi } from 'vitest';

import { REMARK_STATUSES } from '#src/modules/core/constants/core.constant.js';
import * as remarkRepository from '#src/modules/core/repository/postgres/remark.repository.js';

describe('Remark Repository', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      psql: {
        Remark: {
          create: vi.fn(),
          findOne: vi.fn(),
        },
      },
    };
  });

  describe('create', () => {
    it('should create a new remark', async () => {
      const mockData = { content: 'Test remark' };
      const mockOptions = { transaction: {} };
      const mockCreatedRemark = { id: 1, ...mockData };

      fastifyMock.psql.Remark.create.mockResolvedValue(mockCreatedRemark);

      const result = await remarkRepository.create(fastifyMock, mockData, mockOptions);

      expect(fastifyMock.psql.Remark.create).toHaveBeenCalledWith(mockData, mockOptions);
      expect(result).toEqual(mockCreatedRemark);
    });
  });

  describe('findActiveByRemarkable', () => {
    it('should find an active remark for a given remarkable entity', async () => {
      const mockRemarkableId = '123';
      const mockRemarkableType = 'user';
      const mockOptions = { include: [] };
      const mockRemark = { id: 1, status: REMARK_STATUSES.ACTIVE };

      fastifyMock.psql.Remark.findOne.mockResolvedValue(mockRemark);

      const result = await remarkRepository.findActiveByRemarkable(
        fastifyMock,
        mockRemarkableId,
        mockRemarkableType,
        mockOptions,
      );

      expect(fastifyMock.psql.Remark.findOne).toHaveBeenCalledWith({
        where: {
          remarkableId: mockRemarkableId,
          remarkableType: mockRemarkableType,
          status: REMARK_STATUSES.ACTIVE,
        },
        ...mockOptions,
      });
      expect(result).toEqual(mockRemark);
    });

    it('should use an empty object as default options', async () => {
      const mockRemarkableId = '123';
      const mockRemarkableType = 'user';
      const mockRemark = { id: 1, status: REMARK_STATUSES.ACTIVE };

      fastifyMock.psql.Remark.findOne.mockResolvedValue(mockRemark);

      await remarkRepository.findActiveByRemarkable(
        fastifyMock,
        mockRemarkableId,
        mockRemarkableType,
      );

      expect(fastifyMock.psql.Remark.findOne).toHaveBeenCalledWith({
        where: {
          remarkableId: mockRemarkableId,
          remarkableType: mockRemarkableType,
          status: REMARK_STATUSES.ACTIVE,
        },
      });
    });
  });

  describe('archive', () => {
    it('should archive a remark by updating its status', async () => {
      const mockRemark = {
        id: 1,
        status: REMARK_STATUSES.ACTIVE,
        update: vi.fn(),
      };
      const mockOptions = { transaction: {} };
      const mockUpdatedRemark = { ...mockRemark, status: REMARK_STATUSES.ARCHIVED };

      mockRemark.update.mockResolvedValue(mockUpdatedRemark);

      const result = await remarkRepository.archive(mockRemark, mockOptions);

      expect(mockRemark.update).toHaveBeenCalledWith(
        { status: REMARK_STATUSES.ARCHIVED },
        mockOptions,
      );
      expect(result).toEqual(mockUpdatedRemark);
    });
  });
});
