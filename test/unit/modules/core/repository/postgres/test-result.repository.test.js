import { describe, expect, it, vi } from 'vitest';

import { createTestResult } from '#src/modules/core/repository/postgres/test-result.repository.js';

describe('createTestResult', () => {
  it('should call server.psql.TestResult.create with provided data and return result', async () => {
    const mockData = { name: 'Unit Test', passed: true };
    const mockOptions = { transaction: {} };
    const mockResult = { id: 1, ...mockData };

    const server = {
      psql: {
        TestResult: {
          create: vi.fn().mockResolvedValue(mockResult),
        },
      },
    };

    const result = await createTestResult(server, mockData, mockOptions);

    expect(server.psql.TestResult.create).toHaveBeenCalledOnce();
    expect(server.psql.TestResult.create).toHaveBeenCalledWith(mockData, mockOptions);
    expect(result).toEqual(mockResult);
  });

  it('should use default options if not provided', async () => {
    const mockData = { name: 'No Options Test' };
    const mockResult = { id: 2, ...mockData };

    const server = {
      psql: {
        TestResult: {
          create: vi.fn().mockResolvedValue(mockResult),
        },
      },
    };

    const result = await createTestResult(server, mockData);

    expect(server.psql.TestResult.create).toHaveBeenCalledWith(mockData, {});
    expect(result).toEqual(mockResult);
  });
});
