import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('#src/utils/pagination.util.js', () => ({
  applyOffsetPagination: vi.fn().mockResolvedValue({
    rows: [{ id: '123', name: 'Test Entity' }],
    count: 1,
  }),
}));

vi.mock('#src/utils/query.util.js', () => ({
  buildWhereFromFilters: vi.fn().mockImplementation((query) => {
    return {
      where: { accessId: query.filter_accessId_eq || query.filter_name_eq || 'test' },
      include: [],
    };
  }),
}));

import * as EntityRepository from '#src/modules/core/repository/postgres/entity.repository.js';
import { LOCALISATION_CATEGORIES } from '#src/modules/setting/constants/localisation.constant.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import * as QueryUtil from '#src/utils/query.util.js';

const { CURRENCY } = LOCALISATION_CATEGORIES;

describe('Entity Repository', () => {
  let mockFastify;
  let mockQuery;
  let mockWhereFilter;
  let mockIncludeFilter;
  let mockModelData;
  let mockEntityData;
  let mockNewEntity;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Ensure the mock returns the expected structure for each test
    QueryUtil.buildWhereFromFilters.mockReturnValue({
      where: { name: 'test' },
      include: [{ model: 'TestModel' }],
    });

    // Mock Fastify instance
    mockFastify = {
      psql: {
        Entity: {
          findOne: vi.fn(),
          findAll: vi.fn(),
          create: vi.fn(),
          count: vi.fn(),
        },
        Address: {},
        Link: {},
        CustomLocalisation: {},
        Localisation: {},
      },
    };

    // Mock query parameters
    mockQuery = {
      filter_name_eq: 'test',
      page: 1,
      limit: 10,
    };

    // Mock filter results
    mockWhereFilter = { name: 'test' };
    mockIncludeFilter = [{ model: 'TestModel' }];

    // Mock model data for update operations
    mockModelData = {
      update: vi.fn().mockResolvedValue({ id: '123', name: 'updated' }),
    };

    // Mock entity data for create operation
    mockEntityData = {
      name: 'Test Entity',
      code: 'TEST',
      status: 'active',
    };

    // Mock new entity result
    mockNewEntity = {
      id: '123',
      name: 'Test Entity',
      code: 'TEST',
      status: 'active',
      toJSON: vi.fn().mockReturnValue({
        id: '123',
        name: 'Test Entity',
        code: 'TEST',
        status: 'active',
      }),
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('findAll', () => {
    it('should call buildWhereFromFilters with correct parameters', async () => {
      await EntityRepository.findAll(mockFastify, mockQuery);

      expect(QueryUtil.buildWhereFromFilters).toHaveBeenCalledWith(
        mockQuery,
        mockFastify.psql.Entity,
        [
          {
            model: mockFastify.psql.CustomLocalisation,
            as: 'custom_localisations',
            required: false,
            where: {
              status: 'active',
            },
            attributes: ['id', 'name', 'code'],
            include: [
              {
                model: mockFastify.psql.Localisation,
                as: 'localisation',
                required: true,
                where: {
                  category: CURRENCY,
                },
              },
            ],
          },
        ],
      );
    });

    it('should call applyOffsetPagination with correct parameters', async () => {
      await EntityRepository.findAll(mockFastify, mockQuery);

      expect(applyOffsetPagination).toHaveBeenCalledWith(
        mockFastify,
        mockFastify.psql.Entity,
        mockQuery,
        mockWhereFilter,
        mockIncludeFilter,
      );
    });
  });

  describe('findById', () => {
    it('should call buildWhereFromFilters with correct parameters', async () => {
      await EntityRepository.findById(mockFastify, mockQuery);

      expect(QueryUtil.buildWhereFromFilters).toHaveBeenCalledWith(
        mockQuery,
        mockFastify.psql.Entity,
        expect.arrayContaining([
          expect.objectContaining({ as: 'addresses' }),
          expect.objectContaining({ as: 'links' }),
          expect.objectContaining({ as: 'custom_localisations' }),
        ]),
      );
    });

    it('should call Entity.findOne with correct parameters', async () => {
      mockFastify.psql.Entity.findOne.mockResolvedValue({ id: '123', name: 'Test Entity' });

      await EntityRepository.findById(mockFastify, mockQuery);

      expect(mockFastify.psql.Entity.findOne).toHaveBeenCalledWith({
        where: mockWhereFilter,
        include: mockIncludeFilter,
      });
    });

    it('should return the found entity', async () => {
      const mockEntity = { id: '123', name: 'Test Entity' };
      mockFastify.psql.Entity.findOne.mockResolvedValue(mockEntity);

      const result = await EntityRepository.findById(mockFastify, mockQuery);

      expect(result).toEqual(mockEntity);
    });

    it('should return null if entity is not found', async () => {
      mockFastify.psql.Entity.findOne.mockResolvedValue(null);

      const result = await EntityRepository.findById(mockFastify, mockQuery);

      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should call Entity.create with correct parameters', async () => {
      const entityId = '456';
      mockFastify.psql.Entity.create.mockResolvedValue(mockNewEntity);

      await EntityRepository.create(mockFastify, entityId, mockEntityData);

      expect(mockFastify.psql.Entity.create).toHaveBeenCalledWith(
        {
          ...mockEntityData,
          entityId,
        },
        {},
      );
    });

    it('should return the created entity as JSON', async () => {
      const entityId = '456';
      mockFastify.psql.Entity.create.mockResolvedValue(mockNewEntity);

      const result = await EntityRepository.create(mockFastify, entityId, mockEntityData);

      expect(mockNewEntity.toJSON).toHaveBeenCalled();
      expect(result).toEqual({
        id: '123',
        name: 'Test Entity',
        code: 'TEST',
        status: 'active',
      });
    });
  });

  describe('update', () => {
    it('should call model.update with correct parameters', async () => {
      const updateData = { name: 'Updated Name' };

      await EntityRepository.update(mockModelData, updateData);

      expect(mockModelData.update).toHaveBeenCalledWith(updateData, {});
    });

    it('should return the updated entity', async () => {
      const updateData = { name: 'Updated Name' };

      const result = await EntityRepository.update(mockModelData, updateData);

      expect(result).toEqual({ id: '123', name: 'updated' });
    });
  });

  describe('updateStatus', () => {
    it('should call model.update with status parameter', async () => {
      const status = 'inactive';

      await EntityRepository.update(mockModelData, status);

      expect(mockModelData.update).toHaveBeenCalledWith('inactive', {});
    });

    it('should return the updated entity with new status', async () => {
      const status = 'inactive';

      const result = await EntityRepository.update(mockModelData, status);

      expect(result).toEqual({ id: '123', name: 'updated' });
    });
  });

  describe('count', () => {
    it('should call Entity.count with correct parameters', async () => {
      const whereCondition = { status: 'active' };
      const options = { distinct: true };

      mockFastify.psql.Entity.count.mockResolvedValue(10);

      const result = await EntityRepository.count(mockFastify, whereCondition, options);

      expect(mockFastify.psql.Entity.count).toHaveBeenCalledWith({
        where: whereCondition,
        distinct: true,
      });
      expect(result).toBe(10);
    });

    it('should call Entity.count with only where condition when no options provided', async () => {
      const whereCondition = { status: 'active' };

      mockFastify.psql.Entity.count.mockResolvedValue(5);

      const result = await EntityRepository.count(mockFastify, whereCondition);

      expect(mockFastify.psql.Entity.count).toHaveBeenCalledWith({
        where: whereCondition,
      });
      expect(result).toBe(5);
    });

    it('should handle errors during count operation', async () => {
      const whereCondition = { status: 'active' };
      const error = new Error('Database error');

      mockFastify.psql.Entity.count.mockRejectedValue(error);

      await expect(EntityRepository.count(mockFastify, whereCondition)).rejects.toThrow(error);
    });
  });

  describe('getEntity', () => {
    it('should find entity by accessId', async () => {
      const accessId = 'access-123';
      const mockEntity = {
        id: '123',
        name: 'Test Entity',
        hierarchy: 'operator',
      };

      QueryUtil.buildWhereFromFilters.mockReturnValueOnce({
        where: { accessId },
        include: [{ model: mockFastify.psql.Entity, as: 'parent', required: false }],
      });

      mockFastify.psql.Entity.findOne.mockResolvedValue(mockEntity);

      const result = await EntityRepository.getEntity(mockFastify, { accessId });

      expect(result).toEqual(mockEntity);
      expect(QueryUtil.buildWhereFromFilters).toHaveBeenCalledWith(
        { filter_accessId_eq: accessId },
        mockFastify.psql.Entity,
        [{ model: mockFastify.psql.Entity, as: 'parent', required: false }],
      );
      expect(mockFastify.psql.Entity.findOne).toHaveBeenCalledWith({
        where: { accessId },
        include: [{ model: mockFastify.psql.Entity, as: 'parent', required: false }],
      });
    });

    it('should find entity by id', async () => {
      const id = 'entity-123';
      const mockEntity = {
        id: 'entity-123',
        name: 'Test Entity',
        hierarchy: 'operator',
      };

      QueryUtil.buildWhereFromFilters.mockReturnValueOnce({
        where: { id },
        include: [{ model: mockFastify.psql.Entity, as: 'parent', required: false }],
      });

      mockFastify.psql.Entity.findOne.mockResolvedValue(mockEntity);

      const result = await EntityRepository.getEntity(mockFastify, { id });

      expect(result).toEqual(mockEntity);
      expect(QueryUtil.buildWhereFromFilters).toHaveBeenCalledWith(
        { filter_id_eq: id },
        mockFastify.psql.Entity,
        [{ model: mockFastify.psql.Entity, as: 'parent', required: false }],
      );
    });

    it('should find entity by both id and accessId when both are provided', async () => {
      const id = 'entity-123';
      const accessId = 'access-123';
      const mockEntity = {
        id: 'entity-123',
        accessId: 'access-123',
        name: 'Test Entity',
      };

      QueryUtil.buildWhereFromFilters.mockReturnValueOnce({
        where: { id, accessId },
        include: [{ model: mockFastify.psql.Entity, as: 'parent', required: false }],
      });

      mockFastify.psql.Entity.findOne.mockResolvedValue(mockEntity);

      const result = await EntityRepository.getEntity(mockFastify, { id, accessId });

      expect(result).toEqual(mockEntity);
      expect(QueryUtil.buildWhereFromFilters).toHaveBeenCalledWith(
        { filter_id_eq: id, filter_accessId_eq: accessId },
        mockFastify.psql.Entity,
        [{ model: mockFastify.psql.Entity, as: 'parent', required: false }],
      );
    });

    it('should return null when entity is not found', async () => {
      const id = 'nonexistent-id';

      QueryUtil.buildWhereFromFilters.mockReturnValueOnce({
        where: { id },
        include: [{ model: mockFastify.psql.Entity, as: 'parent', required: false }],
      });

      mockFastify.psql.Entity.findOne.mockResolvedValue(null);

      const result = await EntityRepository.getEntity(mockFastify, { id });

      expect(result).toBeNull();
    });

    it('should handle errors during entity lookup', async () => {
      const id = 'entity-123';
      const error = new Error('Database error');

      QueryUtil.buildWhereFromFilters.mockReturnValueOnce({
        where: { id },
        include: [{ model: mockFastify.psql.Entity, as: 'parent', required: false }],
      });

      mockFastify.psql.Entity.findOne.mockRejectedValue(error);

      await expect(EntityRepository.getEntity(mockFastify, { id })).rejects.toThrow(error);
    });
  });
});
