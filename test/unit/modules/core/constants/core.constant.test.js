import { describe, expect, it } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';

const {
  HIERARCHY,
  ACCESS_LEVEL_KEYS,
  PERMISSION_FIELDS,
  CACHE_SECOND,
  COMMON_STATUSES,
  COMMON_TRANSACTION_STATUSES,
  EVENTS,
  EVENT_ACTIONS,
  HCAPTCHA_RISK_LEVELS,
  HTTP_METHODS,
  MODULE_METHODS,
  MODULE_NAMES,
  REMARKABLE_TYPE,
  REDACT_FIELDS,
  REMARK_STATUSES,
  REMARK_TYPE,
  VIEW_ACTION,
} = CoreConstant;

describe('Core Constants', () => {
  it('should have correct HIERARCHY', () => {
    expect(HIERARCHY).toEqual({
      MERCHANT: 'merchant',
      ORGANISATION: 'organisation',
      ROOT: 'root',
      USER: 'user',
    });
  });

  it('should have correct ACCESS_LEVEL_KEYS', () => {
    expect(ACCESS_LEVEL_KEYS).toEqual({
      user: 'user',
      member: 'member',
      webhook: 'webhook',
    });
  });

  it('should have correct PERMISSION_FIELDS', () => {
    expect(PERMISSION_FIELDS).toEqual([
      'canRead',
      'canCreate',
      'canEdit',
      'canManage',
      'canImport',
      'canExport',
    ]);
  });

  it('should have correct CACHE_SECOND values', () => {
    expect(CACHE_SECOND).toEqual({
      SHORT: 10,
      MEDIUM: 30,
      STANDARD: 60,
      LONG: 3600,
      DAILY: 86400,
      WEEKLY: 604800,
      NEVER: 0,
    });
  });

  it('should have correct COMMON_STATUSES', () => {
    expect(COMMON_STATUSES).toEqual({
      ACTIVE: 'active',
      DELETED: 'deleted',
      INACTIVE: 'inactive',
    });
  });

  it('should have correct COMMON_TRANSACTION_STATUSES', () => {
    expect(COMMON_TRANSACTION_STATUSES).toEqual({
      PENDING: 'pending',
      COMPLETED: 'completed',
      FAILED: 'failed',
      CANCELLED: 'cancelled',
      VOIDED: 'voided',
    });
  });

  it('should have correct EVENTS', () => {
    expect(EVENTS).toEqual({
      ACCESS_CONTROL: 'accessControl',
      APP_CENTER: 'appCenter',
      AUDIT_TRAIL: 'auditTrail',
      AUTOMATION: 'automation',
      CREDIT_LIMIT_SETTING: 'creditLimitSetting',
      CURRENCY_SETTING: 'currencySetting',
      DATA_MASKING_SETTING: 'dataMaskingSetting',
      DEPARTMENT: 'department',
      DEPARTMENT_TEMPLATE: 'departmentTemplate',
      DEVELOPER_HUB: 'developerHub',
      GAME_PROVIDER: 'gameProvider',
      LANGUAGE_SETTING: 'languageSetting',
      LOCALISATION_SETTING: 'localisationSetting',
      LOGIN: 'login',
      LOGOUT: 'logout',
      MAINTENANCE: 'maintenance',
      MANAGE_MONITOR: 'manageMonitor',
      MEDIA: 'media',
      MEMBER_POINT: 'memberPoint',
      MEMBER_VIP: 'memberVIP',
      MEMBER: 'member',
      MERCHANT_CREDIT: 'merchantCredit',
      MERCHANT: 'merchant',
      ORGANISATION: 'organisation',
      OTP: 'OTP',
      PERSONAL_SETTING: 'personalSetting',
      REGION_SETTING: 'regionSetting',
      REGISTRATION_FORM: 'registrationForm',
      RESET_PASSWORD: 'resetPassword',
      RISK_GROUP: 'riskGroup',
      ROLE: 'role',
      SECURITY_ACCESS_CONTROL: 'securityAccessControl',
      SETTING: {
        PERSONAL: 'personalSetting',
        SAFETY: 'safety',
        THEMES: 'themesSetting',
      },
      SETTING_OPTIONS: {
        PERSONAL: 'personalSettingsOptions',
        SAFETY: 'safetySettingsOptions',
        THEMES: 'themeSettingsOptions',
      },
      TAG: 'tag',
      TRANSACTION: 'transaction',
      TRIGGERED_EVENT_LOG: 'triggeredEventLog',
      TWO_FACTOR_AUTHENTICATION: 'twoFactorAuthentication',
      USER_AUDIT_TRAIL: 'userAuditTrail',
      USER_EXTERNAL_INVITATION: 'userExternalInvitation',
      USER_HIERARCHY: 'userHierarchy',
      USER_LOGIN_LOG: 'userLoginLog',
      USER_SSO: 'userSSO',
      USER_SUB_ACCOUNT: 'userSubAccount',
      USER: 'user',
    });
  });

  it('should have correct EVENT_ACTIONS', () => {
    expect(EVENT_ACTIONS).toEqual({
      ACCESS_CONTROL_UPDATED: 'accessControlUpdated',
      API_KEY_GENERATED: 'apiKeyGenerated',
      APP_INSTALLED: 'appInstalled',
      APP_UNINSTALLED: 'appUninstalled',
      ARCHIVED: 'archived',
      ASSIGNED: 'assigned',
      BASIC_INFORMATION_UPDATED: 'basicInformationUpdated',
      CREATED: 'created',
      DELETED: 'deleted',
      DETAILS_VIEWED: 'detailsViewed',
      DUPLICATED: 'duplicated',
      EDITED: 'edited',
      EXPORTED: 'exported',
      IMPORTED: 'imported',
      IP_BLACKLISTED: 'ipBlacklisted',
      KILL_SWITCH_ACTIVATED: 'killSwitchActivated',
      KILL_SWITCH_DEACTIVATED: 'killSwitchDeactivated',
      LOGIN: 'login',
      LOGOUT: 'logout',
      POLICY_UPDATED: 'policyUpdated',
      SEARCHED: 'searched',
      SETUP: 'setup',
      STATUS_UPDATED: 'statusUpdated',
      TEST_RAN: 'testRan',
      UNKNOWN_ACTION: 'unknownAction',
      UPDATED: 'updated',
      VIEWED: 'viewed',
    });
  });

  it('should have correct HTTP_METHODS', () => {
    expect(HTTP_METHODS).toEqual({
      GET: 'GET',
      POST: 'POST',
      PUT: 'PUT',
      PATCH: 'PATCH',
      DELETE: 'DELETE',
    });
  });

  it('should have correct HCAPTCHA_RISK_LEVELS', () => {
    expect(HCAPTCHA_RISK_LEVELS).toEqual({
      HIGH: 'high',
      LOW: 'low',
    });
  });

  it('should have correct MODULE_METHODS', () => {
    expect(MODULE_METHODS).toEqual({
      ASSIGN_USER: 'assignUser',
      CREATE: 'create',
      CHECK_AVAILABILITY: 'checkAvailability',
      DELETE: 'delete',
      EXPORT: 'export',
      INDEX: 'index',
      NAVIGATION: 'navigation',
      ONBOARD_USER: 'onboardUser',
      OPTION: 'option',
      REQUEST_2FA_SETUP: 'request2faSetup',
      RESET_2FA: 'reset2fa',
      REVOKE_TRUSTED_DEVICE: 'revokeTrustedDevice',
      TEST: 'test',
      UPDATE_ACCESS_CONTROL: 'updateAccessControls',
      UPDATE_BASIC_INFORMATION: 'updateBasicInformation',
      UPDATE_MAINTENANCE_STATUS: 'updateMaintenanceStatus',
      UPDATE_LOGIN_ACCESS: 'updateLoginAccess',
      UPDATE_ORGANISATION: 'updateOrganisation',
      UPDATE_POLICY: 'updatePolicy',
      UPDATE_PERMISSION: 'updatePermissions',
      UPDATE_PERSONAL: 'updatePersonal',
      UPDATE_SAFETY: 'updateSafety',
      UPDATE_STATUS: 'updateStatus',
      UPDATE_THEMES: 'updateThemes',
      UPDATE: 'update',
      REQUEST_2FA_LOGIN: 'request2faLogin',
      VIEW: 'view',
      LOGIN: 'login',
      REGISTER_SSO: 'registerSSO',
      NAVIGATION: 'navigation',
      VIEW_THEMES: 'viewThemes',
    });
  });

  it('should have correct MODULE_NAMES', () => {
    expect(MODULE_NAMES).toEqual({
      ACCESS_CONTROL: 'accessControls',
      APP: 'apps',
      AUDIT_TRAIL: 'auditTrails',
      BULK_JOB: 'bulkJobs',
      CORE: 'core',
      CREDIT_LIMIT_TRANSACTION: 'creditLimitTransactions',
      CREDIT_LIMIT: 'creditLimits',
      DEPARTMENT_TEMPLATE: 'departmentTemplates',
      DEPARTMENT: 'departments',
      DEVELOPER_HUB: 'developerHubs',
      LOCALISATION: 'localisations',
      MEDIA: 'media',
      MODULE_POLICY: 'modulePolicies',
      ORGANISATION: 'organisations',
      ROLE: 'roles',
      SETTING: 'settings',
      USER: 'users',
      DEPARTMENT: 'departments',
      DEPARTMENT_TEMPLATE: 'departmentTemplates',
      USER: 'users',
      ROLE: 'roles',
    });
  });

  it('should have correct REDACT_FIELDS', () => {
    expect(REDACT_FIELDS).toEqual({
      PASSWORD: 'password',
      CONFIRM_PASSWORD: 'confirmPassword',
      API_KEY: 'apiKey',
    });
  });

  it('should have correct REMARK_STATUSES', () => {
    expect(REMARK_STATUSES).toEqual({
      ACTIVE: 'active',
      ARCHIVED: 'archived',
    });
  });

  it('should have correct REMARK_TYPE', () => {
    expect(REMARK_TYPE).toEqual({
      AUDIT: 'audit',
      NOTE: 'note',
      SECURITY: 'security',
      SYSTEM: 'system',
      WARNING: 'warning',
    });
  });

  it('should have correct REMARKABLE_TYPE', () => {
    expect(REMARKABLE_TYPE).toEqual({
      IP_ACCESS_CONTROL: 'ip_access_control',
    });
  });

  it('should have correct VIEW_ACTION', () => {
    expect(VIEW_ACTION).toEqual({
      SEARCHED: 'searched',
      VIEWED: 'viewed',
      DETAILS_VIEWED: 'detailsViewed',
    });
  });
});
