import { describe, expect, it } from 'vitest';

import { TYPE } from '#src/modules/core/constants/link.constant.js';

describe('Link Constants', () => {
  describe('TYPE', () => {
    it('should define correct constant', () => {
      expect(TYPE).toHaveProperty('EMAIL_DOMAIN');
      expect(TYPE).toHaveProperty('SIGNIN');
      expect(TYPE.EMAIL_DOMAIN).toBe('email_domain');
      expect(TYPE.SIGNIN).toBe('signin');
    });

    it('should have the correct number of types', () => {
      expect(Object.keys(TYPE).length).toBe(2);
    });
  });
});
