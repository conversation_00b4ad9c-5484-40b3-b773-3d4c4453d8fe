import { describe, expect, it } from 'vitest';

import { ENTITY_STATUSES } from '#src/modules/core/constants/entity.constant.js';

describe('Entity Constants', () => {
  describe('ENTITY_STATUSES', () => {
    it('should define all entity status constants', () => {
      expect(ENTITY_STATUSES).toHaveProperty('ACTIVE');
      expect(ENTITY_STATUSES).toHaveProperty('INACTIVE');
      expect(ENTITY_STATUSES).toHaveProperty('SUSPENDED');
      expect(ENTITY_STATUSES).toHaveProperty('ARCHIVED');

      expect(ENTITY_STATUSES.ACTIVE).toBe('active');
      expect(ENTITY_STATUSES.INACTIVE).toBe('inactive');
      expect(ENTITY_STATUSES.SUSPENDED).toBe('suspended');
      expect(ENTITY_STATUSES.ARCHIVED).toBe('archived');
    });

    it('should have the correct number of statuses', () => {
      expect(Object.keys(ENTITY_STATUSES).length).toBe(4);
    });

    it('should have unique values', () => {
      const values = Object.values(ENTITY_STATUSES);
      const uniqueValues = [...new Set(values)];
      expect(values.length).toBe(uniqueValues.length);
    });
  });
});
