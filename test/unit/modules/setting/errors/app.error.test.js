import { describe, expect, it } from 'vitest';

import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

describe('createModuleErrors', () => {
  const APP_ERROR_DEF = {
    appAlreadyInstalled: ['90007', 'error.apps.sentence.installed', 409],
    appNotFound: ['90003', 'error.apps.sentence.notFound', 404],
    appNotInstalled: ['90008', 'error.apps.sentence.notInstalled', 404],
    appConfigNotFound: ['90004', 'error.apps.sentence.configNotFound', 404],
    appNotSupportTest: ['90005', 'error.apps.sentence.notSupportTest', 422],
    testStrategyNotFound: ['90006', 'error.apps.sentence.testStrategyNotFound', 404],
    testFailed: ['90009', 'error.apps.sentence.testFailed', 400],
  };

  const result = createModuleErrors(MODULE_NAMES.APP, APP_ERROR_DEF);

  it('should return error object for all keys in APP_ERROR_DEF', () => {
    expect(Object.keys(result)).toHaveLength(Object.keys(APP_ERROR_DEF).length);
  });
});
