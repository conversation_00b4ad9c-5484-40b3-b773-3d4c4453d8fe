import { describe, expect, it } from 'vitest';

import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { AppConstant } from '#src/modules/setting/constants/index.js';
import * as appSchema from '#src/modules/setting/schemas/app.schema.js';

const { APP } = MODULE_NAMES;

describe('App Schema', () => {
  describe('index schema', () => {
    it('should have correct tags and summary', () => {
      expect(appSchema.index.tags).toEqual(['BO / Setting / App Center']);
      expect(appSchema.index.summary).toBe(`Get a ${APP} center list`);
    });

    it('should include correct querystring properties', () => {
      const { properties } = appSchema.index.querystring;
      expect(properties).toHaveProperty('filter_category_eq');
      expect(properties).toHaveProperty('filter_name_iLike');
      expect(properties).toHaveProperty('sortBy');
    });

    it('should include 200 response with data, message, and meta', () => {
      const response = appSchema.index.response[200];
      expect(response).toBeDefined();
      expect(response).toHaveProperty('type', 'object');
      expect(response.properties).toHaveProperty('message');
      expect(response.properties).toHaveProperty('data');
      expect(response.properties).toHaveProperty('meta');
    });
  });

  describe('create schema', () => {
    it('should have correct tags and summary', () => {
      expect(appSchema.create.tags).toEqual(['BO / Setting / App Center']);
      expect(appSchema.create.summary).toBe(`Create a ${APP}`);
    });

    it('should require appId in body', () => {
      expect(appSchema.create.body.required).toContain('appId');
    });

    it('should return standard create response schema with 201', () => {
      expect(appSchema.create.response).toBeDefined();
      expect(appSchema.create.response['201']).toBeDefined();
      expect(appSchema.create.response['201'].properties).toHaveProperty('data');
    });
  });

  describe('remove schema', () => {
    it('should have correct tags and summary', () => {
      expect(appSchema.remove.tags).toEqual(['BO / Setting / App Center']);
      expect(appSchema.remove.summary).toBe(`Remove an ${APP}`);
    });

    it('should return standard remove response schema with 200', () => {
      expect(appSchema.remove.response).toBeDefined();
      expect(appSchema.remove.response['200']).toBeDefined();
      expect(appSchema.remove.response['200'].properties).toHaveProperty('message');
    });
  });

  describe('update schema', () => {
    it('should have correct tags and summary', () => {
      expect(appSchema.update.tags).toEqual(['BO / Setting / App Center']);
      expect(appSchema.update.summary).toBe(`Update an ${APP}`);
    });

    it('should require status in body only', () => {
      const required = appSchema.update.body.required;
      expect(required).toContain('status');
      expect(required).not.toContain('appId');
    });

    it('should validate status enum', () => {
      const { status } = appSchema.update.body.properties;
      expect(status.enum).toEqual(Object.values(AppConstant.ENTITY_APP_STATUSES));
    });

    it('should include optional config object', () => {
      expect(appSchema.update.body.properties).toHaveProperty('config');
      expect(appSchema.update.body.properties.config.type).toBe('object');
    });

    it('should return standard update response schema with 200', () => {
      expect(appSchema.update.response).toBeDefined();
      expect(appSchema.update.response['200']).toBeDefined();
      expect(appSchema.update.response['200'].properties).toHaveProperty('message');
    });
  });

  describe('options schema', () => {
    it('should have correct options schema', () => {
      expect(appSchema.options).toHaveProperty('tags');
      expect(appSchema.options.tags).toEqual(['BO / Setting / App Center']);

      expect(appSchema.options).toHaveProperty('summary');
      expect(appSchema.options.summary).toBe('Get apps config option');

      expect(appSchema.options).toHaveProperty('response');
      expect(appSchema.options.response).toHaveProperty('200');

      const response200 = appSchema.options.response['200'];
      expect(response200).toHaveProperty('description', 'Success response');
      expect(response200).toHaveProperty('type', 'object');
      expect(response200).toHaveProperty('properties');

      const properties = response200.properties;
      expect(properties).toHaveProperty('message');
      expect(properties.message).toEqual({ type: 'string' });

      expect(properties).toHaveProperty('data');
      expect(properties.data).toHaveProperty('type', 'object');
      expect(properties.data).toHaveProperty('additionalProperties');

      const additionalProperties = properties.data.additionalProperties;
      expect(additionalProperties).toHaveProperty('type', 'object');
      expect(additionalProperties).toHaveProperty('properties');

      const additionalPropertiesProps = additionalProperties.properties;
      expect(additionalPropertiesProps).toHaveProperty('configOptions');
      expect(additionalPropertiesProps.configOptions).toEqual({
        type: 'array',
        items: { type: 'string' },
      });

      expect(additionalPropertiesProps).toHaveProperty('testOptions');
      expect(additionalPropertiesProps.testOptions).toEqual({
        type: 'array',
        items: { type: 'string' },
      });

      expect(additionalProperties).toHaveProperty('required', ['configOptions', 'testOptions']);
    });
  });
});
