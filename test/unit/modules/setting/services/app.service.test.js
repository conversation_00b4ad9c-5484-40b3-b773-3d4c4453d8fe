import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { TestResultRepository } from '#src/modules/core/repository/index.js';
import { AppConstant } from '#src/modules/setting/constants/index.js';
import { AppError } from '#src/modules/setting/errors/index.js';
import { AppRepository } from '#src/modules/setting/repository/index.js';
import * as appService from '#src/modules/setting/services/app.service.js';
import { encrypt } from '#src/utils/aes.util.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';
import { testStrategy } from '#src/utils/test-strategy.util.js';

vi.mock('#src/modules/setting/repository/index.js');
vi.mock('#src/modules/core/repository/index.js');
vi.mock('#src/utils/db-transaction.util.js');
vi.mock('#src/utils/aes.util.js');
vi.mock('#src/utils/test-strategy.util.js', () => ({
  testStrategy: {},
}));
const mockServer = {};

beforeEach(() => {
  vi.resetAllMocks();
});

describe('App Service', () => {
  describe('index', () => {
    it('should call findAll with correct query and return result', async () => {
      const mockApps = { data: ['app1', 'app2'] };
      AppRepository.findAll.mockResolvedValue(mockApps);

      const request = {
        entity: { id: 'entity-id' },
        query: { name: 'Test', category: 'Tool' },
        server: mockServer,
      };

      const result = await appService.index(request);

      expect(AppRepository.findAll).toHaveBeenCalledWith(mockServer, {
        name: 'Test',
        category: 'Tool',
        'filter_entityApp.entityId_eq': 'entity-id',
      });
      expect(result).toEqual({ data: ['app1', 'app2'] });
    });
  });

  describe('create', () => {
    it('should throw error if app is already installed', async () => {
      AppRepository.findById.mockResolvedValue({ id: 'app1', entityApp: [{}] });

      const request = {
        body: { appId: 'app1' },
        entity: { id: 'entity-id' },
        authInfo: { id: 'user-id' },
        server: mockServer,
      };

      await expect(appService.create(request)).rejects.toThrow(
        AppError.appAlreadyInstalled({ appId: 'app1' }),
      );
    });
    it('should create entity app if not already installed', async () => {
      AppRepository.findById.mockResolvedValue({ id: 'app2', entityApp: [] });
      const mockCreated = { id: 'new-app' };

      AppRepository.createEntityApp.mockResolvedValue(mockCreated);

      const request = {
        body: { appId: 'app2' },
        entity: { id: 'entity-id' },
        authInfo: { id: 'user-id' },
        server: mockServer,
      };

      const result = await appService.create(request);

      expect(AppRepository.createEntityApp).toHaveBeenCalledWith(
        mockServer,
        {
          entityId: 'entity-id',
          appId: 'app2',
          status: AppConstant.ENTITY_APP_STATUSES.INSTALLED,
        },
        { authInfoId: 'user-id' },
      );
      expect(result).toEqual({ id: 'new-app' });
    });
    it('should set status to ACTIVE if app has no configurable options', async () => {
      const appWithoutConfig = { id: 'app3', name: 'NoConfigApp', entityApp: [] };

      // Ensure ENTITY_APP_OPTIONS defines this app with no configOptions
      AppConstant.ENTITY_APP_OPTIONS['NoConfigApp'] = { configOptions: [] };

      AppRepository.findById.mockResolvedValue(appWithoutConfig);
      const mockCreated = { id: 'new-app-no-config' };
      AppRepository.createEntityApp.mockResolvedValue(mockCreated);

      const request = {
        body: { appId: 'app3' },
        entity: { id: 'entity-id' },
        authInfo: { id: 'user-id' },
        server: mockServer,
      };

      const result = await appService.create(request);

      expect(AppRepository.createEntityApp).toHaveBeenCalledWith(
        mockServer,
        {
          entityId: 'entity-id',
          appId: 'app3',
          status: AppConstant.ENTITY_APP_STATUSES.ACTIVE,
        },
        { authInfoId: 'user-id' },
      );
      expect(result).toEqual({ id: 'new-app-no-config' });
    });
  });
  describe('update', () => {
    it('should throw error if entity app not found', async () => {
      AppRepository.findEntityAppById.mockResolvedValue(null);

      const request = {
        params: { id: 'app-id' },
        body: { status: 'ACTIVE' },
        entity: { id: 'entity-id' },
        authInfo: { id: 'user-id' },
        server: mockServer,
      };

      await expect(appService.update(request)).rejects.toThrow(CoreError.dataNotFound('app-id'));
    });

    it('should update status and upsert config with encrypted values', async () => {
      const mockEntityApp = { id: 'entity-app-id' };
      AppRepository.findEntityAppById.mockResolvedValue(mockEntityApp);
      encrypt.mockImplementation((val) => `encrypted-${val}`);
      withTransaction.mockImplementation((server, opts, cb) => cb({}));

      const request = {
        params: { id: 'app-id' },
        body: { status: 'active', config: { key1: 'val1', key2: 'val2' } },
        entity: { id: 'entity-id' },
        authInfo: { id: 'user-id' },
        server: mockServer,
      };

      const result = await appService.update(request);

      expect(AppRepository.update).toHaveBeenCalledWith(
        mockEntityApp,
        { status: 'active' },
        expect.any(Object),
      );
      expect(AppRepository.upsertAppConfig).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          entityAppId: 'entity-app-id',
          configKey: 'key1',
          configValue: 'encrypted-val1',
        }),
        expect.objectContaining({ authInfoId: 'user-id' }),
      );
      expect(result).toEqual(undefined);
    });
  });

  describe('view', () => {
    const mockRequest = {
      params: { id: 'test-app-id' },
      entity: { id: 'entity-123' },
      server: {},
    };

    const mockAppData = {
      app: {
        toJSON: () => ({
          id: 'test-app-id',
          name: 'Test App',
        }),
      },
      status: 'active',
    };

    beforeEach(() => {
      vi.resetAllMocks();
    });

    it('should return app data if found', async () => {
      vi.spyOn(AppRepository, 'findEntityAppById').mockResolvedValue(mockAppData);

      const result = await appService.view(mockRequest);

      expect(AppRepository.findEntityAppById).toHaveBeenCalledWith(mockRequest.server, {
        filter_appId_eq: 'test-app-id',
        filter_entityId_eq: 'entity-123',
      });

      expect(result).toEqual({
        app: {
          id: 'test-app-id',
          name: 'Test App',
          status: 'active',
        },
      });
    });

    it('should throw appNotFound error if app is not found', async () => {
      vi.spyOn(AppRepository, 'findEntityAppById').mockResolvedValue(null);
      const mockError = new Error('App not found');
      vi.spyOn(CoreError, 'dataNotFound').mockReturnValue(mockError);

      await expect(() => appService.view(mockRequest)).rejects.toThrow(
        CoreError.dataNotFound('app-id'),
      );

      expect(CoreError.dataNotFound).toHaveBeenCalledWith('app-id');
    });
  });

  describe('remove', () => {
    it('should throw error if entity app not found', async () => {
      AppRepository.findEntityAppById.mockResolvedValue(null);

      const request = {
        params: { id: 'app-id' },
        entity: { id: 'entity-id' },
        authInfo: { id: 'user-id' },
        server: mockServer,
      };

      await expect(appService.remove(request)).rejects.toThrow(CoreError.dataNotFound('app-id'));
    });

    it('should soft delete app and config if exists', async () => {
      const mockApp = {
        id: 'app-id',
        appConfig: [{ id: 'cfg1' }],
      };
      AppRepository.findEntityAppById.mockResolvedValue(mockApp);
      withTransaction.mockImplementation((server, opts, callback) => callback({}));

      const request = {
        params: { id: 'app-id' },
        entity: { id: 'entity-id' },
        authInfo: { id: 'user-id' },
        server: mockServer,
      };

      const result = await appService.remove(request);

      expect(AppRepository.softDelete).toHaveBeenCalledWith(
        mockApp,
        expect.objectContaining({ authInfoId: 'user-id' }),
      );
      expect(AppRepository.softDelete).toHaveBeenCalledWith(
        mockApp.appConfig[0],
        expect.objectContaining({ authInfoId: 'user-id' }),
      );
      expect(result).toEqual(undefined);
    });

    it('should soft delete app as well if config is not exist', async () => {
      const mockAppWithoutConfig = {
        id: 'app-id',
      };
      AppRepository.findEntityAppById.mockResolvedValue(mockAppWithoutConfig);
      withTransaction.mockImplementation((server, opts, callback) => callback({}));
      const request = {
        params: { id: 'app-id' },
        entity: { id: 'entity-id' },
        authInfo: { id: 'user-id' },
        server: mockServer,
      };
      await appService.remove(request);
      expect(AppRepository.softDelete).toHaveBeenCalledOnce();
    });
  });

  describe('runTest', () => {
    const mockApp = {
      id: 'entity-app-id',
      app: { name: 'Exchangerate Host' },
      appConfig: [{ configKey: 'apiAccessKey', configValue: 'secret' }],
    };

    const fakeRequest = {
      params: { id: 'app-id' },
      entity: { id: 'entity-id' },
      authInfo: { id: 'auth789' },
      server: mockServer,
      body: {},
      headers: { 'x-request-id': 'req-1' },
    };

    const mockRequestBase = {
      params: { id: 'app-id' },
      entity: { id: 'entity-id' },
      authInfo: { id: 'auth-user-id' },
      server: mockServer,
      headers: { 'x-trace-id': '12345' },
    };

    it('should throw error if entity app is not found', async () => {
      AppRepository.findEntityAppById.mockResolvedValue(null);

      await expect(appService.runTest(mockRequestBase)).rejects.toThrow(
        AppError.appNotInstalled({ appId: 'app-id' }),
      );
    });

    it('should throw error if testFeature is false', async () => {
      AppRepository.findEntityAppById.mockResolvedValue({
        ...mockApp,
        app: {
          ...mockApp.app,
          testFeature: false,
        },
      });

      await expect(appService.runTest(fakeRequest)).rejects.toMatchObject(
        AppError.appNotSupportTest(),
      );
    });

    it('should throw error if app config is missing', async () => {
      AppRepository.findEntityAppById.mockResolvedValue({
        ...mockApp,
        appConfig: [],
      });

      await expect(appService.runTest(fakeRequest)).rejects.toThrow(
        AppError.appConfigNotFound({ appId: 'app-id' }),
      );
    });

    it('should throw error if test strategy is not found', async () => {
      AppRepository.findEntityAppById.mockResolvedValue({
        ...mockApp,
        app: { ...mockApp.app, name: 'Unknown App' },
      });

      await expect(appService.runTest(fakeRequest)).rejects.toThrow(
        AppError.testStrategyNotFound({ appName: 'Unknown App' }),
      );
    });
    it('should return success result and store SUCCESS status', async () => {
      const successResult = {
        success: true,
        data: 'ok',
        message: 'Test passed',
        requestHeader: {},
      };

      AppRepository.findEntityAppById.mockResolvedValue(mockApp);
      testStrategy['Exchangerate Host'] = vi.fn().mockResolvedValue(successResult);

      TestResultRepository.createTestResult.mockResolvedValue({});

      const result = await appService.runTest(fakeRequest);

      expect(result).toEqual(successResult);

      expect(TestResultRepository.createTestResult).toHaveBeenCalledWith(
        fakeRequest.server,
        expect.objectContaining({
          status: CoreConstant.TEST_RESULT_STATUSES.SUCCESS,
          requestHeader: successResult.requestHeader || {},
          response: successResult.data || {},
        }),
        { authInfoId: 'auth789' },
      );
    });
    it('should store failed status if strategy returns failure result', async () => {
      const failedResult = {
        success: false,
        data: null,
        message: 'fail',
        error: 'invalid',
        requestHeader: {},
      };

      AppRepository.findEntityAppById.mockResolvedValue(mockApp);
      testStrategy['Exchangerate Host'] = vi.fn().mockResolvedValue(failedResult);

      await expect(appService.runTest(fakeRequest)).rejects.toThrow(AppError.testFailed('invalid'));

      expect(TestResultRepository.createTestResult).toHaveBeenCalledWith(
        fakeRequest.server,
        expect.objectContaining({
          status: CoreConstant.TEST_RESULT_STATUSES.FAILED,
          requestHeader: failedResult.requestHeaders || {},
          response: failedResult.data || {},
        }),
        { authInfoId: 'auth789' },
      );
    });
    it('should store only filename when upload is present in request body', async () => {
      const mockApp = {
        id: 'entity-app-id',
        app: { name: 'AWS S3', testFeature: true },
        appConfig: [
          { configKey: 'apiKey', configValue: 'test-api-key' },
          { configKey: 'region', configValue: 'us-west-2' },
        ],
      };

      AppRepository.findEntityAppById.mockResolvedValue(mockApp);

      // Fake strategy that returns success
      const successResult = { success: true, data: { ok: true }, requestHeaders: {} };
      testStrategy['AWS S3'] = vi.fn().mockResolvedValue(successResult);

      TestResultRepository.createTestResult.mockResolvedValue({});

      const fakeRequest = {
        params: { id: 'app-id' },
        entity: { id: 'entity-id' },
        authInfo: { id: 'auth-user-id' },
        server: mockServer,
        body: { upload: { filename: 'test-upload.zip' } },
      };

      const result = await appService.runTest(fakeRequest);

      // Expect only filename is stored
      expect(TestResultRepository.createTestResult).toHaveBeenCalledWith(
        fakeRequest.server,
        expect.objectContaining({
          requestBody: { filename: 'test-upload.zip' },
        }),
        { authInfoId: 'auth-user-id' },
      );

      expect(result).toEqual(successResult);
    });
  });
  describe('options', () => {
    it('should return all expected integration configurations', async () => {
      const result = await appService.options();

      expect(result).toEqual(AppConstant.ENTITY_APP_OPTIONS);
    });
  });
});
