/* eslint-disable sonarjs/no-hardcoded-ip */
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { RemarkRepository } from '#src/modules/core/repository/index.js';
import { IpAccessControlRepository } from '#src/modules/setting/repository/index.js';
import * as ipAccessControlService from '#src/modules/setting/services/access-control.service.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';
import { populateToJSON } from '#src/utils/test-helper.util.js';

vi.mock('#src/modules/setting/repository/index.js');
vi.mock('#src/modules/core/repository/index.js');
vi.mock('#src/utils/db-transaction.util.js');

const { INACTIVE } = CoreConstant.COMMON_STATUSES;
const {
  REMARK_STATUSES: { ACTIVE, ARCHIVED },
} = CoreConstant;

describe('IP Access Control Service', () => {
  let mockRequest;

  beforeEach(() => {
    mockRequest = {
      entity: { id: 'entity1' },
      server: {
        psql: {
          connection: {
            transaction: vi.fn((fn) => fn()),
          },
          Remark: {},
        },
      },
      params: { id: 'ipac1' },
      body: {},
      authInfo: { id: 'user1' },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should return IP access control entries with entityId filter', async () => {
      const mockEntries = [{ id: 'ipac1', ipAddress: '***********' }];
      IpAccessControlRepository.findAll.mockResolvedValue({ rows: mockEntries });

      mockRequest.entity = { id: 'entity1' };
      mockRequest.query = { someOtherFilter: 'value' };

      const result = await ipAccessControlService.index(mockRequest);

      expect(result).toEqual({ rows: mockEntries });
      expect(IpAccessControlRepository.findAll).toHaveBeenCalledWith(mockRequest.server, {
        someOtherFilter: 'value',
        filter_parentId_eq: 'entity1',
      });
    });

    it('should merge entityId filter with existing query filters', async () => {
      const mockEntries = [{ id: 'ipac1', ipAddress: '***********' }];
      IpAccessControlRepository.findAll.mockResolvedValue({ rows: mockEntries });

      mockRequest.entity = { id: 'entity1' };
      mockRequest.query = { filter_status_eq: 'ACTIVE' };

      const result = await ipAccessControlService.index(mockRequest);

      expect(result).toEqual({ rows: mockEntries });
      expect(IpAccessControlRepository.findAll).toHaveBeenCalledWith(mockRequest.server, {
        filter_status_eq: 'ACTIVE',
        filter_parentId_eq: 'entity1',
      });
    });
  });

  describe('view', () => {
    it('should return IP access control entry', async () => {
      const mockEntry = { id: 'ipac1', ipAddress: '***********' };
      IpAccessControlRepository.findById.mockResolvedValue(mockEntry);

      const result = await ipAccessControlService.view(mockRequest);

      expect(result).toEqual(mockEntry);
    });

    it('should throw error if entry not found', async () => {
      IpAccessControlRepository.findById.mockResolvedValue(null);

      await expect(ipAccessControlService.view(mockRequest)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.accessControl',
          attribute: 'ID',
          value: 'ipac1',
        }),
      );
    });
  });

  describe('create', () => {
    it('should create IP access control entry with remark', async () => {
      const mockReload = vi.fn();

      const mockRemarkEntry = populateToJSON({ id: 'remark1' });
      const mockCreatedEntry = populateToJSON({
        id: 'ipac1',
        ipAddress: '***********',
        reload: mockReload,
        activeRemark: mockRemarkEntry,
      });

      IpAccessControlRepository.create.mockResolvedValue(mockCreatedEntry);
      RemarkRepository.create.mockResolvedValue(mockRemarkEntry);
      withTransaction.mockImplementation((_, __, fn) => fn({ transaction: {} }));

      mockRequest.body = { ipAddress: '***********', remark: 'Test remark' };
      const result = await ipAccessControlService.create(mockRequest);

      expect(result).toEqual({
        result: mockCreatedEntry,
        auditModelMapping: {
          Remark: {
            afterState: mockRemarkEntry.toJSON(),
          },
          IpAccessControl: {
            afterState: mockCreatedEntry.toJSON(),
          },
        },
      });

      expect(IpAccessControlRepository.create).toHaveBeenCalled();
      expect(RemarkRepository.create).toHaveBeenCalled();
      expect(mockCreatedEntry.reload).toHaveBeenCalled();
    });

    it('should create IP access control entry without remark', async () => {
      const mockCreatedEntry = populateToJSON({ id: 'ipac1', ipAddress: '***********' });
      IpAccessControlRepository.create.mockResolvedValue(mockCreatedEntry);
      withTransaction.mockImplementation((_, __, fn) => fn({ transaction: {} }));

      mockRequest.body = { ipAddress: '***********' };
      const result = await ipAccessControlService.create(mockRequest);

      expect(result).toEqual({
        result: mockCreatedEntry,
        auditModelMapping: {
          IpAccessControl: {
            afterState: mockCreatedEntry.toJSON(),
          },
        },
      });
      expect(IpAccessControlRepository.create).toHaveBeenCalled();
      expect(RemarkRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('update', async () => {
    it('should update validatyDate when set', async () => {
      const mockRemarkEntry = populateToJSON({
        id: 'remark1',
        status: ACTIVE,
      });
      const mockUpdatedRemarkEntry = populateToJSON({
        id: 'remark1',
        status: ARCHIVED,
      });

      const mockExistingEntry = populateToJSON({
        id: 'ipac1',
        ipAddress: '***********',
      });
      const mockUpdatedEntry = populateToJSON({
        ...mockExistingEntry,
        ipAddress: '***********',
        validityDate: '2025-01-01 12:12:12',
      });
      IpAccessControlRepository.findById.mockResolvedValue(mockExistingEntry);
      IpAccessControlRepository.update.mockResolvedValue(mockUpdatedEntry);
      RemarkRepository.findActiveByRemarkable.mockResolvedValue(populateToJSON(mockRemarkEntry));
      RemarkRepository.archive.mockResolvedValue(populateToJSON(mockUpdatedRemarkEntry));

      withTransaction.mockImplementation((_, __, fn) => fn({ transaction: 'mockTransaction' }));
      mockRequest.body = { ipAddress: '***********', validityDate: '2025-01-01 12:12:12' };

      const result = await ipAccessControlService.update(mockRequest);

      expect(result.result).toEqual(mockUpdatedEntry);
      expect(result.auditModelMapping).toEqual({
        IpAccessControl: {
          beforeState: mockExistingEntry.toJSON(),
          afterState: mockUpdatedEntry.toJSON(),
          fieldsChanged: ['ipAddress', 'validityDate'],
        },
      });

      expect(IpAccessControlRepository.update).toHaveBeenCalledWith(
        mockExistingEntry,
        mockRequest.body,
        {
          transaction: { transaction: 'mockTransaction' },
          authInfoId: 'user1',
        },
      );
    });

    it('should not update remark when it is same', async () => {
      const mockRemark = {
        id: 'remark1',
        content: 'No changes',
      };
      const mockExistingEntry = {
        id: 'ipac1',
        ipAddress: '***********',
        activeRemark: mockRemark,
      };
      const mockUpdatedEntry = {
        ...mockExistingEntry,
        ipAddress: '***********',
        validityDate: '2025-01-01 12:12:12',
      };
      IpAccessControlRepository.findById.mockResolvedValue(populateToJSON(mockExistingEntry));
      IpAccessControlRepository.update.mockResolvedValue(populateToJSON(mockUpdatedEntry));
      RemarkRepository.findActiveByRemarkable.mockResolvedValue(mockRemark);

      withTransaction.mockImplementation((_, __, callback) =>
        callback({ transaction: 'mockTransaction' }),
      );

      mockRequest.body = {
        ipAddress: '***********',
        validityDate: '2025-01-01 12:12:12',
        remark: 'No changes',
      };

      const result = await ipAccessControlService.update(mockRequest);
      expect(result.auditModelMapping).toEqual({
        IpAccessControl: {
          beforeState: mockExistingEntry.toJSON(),
          afterState: mockUpdatedEntry.toJSON(),
          fieldsChanged: ['ipAddress', 'validityDate'],
        },
      });

      expect(RemarkRepository.create).not.toHaveBeenCalled();
    });

    it('should create a new remark when content changes', async () => {
      const mockExistingEntry = populateToJSON({
        id: 'ipac1',
        ipAddress: '***********',
        activeRemark: populateToJSON({ id: 'remark1', content: 'Old remark' }),
      });
      const mockUpdatedEntry = populateToJSON({
        ...mockExistingEntry,
        ipAddress: '***********',
      });
      IpAccessControlRepository.findById.mockResolvedValue(mockExistingEntry);
      IpAccessControlRepository.update.mockResolvedValue(mockUpdatedEntry);
      RemarkRepository.create.mockResolvedValue(
        populateToJSON({ id: 'newRemark1', content: 'New remark' }),
      );
      withTransaction.mockImplementation((_, __, fn) => fn({ transaction: {} }));

      mockRequest.body = { ipAddress: '***********', remark: 'New remark' };
      await ipAccessControlService.update(mockRequest);

      expect(RemarkRepository.create).toHaveBeenCalledWith(
        mockRequest.server,
        {
          remarkableId: 'ipac1',
          remarkableType: 'ip_access_control',
          type: 'note',
          content: 'New remark',
        },
        {
          transaction: expect.anything(),
          authInfoId: 'user1',
        },
      );
    });

    it('should return empty auditModelMapping if no fields changed', async () => {
      const mockRemark = {
        id: 'remark1',
        content: 'No changes',
      };
      const mockExistingEntry = {
        id: 'ipac1',
        ipAddress: '***********',
        activeRemark: mockRemark,
      };
      const mockUpdatedEntry = mockExistingEntry;
      IpAccessControlRepository.findById.mockResolvedValue(populateToJSON(mockExistingEntry));
      IpAccessControlRepository.update.mockResolvedValue(populateToJSON(mockUpdatedEntry));
      RemarkRepository.findActiveByRemarkable.mockResolvedValue(populateToJSON(mockRemark));

      withTransaction.mockImplementation((_, __, callback) =>
        callback({ transaction: 'mockTransaction' }),
      );

      mockRequest.body = {
        ipAddress: '***********',
        validityDate: '2025-01-01 12:12:12',
      };

      const result = await ipAccessControlService.update(mockRequest);

      expect(result.auditModelMapping.IpAccessControl).toBeUndefined();
    });
  });

  describe('updateStatus', () => {
    it('should update status of IP access control entry', async () => {
      const mockExistingEntry = populateToJSON({
        id: 'ipac1',
        ipAddress: '***********',
        status: 'ACTIVE',
      });
      const mockUpdatedEntry = populateToJSON({ ...mockExistingEntry, status: INACTIVE });
      IpAccessControlRepository.findById.mockResolvedValue(mockExistingEntry);
      IpAccessControlRepository.update.mockResolvedValue(mockUpdatedEntry);

      mockRequest.body = { status: INACTIVE };
      const result = await ipAccessControlService.updateStatus(mockRequest);

      expect(result).toEqual({
        result: mockUpdatedEntry,
        auditModelMapping: {
          IpAccessControl: {
            beforeState: mockExistingEntry.toJSON(),
            afterState: mockUpdatedEntry.toJSON(),
            fieldsChanged: ['status'],
          },
        },
      });

      expect(IpAccessControlRepository.update).toHaveBeenCalledWith(
        mockExistingEntry,
        { status: INACTIVE },
        { authInfoId: 'user1' },
      );
    });
  });

  describe('bulkManage', () => {
    beforeEach(() => {
      mockRequest = {
        body: {
          parentId: 'parent123',
          ruleType: 'ALLOW',
          ipAccessControls: [
            { ipAddress: '***********', remark: 'Office IP' },
            { ipAddress: '********', remark: 'VPN IP' },
          ],
        },
        server: { psql: {} },
        authInfo: { id: 'user123' },
        entity: { id: 'entity123' },
      };

      // Reset mocks
      vi.resetAllMocks();

      // Mock the withTransaction function
      withTransaction.mockImplementation((server, options, callback) =>
        callback({ commit: vi.fn() }),
      );

      // Mock the IpAccessControlRepository.removeAll method
      IpAccessControlRepository.removeAll.mockResolvedValue();

      // Mock the create function
      vi.spyOn(ipAccessControlService, 'create').mockResolvedValue({});
    });

    it('should handle empty ipAccessControls array', async () => {
      mockRequest.body.ipAccessControls = [];

      await ipAccessControlService.bulkManage(mockRequest);

      expect(IpAccessControlRepository.removeAll).toHaveBeenCalled();
      expect(ipAccessControlService.create).not.toHaveBeenCalled();
    });

    it('should use withTransaction for database operations', async () => {
      const mockReload = vi.fn();
      IpAccessControlRepository.create.mockResolvedValue(
        populateToJSON({
          id: '1',
          reload: mockReload,
          activeRemark: populateToJSON({ id: '1', content: 'remark' }),
        }),
      );

      await ipAccessControlService.bulkManage(mockRequest);

      expect(withTransaction).toHaveBeenCalledWith(mockRequest.server, {}, expect.any(Function));
    });

    it('should throw an error if removeAll fails', async () => {
      const error = new Error('Database error');
      IpAccessControlRepository.removeAll.mockRejectedValue(error);

      await expect(ipAccessControlService.bulkManage(mockRequest)).rejects.toThrow(
        'Database error',
      );
    });
  });
});
