/* eslint-disable sonarjs/no-hardcoded-ip */
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreError } from '#src/modules/core/errors/index.js';
import { DeveloperHubRepository } from '#src/modules/setting/repository/index.js';
import * as developerHubService from '#src/modules/setting/services/developer-hub.service.js';
import { AccessControlService, ApiRightService } from '#src/modules/setting/services/index.js';
import { clearCacheWithPrefix } from '#src/utils/cache.util.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';
import { populateToJSON } from '#src/utils/test-helper.util.js';

vi.mock('#src/modules/setting/repository/index.js');
vi.mock('#src/modules/setting/services/index.js');
vi.mock('#src/utils/db-transaction.util.js');
vi.mock('#src/utils/cache.util.js');

const CACHE_PREFIX = developerHubService.CACHE_PREFIX;

const mockServer = { psql: { connection: { transaction: vi.fn() } }, redis: {} };
const mockEntity = { id: 'entity-id' };
const mockAuthInfo = { id: 'auth-user-id' };

describe('developerHubService', () => {
  let mockRequest;

  beforeEach(() => {
    mockRequest = {
      entity: mockEntity,
      server: mockServer,
      params: { id: 'dev-id' },
      query: {},
      body: {},
      authInfo: mockAuthInfo,
    };
    vi.clearAllMocks();
  });

  describe('index', () => {
    it('should call findAll with entityId filter', async () => {
      const mockRows = [{ id: '123' }];
      DeveloperHubRepository.findAll.mockResolvedValue({ rows: mockRows });
      const result = await developerHubService.index(mockRequest);
      expect(result).toEqual({ rows: mockRows });
      expect(DeveloperHubRepository.findAll).toHaveBeenCalledWith(mockServer, {
        filter_entityId_eq: 'entity-id',
      });
    });
  });

  describe('view', () => {
    it('should return developer hub when found', async () => {
      const mockDevHub = { id: '123' };
      DeveloperHubRepository.findById.mockResolvedValue(mockDevHub);
      const result = await developerHubService.view(mockRequest);
      expect(result).toEqual(mockDevHub);
    });

    it('should throw error when not found', async () => {
      DeveloperHubRepository.findById.mockResolvedValue(null);
      await expect(() => developerHubService.view(mockRequest)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.developerHub',
          attribute: 'ID',
          value: 'dev-id',
        }),
      );
    });
  });

  describe('create', () => {
    it('should create developer hub with API rights, IP controls, and clear cache', async () => {
      const mockCreatedDevHub = populateToJSON({ id: 'dev-id', set: vi.fn() });
      const mockCreatedApiRight = populateToJSON({ id: 'right1' });
      const mockCreatedAccessControl = {
        result: 'ip1',
        auditModelMapping: {
          IpAccessControl: { afterState: '***********' },
          Remark: { afterState: 'Remark1' },
        },
      };

      DeveloperHubRepository.create.mockResolvedValue(mockCreatedDevHub);
      ApiRightService.create.mockResolvedValue([mockCreatedApiRight]);
      AccessControlService.create.mockResolvedValue(mockCreatedAccessControl);
      clearCacheWithPrefix.mockResolvedValue();

      withTransaction.mockImplementation((_, __, fn) => fn({ transaction: {} }));

      mockRequest.body = {
        application: 'Test App',
        apiKey: 'secret',
        apiRights: [{ endpoint: '/v1/test' }],
        ipAccessControls: [{ ipAddress: '*******' }],
      };

      const result = await developerHubService.create(mockRequest);

      expect(DeveloperHubRepository.create).toHaveBeenCalled();
      expect(ApiRightService.create).toHaveBeenCalled();
      expect(AccessControlService.create).toHaveBeenCalled();
      expect(clearCacheWithPrefix).toHaveBeenCalledWith(
        mockServer.redis,
        CACHE_PREFIX,
        mockEntity.id,
      );
      expect(result).toEqual({
        result: mockCreatedDevHub,
        auditModelMapping: {
          DeveloperHub: { afterState: mockCreatedDevHub.toJSON() },
          ApiRight: [{ afterState: mockCreatedApiRight.toJSON() }],
          IpAccessControl: [mockCreatedAccessControl.auditModelMapping.IpAccessControl],
          Remark: [mockCreatedAccessControl.auditModelMapping.Remark],
        },
      });
    });
  });

  describe('updateBasicInformation', () => {
    it('should update developer hub info and clear cache', async () => {
      const mockDev = { id: '123', application: 'Old App', expiryDate: '2023-12-31T23:59:59Z' };
      const updatedDev = {
        ...mockDev,
        application: 'Updated App',
        expiryDate: '2024-12-31T23:59:59Z',
      };

      DeveloperHubRepository.findById.mockResolvedValue(mockDev);
      DeveloperHubRepository.update.mockResolvedValue(updatedDev);
      clearCacheWithPrefix.mockResolvedValue();

      mockRequest.body = { application: 'Updated App', expiryDate: '2024-12-31T23:59:59Z' };

      const result = await developerHubService.updateBasicInformation(mockRequest);

      expect(DeveloperHubRepository.findById).toHaveBeenCalledWith(
        mockServer,
        { entityId: mockEntity.id, id: 'dev-id' },
        false,
      );
      expect(DeveloperHubRepository.update).toHaveBeenCalledWith(
        mockDev,
        { application: 'Updated App', expiryDate: '2024-12-31T23:59:59Z' },
        { authInfoId: 'auth-user-id' },
      );
      expect(clearCacheWithPrefix).toHaveBeenCalledWith(
        mockServer.redis,
        CACHE_PREFIX,
        mockEntity.id,
      );

      expect(result).toEqual({
        result: mockDev,
        auditModelMapping: { DeveloperHub: { afterState: mockDev } },
      });
    });

    it('should convert empty string expiryDate to null', async () => {
      const mockDev = { id: '123', application: 'Old App', expiryDate: '2023-12-31T23:59:59Z' };
      const updatedDev = { ...mockDev, application: 'Updated App', expiryDate: null };

      DeveloperHubRepository.findById.mockResolvedValue(populateToJSON(mockDev));
      DeveloperHubRepository.update.mockResolvedValue(updatedDev);
      clearCacheWithPrefix.mockResolvedValue();

      mockRequest.body = { application: 'Updated App', expiryDate: '' };

      const result = await developerHubService.updateBasicInformation(mockRequest);

      expect(DeveloperHubRepository.findById).toHaveBeenCalledWith(
        mockServer,
        { entityId: mockEntity.id, id: 'dev-id' },
        false,
      );
      expect(DeveloperHubRepository.update).toHaveBeenCalledWith(
        mockDev,
        { application: 'Updated App', expiryDate: null },
        { authInfoId: 'auth-user-id' },
      );
      expect(clearCacheWithPrefix).toHaveBeenCalledWith(
        mockServer.redis,
        CACHE_PREFIX,
        mockEntity.id,
      );

      expect(result).toEqual({
        result: mockDev,
        auditModelMapping: { DeveloperHub: { afterState: mockDev } },
      });
    });

    it('should throw an error if developer hub is not found', async () => {
      DeveloperHubRepository.findById.mockResolvedValue(null);

      await expect(developerHubService.updateBasicInformation(mockRequest)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.developerHub',
          attribute: 'ID',
          value: 'dev-id',
        }),
      );

      expect(DeveloperHubRepository.findById).toHaveBeenCalledWith(
        mockServer,
        { entityId: mockEntity.id, id: 'dev-id' },
        false,
      );
    });
  });

  describe('updatePermissions', () => {
    it('should update version and API rights', async () => {
      const mockDev = { id: 'dev-id' };
      const updatedDev = { ...mockDev, version: 2, updatedAt: new Date() };
      const mockApiRightUpdate = {
        auditModelMapping: { ApiRight: { afterState: { mock: 'value' } } },
      };

      DeveloperHubRepository.findById.mockResolvedValue(mockDev);
      DeveloperHubRepository.update.mockResolvedValue(updatedDev);
      ApiRightService.update.mockResolvedValue(mockApiRightUpdate);

      withTransaction.mockImplementation((_server, _opts, fn) =>
        Promise.resolve(fn({ transaction: {} })),
      );

      mockRequest.body = { version: 2, apiRights: [{ permission: 'a' }] };
      const { result, auditModelMapping } =
        await developerHubService.updatePermissions(mockRequest);

      expect(result.version).toBe(2);
      expect(auditModelMapping).toEqual({
        DeveloperHub: { afterState: mockDev },
        ...mockApiRightUpdate.auditModelMapping,
      });
      expect(DeveloperHubRepository.update).toHaveBeenCalledWith(
        mockDev,
        expect.objectContaining({ version: 2, updatedAt: expect.any(Date) }),
        expect.objectContaining({ authInfoId: 'auth-user-id', transaction: expect.any(Object) }),
      );
      expect(ApiRightService.update).toHaveBeenCalledWith(
        expect.objectContaining({
          body: {
            apiRights: [{ permission: 'a' }],
            parentId: 'dev-id',
          },
          entity: mockRequest.entity,
          server: mockRequest.server,
          authInfo: mockRequest.authInfo,
        }),
        expect.objectContaining({ transaction: expect.any(Object) }),
      );
    });
  });

  describe('updateAccessControls', () => {
    it('should update version and access controls', async () => {
      const mockDev = { id: 'dev-id' };
      const updatedDev = { ...mockDev, version: 3, updatedAt: new Date() };
      const mockAccessControlUpdate = {
        auditModelMapping: {
          IpAccessControl: { afterState: { mock: 'value' } },
          Remark: { afterState: { mock: 'value' } },
        },
      };

      DeveloperHubRepository.findById.mockResolvedValue(mockDev);
      DeveloperHubRepository.update.mockResolvedValue(updatedDev);
      AccessControlService.bulkManage.mockResolvedValue(mockAccessControlUpdate);

      withTransaction.mockImplementation((_server, _opts, fn) =>
        Promise.resolve(fn({ transaction: {} })),
      );

      mockRequest.body = {
        version: 3,
        ipAccessControls: [{ ipAddress: '*******' }],
      };

      const { result, auditModelMapping } =
        await developerHubService.updateAccessControls(mockRequest);

      expect(result.version).toBe(3);
      expect(auditModelMapping).toEqual({
        DeveloperHub: { afterState: mockDev },
        ...mockAccessControlUpdate.auditModelMapping,
      });
      expect(DeveloperHubRepository.update).toHaveBeenCalledWith(
        mockDev,
        expect.objectContaining({ version: 3, updatedAt: expect.any(Date) }),
        expect.objectContaining({
          authInfoId: mockRequest.authInfo.id,
          transaction: expect.any(Object),
        }),
      );

      expect(AccessControlService.bulkManage).toHaveBeenCalledWith(
        expect.objectContaining({
          body: expect.objectContaining({
            parentId: 'dev-id',
            ipAccessControls: [{ ipAddress: '*******' }],
          }),
          server: mockRequest.server,
          authInfo: mockRequest.authInfo,
          entity: mockRequest.entity,
        }),
        expect.objectContaining({ transaction: expect.any(Object) }),
      );
    });
  });
});
