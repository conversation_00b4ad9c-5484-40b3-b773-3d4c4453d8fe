import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as developerHubRepository from '#src/modules/setting/repository/developer-hub.repository.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/pagination.util.js');
vi.mock('#src/utils/query.util.js');

describe('Developer Hub Repository', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      psql: {
        DeveloperHub: {
          findByPk: vi.fn(),
          findOne: vi.fn(),
          create: vi.fn(),
        },
      },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('findAll', () => {
    it('should call buildWhereFromFilters and applyOffsetPagination with correct parameters', async () => {
      const mockQuery = { page: 1, limit: 10, filter_status_eq: 'active' };
      const mockWhereFilter = { status: 'active' };
      const mockIncludeFilter = [];

      buildWhereFromFilters.mockReturnValue({ where: mockWhereFilter, include: mockIncludeFilter });

      await developerHubRepository.findAll(mockFastify, mockQuery);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(mockQuery, mockFastify.psql.DeveloperHub);
      expect(applyOffsetPagination).toHaveBeenCalledWith(
        mockFastify,
        mockFastify.psql.DeveloperHub,
        mockQuery,
        mockWhereFilter,
        mockIncludeFilter,
      );
    });
  });

  describe('findById', () => {
    it('should call DeveloperHub.findOne with correct parameters', async () => {
      const entityId = 'entity123';
      const id = '123';

      await developerHubRepository.findById(mockFastify, { entityId, id });

      expect(mockFastify.psql.DeveloperHub.findOne).toHaveBeenCalledWith({
        where: { id, entityId },
        include: [
          { association: 'apiRights' },
          {
            association: 'ipAccessControls',
            include: [{ association: 'activeRemark' }],
          },
        ],
      });
    });

    it('should not include association when defined', async () => {
      const entityId = 'entity123';
      const id = '123';

      await developerHubRepository.findById(mockFastify, { entityId, id }, false);

      expect(mockFastify.psql.DeveloperHub.findOne).toHaveBeenCalledWith({
        where: { id, entityId },
      });
    });
  });

  describe('findByApiKey', () => {
    it('should call DeveloperHub.findOne with correct parameters', async () => {
      const apiKey = 'test-api-key';

      await developerHubRepository.findByApiKey(mockFastify, apiKey);

      expect(mockFastify.psql.DeveloperHub.findOne).toHaveBeenCalledWith({
        where: { apiKey },
      });
    });
  });

  describe('create', () => {
    it('should call DeveloperHub.create with correct parameters', async () => {
      const data = { name: 'Test Hub', apiKey: 'test-api-key' };
      const options = { transaction: {} };

      await developerHubRepository.create(mockFastify, data, options);

      expect(mockFastify.psql.DeveloperHub.create).toHaveBeenCalledWith(data, options);
    });
  });

  describe('update', () => {
    it('should call update method on model with correct parameters', async () => {
      const mockModelData = {
        update: vi.fn(),
      };
      const updateData = { name: 'Updated Hub' };
      const options = { transaction: {} };

      await developerHubRepository.update(mockModelData, updateData, options);

      expect(mockModelData.update).toHaveBeenCalledWith(updateData, options);
    });
  });
});
