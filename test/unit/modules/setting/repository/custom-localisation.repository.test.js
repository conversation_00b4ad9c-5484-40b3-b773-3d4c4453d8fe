import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CustomLocalisationRepository } from '#src/modules/setting/repository/index.js';

describe('Custom Localisation Repository', () => {
  let mockServer;

  beforeEach(() => {
    mockServer = {
      psql: {
        CustomLocalisation: {
          create: vi.fn().mockResolvedValue({ id: 1, key: 'welcome_message', value: 'Welcome' }),
          findAll: vi.fn().mockResolvedValue([
            { id: 1, key: 'welcome_message', value: 'Welcome' },
            { id: 2, key: 'goodbye_message', value: 'Goodbye' },
          ]),
          update: vi.fn().mockResolvedValue([2, [{ id: 1 }, { id: 2 }]]),
          findOrCreate: vi
            .fn()
            .mockResolvedValue([{ id: 1, key: 'welcome_message', value: 'Welcome' }, true]),
        },
      },
    };

    vi.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new custom localisation record', async () => {
      const localisationData = { key: 'welcome_message', value: 'Welcome', locale: 'en-US' };
      const options = { transaction: 'mockTransaction' };

      const result = await CustomLocalisationRepository.create(
        mockServer,
        localisationData,
        options,
      );

      expect(mockServer.psql.CustomLocalisation.create).toHaveBeenCalledWith(
        localisationData,
        options,
      );
      expect(result).toEqual({ id: 1, key: 'welcome_message', value: 'Welcome' });
    });

    it('should create a new custom localisation record with default options', async () => {
      const localisationData = { key: 'welcome_message', value: 'Welcome', locale: 'en-US' };
      const result = await CustomLocalisationRepository.create(mockServer, localisationData);

      expect(mockServer.psql.CustomLocalisation.create).toHaveBeenCalledWith(localisationData, {});
      expect(result).toEqual({ id: 1, key: 'welcome_message', value: 'Welcome' });
    });
  });

  describe('findAll', () => {
    it('should find all custom localisation records matching criteria', async () => {
      const query = { where: { locale: 'en-US' } };
      const options = { order: [['key', 'ASC']] };
      const result = await CustomLocalisationRepository.findAll(mockServer, query, options);

      expect(mockServer.psql.CustomLocalisation.findAll).toHaveBeenCalledWith({
        ...query,
        ...options,
      });
      expect(result).toEqual([
        { id: 1, key: 'welcome_message', value: 'Welcome' },
        { id: 2, key: 'goodbye_message', value: 'Goodbye' },
      ]);
    });

    it('should find all custom localisation records with default options', async () => {
      const query = { where: { locale: 'en-US' } };
      const result = await CustomLocalisationRepository.findAll(mockServer, query);

      expect(mockServer.psql.CustomLocalisation.findAll).toHaveBeenCalledWith({
        ...query,
      });
      expect(result).toEqual([
        { id: 1, key: 'welcome_message', value: 'Welcome' },
        { id: 2, key: 'goodbye_message', value: 'Goodbye' },
      ]);
    });
  });

  describe('update', () => {
    it('should update custom localisation records matching criteria', async () => {
      const data = { value: 'Updated Welcome' };
      const where = { key: 'welcome_message' };
      const options = { returning: true };
      const result = await CustomLocalisationRepository.update(mockServer, data, where, options);

      expect(mockServer.psql.CustomLocalisation.update).toHaveBeenCalledWith(data, {
        where,
        ...options,
      });
      expect(result).toEqual([2, [{ id: 1 }, { id: 2 }]]);
    });

    it('should update custom localisation records with default options', async () => {
      const data = { value: 'Updated Welcome' };
      const where = { key: 'welcome_message' };
      const result = await CustomLocalisationRepository.update(mockServer, data, where);

      expect(mockServer.psql.CustomLocalisation.update).toHaveBeenCalledWith(data, {
        where,
      });
      expect(result).toEqual([2, [{ id: 1 }, { id: 2 }]]);
    });
  });

  describe('findOrCreate', () => {
    it('should find or create a custom localisation record', async () => {
      const query = {
        where: { key: 'welcome_message', locale: 'en-US' },
        defaults: { value: 'Welcome' },
      };
      const options = { transaction: 'mockTransaction' };

      const result = await CustomLocalisationRepository.findOrCreate(mockServer, query, options);

      expect(mockServer.psql.CustomLocalisation.findOrCreate).toHaveBeenCalledWith({
        ...query,
        ...options,
      });
      expect(result).toEqual([{ id: 1, key: 'welcome_message', value: 'Welcome' }, true]);
    });

    it('should find or create a custom localisation record with default options', async () => {
      const query = {
        where: { key: 'welcome_message', locale: 'en-US' },
        defaults: { value: 'Welcome' },
      };

      const result = await CustomLocalisationRepository.findOrCreate(mockServer, query);

      expect(mockServer.psql.CustomLocalisation.findOrCreate).toHaveBeenCalledWith({
        ...query,
      });
      expect(result).toEqual([{ id: 1, key: 'welcome_message', value: 'Welcome' }, true]);
    });
  });
});
