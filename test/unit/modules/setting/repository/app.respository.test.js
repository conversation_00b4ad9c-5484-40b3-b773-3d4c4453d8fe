import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as appRepository from '#src/modules/setting/repository/app.repository.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/query.util.js');
vi.mock('#src/utils/pagination.util.js');

describe('App Repository', () => {
  let mockFastify;
  const mockWhereFilter = { mocked: 'where' };
  const mockIncludeFilter = [{ mocked: 'include' }];

  beforeEach(() => {
    mockFastify = {
      psql: {
        App: {
          findOne: vi.fn(),
        },
        EntityApp: {
          findOne: vi.fn(),
          create: vi.fn(),
        },
        AppConfig: {
          findOrCreate: vi.fn(),
        },
      },
    };

    buildWhereFromFilters.mockReturnValue({
      where: mockWhereFilter,
      include: mockIncludeFilter,
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('findAll', () => {
    it('should call applyOffsetPagination with correct args', async () => {
      const query = { page: 1, size: 10 };
      await appRepository.findAll(mockFastify, query);

      expect(applyOffsetPagination).toHaveBeenCalledWith(
        mockFastify,
        mockFastify.psql.App,
        query,
        mockWhereFilter,
        mockIncludeFilter,
      );
    });

    it('should return result from applyOffsetPagination', async () => {
      const mockResult = { data: 'result' };
      applyOffsetPagination.mockResolvedValueOnce(mockResult);

      const result = await appRepository.findAll(mockFastify, {});

      expect(result).toEqual(mockResult);
    });
  });

  describe('findById', () => {
    it('should call App.findOne with correct filters', async () => {
      const query = { id: 1 };
      await appRepository.findById(mockFastify, query);

      expect(mockFastify.psql.App.findOne).toHaveBeenCalledWith({
        where: mockWhereFilter,
        include: mockIncludeFilter,
      });
    });

    it('should return result from findOne', async () => {
      const mockResult = { data: 'result' };
      mockFastify.psql.App.findOne.mockResolvedValueOnce(mockResult);
      const result = await appRepository.findById(mockFastify, {});
      expect(result).toEqual(mockResult);
    });
  });

  describe('update', () => {
    it('should update single model instance', async () => {
      const mockOptions = { transaction: 'mock transaction' };
      const mockModel = { update: vi.fn() };
      const updateData = { name: 'Updated' };
      await appRepository.update(mockModel, updateData, mockOptions);
      expect(mockModel.update).toHaveBeenCalledWith(updateData, mockOptions);
    });

    it('should update multiple model instances', async () => {
      const mockOptions = { transaction: 'mock transaction' };
      const mockModels = [{ update: vi.fn() }, { update: vi.fn() }];
      const updateData = { name: 'Batch Updated' };
      await appRepository.update(mockModels, updateData, mockOptions);
      for (const model of mockModels) {
        expect(model.update).toHaveBeenCalledWith(updateData, mockOptions);
      }
    });
  });

  describe('softDelete', () => {
    it('should destroy a model instance', async () => {
      const mockModel = { destroy: vi.fn() };
      await appRepository.softDelete(mockModel);

      expect(mockModel.destroy).toHaveBeenCalled();
    });

    it('should destroy a model instance with options if provided', async () => {
      const mockModel = { destroy: vi.fn() };
      const mockOptions = { transaction: 'mock transaction' };
      await appRepository.softDelete(mockModel, mockOptions);
      expect(mockModel.destroy).toHaveBeenCalledWith(mockOptions);
    });
  });

  describe('findEntityAppById', () => {
    it('should call EntityApp.findOne with correct filters', async () => {
      const query = { id: 10 };
      await appRepository.findEntityAppById(mockFastify, query);

      expect(mockFastify.psql.EntityApp.findOne).toHaveBeenCalledWith({
        where: mockWhereFilter,
        include: mockIncludeFilter,
      });
    });

    it('should return result from findOne', async () => {
      const mockResult = { data: 'result' };
      mockFastify.psql.App.findOne.mockResolvedValueOnce(mockResult);
      const result = await appRepository.findById(mockFastify, {});
      expect(result).toEqual(mockResult);
    });
  });

  describe('createEntityApp', () => {
    it('should create EntityApp with given data and options', async () => {
      const data = { name: 'New EntityApp' };
      const options = { transaction: 'mockTx' };
      await appRepository.createEntityApp(mockFastify, data, options);

      expect(mockFastify.psql.EntityApp.create).toHaveBeenCalledWith(data, options);
    });

    it('should create EntityApp with given data with no options provided', async () => {
      const data = { name: 'New EntityApp' };
      await appRepository.createEntityApp(mockFastify, data);
      expect(mockFastify.psql.EntityApp.create).toHaveBeenCalledWith(data, {});
    });

    it('should return result from create', async () => {
      const mockResult = { data: 'result' };
      mockFastify.psql.EntityApp.create.mockResolvedValueOnce(mockResult);
      const data = { name: 'New EntityApp' };
      const result = await appRepository.createEntityApp(mockFastify, data);
      expect(result).toEqual(mockResult);
    });
  });

  describe('upsertAppConfig', () => {
    it('should update config if existing found', async () => {
      const mockAppConfig = { update: vi.fn() };
      mockFastify.psql.AppConfig.findOrCreate.mockResolvedValue([mockAppConfig, false]);

      const data = {
        entityAppId: 1,
        configKey: 'theme',
        configValue: 'dark',
      };

      await appRepository.upsertAppConfig(mockFastify, data);

      expect(mockAppConfig.update).toHaveBeenCalledWith({ configValue: 'dark' }, {});
    });

    it('should not call update if new record is created', async () => {
      const mockAppConfig = { update: vi.fn() };
      mockFastify.psql.AppConfig.findOrCreate.mockResolvedValue([mockAppConfig, true]);

      const data = {
        entityAppId: 1,
        configKey: 'theme',
        configValue: 'dark',
      };

      await appRepository.upsertAppConfig(mockFastify, data);

      expect(mockAppConfig.update).not.toHaveBeenCalled();
    });

    it('should call findOrCreate with entityAppId and configKey with default value for new record', async () => {
      mockFastify.psql.AppConfig.findOrCreate.mockResolvedValue([{}, true]);
      const data = { entityAppId: 1, configKey: 'theme', configValue: 'dark' };
      await appRepository.upsertAppConfig(mockFastify, data);
      expect(mockFastify.psql.AppConfig.findOrCreate).toBeCalledWith({
        where: { entityAppId: data.entityAppId, configKey: data.configKey },
        defaults: { configValue: data.configValue },
      });
    });

    it('should use provided options if provided', async () => {
      const mockAppConfig = { update: vi.fn() };
      mockFastify.psql.AppConfig.findOrCreate.mockResolvedValue([mockAppConfig, false]);
      const data = { entityAppId: 1, configKey: 'theme', configValue: 'dark' };
      const mockOptions = { transaction: 'mock transaction' };
      await appRepository.upsertAppConfig(mockFastify, data, mockOptions);
      expect(mockFastify.psql.AppConfig.findOrCreate).toBeCalledWith(
        expect.objectContaining(mockOptions),
      );
      expect(mockAppConfig.update).toHaveBeenCalledWith(expect.any(Object), mockOptions);
    });

    it('should return updated result', async () => {
      const mockAppConfigResult = { data: 'mocked result', update: vi.fn() };
      mockFastify.psql.AppConfig.findOrCreate.mockResolvedValue([mockAppConfigResult, false]);
      const data = { entityAppId: 1, configKey: 'theme', configValue: 'dark' };
      const result = await appRepository.upsertAppConfig(mockFastify, data);
      expect(result).toEqual(mockAppConfigResult);
    });
  });
});
