import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as localisationRepository from '#src/modules/setting/repository/localisation.repository.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';
vi.mock('#src/utils/pagination.util.js');
vi.mock('#src/utils/query.util.js');

describe('Localisation Repository', () => {
  let mockFastify = {};
  const mockWhereFilter = { mock: 'where' };
  const mockIncludeFilter = [{ mock: 'include' }];

  beforeEach(() => {
    mockFastify = {
      psql: {
        CustomLocalisation: {
          scope: vi.fn().mockReturnThis(),
          findOne: vi.fn(),
        },
        Localisation: {
          findOne: vi.fn(),
          count: vi.fn(),
        },
      },
      config: {
        BASE_CURRENCY: 'USD',
      },
    };

    buildWhereFromFilters.mockReturnValue({
      where: mockWhereFilter,
      include: mockIncludeFilter,
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('findAll', () => {
    it('should call applyOffsetPagination with correct parameters', async () => {
      const query = {
        page: 1,
        limit: 10,
        filter_entityId_eq: '123',
      };

      await localisationRepository.findAll(mockFastify, query);

      expect(applyOffsetPagination).toHaveBeenCalledWith(
        mockFastify,
        mockFastify.psql.CustomLocalisation,
        query,
        mockWhereFilter,
        mockIncludeFilter,
      );
    });
  });

  describe('findById', () => {
    it('should call CustomLocalisation.findOne with correct parameters', async () => {
      const entityIds = ['1', '2'];
      const id = '123';

      const query = {
        filter_id_eq: id,
        filter_entityId_eq: entityIds.join(','),
      };

      await localisationRepository.findById(mockFastify, query);

      expect(mockFastify.psql.CustomLocalisation.findOne).toHaveBeenCalledWith({
        where: mockWhereFilter,
        include: mockIncludeFilter,
      });
    });
  });

  describe('findBaseCurrency', () => {
    it('should call Localisation.findOne with correct parameters', async () => {
      await localisationRepository.findBaseCurrency(mockFastify);

      expect(mockFastify.psql.Localisation.findOne).toHaveBeenCalledWith({
        where: {
          code: mockFastify.config.BASE_CURRENCY,
        },
      });
    });
  });

  describe('findActiveByParentId', () => {
    it('should call CustomLocalisation.findOne with correct parameters', async () => {
      const entityIds = ['1', '2'];
      const parentId = '456';

      const query = {
        filter_entityId_in: entityIds.join(','),
        filter_parentId_eq: parentId,
      };

      await localisationRepository.findActiveByParentId(mockFastify, query);

      expect(mockFastify.psql.CustomLocalisation.findOne).toHaveBeenCalledWith({
        where: mockWhereFilter,
      });
    });
  });

  describe('update', () => {
    it('should call update method on model with correct parameters', async () => {
      const mockModelData = {
        update: vi.fn(),
      };
      const updateData = { name: 'New Name' };
      const authInfo = { id: '789' };

      await localisationRepository.update(mockModelData, updateData, authInfo);

      expect(mockModelData.update).toHaveBeenCalledWith(updateData, { authInfoId: authInfo.id });
    });
  });

  describe('count', () => {
    it('should call Localisation.count with correct parameters', async () => {
      const whereCondition = { status: 'active' };
      const options = { distinct: true };

      mockFastify.psql.Localisation.count.mockResolvedValue(5);

      const result = await localisationRepository.count(mockFastify, whereCondition, options);

      expect(mockFastify.psql.Localisation.count).toHaveBeenCalledWith({
        where: whereCondition,
        distinct: true,
      });
      expect(result).toBe(5);
    });

    it('should call Localisation.count with only where condition when no options provided', async () => {
      const whereCondition = { status: 'active' };

      mockFastify.psql.Localisation.count.mockResolvedValue(3);

      const result = await localisationRepository.count(mockFastify, whereCondition);

      expect(mockFastify.psql.Localisation.count).toHaveBeenCalledWith({
        where: whereCondition,
      });
      expect(result).toBe(3);
    });
  });
});
