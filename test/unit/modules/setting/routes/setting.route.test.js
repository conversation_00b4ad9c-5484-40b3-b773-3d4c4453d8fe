import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { SettingHandler } from '#src/modules/setting/handlers/index.js';
import SettingRoute from '#src/modules/setting/routes/setting.route.js';
import { SettingSchema } from '#src/modules/setting/schemas/index.js';

vi.mock('#src/modules/setting/handlers/index.js', () => ({
  SettingHandler: {
    index: vi.fn(),
    updatePersonal: vi.fn(),
    updateSafety: vi.fn(),
    updateThemes: vi.fn(),
    updateMaintenanceStatus: vi.fn(),
    options: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/schemas/index.js', () => ({
  SettingSchema: {
    index: { mock: 'index-schema' },
    updatePersonal: { mock: 'update-personal-schema' },
    updateSafety: { mock: 'update-safety-schema' },
    updateThemes: { mock: 'update-themes-schema' },
    updateMaintenanceStatus: { mock: 'update-maintenance-status-schema' },
    options: { mock: 'options-schema' },
  },
}));

describe('Setting Route', () => {
  let mockFastify;

  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: true,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  beforeEach(() => {
    mockFastify = {
      get: vi.fn(),
      put: vi.fn(),
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should register GET route for listing settings', async () => {
    await SettingRoute(mockFastify);

    expect(mockFastify.get).toHaveBeenCalledWith('/:category', {
      schema: SettingSchema.index,
      config: { name: 'setting.view', access: commonAccessConfig },
      handler: SettingHandler.index,
    });
  });

  it('should register PUT route for updating personal settings', async () => {
    await SettingRoute(mockFastify);

    expect(mockFastify.put).toHaveBeenCalledWith('/personal', {
      schema: SettingSchema.updatePersonal,
      config: { name: 'setting.updatePersonal', access: commonAccessConfig },
      handler: SettingHandler.updatePersonal,
    });
  });

  it('should register PUT route for updating safety settings', async () => {
    await SettingRoute(mockFastify);

    expect(mockFastify.put).toHaveBeenCalledWith('/safety', {
      schema: SettingSchema.updateSafety,
      config: { name: 'setting.updateSafety', access: commonAccessConfig },
      handler: SettingHandler.updateSafety,
    });
  });

  it('should register PUT route for updating themes settings', async () => {
    await SettingRoute(mockFastify);

    expect(mockFastify.put).toHaveBeenCalledWith('/themes', {
      schema: SettingSchema.updateThemes,
      config: { name: 'setting.updateThemes', access: commonAccessConfig },
      handler: SettingHandler.updateThemes,
    });
  });

  it('should register PUT route for updating maintenance status', async () => {
    await SettingRoute(mockFastify);

    expect(mockFastify.put).toHaveBeenCalledWith('/maintenance-status', {
      schema: SettingSchema.updateMaintenanceStatus,
      config: { name: 'setting.updateMaintenanceStatus', access: commonAccessConfig },
      handler: SettingHandler.updateMaintenanceStatus,
    });
  });

  it('should register GET route for additional options', async () => {
    await SettingRoute(mockFastify);

    expect(mockFastify.get).toHaveBeenCalledWith('/:category/options', {
      schema: SettingSchema.options,
      config: { name: 'setting.options', access: commonAccessConfig },
      handler: SettingHandler.options,
    });
  });

  it('should register all routes', async () => {
    await SettingRoute(mockFastify);

    expect(mockFastify.get).toHaveBeenCalledTimes(2);
    expect(mockFastify.put).toHaveBeenCalledTimes(4);
  });
});
