import { beforeEach, describe, expect, it, vi } from 'vitest';

import { AccessControlHandler } from '#src/modules/setting/handlers/index.js';
import AccessControlRoute from '#src/modules/setting/routes/access-control.route.js';
import { AccessControlSchema } from '#src/modules/setting/schemas/index.js';

describe('Access Control Route', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      patch: vi.fn(),
    };
  });

  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: false,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  it('should register all routes correctly', async () => {
    await AccessControlRoute(fastifyMock);

    // Check if all routes are registered
    expect(fastifyMock.get).toHaveBeenCalledTimes(2);
    expect(fastifyMock.post).toHaveBeenCalledTimes(1);
    expect(fastifyMock.put).toHaveBeenCalledTimes(1);
    expect(fastifyMock.patch).toHaveBeenCalledTimes(1);
  });

  it('should register the list route correctly', async () => {
    await AccessControlRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith('/', {
      schema: AccessControlSchema.index,
      config: { name: 'accessControl.list', access: commonAccessConfig },
      handler: AccessControlHandler.index,
    });
  });

  it('should register the create route correctly', async () => {
    await AccessControlRoute(fastifyMock);

    expect(fastifyMock.post).toHaveBeenCalledWith('/', {
      schema: AccessControlSchema.create,
      config: { name: 'accessControl.create', access: commonAccessConfig },
      handler: AccessControlHandler.create,
    });
  });

  it('should register the view route correctly', async () => {
    await AccessControlRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith('/:id', {
      schema: AccessControlSchema.view,
      config: { name: 'accessControl.view', access: commonAccessConfig },
      handler: AccessControlHandler.view,
    });
  });

  it('should register the update route correctly', async () => {
    await AccessControlRoute(fastifyMock);

    expect(fastifyMock.put).toHaveBeenCalledWith('/:id', {
      schema: AccessControlSchema.update,
      config: { name: 'accessControl.update', access: commonAccessConfig },
      handler: AccessControlHandler.update,
    });
  });

  it('should register the updateStatus route correctly', async () => {
    await AccessControlRoute(fastifyMock);

    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/status', {
      schema: AccessControlSchema.updateStatus,
      config: { name: 'accessControl.updateStatus', access: commonAccessConfig },
      handler: AccessControlHandler.updateStatus,
    });
  });
});
