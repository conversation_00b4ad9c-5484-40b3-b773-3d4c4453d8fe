import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { AppHandler } from '#src/modules/setting/handlers/index.js';
import AppRoute from '#src/modules/setting/routes/app.route.js';
import { AppSchema } from '#src/modules/setting/schemas/index.js';

vi.mock('#src/modules/setting/handlers/index.js', () => ({
  AppHandler: {
    index: vi.fn(),
    create: vi.fn(),
    view: vi.fn(),
    update: vi.fn(),
    remove: vi.fn(),
    runTest: vi.fn(),
    options: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/schemas/index.js', () => ({
  AppSchema: {
    index: { mock: 'index-schema' },
    create: { mock: 'create-schema' },
    view: { mock: 'view-schema' },
    update: { mock: 'update-schema' },
    remove: { mock: 'remove-schema' },
    runTest: { mock: 'runTest-schema' },
    options: { mock: 'options-schema' },
  },
}));

describe('App Route', () => {
  let mockFastify;

  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: false,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  beforeEach(() => {
    mockFastify = {
      get: vi.fn(),
      post: vi.fn(),
      delete: vi.fn(),
      put: vi.fn(),
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should register GET route for listing apps', async () => {
    await AppRoute(mockFastify);

    expect(mockFastify.get).toHaveBeenCalledWith('/', {
      schema: AppSchema.index,
      config: { name: 'app.list', policy: 'apps.canView', access: commonAccessConfig },
      handler: AppHandler.index,
    });
  });

  it('should register POST route for creating an app', async () => {
    await AppRoute(mockFastify);

    expect(mockFastify.post).toHaveBeenCalledWith('/', {
      schema: AppSchema.create,
      config: { name: 'app.create', policy: 'apps.canCreate', access: commonAccessConfig },
      handler: AppHandler.create,
    });
  });

  it('should register GET route for viewing an app', async () => {
    await AppRoute(mockFastify);

    expect(mockFastify.get).toHaveBeenCalledWith('/:id', {
      schema: AppSchema.view,
      config: { name: 'app.view', policy: 'apps.canView', access: commonAccessConfig },
      handler: AppHandler.view,
    });
  });

  it('should register PUT route for updating an app', async () => {
    await AppRoute(mockFastify);

    expect(mockFastify.put).toHaveBeenCalledWith('/:id', {
      schema: AppSchema.update,
      config: { name: 'app.update', policy: 'apps.canEdit', access: commonAccessConfig },
      handler: AppHandler.update,
    });
  });

  it('should register DELETE route for removing an app', async () => {
    await AppRoute(mockFastify);

    expect(mockFastify.delete).toHaveBeenCalledWith('/:id', {
      schema: AppSchema.remove,
      config: { name: 'app.remove', policy: 'apps.canManage', access: commonAccessConfig },
      handler: AppHandler.remove,
    });
  });

  it('should register POST route for testing an app', async () => {
    await AppRoute(mockFastify);

    expect(mockFastify.post).toHaveBeenCalledWith('/test/:id', {
      schema: AppSchema.runTest,
      config: { name: 'app.runTest', policy: 'apps.canManage', access: commonAccessConfig },
      handler: AppHandler.runTest,
    });
  });

  it('should register POST route for testing (upload) an app', async () => {
    await AppRoute(mockFastify);

    expect(mockFastify.post).toHaveBeenCalledWith('/test-upload/:id', {
      schema: AppSchema.runTestUpload,
      config: { name: 'app.runTest', policy: 'apps.canManage', access: commonAccessConfig },
      handler: AppHandler.runTest,
    });
  });

  it('should register GET route for options', async () => {
    await AppRoute(mockFastify);

    expect(mockFastify.get).toHaveBeenCalledWith('/options', {
      schema: AppSchema.options,
      config: { name: 'app.options', policy: 'apps.canView', access: commonAccessConfig },
      handler: AppHandler.options,
    });
  });

  it('should register all routes correctly', async () => {
    await AppRoute(mockFastify);

    expect(mockFastify.get).toHaveBeenCalledTimes(3); // '/', '/:id', '/options'
    expect(mockFastify.post).toHaveBeenCalledTimes(3); // '/', '/test/:id'
    expect(mockFastify.put).toHaveBeenCalledTimes(1); // '/:id'
    expect(mockFastify.delete).toHaveBeenCalledTimes(1); // '/:id'
  });
});
