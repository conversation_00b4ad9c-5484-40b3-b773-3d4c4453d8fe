import { beforeEach, describe, expect, it, vi } from 'vitest';

import { LocalisationHandler } from '#src/modules/setting/handlers/index.js';
import LocalisationRoute from '#src/modules/setting/routes/localisation.route.js';
import { LocalisationSchema } from '#src/modules/setting/schemas/index.js';

describe('Localisation Route', () => {
  let fastifyMock;

  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: true,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  beforeEach(() => {
    fastifyMock = {
      get: vi.fn(),
      put: vi.fn(),
      patch: vi.fn(),
    };
  });

  it('should register all routes correctly', async () => {
    await LocalisationRoute(fastifyMock);

    // Check if all routes are registered
    expect(fastifyMock.get).toHaveBeenCalledTimes(2);
    expect(fastifyMock.put).toHaveBeenCalledTimes(1);
    expect(fastifyMock.patch).toHaveBeenCalledTimes(1);
  });

  it('should register the list route correctly', async () => {
    await LocalisationRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith('/', {
      schema: LocalisationSchema.index,
      config: { name: 'localisation.list', access: commonAccessConfig },
      handler: LocalisationHandler.index,
    });
  });

  it('should register the view route correctly', async () => {
    await LocalisationRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith('/:id', {
      schema: LocalisationSchema.view,
      config: { name: 'localisation.view', access: commonAccessConfig },
      handler: LocalisationHandler.view,
    });
  });

  it('should register the update route correctly', async () => {
    await LocalisationRoute(fastifyMock);

    expect(fastifyMock.put).toHaveBeenCalledWith('/:id', {
      schema: LocalisationSchema.update,
      config: { name: 'localisation.update', access: commonAccessConfig },
      handler: LocalisationHandler.update,
    });
  });

  it('should register the updateStatus route correctly', async () => {
    await LocalisationRoute(fastifyMock);

    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/status', {
      schema: LocalisationSchema.updateStatus,
      config: { name: 'localisation.updateStatus', access: commonAccessConfig },
      handler: LocalisationHandler.updateStatus,
    });
  });
});
