import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  create,
  index,
  options,
  updateAccessControls,
  updateBasicInformation,
  updatePermissions,
  updateStatus,
  view,
} from '#src/modules/setting/handlers/developer-hub.handler.js';
import { DeveloperHubService } from '#src/modules/setting/services/index.js';
import { initializeAuditMeta } from '#src/utils/audit-trail.util.js';
import * as cacheUtil from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

// Mock dependencies
vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/setting/services/index.js');
vi.mock('#src/utils/audit-trail.util.js');

describe('Developer Hub Handler', () => {
  let mockRequest;
  let mockReply;
  const mockResult = { result: 'created', auditModelMapping: 'auditMapping' };

  beforeEach(() => {
    mockRequest = {
      server: {
        redis: {},
      },
      params: { id: 'uuid' },
    };
    mockReply = {};

    // Reset mocks
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      cacheUtil.generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      expect(cacheUtil.generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.DEVELOPER_HUB}_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(cacheUtil.fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should prepare for audit log object', async () => {
      await index(mockRequest, mockReply);

      expect(initializeAuditMeta).toHaveBeenCalledWith(mockRequest, {
        module: 'developerHubs',
        event: 'developerHub',
        action: 'searched',
      });
    });
  });

  describe('view', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      cacheUtil.generateCacheKey.mockReturnValue(mockCacheKey);

      const mockResult = 'mockResult';
      // DeveloperHubService.view.mockResolvedValue(mockResult);
      cacheUtil.fetchFromCache.mockResolvedValue(mockResult);

      await view(mockRequest, mockReply);

      expect(cacheUtil.generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.DEVELOPER_HUB}_${CoreConstant.MODULE_METHODS.VIEW}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.VIEW,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(cacheUtil.fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should prepare for audit log object', async () => {
      await view(mockRequest, mockReply);

      expect(initializeAuditMeta).toHaveBeenCalledWith(
        mockRequest,
        {
          module: 'developerHubs',
          event: 'developerHub',
          action: 'detailsViewed',
        },
        mockRequest.params.id,
      );
    });
  });

  describe('create', () => {
    beforeEach(() => {
      DeveloperHubService.create.mockResolvedValue(mockResult);
    });

    it('should call handleServiceResponse with correct parameters', async () => {
      await create(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.CREATE,
        audit: {
          modelMapping: mockResult.auditModelMapping,
        },
      });
    });

    it('should call DeveloperHubService.create when serviceFn is executed', async () => {
      await create(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(DeveloperHubService.create).toHaveBeenCalledWith(mockRequest);
    });

    it('should prepare for audit log object', async () => {
      await create(mockRequest, mockReply);

      expect(initializeAuditMeta).toHaveBeenCalledWith(mockRequest, {
        module: 'developerHubs',
        event: 'developerHub',
        action: 'created',
      });
    });
  });

  describe('updateBasicInformation', () => {
    beforeEach(() => {
      DeveloperHubService.updateBasicInformation.mockResolvedValue(mockResult);
    });

    it('should call handleServiceResponse with correct parameters', async () => {
      await updateBasicInformation(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.UPDATE_BASIC_INFORMATION,
        audit: {
          modelMapping: mockResult.auditModelMapping,
        },
      });
    });

    it('should call DeveloperHubService.updateBasicInformation when serviceFn is executed', async () => {
      await updateBasicInformation(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(DeveloperHubService.updateBasicInformation).toHaveBeenCalledWith(mockRequest);
    });

    it('should prepare for audit log object', async () => {
      await updateBasicInformation(mockRequest, mockReply);

      expect(initializeAuditMeta).toHaveBeenCalledWith(
        mockRequest,
        {
          module: 'developerHubs',
          event: 'developerHub',
          action: 'basicInformationUpdated',
        },
        mockRequest.params.id,
      );
    });
  });

  describe('updatePermissions', () => {
    beforeEach(() => {
      DeveloperHubService.updatePermissions.mockResolvedValue(mockResult);
    });

    it('should call handleServiceResponse with correct parameters', async () => {
      await updatePermissions(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.UPDATE_PERMISSION,
        audit: {
          modelMapping: mockResult.auditModelMapping,
        },
      });
    });

    it('should call DeveloperHubService.updatePermissions when serviceFn is executed', async () => {
      await updatePermissions(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(DeveloperHubService.updatePermissions).toHaveBeenCalledWith(mockRequest);
    });

    it('should prepare for audit log object', async () => {
      await updatePermissions(mockRequest, mockReply);

      expect(initializeAuditMeta).toHaveBeenCalledWith(
        mockRequest,
        {
          module: 'developerHubs',
          event: 'developerHub',
          action: 'policyUpdated',
        },
        mockRequest.params.id,
      );
    });
  });

  describe('updateAccessControls', () => {
    beforeEach(() => {
      DeveloperHubService.updateAccessControls.mockResolvedValue(mockResult);
    });

    it('should call handleServiceResponse with correct parameters', async () => {
      await updateAccessControls(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.UPDATE_ACCESS_CONTROL,
        audit: {
          modelMapping: mockResult.auditModelMapping,
        },
      });
    });

    it('should call DeveloperHubService.updateAccessControls when serviceFn is executed', async () => {
      await updateAccessControls(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(DeveloperHubService.updateAccessControls).toHaveBeenCalledWith(mockRequest);
    });

    it('should prepare for audit log object', async () => {
      await updateAccessControls(mockRequest, mockReply);

      expect(initializeAuditMeta).toHaveBeenCalledWith(
        mockRequest,
        {
          module: 'developerHubs',
          event: 'developerHub',
          action: 'accessControlUpdated',
        },
        mockRequest.params.id,
      );
    });
  });

  describe('updateStatus', () => {
    beforeEach(() => {
      DeveloperHubService.updateBasicInformation.mockResolvedValue(mockResult);
    });

    it('should call handleServiceResponse with correct parameters', async () => {
      await updateStatus(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.UPDATE_STATUS,
        audit: {
          modelMapping: mockResult.auditModelMapping,
        },
      });
    });

    it('should call DeveloperHubService.updateBasicInformation when serviceFn is executed', async () => {
      await updateStatus(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(DeveloperHubService.updateBasicInformation).toHaveBeenCalledWith(mockRequest);
    });

    it('should prepare for audit log object', async () => {
      await updateStatus(mockRequest, mockReply);

      expect(initializeAuditMeta).toHaveBeenCalledWith(
        mockRequest,
        {
          module: 'developerHubs',
          event: 'developerHub',
          action: 'statusUpdated',
        },
        mockRequest.params.id,
      );
    });
  });

  describe('options', () => {
    it('should call handleServiceResponse with correct parameters and return module permissions', async () => {
      await options(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.OPTION,
      });

      // Call the serviceFn to verify the returned data
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();

      expect(result).toEqual({
        modulePermissions: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            name: expect.any(String),
            availablePermissions: expect.any(Array),
          }),
        ]),
      });
    });

    it('should prepare for audit log object', async () => {
      await options(mockRequest, mockReply);

      expect(initializeAuditMeta).toHaveBeenCalledWith(mockRequest, {
        module: 'developerHubs',
        event: 'developerHub',
        action: 'viewed',
      });
    });
  });
});
