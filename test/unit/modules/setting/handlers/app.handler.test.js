import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  create,
  index,
  options,
  remove,
  runTest,
  update,
  view,
} from '#src/modules/setting/handlers/app.handler.js';
import { AppService } from '#src/modules/setting/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/setting/services/index.js');

describe('App Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      server: {
        redis: {},
      },
    };
    mockReply = {};

    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should generate cache key and call handleServiceResponse with cached function', async () => {
      const mockCacheKey = 'app_index_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.APP}_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.APP,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should call AppService.index if cache miss', async () => {
      generateCacheKey.mockReturnValue('miss_key');
      fetchFromCache.mockImplementation((_, __, callback) => callback());

      await index(mockRequest, mockReply);
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(AppService.index).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with AppService.create', async () => {
      await create(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AppService.create,
        module: CoreConstant.MODULE_NAMES.APP,
        method: CoreConstant.MODULE_METHODS.CREATE,
      });
    });
  });

  describe('remove', () => {
    it('should call handleServiceResponse with AppService.remove', async () => {
      await remove(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AppService.remove,
        module: CoreConstant.MODULE_NAMES.APP,
        method: CoreConstant.MODULE_METHODS.DELETE,
      });
    });
  });

  describe('update', () => {
    it('should call handleServiceResponse with AppService.update', async () => {
      await update(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AppService.update,
        module: CoreConstant.MODULE_NAMES.APP,
        method: CoreConstant.MODULE_METHODS.UPDATE,
      });
    });
  });

  describe('view', () => {
    it('should call handleServiceResponse with AppService.view', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await view(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.APP}_${CoreConstant.MODULE_METHODS.VIEW}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.APP,
        method: CoreConstant.MODULE_METHODS.VIEW,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });
  });

  describe('runTest', () => {
    it('should call handleServiceResponse with AppService.runTest', async () => {
      await runTest(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AppService.runTest,
        module: CoreConstant.MODULE_NAMES.APP,
        method: CoreConstant.MODULE_METHODS.TEST,
      });
    });
  });

  describe('options', () => {
    it('should call handleServiceResponse with AppService.options', async () => {
      await options(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AppService.options,
        module: CoreConstant.MODULE_NAMES.APP,
        method: CoreConstant.MODULE_METHODS.OPTION,
      });
    });
  });
});
