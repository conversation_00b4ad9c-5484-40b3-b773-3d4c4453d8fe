import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  create,
  index,
  update,
  updateStatus,
  view,
} from '#src/modules/setting/handlers/access-control.handler.js';
import { AccessControlService } from '#src/modules/setting/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

// Mock dependencies
vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/setting/services/index.js');

describe('Access Control Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      server: {
        redis: {},
        withAuditLogging: vi.fn().mockResolvedValue(undefined),
      },
      params: { id: 'ac1' },
    };
    mockReply = {};

    // Reset mocks
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ACCESS_CONTROL}_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn,
        module: CoreConstant.MODULE_NAMES.ACCESS_CONTROL,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should call AccessControlService.index if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await index(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(AccessControlService.index).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('view', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      const mockResult = 'mockResult';
      AccessControlService.view.mockResolvedValue(mockResult);

      fetchFromCache.mockResolvedValue(mockResult);

      await view(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ACCESS_CONTROL}_${CoreConstant.MODULE_METHODS.VIEW}`,
        mockRequest,
      );

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn,
        module: CoreConstant.MODULE_NAMES.ACCESS_CONTROL,
        method: CoreConstant.MODULE_METHODS.VIEW,
        audit: {
          modelMapping: {
            IpAccessControl: {
              beforeState: mockResult,
            },
          },
        },
      });

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should call AccessControlService.view if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await view(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(AccessControlService.view).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      const mockResult = { result: 'updated', auditModelMapping: 'auditMapping' };
      AccessControlService.create.mockResolvedValue(mockResult);

      await create(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('updated');

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn,
        module: CoreConstant.MODULE_NAMES.ACCESS_CONTROL,
        method: CoreConstant.MODULE_METHODS.CREATE,
        audit: {
          modelMapping: 'auditMapping',
        },
      });
    });
  });

  describe('update', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      const mockResult = { result: 'updated', auditModelMapping: 'auditMapping' };
      AccessControlService.update.mockResolvedValue(mockResult);

      await update(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('updated');

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn,
        module: CoreConstant.MODULE_NAMES.ACCESS_CONTROL,
        method: CoreConstant.MODULE_METHODS.UPDATE,
        audit: {
          modelMapping: 'auditMapping',
        },
      });
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      const mockResult = { result: 'updated', auditModelMapping: 'auditMapping' };
      AccessControlService.updateStatus.mockResolvedValue(mockResult);

      await updateStatus(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('updated');

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn,
        module: CoreConstant.MODULE_NAMES.ACCESS_CONTROL,
        method: CoreConstant.MODULE_METHODS.UPDATE_STATUS,
        audit: {
          modelMapping: 'auditMapping',
        },
      });
    });
  });
});
