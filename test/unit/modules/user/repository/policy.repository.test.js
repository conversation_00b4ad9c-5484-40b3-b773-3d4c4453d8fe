import { Op } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  bulkCreate,
  bulkUpsert,
  findByParentId,
  findByParentIds,
  upsert,
} from '#src/modules/user/repository/policy.repository.js';

describe('Policy Repository', () => {
  let mockFastify;
  let mockPolicy;

  beforeEach(() => {
    vi.resetAllMocks();

    mockPolicy = {
      findAll: vi.fn(),
      findOne: vi.fn(),
      upsert: vi.fn(),
      bulkCreate: vi.fn(),
      findOrCreate: vi.fn(),
    };

    mockFastify = {
      psql: {
        Policy: mockPolicy,
      },
    };
  });

  describe('findByParentId', () => {
    it('should call Policy.findOne with correct parameters', async () => {
      const parentId = 'test-parent-id';
      const options = { transaction: 'test-transaction' };
      const expectedPolicy = { id: 'policy-1', parentId, read: true, write: false };

      mockPolicy.findOne.mockResolvedValue(expectedPolicy);

      const result = await findByParentId(mockFastify, parentId, options);

      expect(mockPolicy.findOne).toHaveBeenCalledTimes(1);
      expect(mockPolicy.findOne).toHaveBeenCalledWith({
        where: { parentId },
        transaction: 'test-transaction',
      });
      expect(result).toEqual(expectedPolicy);
    });

    it('should call Policy.findOne with default options when options not provided', async () => {
      const parentId = 'test-parent-id';
      const expectedPolicy = { id: 'policy-1', parentId, read: true, write: false };

      mockPolicy.findOne.mockResolvedValue(expectedPolicy);

      const result = await findByParentId(mockFastify, parentId);

      expect(mockPolicy.findOne).toHaveBeenCalledTimes(1);
      expect(mockPolicy.findOne).toHaveBeenCalledWith({
        where: { parentId },
      });
      expect(result).toEqual(expectedPolicy);
    });

    it('should return null when no policy found', async () => {
      const parentId = 'non-existent-parent-id';

      mockPolicy.findOne.mockResolvedValue(null);

      const result = await findByParentId(mockFastify, parentId);

      expect(mockPolicy.findOne).toHaveBeenCalledTimes(1);
      expect(result).toBeNull();
    });

    it('should propagate errors from the database', async () => {
      const parentId = 'test-parent-id';
      const dbError = new Error('Database error');

      mockPolicy.findOne.mockRejectedValue(dbError);

      await expect(findByParentId(mockFastify, parentId)).rejects.toThrow(dbError);
      expect(mockPolicy.findOne).toHaveBeenCalledTimes(1);
    });
  });

  describe('findByParentIds', () => {
    it('should call Policy.findAll with correct parameters', async () => {
      const parentIds = ['parent-id-1', 'parent-id-2', 'parent-id-3'];
      const options = { transaction: 'test-transaction' };
      const expectedPolicy = [
        { id: 'policy-1', parentId: 'parent-id-1', canView: true, canEdit: false },
        { id: 'policy-2', parentId: 'parent-id-2', canView: true, canEdit: true },
      ];

      mockPolicy.findAll.mockResolvedValue(expectedPolicy);

      const result = await findByParentIds(mockFastify, parentIds, options);

      expect(mockPolicy.findAll).toHaveBeenCalledTimes(1);
      expect(mockPolicy.findAll).toHaveBeenCalledWith({
        where: {
          parentId: {
            [Op.in]: parentIds,
          },
        },
        transaction: 'test-transaction',
      });
      expect(result).toEqual(expectedPolicy);
    });

    it('should call Policy.findAll with default options when options not provided', async () => {
      const parentIds = ['parent-id-1', 'parent-id-2'];
      const expectedPolicy = [
        { id: 'policy-1', parentId: 'parent-id-1', canView: true, canEdit: false },
      ];

      mockPolicy.findAll.mockResolvedValue(expectedPolicy);

      const result = await findByParentIds(mockFastify, parentIds);

      expect(mockPolicy.findAll).toHaveBeenCalledTimes(1);
      expect(mockPolicy.findAll).toHaveBeenCalledWith({
        where: {
          parentId: {
            [Op.in]: parentIds,
          },
        },
      });
      expect(result).toEqual(expectedPolicy);
    });

    it('should return empty array when parentIds is empty', async () => {
      const result = await findByParentIds(mockFastify, []);

      expect(mockPolicy.findAll).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it('should return empty array when parentIds is null', async () => {
      const result = await findByParentIds(mockFastify, null);

      expect(mockPolicy.findAll).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it('should propagate errors from the database', async () => {
      const parentIds = ['parent-id-1', 'parent-id-2'];
      const dbError = new Error('Database error');

      mockPolicy.findAll.mockRejectedValue(dbError);

      await expect(findByParentIds(mockFastify, parentIds)).rejects.toThrow(dbError);
      expect(mockPolicy.findAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('upsert', () => {
    it('should find or create a policy and update if found', async () => {
      const values = { parentId: 'test-parent-id', canView: true, canEdit: false };
      const options = { transaction: 'test-transaction' };
      const mockInstance = {
        update: vi.fn().mockResolvedValue({ id: 'policy-1', ...values }),
      };

      mockPolicy.findOrCreate.mockResolvedValue([mockInstance, false]);

      const result = await upsert(mockFastify, values, options);

      expect(mockPolicy.findOrCreate).toHaveBeenCalledTimes(1);
      expect(mockPolicy.findOrCreate).toHaveBeenCalledWith({
        where: { parentId: 'test-parent-id' },
        defaults: values,
        transaction: 'test-transaction',
      });

      expect(mockInstance.update).toHaveBeenCalledTimes(1);
      expect(mockInstance.update).toHaveBeenCalledWith({ canView: true, canEdit: false }, options);

      expect(result).toEqual([mockInstance, false]);
    });

    it('should not update if the instance was newly created', async () => {
      const values = { parentId: 'test-parent-id', canView: true, canEdit: false };
      const options = { transaction: 'test-transaction' };
      const mockInstance = {
        update: vi.fn(),
        id: 'policy-1',
        ...values,
      };

      mockPolicy.findOrCreate.mockResolvedValue([mockInstance, true]);

      const result = await upsert(mockFastify, values, options);

      expect(mockPolicy.findOrCreate).toHaveBeenCalledTimes(1);
      expect(mockPolicy.findOrCreate).toHaveBeenCalledWith({
        where: { parentId: 'test-parent-id' },
        defaults: values,
        transaction: 'test-transaction',
      });

      expect(mockInstance.update).not.toHaveBeenCalled();

      expect(result).toEqual([mockInstance, true]);
    });

    it('should call with default options when options not provided', async () => {
      const values = { parentId: 'test-parent-id', canView: true, canEdit: false };
      const mockInstance = {
        update: vi.fn().mockResolvedValue({ id: 'policy-1', ...values }),
      };

      mockPolicy.findOrCreate.mockResolvedValue([mockInstance, false]);

      const result = await upsert(mockFastify, values);

      expect(mockPolicy.findOrCreate).toHaveBeenCalledTimes(1);
      expect(mockPolicy.findOrCreate).toHaveBeenCalledWith({
        where: { parentId: 'test-parent-id' },
        defaults: values,
      });

      expect(mockInstance.update).toHaveBeenCalledWith({ canView: true, canEdit: false }, {});

      expect(result).toEqual([mockInstance, false]);
    });

    it('should propagate errors from the database during findOrCreate', async () => {
      const values = { parentId: 'test-parent-id', canView: true, canEdit: false };
      const dbError = new Error('Database error');

      mockPolicy.findOrCreate.mockRejectedValue(dbError);

      await expect(upsert(mockFastify, values)).rejects.toThrow(dbError);
      expect(mockPolicy.findOrCreate).toHaveBeenCalledTimes(1);
    });

    it('should propagate errors from the database during update', async () => {
      const values = { parentId: 'test-parent-id', canView: true, canEdit: false };
      const dbError = new Error('Update error');
      const mockInstance = {
        update: vi.fn().mockRejectedValue(dbError),
      };

      mockPolicy.findOrCreate.mockResolvedValue([mockInstance, false]);

      await expect(upsert(mockFastify, values)).rejects.toThrow(dbError);
      expect(mockPolicy.findOrCreate).toHaveBeenCalledTimes(1);
      expect(mockInstance.update).toHaveBeenCalledTimes(1);
    });
  });

  describe('bulkCreate', () => {
    it('should call Policy.bulkCreate with correct parameters', async () => {
      const data = [
        { parentId: 'parent-1', canView: true, canEdit: false },
        { parentId: 'parent-2', canView: true, canEdit: true },
      ];
      const options = { transaction: 'test-transaction' };
      const expectedResult = [
        { id: 'policy-1', parentId: 'parent-1', canView: true, canEdit: false },
        { id: 'policy-2', parentId: 'parent-2', canView: true, canEdit: true },
      ];

      mockPolicy.bulkCreate.mockResolvedValue(expectedResult);

      const result = await bulkCreate(mockFastify, data, options);

      expect(mockPolicy.bulkCreate).toHaveBeenCalledTimes(1);
      expect(mockPolicy.bulkCreate).toHaveBeenCalledWith(data, {
        transaction: 'test-transaction',
        returning: true,
      });
      expect(result).toEqual(expectedResult);
    });

    it('should call Policy.bulkCreate with default options when options not provided', async () => {
      const data = [
        { parentId: 'parent-1', canView: true, canEdit: false },
        { parentId: 'parent-2', canView: true, canEdit: true },
      ];
      const expectedResult = [
        { id: 'policy-1', parentId: 'parent-1', canView: true, canEdit: false },
        { id: 'policy-2', parentId: 'parent-2', canView: true, canEdit: true },
      ];

      mockPolicy.bulkCreate.mockResolvedValue(expectedResult);

      const result = await bulkCreate(mockFastify, data);

      expect(mockPolicy.bulkCreate).toHaveBeenCalledTimes(1);
      expect(mockPolicy.bulkCreate).toHaveBeenCalledWith(data, {
        returning: true,
      });
      expect(result).toEqual(expectedResult);
    });

    it('should propagate errors from the database', async () => {
      const data = [
        { parentId: 'parent-1', canView: true, canEdit: false },
        { parentId: 'parent-2', canView: true, canEdit: true },
      ];
      const dbError = new Error('Database error');

      mockPolicy.bulkCreate.mockRejectedValue(dbError);

      await expect(bulkCreate(mockFastify, data)).rejects.toThrow(dbError);
      expect(mockPolicy.bulkCreate).toHaveBeenCalledTimes(1);
    });
  });

  describe('bulkUpsert', () => {
    it('should call Policy.bulkCreate with updateOnDuplicate option', async () => {
      const data = [
        { parentId: 'parent-1', canView: true, canEdit: false },
        { parentId: 'parent-2', canView: true, canEdit: true },
      ];
      const options = {
        transaction: 'test-transaction',
      };
      const conflictFields = ['parentId'];
      const expectedResult = [
        { id: 'policy-1', parentId: 'parent-1', canView: true, canEdit: false },
        { id: 'policy-2', parentId: 'parent-2', canView: true, canEdit: true },
      ];

      mockPolicy.bulkCreate.mockResolvedValue(expectedResult);

      const result = await bulkUpsert(mockFastify, data, conflictFields, options);

      expect(mockPolicy.bulkCreate).toHaveBeenCalledTimes(1);
      expect(mockPolicy.bulkCreate).toHaveBeenCalledWith(data, {
        updateOnDuplicate: ['canView', 'canEdit', 'updatedAt'],
        transaction: 'test-transaction',
        returning: true,
      });
      expect(result).toEqual(expectedResult);
    });

    it('should handle complex data with multiple fields', async () => {
      const data = [
        {
          parentId: 'parent-1',
          canView: true,
          canEdit: false,
          canDelete: true,
          canCreate: false,
          updatedBy: 'user-1',
        },
      ];
      const options = {
        transaction: 'test-transaction',
      };
      const conflictFields = ['parentId'];
      const expectedResult = [
        {
          id: 'policy-1',
          parentId: 'parent-1',
          canView: true,
          canEdit: false,
          canDelete: true,
          canCreate: false,
          updatedBy: 'user-1',
        },
      ];

      mockPolicy.bulkCreate.mockResolvedValue(expectedResult);

      const result = await bulkUpsert(mockFastify, data, conflictFields, options);

      expect(mockPolicy.bulkCreate).toHaveBeenCalledTimes(1);
      expect(mockPolicy.bulkCreate).toHaveBeenCalledWith(data, {
        updateOnDuplicate: [
          'canView',
          'canEdit',
          'canDelete',
          'canCreate',
          'updatedBy',
          'updatedAt',
        ],
        transaction: 'test-transaction',
        returning: true,
      });
      expect(result).toEqual(expectedResult);
    });

    it('should propagate errors from the database', async () => {
      const data = [
        { parentId: 'parent-1', canView: true, canEdit: false },
        { parentId: 'parent-2', canView: true, canEdit: true },
      ];
      const dbError = new Error('Database error');

      mockPolicy.bulkCreate.mockRejectedValue(dbError);

      const conflictFields = ['parentId'];
      await expect(bulkUpsert(mockFastify, data, conflictFields)).rejects.toThrow(dbError);
      expect(mockPolicy.bulkCreate).toHaveBeenCalledTimes(1);
    });
  });
});
