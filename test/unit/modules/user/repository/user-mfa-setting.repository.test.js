import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as mfaRepository from '#src/modules/user/repository/user-mfa-setting.repository.js';

describe('UserMfaSetting Repository', () => {
  describe('create', () => {
    let mockFastify;

    beforeEach(() => {
      mockFastify = {
        psql: {
          UserMfaSetting: {
            create: vi.fn(),
          },
        },
      };
    });

    afterEach(() => {
      vi.resetAllMocks();
    });
    it('should call Mfa.create with correct parameters', async () => {
      const data = { userId: 1, secret: 'Abcd@1234' };
      const options = { transaction: {} };
      const expectedResult = { id: 1, ...data };

      mockFastify.psql.UserMfaSetting.create.mockResolvedValue(expectedResult);

      const result = await mfaRepository.create(mockFastify, data, options);

      expect(mockFastify.psql.UserMfaSetting.create).toHaveBeenCalledWith(data, options);
      expect(result).toEqual(expectedResult);
    });
  });
  describe('update', () => {
    let mockModel;

    beforeEach(() => {
      mockModel = {
        update: vi.fn(),
      };
    });

    afterEach(() => {
      vi.resetAllMocks();
    });

    it('should call update with correct parameters and return result', async () => {
      const data = { verified: true };
      const options = { transaction: {} };
      const expectedResponse = [1, [{ id: 1, verified: true }]];

      mockModel.update.mockResolvedValue(expectedResponse);

      const result = await mfaRepository.update(mockModel, data, options);

      expect(mockModel.update).toHaveBeenCalledWith(data, options);
      expect(result).toEqual(expectedResponse);
    });
  });
  describe('findByUserId', () => {
    let mockFastify;

    beforeEach(() => {
      mockFastify = {
        psql: {
          UserMfaSetting: {
            findOne: vi.fn(),
          },
        },
      };
    });

    afterEach(() => {
      vi.resetAllMocks();
    });

    it('should call findOne with correct userId condition', async () => {
      const userId = 42;
      const expectedResult = { id: 1, userId };

      mockFastify.psql.UserMfaSetting.findOne.mockResolvedValue(expectedResult);

      const result = await mfaRepository.findByUserId(mockFastify, userId);

      expect(mockFastify.psql.UserMfaSetting.findOne).toHaveBeenCalledWith({
        where: { userId },
      });
      expect(result).toEqual(expectedResult);
    });
  });
});
