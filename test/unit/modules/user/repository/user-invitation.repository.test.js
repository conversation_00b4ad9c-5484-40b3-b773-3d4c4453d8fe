import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  create,
  findAll,
  findById,
  update,
} from '#src/modules/user/repository/user-invitation.repository.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/pagination.util.js', () => ({
  applyOffsetPagination: vi.fn(),
}));
vi.mock('#src/utils/query.util.js', () => ({
  buildWhereFromFilters: vi.fn(),
}));

describe('UserInvitationRepository', () => {
  let fastify;

  beforeEach(() => {
    vi.clearAllMocks();

    fastify = {
      psql: {
        UserInvitation: {
          create: vi.fn(),
          update: vi.fn(),
          findOne: vi.fn(),
        },
        UserAssociation: {},
      },
    };
  });

  describe('findAll', () => {
    it('should build filters and apply pagination correctly', async () => {
      const query = { filter_status_eq: 'pending' };
      const mockWhere = {};
      const mockInclude = [{ model: fastify.psql.UserAssociation, as: 'ua', required: true }];
      const expectedResult = { rows: [], count: 0 };

      buildWhereFromFilters.mockReturnValue({
        where: mockWhere,
        include: mockInclude,
      });

      applyOffsetPagination.mockResolvedValue(expectedResult);

      const result = await findAll(fastify, query);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(
        query,
        fastify.psql.UserInvitation,
        expect.arrayContaining([
          expect.objectContaining({ model: fastify.psql.UserAssociation, as: 'ua' }),
        ]),
      );

      expect(applyOffsetPagination).toHaveBeenCalledWith(
        fastify,
        fastify.psql.UserInvitation,
        query,
        mockWhere,
        mockInclude,
      );

      expect(result).toBe(expectedResult);
    });
  });

  describe('create', () => {
    it('should create a new user invitation', async () => {
      const data = { userAssociationId: 'ua-id', status: 'pending' };
      const options = { transaction: {} };
      const expected = { id: 'inv-123', ...data };

      fastify.psql.UserInvitation.create.mockResolvedValue(expected);

      const result = await create(fastify, data, options);

      expect(fastify.psql.UserInvitation.create).toHaveBeenCalledWith(data, options);
      expect(result).toBe(expected);
    });
  });

  describe('findById', () => {
    it('should return a user invitation record when a valid ID is provided', async () => {
      const mockUserInvitation = { id: '123', invitedBy: '456' };
      const fastify = {
        psql: {
          UserInvitation: {
            findByPk: vi.fn().mockResolvedValue(mockUserInvitation),
          },
        },
      };

      const result = await findById(fastify, '123');
      expect(result).toEqual(mockUserInvitation);
      expect(fastify.psql.UserInvitation.findByPk).toHaveBeenCalledWith('123');
    });
  });
  describe('update', () => {
    it('should call model.update with data and options', async () => {
      const mockModel = {
        update: vi.fn(),
      };

      const data = { status: 'approved' };
      const options = { transaction: {}, returning: true };
      const expected = [{ id: 'inv-123', status: 'approved' }];

      mockModel.update.mockResolvedValue(expected);

      const result = await update(mockModel, data, options);

      expect(mockModel.update).toHaveBeenCalledWith(data, options);
      expect(result).toBe(expected);
    });
  });
});
