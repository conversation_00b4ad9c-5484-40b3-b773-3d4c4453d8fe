import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as userAssociationRepository from '#src/modules/user/repository/user-association.repository.js';

describe('User Association Repository', () => {
  let mockFastify;

  beforeEach(() => {
    vi.clearAllMocks();

    mockFastify = {
      psql: {
        Entity: {
          findOne: vi.fn(),
        },
        UserAssociation: {
          create: vi.fn(),
          findOne: vi.fn(),
          update: vi.fn(),
        },
      },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('create', () => {
    it('should call UserAssociation.create with correct parameters', async () => {
      const data = { userId: 1, roleId: 'admin' };
      const options = { transaction: {} };
      const expected = { id: 1, ...data };

      mockFastify.psql.UserAssociation.create.mockResolvedValue(expected);

      const result = await userAssociationRepository.create(mockFastify, data, options);

      expect(mockFastify.psql.UserAssociation.create).toHaveBeenCalledWith(data, options);
      expect(result).toEqual(expected);
    });
  });

  describe('findByUserId', () => {
    it('should call UserAssociation.findOne with correct where clause and includes', async () => {
      const mockUserAssociation = { id: 1, userId: 123, entityId: 456, roleId: 7 };

      const server = {
        psql: {
          UserAssociation: {
            findOne: vi.fn().mockResolvedValue(mockUserAssociation),
          },
        },
      };

      const entity = { id: 456 };
      const userId = 123;
      const result = await userAssociationRepository.findByUserId(server, entity, userId);

      expect(server.psql.UserAssociation.findOne).toHaveBeenCalledWith({
        where: {
          userId: userId,
          entityId: entity.id,
        },
        include: [
          {
            association: 'uac',
            required: false,
            include: [
              {
                association: 'cl',
                required: false,
              },
            ],
          },
        ],
      });

      expect(result).toEqual(mockUserAssociation);
    });

    it('should return null if no user association is found', async () => {
      const entity = { id: 456 };
      const userId = 999;

      mockFastify.psql.UserAssociation.findOne.mockResolvedValue(null);

      const result = await userAssociationRepository.findByUserId(mockFastify, entity, userId);

      expect(mockFastify.psql.UserAssociation.findOne).toHaveBeenCalledWith({
        where: {
          userId: userId,
          entityId: entity.id,
        },
        include: [
          {
            association: 'uac',
            required: false,
            include: [
              {
                association: 'cl',
                required: false,
              },
            ],
          },
        ],
      });

      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should call update with correct parameters and return result', async () => {
      const mockModel = {
        update: vi.fn(),
      };

      const data = { roleId: 5 };
      const options = { transaction: 'fake-transaction' };
      const expectedResult = { id: 1, userId: 123, roleId: 5 };

      mockModel.update.mockResolvedValue(expectedResult);

      const result = await userAssociationRepository.update(mockModel, data, options);

      expect(mockModel.update).toHaveBeenCalledWith(data, options);
      expect(result).toEqual(expectedResult);
    });
  });
});
