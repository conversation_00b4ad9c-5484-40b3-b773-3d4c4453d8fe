import { Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  create,
  findAll,
  findAllByParentId,
  findByEntityIdAndName,
  findById,
  findByIdWithModulePolicies,
  findDescendantsByParentPath,
  update,
  updateStatus,
} from '#src/modules/user/repository/role.repository.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/pagination.util.js', () => ({
  default: vi.fn(),
}));

vi.mock('#src/utils/query.util.js', () => ({
  buildWhereFromFilters: vi.fn(),
}));

vi.mock('#src/utils/pagination.util.js', () => ({
  applyOffsetPagination: vi.fn(),
}));

vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    COMMON_STATUSES: {
      ACTIVE: 'active',
      INACTIVE: 'inactive',
    },
  },
}));

describe('Role Repository', () => {
  const mockFastify = {
    psql: {
      Role: {
        findOne: vi.fn(),
        findAll: vi.fn(),
        create: vi.fn(),
      },
      Department: {
        findOne: vi.fn(),
        findAll: vi.fn(),
      },
      connection: {
        models: {
          Role: {
            findOne: vi.fn(),
            findAll: vi.fn(),
          },
          Policy: {},
          Module: {},
        },
        Sequelize: {
          Op: {
            or: Symbol('or'),
            ne: Symbol('ne'),
          },
          literal: vi.fn((str) => `LITERAL(${str})`),
        },
      },
    },
  };

  const mockRoleInstance = {
    toJSON: vi.fn(() => ({
      id: 1,
      name: 'Test Role',
      parentRole: { name: 'Parent Role' },
      department: { name: 'Test Department' },
    })),
    update: vi.fn().mockResolvedValue({ id: 1, updated: true }),
    getModulePolicies: vi.fn(),
  };

  const mockQuery = {
    page: 1,
    limit: 10,
    sortBy: [{ field: 'name', direction: 'asc' }],
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('findAll', () => {
    it('should call buildWhereFromFilters with correct parameters', async () => {
      const expectedIncludes = [
        {
          model: mockFastify.psql.Role,
          as: 'parentRole',
          attributes: ['name'],
          required: false,
        },
        {
          model: mockFastify.psql.Department,
          as: 'department',
          attributes: ['name'],
          required: false,
        },
      ];

      buildWhereFromFilters.mockReturnValue({
        where: { status: 'active' },
        include: [{ model: 'ParentRole' }, { model: 'Department' }],
      });

      applyOffsetPagination.mockResolvedValue({
        rows: [{ id: 1, name: 'Role 1', department: { name: 'Dept 1' } }],
        pagination: { totalCount: 1 },
      });

      await findAll(mockFastify, mockQuery);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(
        mockQuery,
        mockFastify.psql.Role,
        expectedIncludes,
      );
    });

    it('should call applyOffsetPagination with correct parameters', async () => {
      const mockWhereFilter = { status: 'active' };
      const mockIncludeFilter = [
        { model: mockFastify.psql.Role, as: 'parentRole' },
        { model: mockFastify.psql.Department, as: 'department' },
      ];

      buildWhereFromFilters.mockReturnValue({
        where: mockWhereFilter,
        include: mockIncludeFilter,
      });

      const expectedResult = {
        rows: [
          {
            id: 1,
            name: 'Role 1',
            department: { name: 'Department 1' },
          },
        ],
        pagination: { totalCount: 1 },
      };

      applyOffsetPagination.mockResolvedValue(expectedResult);

      const result = await findAll(mockFastify, mockQuery);

      expect(applyOffsetPagination).toHaveBeenCalledWith(
        mockFastify,
        mockFastify.psql.Role,
        mockQuery,
        mockWhereFilter,
        mockIncludeFilter,
        ['id', 'name', 'status', 'createdAt', 'updatedAt'],
      );

      expect(result).toEqual(expectedResult);
    });
  });

  describe('findById', () => {
    it('should call findOne with correct parameters when only id is provided', async () => {
      const roleId = 1;
      mockFastify.psql.Role.findOne.mockResolvedValue(mockRoleInstance);

      const expectedIncludes = [
        {
          model: mockFastify.psql.Role,
          as: 'parentRole',
          attributes: ['name'],
          required: false,
        },
      ];

      await findById(mockFastify, roleId);

      expect(mockFastify.psql.Role.findOne).toHaveBeenCalledWith({
        where: { id: roleId },
        include: expectedIncludes,
      });
    });

    it('should call findOne with correct parameters when entityId is provided', async () => {
      const roleId = 1;
      const entityId = 'entity-1';
      mockFastify.psql.Role.findOne.mockResolvedValue(mockRoleInstance);

      const expectedIncludes = [
        {
          model: mockFastify.psql.Role,
          as: 'parentRole',
          attributes: ['name'],
          required: false,
        },
      ];

      await findById(mockFastify, roleId, entityId);

      expect(mockFastify.psql.Role.findOne).toHaveBeenCalledWith({
        where: {
          id: roleId,
          entityId,
        },
        include: expectedIncludes,
      });
    });

    it('should include active status in where clause when activeOnly is true', async () => {
      const roleId = 1;
      mockFastify.psql.Role.findOne.mockResolvedValue(mockRoleInstance);

      const expectedIncludes = [
        {
          model: mockFastify.psql.Role,
          as: 'parentRole',
          attributes: ['name'],
          required: false,
        },
      ];

      await findById(mockFastify, roleId, null, true);

      expect(mockFastify.psql.Role.findOne).toHaveBeenCalledWith({
        where: { id: roleId, status: 'active' },
        include: expectedIncludes,
      });
    });

    it('should include both entityId and active status when both are provided', async () => {
      const roleId = 1;
      const entityId = 'entity-1';
      mockFastify.psql.Role.findOne.mockResolvedValue(mockRoleInstance);

      const expectedIncludes = [
        {
          model: mockFastify.psql.Role,
          as: 'parentRole',
          attributes: ['name'],
          required: false,
        },
      ];

      await findById(mockFastify, roleId, entityId, true);

      expect(mockFastify.psql.Role.findOne).toHaveBeenCalledWith({
        where: {
          id: roleId,
          entityId,
          status: 'active',
        },
        include: expectedIncludes,
      });
    });

    it('should pass additional options to findOne', async () => {
      const roleId = 1;
      const entityId = 'entity-1';
      const options = { transaction: 'mock-transaction' };
      mockFastify.psql.Role.findOne.mockResolvedValue(mockRoleInstance);

      const expectedIncludes = [
        {
          model: mockFastify.psql.Role,
          as: 'parentRole',
          attributes: ['name'],
          required: false,
        },
      ];

      await findById(mockFastify, roleId, entityId, false, options);

      expect(mockFastify.psql.Role.findOne).toHaveBeenCalledWith({
        where: {
          id: roleId,
          entityId,
        },
        include: expectedIncludes,
        transaction: 'mock-transaction',
      });
    });

    it('should return the found role', async () => {
      const roleId = 1;
      mockFastify.psql.Role.findOne.mockResolvedValue(mockRoleInstance);

      const result = await findById(mockFastify, roleId);

      expect(result).toBe(mockRoleInstance);
    });

    it('should return null when role is not found', async () => {
      const roleId = 999;
      mockFastify.psql.Role.findOne.mockResolvedValue(null);

      const result = await findById(mockFastify, roleId);

      expect(result).toBeNull();
    });
  });

  describe('findDescendantsByParentPath', () => {
    it('should call findAll with correct ltree query', async () => {
      const entityId = 'entity-1';
      const parentPath = 'root.admin';
      const options = { transaction: 'tx1' };

      mockFastify.psql.Role.findAll.mockResolvedValue([mockRoleInstance]);

      await findDescendantsByParentPath(mockFastify, entityId, parentPath, options);

      expect(mockFastify.psql.Role.findAll).toHaveBeenCalledWith({
        where: {
          entityId: entityId,
          path: Sequelize.literal(`"path" <@ '${parentPath}'::ltree`),
        },
        ...options,
      });
    });

    it('should return the found roles', async () => {
      const entityId = 'entity-1';
      const parentPath = 'root.admin';

      mockFastify.psql.Role.findAll.mockResolvedValue([mockRoleInstance]);

      const result = await findDescendantsByParentPath(mockFastify, entityId, parentPath);

      expect(result).toEqual([mockRoleInstance]);
    });
  });

  describe('create', () => {
    it('should call create with correct parameters', async () => {
      const roleData = {
        name: 'New Role',
        entityId: 'entity-1',
        status: 'active',
      };
      const options = { transaction: 'tx1' };

      mockFastify.psql.Role.create.mockResolvedValue(mockRoleInstance);

      await create(mockFastify, roleData, options);

      expect(mockFastify.psql.Role.create).toHaveBeenCalledWith(roleData, options);
    });

    it('should return the created role as JSON', async () => {
      const roleId = 'creator-1';
      const roleData = {
        name: 'New Role',
        entityId: 'entity-1',
        status: 'active',
      };

      const expectedResult = {
        id: 1,
        name: 'Test Role',
        parentRole: { name: 'Parent Role' },
      };

      mockRoleInstance.toJSON.mockReturnValue(expectedResult);
      mockFastify.psql.Role.create.mockResolvedValue(mockRoleInstance);

      const result = await create(mockFastify, roleId, roleData);

      expect(mockRoleInstance.toJSON).toHaveBeenCalled();
      expect(result).toEqual(expectedResult);
    });
  });

  describe('update', () => {
    it('should call update with correct parameters', async () => {
      const updateData = {
        name: 'Updated Role',
        status: 'inactive',
      };
      const options = { transaction: 'tx1' };

      await update(mockRoleInstance, updateData, options);

      expect(mockRoleInstance.update).toHaveBeenCalledWith(updateData, options);
    });

    it('should return the update result', async () => {
      const updateData = {
        name: 'Updated Role',
      };
      const expectedResult = { id: 1, updated: true };

      mockRoleInstance.update.mockResolvedValue(expectedResult);

      const result = await update(mockRoleInstance, updateData);

      expect(result).toEqual(expectedResult);
    });
  });

  describe('updateStatus', () => {
    it('should call update with status parameter', async () => {
      const status = 'inactive';
      const options = { transaction: 'tx1' };

      await updateStatus(mockRoleInstance, status, options);

      expect(mockRoleInstance.update).toHaveBeenCalledWith({ status }, options);
    });

    it('should return the update result', async () => {
      const status = 'inactive';
      const expectedResult = { id: 1, updated: true };

      mockRoleInstance.update.mockResolvedValue(expectedResult);

      const result = await updateStatus(mockRoleInstance, status);

      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByIdWithModulePolicies', () => {
    it('should return null if role not found', async () => {
      const entityId = 'entity-1';
      const roleId = 1;

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(null);

      const result = await findByIdWithModulePolicies(mockFastify, entityId, roleId);

      expect(result).toBeNull();
    });

    it('should include active status in where clause when activeOnly is true', async () => {
      const entityId = 'entity-1';
      const roleId = 1;
      const activeOnly = true;

      const mockRole = {
        toJSON: vi.fn(() => ({
          id: roleId,
          name: 'Admin Role',
          parentRole: { name: 'Super Admin' },
        })),
        getModulePolicies: vi.fn().mockResolvedValue([]),
      };

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRole);

      await findByIdWithModulePolicies(mockFastify, entityId, roleId, activeOnly);

      expect(mockFastify.psql.connection.models.Role.findOne).toHaveBeenCalledWith({
        where: {
          id: roleId,
          entityId,
          status: 'active',
        },
        include: [
          {
            association: 'parentRole',
            attributes: ['name'],
            required: false,
          },
          {
            association: 'department',
            attributes: ['name'],
            required: false,
          },
          {
            association: 'modulePolicies',
            include: [
              {
                model: mockFastify.psql.connection.models.Policy,
                as: 'policy',
              },
              {
                model: mockFastify.psql.connection.models.Module,
                as: 'module',
              },
            ],
          },
        ],
      });
    });

    it('should not include active status in where clause when activeOnly is false', async () => {
      const entityId = 'entity-1';
      const roleId = 1;
      const activeOnly = false;

      const mockRole = {
        toJSON: vi.fn(() => ({
          id: roleId,
          name: 'Admin Role',
          parentRole: { name: 'Super Admin' },
        })),
        getModulePolicies: vi.fn().mockResolvedValue([]),
      };

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRole);

      await findByIdWithModulePolicies(mockFastify, entityId, roleId, activeOnly);

      expect(mockFastify.psql.connection.models.Role.findOne).toHaveBeenCalledWith({
        where: {
          id: roleId,
          entityId,
        },
        include: [
          {
            association: 'parentRole',
            attributes: ['name'],
            required: false,
          },
          {
            association: 'department',
            attributes: ['name'],
            required: false,
          },
          {
            association: 'modulePolicies',
            include: [
              {
                model: mockFastify.psql.connection.models.Policy,
                as: 'policy',
              },
              {
                model: mockFastify.psql.connection.models.Module,
                as: 'module',
              },
            ],
          },
        ],
      });
    });

    it('should correctly process module policies by hierarchy', async () => {
      const entityId = 'entity-1';
      const roleId = 1;

      const mockRole = {
        toJSON: vi.fn(() => ({
          id: roleId,
          name: 'Test Role',
          status: 'active',
        })),
        modulePolicies: [
          {
            moduleId: 'module-1',
            module: {
              name: 'User Management',
              hierarchy: 'admin',
            },
            policy: {
              toJSON: () => ({
                id: 101,
                parentId: 'parent-1',
                key: 'canView',
                value: true,
                createdAt: '2023-01-01',
                updatedAt: '2023-01-01',
                createdBy: 'system',
                updatedBy: 'system',
              }),
            },
          },
          {
            moduleId: 'module-2',
            module: {
              name: 'Role Management',
              hierarchy: 'admin',
            },
            policy: null,
          },
          {
            moduleId: 'module-3',
            module: {
              name: 'Profile',
              hierarchy: 'user',
            },
            policy: {
              toJSON: () => ({
                id: 103,
                parentId: 'parent-3',
                key: 'canEdit',
                value: false,
                createdAt: '2023-01-01',
                updatedAt: '2023-01-01',
                createdBy: 'system',
                updatedBy: 'system',
              }),
            },
          },
        ],
        getModulePolicies: vi.fn().mockResolvedValue([]),
      };

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRole);

      const result = await findByIdWithModulePolicies(mockFastify, entityId, roleId);

      expect(result.modules).toEqual({
        admin: [
          {
            id: 'module-1',
            name: 'User Management',
            policies: {
              key: 'canView',
              value: true,
            },
          },
          {
            id: 'module-2',
            name: 'Role Management',
            policies: {},
          },
        ],
        user: [
          {
            id: 'module-3',
            name: 'Profile',
            policies: {
              key: 'canEdit',
              value: false,
            },
          },
        ],
      });

      const adminModule = result.modules.admin[0];
      expect(adminModule.policies).not.toHaveProperty('id');
      expect(adminModule.policies).not.toHaveProperty('parentId');
      expect(adminModule.policies).not.toHaveProperty('createdAt');
      expect(adminModule.policies).not.toHaveProperty('updatedAt');
      expect(adminModule.policies).not.toHaveProperty('createdBy');
      expect(adminModule.policies).not.toHaveProperty('updatedBy');

      expect(result).toEqual({
        id: roleId,
        name: 'Test Role',
        status: 'active',
        parentName: null,
        parentRole: undefined,
        modules: {
          admin: [
            {
              id: 'module-1',
              name: 'User Management',
              policies: {
                key: 'canView',
                value: true,
              },
            },
            {
              id: 'module-2',
              name: 'Role Management',
              policies: {},
            },
          ],
          user: [
            {
              id: 'module-3',
              name: 'Profile',
              policies: {
                key: 'canEdit',
                value: false,
              },
            },
          ],
        },
      });
    });

    it('should handle role with no parent role and empty module policies', async () => {
      const entityId = 'entity-1';
      const roleId = 1;

      const mockRole = {
        toJSON: vi.fn(() => ({
          id: roleId,
          name: 'Root Role',
          status: 'active',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
        })),
        modulePolicies: [],
        getModulePolicies: vi.fn().mockResolvedValue([]),
      };

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRole);

      const result = await findByIdWithModulePolicies(mockFastify, entityId, roleId);

      expect(mockFastify.psql.connection.models.Role.findOne).toHaveBeenCalledWith({
        where: { id: roleId, entityId },
        include: [
          {
            association: 'parentRole',
            attributes: ['name'],
            required: false,
          },
          {
            association: 'department',
            attributes: ['name'],
            required: false,
          },
          {
            association: 'modulePolicies',
            include: [
              {
                model: mockFastify.psql.connection.models.Policy,
                as: 'policy',
              },
              {
                model: mockFastify.psql.connection.models.Module,
                as: 'module',
              },
            ],
          },
        ],
      });

      expect(result).toEqual({
        id: roleId,
        name: 'Root Role',
        status: 'active',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        parentName: null,
        parentRole: undefined,
        modules: {},
      });

      expect(Object.keys(result.modules).length).toBe(0);
    });
  });

  describe('findByEntityIdAndName', () => {
    it('should call findOne with correct parameters without excludeId', async () => {
      const entityId = 'entity-1';
      const name = 'Admin Role';

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRoleInstance);

      await findByEntityIdAndName(mockFastify, entityId, name);

      expect(mockFastify.psql.connection.models.Role.findOne).toHaveBeenCalledWith({
        where: {
          entityId,
          name,
        },
      });
    });

    it('should call findOne with correct parameters with excludeId', async () => {
      const entityId = 'entity-1';
      const name = 'Admin Role';
      const excludeId = 5;

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRoleInstance);

      await findByEntityIdAndName(mockFastify, entityId, name, excludeId);

      expect(mockFastify.psql.connection.models.Role.findOne).toHaveBeenCalledWith({
        where: {
          entityId,
          name,
          id: { [mockFastify.psql.connection.Sequelize.Op.ne]: excludeId },
        },
      });
    });

    it('should return the found role', async () => {
      const entityId = 'entity-1';
      const name = 'Admin Role';

      mockFastify.psql.connection.models.Role.findOne.mockResolvedValue(mockRoleInstance);

      const result = await findByEntityIdAndName(mockFastify, entityId, name);

      expect(result).toBe(mockRoleInstance);
    });
  });

  describe('findAllByParentId', () => {
    it('should call findAll with correct parameters', async () => {
      const parentId = 1;
      const mockRoles = [mockRoleInstance, { ...mockRoleInstance, id: 2 }];

      mockFastify.psql.connection.models.Role.findAll.mockResolvedValue(mockRoles);

      await findAllByParentId(mockFastify, parentId);

      expect(mockFastify.psql.connection.models.Role.findAll).toHaveBeenCalledWith({
        where: { parentId },
      });
    });

    it('should return the found roles', async () => {
      const parentId = 1;
      const mockRoles = [mockRoleInstance, { ...mockRoleInstance, id: 2 }];

      mockFastify.psql.connection.models.Role.findAll.mockResolvedValue(mockRoles);

      const result = await findAllByParentId(mockFastify, parentId);

      expect(result).toBe(mockRoles);
    });
  });
});
