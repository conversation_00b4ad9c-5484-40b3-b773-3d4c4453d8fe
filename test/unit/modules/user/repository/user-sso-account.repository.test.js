import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as userSSOAccountRepository from '#src/modules/user/repository/user-sso-account.repository.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/query.util.js', () => ({
  buildWhereFromFilters: vi.fn(),
}));

vi.mock('#src/utils/pagination.util.js', () => ({
  applyOffsetPagination: vi.fn(),
}));

describe('User SSO Account Repository', () => {
  let mockFastify;
  let mockUserSSOAccount;
  let mockModelInstance;
  let mockData;
  let mockOptions;
  let mockWhere;
  let mockUpdateData;

  beforeEach(() => {
    vi.resetAllMocks();

    mockModelInstance = {
      update: vi.fn().mockResolvedValue({ id: '1', updated: true }),
    };

    mockUserSSOAccount = {
      findOne: vi.fn().mockResolvedValue(mockModelInstance),
      create: vi.fn().mockResolvedValue({ id: '1', created: true }),
    };

    mockFastify = {
      psql: {
        UserSSOAccount: mockUserSSOAccount,
      },
    };

    mockData = {
      userId: '123',
      provider: 'google',
      providerAccountId: 'google-123',
      email: '<EMAIL>',
    };

    mockWhere = {
      provider: 'google',
      providerAccountId: 'google-123',
    };

    mockUpdateData = {
      email: '<EMAIL>',
      lastSignInAt: new Date(),
    };

    mockOptions = {
      transaction: 'mock-transaction',
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('findAll', () => {
    it('should call applyOffsetPagination with the correct parameters', async () => {
      const mockQuery = { status: 'pending' };
      const mockWhere = { status: 'pending' };
      const mockInclude = [{ association: 'user', required: false }];
      const expectedResult = { rows: [], count: 0 };

      buildWhereFromFilters.mockReturnValue({
        where: mockWhere,
        include: mockInclude,
      });

      applyOffsetPagination.mockResolvedValue(expectedResult);

      const result = await userSSOAccountRepository.findAll(mockFastify, mockQuery);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(
        mockQuery,
        mockFastify.psql.UserSSOAccount,
        mockInclude,
      );

      expect(applyOffsetPagination).toHaveBeenCalledWith(
        mockFastify,
        mockFastify.psql.UserSSOAccount,
        mockQuery,
        mockWhere,
        mockInclude,
      );

      expect(result).toEqual(expectedResult);
    });
  });

  describe('findById', () => {
    it('should call UserSSOAccount.findOne with the correct filters', async () => {
      const query = { id: 'user-id-123' };
      const mockWhere = { id: 'user-id-123' };
      const mockInclude = [];
      const expectedResult = { id: 'user-id-123', status: 'pending' };

      buildWhereFromFilters.mockReturnValue({
        where: mockWhere,
        include: mockInclude,
      });

      mockFastify.psql.UserSSOAccount.findOne.mockResolvedValue(expectedResult);

      const result = await userSSOAccountRepository.findById(mockFastify, query);

      expect(mockFastify.psql.UserSSOAccount.findOne).toHaveBeenCalledWith({
        where: mockWhere,
        include: mockInclude,
      });

      expect(result).toEqual(expectedResult);
    });
  });

  describe('findOne', () => {
    it('should call UserSSOAccount.findOne with the correct parameters', async () => {
      await userSSOAccountRepository.findOne(mockFastify, mockWhere, mockOptions);

      expect(mockUserSSOAccount.findOne).toHaveBeenCalledWith({
        where: mockWhere,
        transaction: 'mock-transaction',
      });
    });

    it('should return the result from UserSSOAccount.findOne', async () => {
      const result = await userSSOAccountRepository.findOne(mockFastify, mockWhere);

      expect(result).toBe(mockModelInstance);
    });

    it('should work with empty options', async () => {
      await userSSOAccountRepository.findOne(mockFastify, mockWhere);

      expect(mockUserSSOAccount.findOne).toHaveBeenCalledWith({
        where: mockWhere,
      });
    });

    it('should handle null result', async () => {
      mockUserSSOAccount.findOne.mockResolvedValueOnce(null);

      const result = await userSSOAccountRepository.findOne(mockFastify, mockWhere);

      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('should call UserSSOAccount.create with the correct parameters', async () => {
      await userSSOAccountRepository.create(mockFastify, mockData, mockOptions);

      expect(mockUserSSOAccount.create).toHaveBeenCalledWith(mockData, mockOptions);
    });

    it('should return the result from UserSSOAccount.create', async () => {
      const result = await userSSOAccountRepository.create(mockFastify, mockData);

      expect(result).toEqual({ id: '1', created: true });
    });

    it('should work with empty options', async () => {
      await userSSOAccountRepository.create(mockFastify, mockData);

      expect(mockUserSSOAccount.create).toHaveBeenCalledWith(mockData, {});
    });

    it('should propagate errors from UserSSOAccount.create', async () => {
      const error = new Error('Create error');
      mockUserSSOAccount.create.mockRejectedValueOnce(error);

      await expect(userSSOAccountRepository.create(mockFastify, mockData)).rejects.toThrow(
        'Create error',
      );
    });
  });

  describe('update', () => {
    it('should call model.update with the correct parameters', async () => {
      await userSSOAccountRepository.update(mockModelInstance, mockUpdateData, mockOptions);

      expect(mockModelInstance.update).toHaveBeenCalledWith(mockUpdateData, mockOptions);
    });

    it('should return the result from model.update', async () => {
      const result = await userSSOAccountRepository.update(mockModelInstance, mockUpdateData);

      expect(result).toEqual({ id: '1', updated: true });
    });

    it('should work with empty options', async () => {
      await userSSOAccountRepository.update(mockModelInstance, mockUpdateData);

      expect(mockModelInstance.update).toHaveBeenCalledWith(mockUpdateData, {});
    });

    it('should propagate errors from model.update', async () => {
      const error = new Error('Update error');
      mockModelInstance.update.mockRejectedValueOnce(error);

      await expect(
        userSSOAccountRepository.update(mockModelInstance, mockUpdateData),
      ).rejects.toThrow('Update error');
    });

    it('should call model.update with correct data and options', async () => {
      const mockModel = {
        update: vi.fn(),
      };

      const data = { status: 'completed' };
      const options = { where: { id: 'user-id-456' }, returning: true };
      const expectedResponse = [1, [{ id: 'user-id-456', ...data }]];
      mockModel.update.mockResolvedValue(expectedResponse);

      const result = await userSSOAccountRepository.update(mockModel, data, options);

      expect(mockModel.update).toHaveBeenCalledWith(data, options);
      expect(result).toEqual(expectedResponse);
    });
  });
});
