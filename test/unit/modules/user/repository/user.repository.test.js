import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as UserRepository from '#src/modules/user/repository/user.repository.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import * as queryUtils from '#src/utils/query.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/query.util.js', () => ({
  buildWhereFromFilters: vi.fn(),
}));

vi.mock('#src/utils/pagination.util.js', () => ({
  applyOffsetPagination: vi.fn(),
}));

describe('UserRepository', () => {
  let mockFastify;
  let mockUser;
  const mockUsers = [{ id: '1' }, { id: '2' }];

  beforeEach(() => {
    vi.clearAllMocks();

    mockUser = {
      update: vi.fn().mockResolvedValue({ id: 1, status: 'active' }),
    };

    mockFastify = {
      psql: {
        User: {
          findByPk: vi.fn(),
          findOne: vi.fn().mockResolvedValue(mockUser),
          findAll: vi.fn(),
          create: vi.fn(),
        },
      },
    };

    buildWhereFromFilters.mockReturnValue({
      where: { email: '<EMAIL>' },
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('findAll', () => {
    it('should return users using findAll', async () => {
      queryUtils.buildWhereFromFilters.mockReturnValue({
        where: { type: 'normal' },
        include: [],
      });

      applyOffsetPagination.mockResolvedValue(mockUsers);

      const query = { filter_type_eq: 'normal' };
      const result = await UserRepository.findAll(mockFastify, query);

      expect(queryUtils.buildWhereFromFilters).toHaveBeenCalled();
      expect(applyOffsetPagination).toHaveBeenCalled();
      expect(result).toEqual(mockUsers);
    });
  });

  describe('findAllSubAccount', () => {
    it('should return subaccount users', async () => {
      queryUtils.buildWhereFromFilters.mockReturnValue({
        where: { type: 'sub_account' },
        include: [],
      });

      applyOffsetPagination.mockResolvedValue(mockUsers);

      const query = { filter_type_eq: 'sub_account' };
      const result = await UserRepository.findAllSubAccount(mockFastify, query);

      expect(queryUtils.buildWhereFromFilters).toHaveBeenCalled();
      expect(applyOffsetPagination).toHaveBeenCalled();
      expect(result).toEqual(mockUsers);
    });
  });

  describe('findById', () => {
    it('should find a user by ID with includes', async () => {
      const mockUser = { id: '123', name: 'Test User' };
      mockFastify.psql.User.findByPk.mockResolvedValue(mockUser);

      const result = await UserRepository.findById(mockFastify, '123');

      expect(mockFastify.psql.User.findByPk).toHaveBeenCalledWith('123', {
        include: expect.any(Array),
      });
      expect(result).toEqual(mockUser);
    });
  });

  describe('findUser', () => {
    it('should find a user with basic associations when withEntity is false', async () => {
      const mockUser = { id: 'u-1', username: 'johndoe' };
      const query = { filter_username_eq: 'johndoe' };

      const whereFilter = { username: 'johndoe' };
      const includeFilter = [
        {
          association: 'ua',
          required: true,
          include: [],
        },
      ];

      const mockFastify = {
        psql: {
          User: {
            findOne: vi.fn().mockResolvedValue(mockUser),
          },
        },
      };

      queryUtils.buildWhereFromFilters.mockReturnValue({
        where: whereFilter,
        include: includeFilter,
      });

      const result = await UserRepository.findUser(mockFastify, query, false);

      expect(queryUtils.buildWhereFromFilters).toHaveBeenCalledWith(query, mockFastify.psql.User, [
        {
          association: 'ua',
          required: true,
          include: [],
        },
      ]);

      expect(mockFastify.psql.User.findOne).toHaveBeenCalledWith({
        where: whereFilter,
        include: includeFilter,
        subQuery: false,
      });

      expect(result).toEqual(mockUser);
    });

    it('should find a user with entity and parent associations when withEntity is true', async () => {
      const mockUser = {
        id: 'u-1',
        username: 'johndoe',
        ua: [
          {
            entity: {
              id: 'entity-1',
              parent: { id: 'parent-1' },
            },
          },
        ],
      };
      const query = { filter_username_eq: 'johndoe' };

      const whereFilter = { username: 'johndoe' };
      const includeFilter = [
        {
          association: 'ua',
          required: true,
          include: [
            {
              association: 'entity',
              required: true,
              include: [
                {
                  association: 'parent',
                },
              ],
            },
          ],
        },
      ];

      const mockFastify = {
        psql: {
          User: {
            findOne: vi.fn().mockResolvedValue(mockUser),
          },
        },
      };

      queryUtils.buildWhereFromFilters.mockReturnValue({
        where: whereFilter,
        include: includeFilter,
      });

      const result = await UserRepository.findUser(mockFastify, query, true);

      expect(queryUtils.buildWhereFromFilters).toHaveBeenCalledWith(query, mockFastify.psql.User, [
        {
          association: 'ua',
          required: true,
          include: [
            {
              association: 'entity',
              required: true,
              include: [
                {
                  association: 'parent',
                },
              ],
            },
          ],
        },
      ]);

      expect(mockFastify.psql.User.findOne).toHaveBeenCalledWith({
        where: whereFilter,
        include: includeFilter,
        subQuery: false,
      });

      expect(result).toEqual(mockUser);
    });

    it('should return null when user is not found', async () => {
      const query = { filter_username_eq: 'nonexistent' };

      const mockFastify = {
        psql: {
          User: {
            findOne: vi.fn().mockResolvedValue(null),
          },
        },
      };

      queryUtils.buildWhereFromFilters.mockReturnValue({
        where: { username: 'nonexistent' },
        include: expect.any(Array),
      });

      const result = await UserRepository.findUser(mockFastify, query);

      expect(mockFastify.psql.User.findOne).toHaveBeenCalled();
      expect(result).toBeNull();
    });

    it('should handle errors during query execution', async () => {
      const query = { filter_username_eq: 'johndoe' };
      const error = new Error('Database error');

      const mockFastify = {
        psql: {
          User: {
            findOne: vi.fn().mockRejectedValue(error),
          },
        },
      };

      queryUtils.buildWhereFromFilters.mockReturnValue({
        where: { username: 'johndoe' },
        include: expect.any(Array),
      });

      await expect(UserRepository.findUser(mockFastify, query)).rejects.toThrow('Database error');
    });
  });

  describe('create', () => {
    it('should create user with given data and options', async () => {
      const mockData = { username: 'newuser' };
      const mockOptions = { transaction: {} };
      const mockCreated = { id: 'new-1', ...mockData };

      mockFastify.psql.User.create.mockResolvedValue(mockCreated);

      const result = await UserRepository.create(mockFastify, mockData, mockOptions);

      expect(mockFastify.psql.User.create).toHaveBeenCalledWith(mockData, mockOptions);
      expect(result).toEqual(mockCreated);
    });
  });

  describe('update', () => {
    it('should call model.update with data and options', async () => {
      const model = { update: vi.fn() };
      const data = { name: 'Updated' };
      const options = { transaction: {} };
      const expected = [1, [{ id: '1', name: 'Updated' }]];

      model.update.mockResolvedValue(expected);

      const result = await UserRepository.update(model, data, options);

      expect(model.update).toHaveBeenCalledWith(data, options);
      expect(result).toEqual(expected);
    });
  });

  describe('getSubAccounts', () => {
    it('should return subaccounts by parentId and status', async () => {
      const data = { parentId: 'parent-1', status: 'active' };
      const expected = [{ id: 'sub-1' }, { id: 'sub-2' }];

      mockFastify.psql.User.findAll.mockResolvedValue(expected);

      const result = await UserRepository.getSubAccounts(mockFastify, data);

      expect(mockFastify.psql.User.findAll).toHaveBeenCalledWith({
        where: {
          parentId: 'parent-1',
          status: 'active',
        },
      });
      expect(result).toEqual(expected);
    });
  });
});
