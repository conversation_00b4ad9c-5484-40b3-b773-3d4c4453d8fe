import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  bulkCreate,
  bulkUpsert,
  findAllByRoleId,
  findAllByRoleIdWithPolicy,
  findAllByRoleIdWithPolicyAndModules,
  findAllByRoleIdsAndModuleId,
  findOrCreate,
  update,
} from '#src/modules/user/repository/role-module.repository.js';

describe('Role Module Repository', () => {
  const mockFastify = {
    psql: {
      RoleModule: {
        findOrCreate: vi.fn(),
        findAll: vi.fn(),
        bulkCreate: vi.fn(),
        update: vi.fn(),
      },
      Module: {},
      Policy: {},
    },
  };

  const mockServer = {
    psql: {
      connection: {
        models: {
          RoleModule: {
            findAll: vi.fn(),
          },
          Policy: {},
        },
      },
    },
  };

  const mockRoleModuleInstance = {
    id: 'role-module-1',
    roleId: 'role-1',
    moduleId: 'module-1',
    update: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('findOrCreate', () => {
    it('should call findOrCreate with correct parameters', async () => {
      const where = { roleId: 'role-1', moduleId: 'module-1' };
      const defaults = { status: 'active' };
      const options = { transaction: 'tx1' };

      mockFastify.psql.RoleModule.findOrCreate.mockResolvedValue([mockRoleModuleInstance, true]);

      await findOrCreate(mockFastify, where, defaults, options);

      expect(mockFastify.psql.RoleModule.findOrCreate).toHaveBeenCalledWith({
        where,
        defaults,
        transaction: 'tx1',
      });
    });

    it('should return the result of findOrCreate', async () => {
      const where = { roleId: 'role-1', moduleId: 'module-1' };
      const defaults = { status: 'active' };
      const expectedResult = [mockRoleModuleInstance, true];

      mockFastify.psql.RoleModule.findOrCreate.mockResolvedValue(expectedResult);

      const result = await findOrCreate(mockFastify, where, defaults);

      expect(result).toEqual(expectedResult);
    });

    it('should use empty options object by default', async () => {
      const where = { roleId: 'role-1', moduleId: 'module-1' };
      const defaults = { status: 'active' };

      mockFastify.psql.RoleModule.findOrCreate.mockResolvedValue([mockRoleModuleInstance, true]);

      await findOrCreate(mockFastify, where, defaults);

      expect(mockFastify.psql.RoleModule.findOrCreate).toHaveBeenCalledWith({
        where,
        defaults,
      });
    });
  });

  describe('findAllByRoleId', () => {
    it('should call findAll with correct parameters', async () => {
      const roleId = 'role-1';
      const options = { transaction: 'tx1' };

      mockFastify.psql.RoleModule.findAll.mockResolvedValue([mockRoleModuleInstance]);

      await findAllByRoleId(mockFastify, roleId, options);

      expect(mockFastify.psql.RoleModule.findAll).toHaveBeenCalledWith({
        where: { roleId },
        transaction: 'tx1',
      });
    });

    it('should return the result of findAll', async () => {
      const roleId = 'role-1';
      const expectedResult = [mockRoleModuleInstance];

      mockFastify.psql.RoleModule.findAll.mockResolvedValue(expectedResult);

      const result = await findAllByRoleId(mockFastify, roleId);

      expect(result).toEqual(expectedResult);
    });

    it('should use empty options object by default', async () => {
      const roleId = 'role-1';

      mockFastify.psql.RoleModule.findAll.mockResolvedValue([mockRoleModuleInstance]);

      await findAllByRoleId(mockFastify, roleId);

      expect(mockFastify.psql.RoleModule.findAll).toHaveBeenCalledWith({
        where: { roleId },
      });
    });
  });

  describe('update', () => {
    it('should call update with correct parameters', async () => {
      const data = { status: 'inactive' };
      const options = { transaction: 'tx1' };
      const expectedResult = { id: 'role-module-1', updated: true };

      mockRoleModuleInstance.update.mockResolvedValue(expectedResult);

      await update(mockRoleModuleInstance, data, options);

      expect(mockRoleModuleInstance.update).toHaveBeenCalledWith(data, options);
    });

    it('should return the result of update', async () => {
      const data = { status: 'inactive' };
      const expectedResult = { id: 'role-module-1', updated: true };

      mockRoleModuleInstance.update.mockResolvedValue(expectedResult);

      const result = await update(mockRoleModuleInstance, data);

      expect(result).toEqual(expectedResult);
    });

    it('should use empty options object by default', async () => {
      const data = { status: 'inactive' };

      mockRoleModuleInstance.update.mockResolvedValue({ id: 'role-module-1', updated: true });

      await update(mockRoleModuleInstance, data);

      expect(mockRoleModuleInstance.update).toHaveBeenCalledWith(data, {});
    });
  });

  describe('findAllByRoleIdWithPolicy', () => {
    it('should call findAll with correct parameters', async () => {
      const roleId = 'role-1';
      const expectedRoleModules = [
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          policy: {
            id: 'policy-1',
            key: 'canView',
            value: true,
          },
        },
      ];

      mockServer.psql.connection.models.RoleModule.findAll.mockResolvedValue(expectedRoleModules);

      await findAllByRoleIdWithPolicy(mockServer, roleId);

      expect(mockServer.psql.connection.models.RoleModule.findAll).toHaveBeenCalledWith({
        where: { roleId },
        include: [
          {
            model: mockServer.psql.connection.models.Policy,
            as: 'policy',
          },
        ],
      });
    });

    it('should return the result of findAll with policy', async () => {
      const roleId = 'role-1';
      const expectedRoleModules = [
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          policy: {
            id: 'policy-1',
            key: 'canView',
            value: true,
          },
        },
      ];

      mockServer.psql.connection.models.RoleModule.findAll.mockResolvedValue(expectedRoleModules);

      const result = await findAllByRoleIdWithPolicy(mockServer, roleId);

      expect(result).toEqual(expectedRoleModules);
    });
  });

  describe('findAllByRoleIdWithPolicyAndModules', () => {
    it('should call findAll with correct parameters', async () => {
      const roleId = 'role-1';
      const expectedRoleModules = [
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          policy: {
            id: 'policy-1',
            canView: true,
            canEdit: false,
          },
          module: {
            id: 'module-1',
            name: 'User Management',
            hierarchy: 'admin',
            navigationType: 'SIDE',
          },
        },
      ];

      mockFastify.psql.RoleModule.findAll.mockResolvedValue(expectedRoleModules);

      await findAllByRoleIdWithPolicyAndModules(mockFastify, roleId);

      expect(mockFastify.psql.RoleModule.findAll).toHaveBeenCalledWith({
        where: { roleId },
        include: [
          {
            model: mockFastify.psql.Policy,
            as: 'policy',
          },
          {
            model: mockFastify.psql.Module,
            as: 'module',
          },
        ],
      });
    });

    it('should return the result of findAll with policy and modules', async () => {
      const roleId = 'role-1';
      const expectedRoleModules = [
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          policy: {
            id: 'policy-1',
            canView: true,
            canEdit: false,
          },
          module: {
            id: 'module-1',
            name: 'User Management',
            hierarchy: 'admin',
            navigationType: 'SIDE',
          },
        },
        {
          id: 'role-module-2',
          roleId: 'role-1',
          moduleId: 'module-2',
          policy: {
            id: 'policy-2',
            canView: true,
            canEdit: true,
          },
          module: {
            id: 'module-2',
            name: 'Role Management',
            hierarchy: 'admin',
            navigationType: 'SIDE',
          },
        },
      ];

      mockFastify.psql.RoleModule.findAll.mockResolvedValue(expectedRoleModules);

      const result = await findAllByRoleIdWithPolicyAndModules(mockFastify, roleId);

      expect(result).toEqual(expectedRoleModules);
      expect(result.length).toBe(2);
    });

    it('should return empty array when no role modules are found', async () => {
      const roleId = 'non-existent-role';

      mockFastify.psql.RoleModule.findAll.mockResolvedValue([]);

      const result = await findAllByRoleIdWithPolicyAndModules(mockFastify, roleId);

      expect(result).toEqual([]);
      expect(result.length).toBe(0);
    });

    it('should handle role modules with missing policy or modules', async () => {
      const roleId = 'role-1';
      const expectedRoleModules = [
        {
          id: 'role-module-1',
          roleId: 'role-1',
          moduleId: 'module-1',
          policy: null,
          module: {
            id: 'module-1',
            name: 'User Management',
            hierarchy: 'admin',
            navigationType: 'SIDE',
          },
        },
        {
          id: 'role-module-2',
          roleId: 'role-1',
          moduleId: 'module-2',
          policy: {
            id: 'policy-2',
            canView: true,
            canEdit: true,
          },
          module: null,
        },
      ];

      mockFastify.psql.RoleModule.findAll.mockResolvedValue(expectedRoleModules);

      const result = await findAllByRoleIdWithPolicyAndModules(mockFastify, roleId);

      expect(result).toEqual(expectedRoleModules);
      expect(result[0].policy).toBeNull();
      expect(result[1].module).toBeNull();
    });
  });

  describe('bulkCreate', () => {
    it('should call RoleModule.bulkCreate with correct parameters', async () => {
      const data = [
        { roleId: 'role-1', moduleId: 'module-1' },
        { roleId: 'role-1', moduleId: 'module-2' },
      ];
      const options = { transaction: 'tx1' };
      const expectedResult = [
        { id: 'role-module-1', roleId: 'role-1', moduleId: 'module-1' },
        { id: 'role-module-2', roleId: 'role-1', moduleId: 'module-2' },
      ];

      mockFastify.psql.RoleModule.bulkCreate.mockResolvedValue(expectedResult);

      const result = await bulkCreate(mockFastify, data, options);

      expect(mockFastify.psql.RoleModule.bulkCreate).toHaveBeenCalledWith(data, {
        transaction: 'tx1',
        returning: true,
      });
      expect(result).toEqual(expectedResult);
    });

    it('should use default options when not provided', async () => {
      const data = [
        { roleId: 'role-1', moduleId: 'module-1' },
        { roleId: 'role-1', moduleId: 'module-2' },
      ];
      const expectedResult = [
        { id: 'role-module-1', roleId: 'role-1', moduleId: 'module-1' },
        { id: 'role-module-2', roleId: 'role-1', moduleId: 'module-2' },
      ];

      mockFastify.psql.RoleModule.bulkCreate.mockResolvedValue(expectedResult);

      const result = await bulkCreate(mockFastify, data);

      expect(mockFastify.psql.RoleModule.bulkCreate).toHaveBeenCalledWith(data, {
        returning: true,
      });
      expect(result).toEqual(expectedResult);
    });

    it('should propagate errors from the database', async () => {
      const data = [
        { roleId: 'role-1', moduleId: 'module-1' },
        { roleId: 'role-1', moduleId: 'module-2' },
      ];
      const dbError = new Error('Database error');

      mockFastify.psql.RoleModule.bulkCreate.mockRejectedValue(dbError);

      await expect(bulkCreate(mockFastify, data)).rejects.toThrow(dbError);
      expect(mockFastify.psql.RoleModule.bulkCreate).toHaveBeenCalledTimes(1);
    });
  });

  describe('bulkUpsert', () => {
    it('should call RoleModule.bulkCreate with correct parameters and default conflict fields', async () => {
      const data = [
        { roleId: 'role-1', moduleId: 'module-1', updatedBy: 'user-1' },
        { roleId: 'role-1', moduleId: 'module-2', updatedBy: 'user-1' },
      ];
      const options = { transaction: 'tx1' };
      const conflictFields = ['roleId', 'moduleId'];
      const expectedResult = [
        { id: 'role-module-1', roleId: 'role-1', moduleId: 'module-1', updatedBy: 'user-1' },
        { id: 'role-module-2', roleId: 'role-1', moduleId: 'module-2', updatedBy: 'user-1' },
      ];

      mockFastify.psql.RoleModule.bulkCreate.mockResolvedValue(expectedResult);

      const result = await bulkUpsert(mockFastify, data, conflictFields, options);

      expect(mockFastify.psql.RoleModule.bulkCreate).toHaveBeenCalledWith(data, {
        updateOnDuplicate: ['updatedBy'],
        returning: true,
        transaction: 'tx1',
      });
      expect(result).toEqual(expectedResult);
    });

    it('should use custom conflict fields when provided', async () => {
      const data = [
        { roleId: 'role-1', moduleId: 'module-1', updatedBy: 'user-1' },
        { roleId: 'role-1', moduleId: 'module-2', updatedBy: 'user-1' },
      ];
      const options = {
        transaction: 'tx1',
      };
      const conflictFields = ['roleId', 'moduleId'];
      const expectedResult = [
        { id: 'role-module-1', roleId: 'role-1', moduleId: 'module-1', updatedBy: 'user-1' },
        { id: 'role-module-2', roleId: 'role-1', moduleId: 'module-2', updatedBy: 'user-1' },
      ];

      mockFastify.psql.RoleModule.bulkCreate.mockResolvedValue(expectedResult);

      const result = await bulkUpsert(mockFastify, data, conflictFields, options);

      expect(mockFastify.psql.RoleModule.bulkCreate).toHaveBeenCalledWith(data, {
        updateOnDuplicate: ['updatedBy'],
        returning: true,
        transaction: 'tx1',
      });
      expect(result).toEqual(expectedResult);
    });

    it('should handle data with id field correctly', async () => {
      const data = [
        { id: 'existing-1', roleId: 'role-1', moduleId: 'module-1', updatedBy: 'user-1' },
        { roleId: 'role-1', moduleId: 'module-2', updatedBy: 'user-1' },
      ];
      const expectedResult = [
        { id: 'existing-1', roleId: 'role-1', moduleId: 'module-1', updatedBy: 'user-1' },
        { id: 'new-1', roleId: 'role-1', moduleId: 'module-2', updatedBy: 'user-1' },
      ];

      mockFastify.psql.RoleModule.bulkCreate.mockResolvedValue(expectedResult);

      const conflictFields = ['roleId', 'moduleId'];
      const result = await bulkUpsert(mockFastify, data, conflictFields);

      expect(mockFastify.psql.RoleModule.bulkCreate).toHaveBeenCalledWith(data, {
        updateOnDuplicate: ['id', 'updatedBy'],
        returning: true,
      });
      expect(result).toEqual(expectedResult);
    });

    it('should propagate errors from the database', async () => {
      const data = [
        { roleId: 'role-1', moduleId: 'module-1', updatedBy: 'user-1' },
        { roleId: 'role-1', moduleId: 'module-2', updatedBy: 'user-1' },
      ];
      const dbError = new Error('Database error');

      mockFastify.psql.RoleModule.bulkCreate.mockRejectedValue(dbError);

      const conflictFields = ['roleId', 'moduleId'];
      await expect(bulkUpsert(mockFastify, conflictFields, data)).rejects.toThrow(dbError);
      expect(mockFastify.psql.RoleModule.bulkCreate).toHaveBeenCalledTimes(1);
    });
  });

  describe('findAllByRoleIdsAndModuleId', () => {
    it('should call findAll with correct parameters', async () => {
      const roleIds = ['role-1', 'role-2'];
      const moduleId = 'module-1';
      const options = { transaction: 'tx1' };

      mockFastify.psql.RoleModule.findAll.mockResolvedValue([mockRoleModuleInstance]);

      await findAllByRoleIdsAndModuleId(mockFastify, roleIds, moduleId, options);

      expect(mockFastify.psql.RoleModule.findAll).toHaveBeenCalledWith({
        where: {
          roleId: roleIds,
          moduleId,
        },
        transaction: 'tx1',
      });
    });

    it('should return the result of findAll', async () => {
      const roleIds = ['role-1', 'role-2'];
      const moduleId = 'module-1';
      const expectedResult = [
        mockRoleModuleInstance,
        { ...mockRoleModuleInstance, id: 'role-module-2', roleId: 'role-2' },
      ];

      mockFastify.psql.RoleModule.findAll.mockResolvedValue(expectedResult);

      const result = await findAllByRoleIdsAndModuleId(mockFastify, roleIds, moduleId);

      expect(result).toEqual(expectedResult);
      expect(result.length).toBe(2);
    });

    it('should use empty options object by default', async () => {
      const roleIds = ['role-1', 'role-2'];
      const moduleId = 'module-1';

      mockFastify.psql.RoleModule.findAll.mockResolvedValue([mockRoleModuleInstance]);

      await findAllByRoleIdsAndModuleId(mockFastify, roleIds, moduleId);

      expect(mockFastify.psql.RoleModule.findAll).toHaveBeenCalledWith({
        where: {
          roleId: roleIds,
          moduleId,
        },
      });
    });

    it('should handle empty roleIds array', async () => {
      const roleIds = [];
      const moduleId = 'module-1';

      mockFastify.psql.RoleModule.findAll.mockResolvedValue([]);

      const result = await findAllByRoleIdsAndModuleId(mockFastify, roleIds, moduleId);

      expect(result).toEqual([]);
      expect(mockFastify.psql.RoleModule.findAll).toHaveBeenCalledWith({
        where: {
          roleId: [],
          moduleId,
        },
      });
    });
  });
});
