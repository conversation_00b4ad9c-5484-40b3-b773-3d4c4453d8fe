import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as userAssociationCurrencyRepository from '#src/modules/user/repository/user-association-currency.repository.js';

describe('UserAssociationCurrency Repository', () => {
  let mockServer;
  const mockTransaction = {};

  beforeEach(() => {
    mockServer = {
      psql: {
        UserAssociationCurrency: {
          bulkCreate: vi.fn(),
          destroy: vi.fn(),
        },
      },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('create', () => {
    it('should bulkCreate with array of objects', async () => {
      const data = [
        { userAssociationId: 1, currencyId: 2 },
        { userAssociationId: 1, currencyId: 3 },
      ];
      mockServer.psql.UserAssociationCurrency.bulkCreate.mockResolvedValue(data);

      const result = await userAssociationCurrencyRepository.create(mockServer, data);

      expect(mockServer.psql.UserAssociationCurrency.bulkCreate).toHaveBeenCalledWith(data, {
        individualHooks: true,
      });
      expect(result).toEqual(data);
    });
  });

  describe('deleteByUserAssociationId', () => {
    it('should call destroy with correct where clause', async () => {
      const userAssociationId = 1;
      const options = { transaction: mockTransaction };
      const expected = 2;

      mockServer.psql.UserAssociationCurrency.destroy.mockResolvedValue(expected);

      const result = await userAssociationCurrencyRepository.deleteByUserAssociationId(
        mockServer,
        userAssociationId,
        options,
      );

      expect(mockServer.psql.UserAssociationCurrency.destroy).toHaveBeenCalledWith({
        where: { userAssociationId: userAssociationId },
        ...options,
      });
      expect(result).toBe(expected);
    });
  });
});
