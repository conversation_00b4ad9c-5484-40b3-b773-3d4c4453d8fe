import bcrypt from 'bcrypt';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant, LinkConstant } from '#src/modules/core/constants/index.js';
import { EntityRepository, LinkRepository } from '#src/modules/core/repository/index.js';
import { SettingRepository } from '#src/modules/setting/repository/index.js';
import { UserConstant, AuthConstant } from '#src/modules/user/constants/index.js';
import { AuthError } from '#src/modules/user/errors/index.js';
import { UserMfaSettingRepository, UserRepository } from '#src/modules/user/repository/index.js';
import * as authValidation from '#src/modules/user/validations/auth.validation.js';
import { generateCacheKey, getCache } from '#src/utils/cache.util.js';
import { decodeJWT } from '#src/utils/jwt.util.js';
import { handle2FASetup } from '#src/utils/twofa.util.js';

vi.mock('#src/modules/core/repository/index.js', () => ({
  EntityRepository: {
    getEntity: vi.fn(),
  },
  LinkRepository: {
    findOne: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/repository/index.js', () => ({
  SettingRepository: {
    getSingleSetting: vi.fn(),
  },
}));

vi.mock('#src/modules/user/repository/index.js', () => ({
  UserRepository: {
    findUser: vi.fn(),
    update: vi.fn(),
  },
  UserMfaSettingRepository: {
    findByUserId: vi.fn(),
    update: vi.fn(),
  },
}));

vi.mock('#src/modules/user/errors/index.js', () => ({
  AuthError: {
    tooManyRequests: vi.fn(() => new Error('Too many requests')),
    maintenanceMode: vi.fn(() => new Error('System under maintenance')),
    accountInactive: vi.fn(() => new Error('Account inactive')),
    accountSuspended: vi.fn(() => new Error('Account suspended')),
    accountPending: vi.fn(() => new Error('Account pending')),
    invalidCredentials: vi.fn(() => new Error('Invalid credentials')),
    invalidAccessId: vi.fn(() => new Error('Unauthorized entity access')),
    unauthorisedDomain: vi.fn(() => new Error('Unauthorised domain')),
    invalidPreAuthToken: vi.fn(() => new Error('Invalid pre-auth token')),
  },
}));

vi.mock('#src/utils/cache.util.js', () => ({
  clearCache: vi.fn(),
  generateCacheKey: vi.fn(),
  fetchFromCache: vi.fn(),
  getCache: vi.fn(),
}));

vi.mock('#src/utils/jwt.util.js', () => ({
  decodeJWT: vi.fn(),
}));

vi.mock('bcrypt', () => ({
  default: {
    compare: vi.fn(),
  },
}));

vi.mock('#src/utils/twofa.util.js', () => ({
  handle2FASetup: vi.fn(),
}));

vi.mock('#src/utils/jwt.util.js', () => ({
  decodeJWT: vi.fn(),
}));

describe('Auth Validation', () => {
  let mockServer;
  let mockUser;
  const mockIp = '127.0.0.1';

  beforeEach(() => {
    vi.clearAllMocks();

    mockServer = {
      redis: {
        get: vi.fn(),
        set: vi.fn(),
        del: vi.fn(),
        db3: {
          get: vi.fn(),
          incr: vi.fn(),
          expire: vi.fn(),
          del: vi.fn(),
        },
      },
      log: {
        debug: vi.fn(),
      },
    };

    mockUser = {
      id: 'user-123',
      username: 'testuser',
      status: UserConstant.USER_STATUSES.ACTIVE,
      password: 'hashed-password',
      ua: [
        {
          entity: {
            id: 'entity-123',
            hierarchy: CoreConstant.HIERARCHY.USER,
          },
        },
      ],
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('checkIpRateLimiting', () => {
    it('should not throw error when IP has not exceeded attempts', async () => {
      const mockCachedData = JSON.stringify(3);
      getCache.mockResolvedValue(mockCachedData);
      generateCacheKey.mockReturnValue(`failed_login:ip:${mockIp}`);

      await expect(authValidation.checkIpRateLimiting(mockServer, mockIp)).resolves.not.toThrow();

      expect(getCache).toHaveBeenCalledWith(mockServer.redis.db3, `failed_login:ip:${mockIp}`);
    });

    it('should not throw error when IP has no attempts', async () => {
      getCache.mockResolvedValue(null);
      generateCacheKey.mockReturnValue(`failed_login:ip:${mockIp}`);

      await expect(authValidation.checkIpRateLimiting(mockServer, mockIp)).resolves.not.toThrow();

      expect(getCache).toHaveBeenCalledWith(mockServer.redis.db3, `failed_login:ip:${mockIp}`);
    });

    it('should throw error when IP has exceeded attempts', async () => {
      const mockCachedData = JSON.stringify(5);
      getCache.mockResolvedValue(mockCachedData);
      generateCacheKey.mockReturnValue(`failed_login:ip:${mockIp}`);

      await expect(authValidation.checkIpRateLimiting(mockServer, mockIp)).rejects.toThrow();

      expect(getCache).toHaveBeenCalledWith(mockServer.redis.db3, `failed_login:ip:${mockIp}`);
      expect(AuthError.tooManyRequests).toHaveBeenCalled();
    });

    it('should not throw error when cached data is empty string', async () => {
      getCache.mockResolvedValue('');
      generateCacheKey.mockReturnValue(`failed_login:ip:${mockIp}`);

      await expect(authValidation.checkIpRateLimiting(mockServer, mockIp)).resolves.not.toThrow();

      expect(getCache).toHaveBeenCalledWith(mockServer.redis.db3, `failed_login:ip:${mockIp}`);
    });

    it('should handle JSON parsing correctly for valid cached data', async () => {
      const mockCachedData = JSON.stringify(4);
      getCache.mockResolvedValue(mockCachedData);
      generateCacheKey.mockReturnValue(`failed_login:ip:${mockIp}`);

      await expect(authValidation.checkIpRateLimiting(mockServer, mockIp)).resolves.not.toThrow();

      expect(getCache).toHaveBeenCalledWith(mockServer.redis.db3, `failed_login:ip:${mockIp}`);
    });
  });

  describe('checkSystemStatus', () => {
    it('should bypass check when user is ROOT', async () => {
      const rootUser = {
        ua: [
          {
            entity: {
              hierarchy: CoreConstant.HIERARCHY.ROOT,
            },
          },
        ],
      };

      await expect(authValidation.checkSystemStatus(mockServer, rootUser)).resolves.not.toThrow();

      expect(SettingRepository.getSingleSetting).not.toHaveBeenCalled();
    });

    it('should do nothing when maintenance mode is off', async () => {
      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockImplementation(async (redis, key, callback) => {
        return await callback();
      });

      SettingRepository.getSingleSetting.mockResolvedValue({
        customSettings: { value: 'off' },
      });

      await expect(authValidation.checkSystemStatus(mockServer, mockUser)).resolves.not.toThrow();

      expect(generateCacheKey).toHaveBeenCalledWith(
        'maintenance_mode_settings',
        {
          raw: { url: '/settings/safety/maintenanceStatus' },
          query: {},
          entity: { id: 'global' },
        },
        { excludeKey: true },
      );
      expect(SettingRepository.getSingleSetting).toHaveBeenCalledWith(mockServer, {
        category: 'safety',
        field: 'maintenanceStatus',
      });
    });

    it('should throw error when maintenance mode is on and user is not ROOT', async () => {
      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockImplementation(async (redis, key, callback) => {
        return await callback();
      });

      SettingRepository.getSingleSetting.mockResolvedValue({
        customSettings: { value: 'on' },
      });

      await expect(authValidation.checkSystemStatus(mockServer, mockUser)).rejects.toThrow();

      expect(generateCacheKey).toHaveBeenCalledWith(
        'maintenance_mode_settings',
        {
          raw: { url: '/settings/safety/maintenanceStatus' },
          query: {},
          entity: { id: 'global' },
        },
        { excludeKey: true },
      );
      expect(SettingRepository.getSingleSetting).toHaveBeenCalledWith(mockServer, {
        category: 'safety',
        field: 'maintenanceStatus',
      });
      expect(AuthError.maintenanceMode).toHaveBeenCalled();
    });

    it('should use cached settings when available', async () => {
      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockResolvedValue({
        customSettings: { value: 'on' },
      });

      await expect(authValidation.checkSystemStatus(mockServer, mockUser)).rejects.toThrow();

      expect(generateCacheKey).toHaveBeenCalledWith(
        'maintenance_mode_settings',
        {
          raw: { url: '/settings/safety/maintenanceStatus' },
          query: {},
          entity: { id: 'global' },
        },
        { excludeKey: true },
      );
      expect(SettingRepository.getSingleSetting).not.toHaveBeenCalled();
      expect(AuthError.maintenanceMode).toHaveBeenCalled();
    });
  });

  describe('validateUserStatus', () => {
    it('should not throw error when user status is active', () => {
      mockUser.status = UserConstant.USER_STATUSES.ACTIVE;

      expect(() => authValidation.validateUserStatus(mockUser)).not.toThrow();
    });

    it('should throw error when user status is inactive', () => {
      mockUser.status = UserConstant.USER_STATUSES.INACTIVE;

      expect(() => authValidation.validateUserStatus(mockUser)).toThrow();
      expect(AuthError.accountInactive).toHaveBeenCalled();
    });

    it('should throw error when user status is suspended', () => {
      mockUser.status = UserConstant.USER_STATUSES.SUSPENDED;

      expect(() => authValidation.validateUserStatus(mockUser)).toThrow();
      expect(AuthError.accountSuspended).toHaveBeenCalled();
    });

    it('should throw error when user status is pending', () => {
      mockUser.status = UserConstant.USER_STATUSES.PENDING;

      expect(() => authValidation.validateUserStatus(mockUser)).toThrow();
      expect(AuthError.accountPending).toHaveBeenCalled();
    });

    it('should throw accountInactive error for any other status', () => {
      mockUser.status = 'UNKNOWN_STATUS';

      expect(() => authValidation.validateUserStatus(mockUser)).toThrow();
      expect(AuthError.accountInactive).toHaveBeenCalled();
    });
  });

  describe('validatePassword', () => {
    it('should not throw error when password is valid', async () => {
      bcrypt.compare.mockResolvedValue(true);

      await expect(
        authValidation.validatePassword(mockServer, mockUser, 'password', mockIp),
      ).resolves.not.toThrow();

      expect(bcrypt.compare).toHaveBeenCalledWith('password', mockUser.password);
    });

    it('should throw error when password is invalid', async () => {
      bcrypt.compare.mockResolvedValue(false);

      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockImplementation(async (redis, key, callback) => {
        return await callback();
      });

      SettingRepository.getSingleSetting.mockResolvedValue({
        customSettings: { value: 5 },
      });
      mockServer.redis.db3.incr.mockResolvedValueOnce(1).mockResolvedValueOnce(1);

      await expect(
        authValidation.validatePassword(mockServer, mockUser, 'wrong-password', mockIp),
      ).rejects.toThrow();

      expect(AuthError.invalidCredentials).toHaveBeenCalled();
      expect(bcrypt.compare).toHaveBeenCalledWith('wrong-password', mockUser.password);
      expect(generateCacheKey).toHaveBeenCalledWith(
        'login_attempts_settings',
        {
          raw: { url: '/settings/safety/passwordMaximumAttempts' },
          query: {},
          entity: { id: 'global' },
        },
        { excludeKey: true },
      );
      expect(mockServer.redis.db3.incr).toHaveBeenCalledTimes(2);
      expect(mockServer.redis.db3.expire).toHaveBeenCalledTimes(2);
      expect(UserRepository.update).not.toHaveBeenCalled();
    });

    it('should suspend user when max attempts reached', async () => {
      bcrypt.compare.mockResolvedValue(false);

      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockImplementation(async (redis, key, callback) => {
        return await callback();
      });

      SettingRepository.getSingleSetting.mockResolvedValue({
        customSettings: { value: 5 },
      });
      mockServer.redis.db3.incr.mockResolvedValueOnce(5).mockResolvedValueOnce(5);

      await expect(
        authValidation.validatePassword(mockServer, mockUser, 'wrong-password', mockIp),
      ).rejects.toThrow();

      expect(bcrypt.compare).toHaveBeenCalledWith('wrong-password', mockUser.password);
      expect(generateCacheKey).toHaveBeenCalledWith(
        'login_attempts_settings',
        {
          raw: { url: '/settings/safety/passwordMaximumAttempts' },
          query: {},
          entity: { id: 'global' },
        },
        { excludeKey: true },
      );
      expect(UserRepository.update).toHaveBeenCalledWith(mockUser, {
        status: UserConstant.USER_STATUSES.SUSPENDED,
      });
    });

    it('should use default max attempts when setting is not found in cache', async () => {
      bcrypt.compare.mockResolvedValue(false);

      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockImplementation(async (redis, key, callback) => {
        return null;
      });

      mockServer.redis.db3.incr.mockResolvedValueOnce(1).mockResolvedValueOnce(1);
      generateCacheKey.mockReturnValue(`failed_login:${mockUser.id}`);

      await expect(
        authValidation.validatePassword(mockServer, mockUser, 'wrong-password', mockIp),
      ).rejects.toThrow();

      expect(generateCacheKey).toHaveBeenCalledWith(
        'login_attempts_settings',
        {
          raw: { url: '/settings/safety/passwordMaximumAttempts' },
          query: {},
          entity: { id: 'global' },
        },
        { excludeKey: true },
      );
      expect(mockServer.redis.db3.incr).toHaveBeenCalledWith(`failed_login:${mockUser.id}`);
    });

    it('should use cached settings when available', async () => {
      bcrypt.compare.mockResolvedValue(false);

      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockResolvedValue({
        customSettings: { value: 10 },
      });

      mockServer.redis.db3.incr.mockResolvedValueOnce(1).mockResolvedValueOnce(1);

      await expect(
        authValidation.validatePassword(mockServer, mockUser, 'wrong-password', mockIp),
      ).rejects.toThrow();

      expect(generateCacheKey).toHaveBeenCalledWith(
        'login_attempts_settings',
        {
          raw: { url: '/settings/safety/passwordMaximumAttempts' },
          query: {},
          entity: { id: 'global' },
        },
        { excludeKey: true },
      );
      expect(SettingRepository.getSingleSetting).not.toHaveBeenCalled();
    });

    it('should use AuthConstant.LOGIN_ATTEMPTS when setting value is missing', async () => {
      bcrypt.compare.mockResolvedValue(false);

      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockResolvedValue({
        customSettings: {},
      });

      mockServer.redis.db3.incr.mockResolvedValueOnce(1).mockResolvedValueOnce(1);

      await expect(
        authValidation.validatePassword(mockServer, mockUser, 'wrong-password', mockIp),
      ).rejects.toThrow();

      expect(generateCacheKey).toHaveBeenCalledWith(
        'login_attempts_settings',
        {
          raw: { url: '/settings/safety/passwordMaximumAttempts' },
          query: {},
          entity: { id: 'global' },
        },
        { excludeKey: true },
      );
    });
  });

  describe('determineEntityAccess', () => {
    it('should return false when entities are null or undefined', () => {
      expect(authValidation.determineEntityAccess(null, null)).toBe(false);
      expect(authValidation.determineEntityAccess(undefined, {})).toBe(false);
      expect(authValidation.determineEntityAccess({}, null)).toBe(false);
    });

    it('should return true for root user', () => {
      const userEntity = { id: '1', hierarchy: CoreConstant.HIERARCHY.ROOT };
      const targetEntity = { id: '2', hierarchy: 'user' };

      expect(authValidation.determineEntityAccess(userEntity, targetEntity)).toBe(true);
    });

    it('should return true for same entity', () => {
      const userEntity = { id: '1', hierarchy: 'user' };
      const targetEntity = { id: '1', hierarchy: 'user' };

      expect(authValidation.determineEntityAccess(userEntity, targetEntity)).toBe(true);
    });

    it('should return true for child entity', () => {
      const userEntity = { id: '1', hierarchy: 'user' };
      const targetEntity = { id: '2', parentId: '1', hierarchy: 'sub' };

      expect(authValidation.determineEntityAccess(userEntity, targetEntity)).toBe(true);
    });

    it('should return false for unrelated entity', () => {
      const userEntity = { id: '1', hierarchy: 'user' };
      const targetEntity = { id: '2', parentId: '3', hierarchy: 'other' };

      expect(authValidation.determineEntityAccess(userEntity, targetEntity)).toBe(false);
    });
  });

  describe('incrementFailedLoginAttempts', () => {
    it('should increment user and IP attempts and set expiry for first attempt', async () => {
      generateCacheKey
        .mockReturnValueOnce(`failed_login:${mockUser.id}`)
        .mockReturnValueOnce(`failed_login:ip:${mockIp}`);
      mockServer.redis.db3.incr.mockResolvedValueOnce(1).mockResolvedValueOnce(1);

      const result = await authValidation.incrementFailedLoginAttempts(
        mockServer,
        mockUser.id,
        mockIp,
      );

      expect(result).toBe(1);
      expect(mockServer.redis.db3.incr).toHaveBeenCalledWith(`failed_login:${mockUser.id}`);
      expect(mockServer.redis.db3.incr).toHaveBeenCalledWith(`failed_login:ip:${mockIp}`);
      expect(mockServer.redis.db3.expire).toHaveBeenCalledWith(
        `failed_login:${mockUser.id}`,
        86400,
      );
      expect(mockServer.redis.db3.expire).toHaveBeenCalledWith(`failed_login:ip:${mockIp}`, 86400);
    });

    it('should increment user and IP attempts without setting expiry for subsequent attempts', async () => {
      generateCacheKey
        .mockReturnValueOnce(`failed_login:${mockUser.id}`)
        .mockReturnValueOnce(`failed_login:ip:${mockIp}`);
      mockServer.redis.db3.incr.mockResolvedValueOnce(2).mockResolvedValueOnce(2);

      const result = await authValidation.incrementFailedLoginAttempts(
        mockServer,
        mockUser.id,
        mockIp,
      );

      expect(result).toBe(2);
      expect(mockServer.redis.db3.incr).toHaveBeenCalledWith(`failed_login:${mockUser.id}`);
      expect(mockServer.redis.db3.incr).toHaveBeenCalledWith(`failed_login:ip:${mockIp}`);
      expect(mockServer.redis.db3.expire).not.toHaveBeenCalledWith(
        `failed_login:${mockUser.id}`,
        86400,
      );
      expect(mockServer.redis.db3.expire).toHaveBeenCalledWith(`failed_login:ip:${mockIp}`, 86400);
    });
  });

  describe('resetFailedLoginAttempts', () => {
    it('should reset user attempts only when IP is not provided', async () => {
      const { clearCache } = await import('#src/utils/cache.util.js');
      generateCacheKey.mockReturnValueOnce(`failed_login:${mockUser.id}`);

      await authValidation.resetFailedLoginAttempts(mockServer, mockUser.id);

      expect(clearCache).toHaveBeenCalledWith(mockServer.redis.db3, `failed_login:${mockUser.id}`);
      expect(clearCache).toHaveBeenCalledTimes(1);
    });

    it('should reset both user and IP attempts when IP is provided', async () => {
      const { clearCache } = await import('#src/utils/cache.util.js');

      generateCacheKey
        .mockReturnValueOnce(`failed_login:${mockUser.id}`)
        .mockReturnValueOnce(`failed_login:ip:${mockIp}`);

      await authValidation.resetFailedLoginAttempts(mockServer, mockUser.id, mockIp);

      expect(clearCache).toHaveBeenCalledWith(mockServer.redis.db3, `failed_login:${mockUser.id}`);
      expect(clearCache).toHaveBeenCalledWith(mockServer.redis.db3, `failed_login:ip:${mockIp}`);
      expect(clearCache).toHaveBeenCalledTimes(2);
    });
  });

  describe('validateAccessAndGetEntityData', () => {
    let mockUserWithAssociation;
    let mockParentEntity;

    beforeEach(() => {
      mockParentEntity = {
        id: 'parent-123',
        accessId: 'parent-access-123',
        hierarchy: CoreConstant.HIERARCHY.ROOT,
      };

      mockUserWithAssociation = {
        id: 'user-123',
        username: 'testuser',
        ua: [
          {
            id: 'association-123',
            entity: {
              id: 'user-entity-123',
              accessId: 'user-access-123',
              hierarchy: CoreConstant.HIERARCHY.USER,
              parent: mockParentEntity,
            },
          },
        ],
      };
    });

    describe('when accessId is not provided', () => {
      it('should return user entity as target entity with hasAccess true', async () => {
        const result = await authValidation.validateAccessAndGetEntityData(
          mockServer,
          mockUserWithAssociation,
          null,
        );

        expect(result).toEqual({
          hasAccess: true,
          userAssociation: mockUserWithAssociation.ua[0],
          targetEntity: mockUserWithAssociation.ua[0].entity,
          parentEntity: mockUserWithAssociation.ua[0].entity.parent,
        });

        expect(EntityRepository.getEntity).not.toHaveBeenCalled();
        expect(UserRepository.findUser).not.toHaveBeenCalled();
      });

      it('should handle user entity without parent', async () => {
        const userWithoutParent = {
          id: 'user-without-parent',
          ua: [
            {
              id: 'association-123',
              entity: {
                id: 'user-entity-123',
                accessId: 'user-access-123',
                hierarchy: CoreConstant.HIERARCHY.USER,
                parent: null,
              },
            },
          ],
        };

        const result = await authValidation.validateAccessAndGetEntityData(
          mockServer,
          userWithoutParent,
          null,
        );

        expect(result).toEqual({
          hasAccess: true,
          userAssociation: userWithoutParent.ua[0],
          targetEntity: userWithoutParent.ua[0].entity,
          parentEntity: null,
        });

        expect(EntityRepository.getEntity).not.toHaveBeenCalled();
        expect(UserRepository.findUser).not.toHaveBeenCalled();
      });

      it('should handle empty string accessId as null', async () => {
        const result = await authValidation.validateAccessAndGetEntityData(
          mockServer,
          mockUserWithAssociation,
          '',
        );

        expect(result).toEqual({
          hasAccess: true,
          userAssociation: mockUserWithAssociation.ua[0],
          targetEntity: mockUserWithAssociation.ua[0].entity,
          parentEntity: mockUserWithAssociation.ua[0].entity.parent,
        });

        expect(EntityRepository.getEntity).not.toHaveBeenCalled();
        expect(UserRepository.findUser).not.toHaveBeenCalled();
      });
    });

    describe('when accessId is provided', () => {
      it('should throw invalidAccessId error when target entity does not exist', async () => {
        EntityRepository.getEntity.mockResolvedValue(null);

        await expect(
          authValidation.validateAccessAndGetEntityData(
            mockServer,
            mockUserWithAssociation,
            'non-existent-access-123',
          ),
        ).rejects.toThrow();

        expect(EntityRepository.getEntity).toHaveBeenCalledWith(mockServer, {
          accessId: 'non-existent-access-123',
        });
        expect(AuthError.invalidAccessId).toHaveBeenCalled();
        expect(UserRepository.findUser).not.toHaveBeenCalled();
      });

      it('should throw invalidAccessId error when user does not have access to the entity', async () => {
        const targetEntity = {
          id: 'target-entity-123',
          accessId: 'target-access-123',
          hierarchy: CoreConstant.HIERARCHY.USER,
        };

        EntityRepository.getEntity.mockResolvedValue(targetEntity);
        UserRepository.findUser.mockResolvedValue(null);

        await expect(
          authValidation.validateAccessAndGetEntityData(
            mockServer,
            mockUserWithAssociation,
            'target-access-123',
          ),
        ).rejects.toThrow();

        expect(EntityRepository.getEntity).toHaveBeenCalledWith(mockServer, {
          accessId: 'target-access-123',
        });
        expect(AuthError.invalidAccessId).toHaveBeenCalled();
      });

      it('should return entity data when user has access to the entity', async () => {
        const targetEntity = {
          id: 'target-entity-123',
          accessId: 'target-access-123',
          hierarchy: CoreConstant.HIERARCHY.USER,
          parent: mockParentEntity,
        };

        EntityRepository.getEntity.mockResolvedValue(targetEntity);
        UserRepository.findUser.mockResolvedValue(mockUserWithAssociation);

        const result = await authValidation.validateAccessAndGetEntityData(
          mockServer,
          mockUserWithAssociation,
          'target-access-123',
        );

        expect(result).toEqual({
          hasAccess: true,
          userAssociation: mockUserWithAssociation.ua[0],
          targetEntity,
          parentEntity: mockParentEntity,
        });

        expect(EntityRepository.getEntity).toHaveBeenCalledWith(mockServer, {
          accessId: 'target-access-123',
        });
        expect(UserRepository.findUser).toHaveBeenCalledWith(
          mockServer,
          {
            filter_username_eq: mockUserWithAssociation.username,
            'filter_ua.entity.id_in': 'target-entity-123',
          },
          true,
        );
      });
    });

    describe('edge cases', () => {
      it('should handle undefined accessId', async () => {
        const result = await authValidation.validateAccessAndGetEntityData(
          mockServer,
          mockUserWithAssociation,
          undefined,
        );

        expect(result.hasAccess).toBe(true);
        expect(result.targetEntity).toEqual(mockUserWithAssociation.ua[0].entity);
        expect(EntityRepository.getEntity).not.toHaveBeenCalled();
        expect(UserRepository.findUser).not.toHaveBeenCalled();
      });

      it('should handle user with multiple associations (uses first one)', async () => {
        const userWithMultipleAssociations = {
          ...mockUserWithAssociation,
          ua: [
            mockUserWithAssociation.ua[0],
            {
              id: 'association-456',
              entity: {
                id: 'user-entity-456',
                accessId: 'user-access-456',
                hierarchy: CoreConstant.HIERARCHY.USER,
              },
            },
          ],
        };

        const result = await authValidation.validateAccessAndGetEntityData(
          mockServer,
          userWithMultipleAssociations,
          null,
        );

        expect(result.userAssociation).toEqual(userWithMultipleAssociations.ua[0]);
      });
    });
  });

  describe('validateEmailDomain', () => {
    const validEmailDomain = 'example.com';
    const unauthorizedEmailDomain = 'blocked.com';

    beforeEach(() => {
      LinkRepository.findOne.mockReset();
    });

    it('should not throw error when email domain is allowed', async () => {
      const mockAllowedDomain = {
        id: 'domain-123',
        type: LinkConstant.TYPE.EMAIL_DOMAIN,
        url: 'example.com',
      };

      LinkRepository.findOne.mockResolvedValue(mockAllowedDomain);

      await expect(
        authValidation.validateEmailDomain(mockServer, validEmailDomain),
      ).resolves.not.toThrow();

      expect(LinkRepository.findOne).toHaveBeenCalledWith(mockServer, {
        filter_type_eq: LinkConstant.TYPE.EMAIL_DOMAIN,
        filter_url_eq: 'example.com',
      });
    });

    it('should throw error when email domain is not allowed', async () => {
      LinkRepository.findOne.mockResolvedValue(null);

      await expect(
        authValidation.validateEmailDomain(mockServer, unauthorizedEmailDomain),
      ).rejects.toThrow();

      expect(LinkRepository.findOne).toHaveBeenCalledWith(mockServer, {
        filter_type_eq: LinkConstant.TYPE.EMAIL_DOMAIN,
        filter_url_eq: 'blocked.com',
      });
      expect(AuthError.unauthorisedDomain).toHaveBeenCalled();
    });

    it('should handle case-insensitive domain validation', async () => {
      const mockAllowedDomain = {
        id: 'domain-123',
        type: LinkConstant.TYPE.EMAIL_DOMAIN,
        url: 'Example.Com',
      };

      LinkRepository.findOne.mockResolvedValue(mockAllowedDomain);

      await expect(
        authValidation.validateEmailDomain(mockServer, 'EXAMPLE.COM'),
      ).resolves.not.toThrow();

      expect(LinkRepository.findOne).toHaveBeenCalledWith(mockServer, {
        filter_type_eq: LinkConstant.TYPE.EMAIL_DOMAIN,
        filter_url_eq: 'EXAMPLE.COM',
      });
    });

    it('should handle empty domain string', async () => {
      LinkRepository.findOne.mockResolvedValue(null);

      await expect(authValidation.validateEmailDomain(mockServer, '')).rejects.toThrow();

      expect(LinkRepository.findOne).toHaveBeenCalledWith(mockServer, {
        filter_type_eq: LinkConstant.TYPE.EMAIL_DOMAIN,
        filter_url_eq: '',
      });
      expect(AuthError.unauthorisedDomain).toHaveBeenCalled();
    });

    it('should handle null domain parameter', async () => {
      LinkRepository.findOne.mockResolvedValue(null);

      await expect(authValidation.validateEmailDomain(mockServer, null)).rejects.toThrow();

      expect(LinkRepository.findOne).toHaveBeenCalledWith(mockServer, {
        filter_type_eq: LinkConstant.TYPE.EMAIL_DOMAIN,
        filter_url_eq: null,
      });
      expect(AuthError.unauthorisedDomain).toHaveBeenCalled();
    });

    it('should handle repository errors gracefully', async () => {
      const repositoryError = new Error('Database connection failed');
      LinkRepository.findOne.mockRejectedValue(repositoryError);

      await expect(
        authValidation.validateEmailDomain(mockServer, validEmailDomain),
      ).rejects.toThrow('Database connection failed');

      expect(LinkRepository.findOne).toHaveBeenCalledWith(mockServer, {
        filter_type_eq: LinkConstant.TYPE.EMAIL_DOMAIN,
        filter_url_eq: 'example.com',
      });
    });
  });

  describe('check2FARequirement', () => {
    let mockUserMFA;

    beforeEach(() => {
      mockUserMFA = {
        id: 'userMFA-123',
        userId: 'user-123',
        status: UserConstant.USER_MFA_STATUSES.ENABLED,
        setupStatus: UserConstant.USER_MFA_SETUP_STATUSES.ACTIVE,
      };
    });

    it('should return null when 2FA is not required by settings', async () => {
      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockImplementation(async (redis, key, callback) => {
        return await callback();
      });

      SettingRepository.getSingleSetting.mockResolvedValue({
        customSettings: { value: 'off' },
      });

      // Use disabled userMFA to ensure only settings are checked
      const disabledUserMFA = {
        ...mockUserMFA,
        status: UserConstant.USER_MFA_STATUSES.DISABLED,
      };

      const result = await authValidation.check2FARequirement(
        mockServer,
        mockUser,
        disabledUserMFA,
      );

      expect(result).toBe(false);
      expect(generateCacheKey).toHaveBeenCalledWith('twoFactorAuthentication', {
        raw: { url: '/settings/safety/twoFactorAuthentication' },
        query: {},
        entity: { id: 'entity-123' },
      });
      expect(SettingRepository.getSingleSetting).toHaveBeenCalledWith(
        mockServer,
        {
          category: 'safety',
          field: 'twoFactorAuthentication',
        },
        { 'filter_customSettings.entityId_eq': mockUser.ua[0].entity.id },
      );
      expect(handle2FASetup).not.toHaveBeenCalled();
    });

    it('should return null when 2FA setting is not found', async () => {
      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockImplementation(async (redis, key, callback) => {
        return await callback();
      });

      SettingRepository.getSingleSetting.mockResolvedValue(null);

      // Use disabled userMFA to ensure only settings are checked
      const disabledUserMFA = {
        ...mockUserMFA,
        status: UserConstant.USER_MFA_STATUSES.DISABLED,
      };

      const result = await authValidation.check2FARequirement(
        mockServer,
        mockUser,
        disabledUserMFA,
      );

      expect(result).toBe(false);
      expect(generateCacheKey).toHaveBeenCalledWith('twoFactorAuthentication', {
        raw: { url: '/settings/safety/twoFactorAuthentication' },
        query: {},
        entity: { id: 'entity-123' },
      });
      expect(handle2FASetup).not.toHaveBeenCalled();
    });

    it('should use cached 2FA settings when available', async () => {
      const { fetchFromCache, generateCacheKey } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockResolvedValue({
        customSettings: { value: 'off' },
      });

      // Use disabled userMFA to ensure only settings are checked
      const disabledUserMFA = {
        ...mockUserMFA,
        status: UserConstant.USER_MFA_STATUSES.DISABLED,
      };

      const result = await authValidation.check2FARequirement(
        mockServer,
        mockUser,
        disabledUserMFA,
      );

      expect(result).toBe(false);
      expect(generateCacheKey).toHaveBeenCalledWith('twoFactorAuthentication', {
        raw: { url: '/settings/safety/twoFactorAuthentication' },
        query: {},
        entity: { id: 'entity-123' },
      });
      // Verify that SettingRepository was not called since we used the cached value
      expect(SettingRepository.getSingleSetting).not.toHaveBeenCalled();
      expect(handle2FASetup).not.toHaveBeenCalled();
    });

    it('should handle missing customSettings value', async () => {
      const { fetchFromCache } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockResolvedValue({
        customSettings: {},
      });

      // Use disabled userMFA to ensure only settings are checked
      const disabledUserMFA = {
        ...mockUserMFA,
        status: UserConstant.USER_MFA_STATUSES.DISABLED,
      };

      const result = await authValidation.check2FARequirement(
        mockServer,
        mockUser,
        disabledUserMFA,
      );

      expect(result).toBe(false);
      expect(handle2FASetup).not.toHaveBeenCalled();
    });

    it('should return null when all conditions are false', async () => {
      const { fetchFromCache } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockResolvedValue({
        customSettings: { value: 'off' },
      });

      const nonRootUser = {
        ...mockUser,
        ua: [
          {
            entity: {
              id: 'user-entity-123',
              hierarchy: CoreConstant.HIERARCHY.USER, // Not ROOT
            },
          },
        ],
      };

      const disabledUserMFA = {
        ...mockUserMFA,
        status: UserConstant.USER_MFA_STATUSES.DISABLED, // Not enabled
      };

      const result = await authValidation.check2FARequirement(
        mockServer,
        nonRootUser,
        disabledUserMFA,
      );

      expect(result).toBe(false);
    });
  });

  describe('validate2FA', () => {
    let mockUserMFA;
    let mockRequest;
    let mockHeaders;

    beforeEach(() => {
      mockUserMFA = {
        id: 'userMFA-123',
        userId: 'user-123',
        status: UserConstant.USER_MFA_STATUSES.ENABLED,
        setupStatus: UserConstant.USER_MFA_SETUP_STATUSES.ACTIVE,
      };

      mockRequest = {
        cookies: {},
      };

      mockHeaders = {
        fingerprint: 'mock-fingerprint-123',
      };

      if (vi.isMockFunction(authValidation.validateTrustedDeviceCookie)) {
        authValidation.validateTrustedDeviceCookie.mockRestore();
      }
    });

    it('should return null when user has no 2FA enabled and 2FA is not required', async () => {
      UserMfaSettingRepository.findByUserId.mockResolvedValue(null);

      const { fetchFromCache } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockResolvedValue({
        customSettings: { value: 'off' },
      });

      const result = await authValidation.validate2FA(
        mockServer,
        mockUser,
        mockRequest,
        mockHeaders,
      );

      expect(result).toBe(null);
      expect(UserMfaSettingRepository.findByUserId).toHaveBeenCalledWith(mockServer, mockUser.id);
    });

    it('should require 2FA when user has 2FA enabled but no trusted device', async () => {
      UserMfaSettingRepository.findByUserId.mockResolvedValue(mockUserMFA);

      const result = await authValidation.validate2FA(
        mockServer,
        mockUser,
        mockRequest,
        mockHeaders,
      );

      expect(result).toEqual({
        requiresTwoFactor: true,
        userId: mockUser.id,
      });
      expect(UserMfaSettingRepository.findByUserId).toHaveBeenCalledWith(mockServer, mockUser.id);
    });

    it('should require 2FA when user has 2FA enabled but invalid trusted device', async () => {
      UserMfaSettingRepository.findByUserId.mockResolvedValue(mockUserMFA);

      vi.spyOn(authValidation, 'validateTrustedDeviceCookie').mockResolvedValue(false);

      const result = await authValidation.validate2FA(
        mockServer,
        mockUser,
        mockRequest,
        mockHeaders,
      );

      expect(result).toEqual({
        requiresTwoFactor: true,
        userId: mockUser.id,
      });
      expect(UserMfaSettingRepository.findByUserId).toHaveBeenCalledWith(mockServer, mockUser.id);
    });

    it('should handle 2FA setup when user has disabled 2FA but system requires it', async () => {
      const disabledUserMFA = {
        status: UserConstant.USER_MFA_STATUSES.DISABLED,
        setupStatus: UserConstant.USER_MFA_SETUP_STATUSES.INACTIVE,
      };
      UserMfaSettingRepository.findByUserId.mockResolvedValue(disabledUserMFA);

      const { fetchFromCache } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockResolvedValue({
        customSettings: { value: 'on' },
      });

      vi.spyOn(authValidation, 'check2FARequirement').mockResolvedValue(true);

      const mockSetupResult = 'mock-qr-code';
      handle2FASetup.mockResolvedValue(mockSetupResult);

      const result = await authValidation.validate2FA(
        mockServer,
        mockUser,
        mockRequest,
        mockHeaders,
      );

      expect(result).toEqual({
        requiresTwoFactor: true,
        userId: mockUser.id,
        qrCode: mockSetupResult,
      });
      expect(handle2FASetup).toHaveBeenCalledWith(mockUser, disabledUserMFA);
    });

    it('should call check2FARequirement when user setup status is not enabled', async () => {
      const pendingUserMFA = {
        ...mockUserMFA,
        status: UserConstant.USER_MFA_SETUP_STATUSES.INACTIVE,
        setupStatus: UserConstant.USER_MFA_SETUP_STATUSES.INACTIVE,
      };
      UserMfaSettingRepository.findByUserId.mockResolvedValue(pendingUserMFA);

      const { fetchFromCache } = await import('#src/utils/cache.util.js');
      fetchFromCache.mockResolvedValue({
        customSettings: { value: 'off' },
      });

      const result = await authValidation.validate2FA(
        mockServer,
        mockUser,
        mockRequest,
        mockHeaders,
      );

      expect(result).toBe(null);
      expect(UserMfaSettingRepository.findByUserId).toHaveBeenCalledWith(mockServer, mockUser.id);
    });

    it('should return null when handle2FASetup returns null (twoFactorResponse ? {...} : null)', async () => {
      const disabledUserMFA = {
        ...mockUserMFA,
        setupStatus: UserConstant.USER_MFA_SETUP_STATUSES.INACTIVE,
      };
      UserMfaSettingRepository.findByUserId.mockResolvedValue(disabledUserMFA);

      vi.spyOn(authValidation, 'check2FARequirement').mockResolvedValue(true);

      handle2FASetup.mockResolvedValue(null);

      const result = await authValidation.validate2FA(
        mockServer,
        mockUser,
        mockRequest,
        mockHeaders,
      );

      expect(result).toBe(null);
      expect(handle2FASetup).toHaveBeenCalledWith(mockUser, disabledUserMFA);
    });

    it('should return merged response when handle2FASetup returns data (twoFactorResponse ? {...} : null)', async () => {
      const disabledUserMFA = {
        ...mockUserMFA,
        setupStatus: UserConstant.USER_MFA_SETUP_STATUSES.INACTIVE,
      };
      UserMfaSettingRepository.findByUserId.mockResolvedValue(disabledUserMFA);

      vi.spyOn(authValidation, 'check2FARequirement').mockResolvedValue(true);

      const mockSetupResult = 'mock-qr-code';
      handle2FASetup.mockResolvedValue(mockSetupResult);

      const result = await authValidation.validate2FA(
        mockServer,
        mockUser,
        mockRequest,
        mockHeaders,
      );

      expect(result).toEqual({
        requiresTwoFactor: true,
        userId: mockUser.id,
        qrCode: mockSetupResult,
      });
      expect(handle2FASetup).toHaveBeenCalledWith(mockUser, disabledUserMFA);
    });
  });

  describe('validateTrustedDeviceCookie', () => {
    const mockCookieValue = 'mock-jwt-token';
    const mockUserId = 'user-123';
    const mockFingerprint = 'mock-fingerprint-123';

    beforeEach(() => {
      mockServer.log = { debug: vi.fn() };
      mockServer.redis = {
        db3: {
          get: vi.fn(),
        },
      };
    });

    it('should return false when cookieValue is missing', async () => {
      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        null,
        mockUserId,
        mockFingerprint,
      );

      expect(result).toBe(false);
    });

    it('should return false when fingerprint is missing', async () => {
      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        mockCookieValue,
        mockUserId,
        null,
      );

      expect(result).toBe(false);
    });

    it('should return false when userId is missing', async () => {
      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        mockCookieValue,
        null,
        mockFingerprint,
      );

      expect(result).toBe(false);
    });

    it('should return false when JWT decode fails', async () => {
      decodeJWT.mockRejectedValue(new Error('JWT decode failed'));

      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        mockCookieValue,
        mockUserId,
        mockFingerprint,
      );

      expect(result).toBe(false);
      expect(mockServer.log.debug).toHaveBeenCalled();
    });

    it('should return false when payload is missing required fields', async () => {
      decodeJWT.mockResolvedValue({
        userId: mockUserId,
      });

      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        mockCookieValue,
        mockUserId,
        mockFingerprint,
      );

      expect(result).toBe(false);
    });

    it('should return false when userId does not match', async () => {
      decodeJWT.mockResolvedValue({
        userId: 'different-user-123',
        fingerprint: mockFingerprint,
        expiresAt: Math.floor(Date.now() / 1000) + 3600,
        createdAt: Math.floor(Date.now() / 1000) - 3600,
      });

      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        mockCookieValue,
        mockUserId,
        mockFingerprint,
      );

      expect(result).toBe(false);
    });

    it('should return false when fingerprint does not match', async () => {
      decodeJWT.mockResolvedValue({
        userId: mockUserId,
        fingerprint: 'different-fingerprint',
        expiresAt: Math.floor(Date.now() / 1000) + 3600,
        createdAt: Math.floor(Date.now() / 1000) - 3600,
      });

      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        mockCookieValue,
        mockUserId,
        mockFingerprint,
      );

      expect(result).toBe(false);
    });

    it('should return false when cookie has expired', async () => {
      decodeJWT.mockResolvedValue({
        userId: mockUserId,
        fingerprint: mockFingerprint,
        expiresAt: Math.floor(Date.now() / 1000) - 3600, // Expired 1 hour ago
        createdAt: Math.floor(Date.now() / 1000) - 7200,
      });

      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        mockCookieValue,
        mockUserId,
        mockFingerprint,
      );

      expect(result).toBe(false);
    });

    it('should return false when token is not found in cache (revoked)', async () => {
      decodeJWT.mockResolvedValue({
        userId: mockUserId,
        fingerprint: mockFingerprint,
        expiresAt: Math.floor(Date.now() / 1000) + 3600,
        createdAt: Math.floor(Date.now() / 1000) - 3600,
      });

      getCache.mockResolvedValue(null);

      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        mockCookieValue,
        mockUserId,
        mockFingerprint,
      );

      expect(result).toBe(false);
      expect(getCache).toHaveBeenCalledWith(
        mockServer.redis.db3,
        `trusted_device:${mockUserId}:${mockFingerprint}`,
      );
    });

    it('should return false when cached token does not match cookie value', async () => {
      decodeJWT.mockResolvedValue({
        userId: mockUserId,
        fingerprint: mockFingerprint,
        expiresAt: Math.floor(Date.now() / 1000) + 3600,
        createdAt: Math.floor(Date.now() / 1000) - 3600,
      });

      getCache.mockResolvedValue('different-token-value');

      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        mockCookieValue,
        mockUserId,
        mockFingerprint,
      );

      expect(result).toBe(false);
      expect(getCache).toHaveBeenCalledWith(
        mockServer.redis.db3,
        `trusted_device:${mockUserId}:${mockFingerprint}`,
      );
    });

    it('should return true when cookie is valid and matches cached token', async () => {
      decodeJWT.mockResolvedValue({
        userId: mockUserId,
        fingerprint: mockFingerprint,
        expiresAt: Math.floor(Date.now() / 1000) + 3600,
        createdAt: Math.floor(Date.now() / 1000) - 3600,
      });

      getCache.mockResolvedValue(mockCookieValue);

      const result = await authValidation.validateTrustedDeviceCookie(
        mockServer,
        mockCookieValue,
        mockUserId,
        mockFingerprint,
      );

      expect(result).toBe(true);
      expect(decodeJWT).toHaveBeenCalledWith(mockCookieValue, mockServer);
      expect(getCache).toHaveBeenCalledWith(
        mockServer.redis.db3,
        `trusted_device:${mockUserId}:${mockFingerprint}`,
      );
    });
  });

  describe('validatePreAuthToken', () => {
    const mockTempToken = 'mock-temp-token';
    const mockIp = '127.0.0.1';
    let decodeJWT;

    beforeEach(async () => {
      // Import and mock decodeJWT
      const jwtModule = await import('#src/utils/jwt.util.js');
      decodeJWT = jwtModule.decodeJWT;
    });

    it('should return userId and accessId when token is valid', async () => {
      const mockTokenInfo = {
        type: AuthConstant.KEYS.PRE_AUTH_TOKEN_NAME,
        ip: mockIp,
        sub: 'user-123',
        accessId: 'access-123',
      };

      vi.mocked(decodeJWT).mockResolvedValue(mockTokenInfo);

      const result = await authValidation.validatePreAuthToken(mockTempToken, mockIp, mockServer);

      expect(result).toEqual({
        userId: 'user-123',
        accessId: 'access-123',
      });
      expect(decodeJWT).toHaveBeenCalledWith(mockTempToken, mockServer);
    });

    it('should throw error when token type is invalid', async () => {
      const mockTokenInfo = {
        type: 'INVALID_TOKEN_TYPE',
        ip: mockIp,
        sub: 'user-123',
        accessId: 'access-123',
      };

      vi.mocked(decodeJWT).mockResolvedValue(mockTokenInfo);

      await expect(
        authValidation.validatePreAuthToken(mockTempToken, mockIp, mockServer),
      ).rejects.toThrow();

      expect(decodeJWT).toHaveBeenCalledWith(mockTempToken, mockServer);
      expect(AuthError.invalidPreAuthToken).toHaveBeenCalled();
    });

    it('should throw error when IP address does not match', async () => {
      const mockTokenInfo = {
        type: AuthConstant.PRE_AUTH_TOKEN_NAME,
        ip: '*********', // Different IP
        sub: 'user-123',
        accessId: 'access-123',
      };

      vi.mocked(decodeJWT).mockResolvedValue(mockTokenInfo);

      await expect(
        authValidation.validatePreAuthToken(mockTempToken, mockIp, mockServer),
      ).rejects.toThrow();

      expect(decodeJWT).toHaveBeenCalledWith(mockTempToken, mockServer);
      expect(AuthError.invalidPreAuthToken).toHaveBeenCalled();
    });

    it('should throw error when token type is missing', async () => {
      const mockTokenInfo = {
        type: null,
        ip: mockIp,
        sub: 'user-123',
        accessId: 'access-123',
      };

      vi.mocked(decodeJWT).mockResolvedValue(mockTokenInfo);

      await expect(
        authValidation.validatePreAuthToken(mockTempToken, mockIp, mockServer),
      ).rejects.toThrow();

      expect(AuthError.invalidPreAuthToken).toHaveBeenCalled();
    });

    it('should throw error when IP is missing from token', async () => {
      const mockTokenInfo = {
        type: AuthConstant.PRE_AUTH_TOKEN_NAME,
        ip: null,
        sub: 'user-123',
        accessId: 'access-123',
      };

      vi.mocked(decodeJWT).mockResolvedValue(mockTokenInfo);

      await expect(
        authValidation.validatePreAuthToken(mockTempToken, mockIp, mockServer),
      ).rejects.toThrow();

      expect(AuthError.invalidPreAuthToken).toHaveBeenCalled();
    });

    it('should throw error when decodeJWT returns null', async () => {
      vi.mocked(decodeJWT).mockResolvedValue(null);

      await expect(
        authValidation.validatePreAuthToken(mockTempToken, mockIp, mockServer),
      ).rejects.toThrow();

      expect(AuthError.invalidPreAuthToken).toHaveBeenCalled();
    });

    it('should throw error when decodeJWT returns undefined', async () => {
      vi.mocked(decodeJWT).mockResolvedValue(undefined);

      await expect(
        authValidation.validatePreAuthToken(mockTempToken, mockIp, mockServer),
      ).rejects.toThrow();

      expect(AuthError.invalidPreAuthToken).toHaveBeenCalled();
    });

    it('should handle token with missing userId', async () => {
      const mockTokenInfo = {
        type: AuthConstant.KEYS.PRE_AUTH_TOKEN_NAME,
        ip: mockIp,
        sub: undefined,
        accessId: 'access-123',
      };

      vi.mocked(decodeJWT).mockResolvedValue(mockTokenInfo);

      const result = await authValidation.validatePreAuthToken(mockTempToken, mockIp, mockServer);

      expect(result).toEqual({
        userId: undefined,
        accessId: 'access-123',
      });
    });

    it('should handle token with missing accessId', async () => {
      const mockTokenInfo = {
        type: AuthConstant.KEYS.PRE_AUTH_TOKEN_NAME,
        ip: mockIp,
        sub: 'user-123',
        accessId: undefined,
      };

      vi.mocked(decodeJWT).mockResolvedValue(mockTokenInfo);

      const result = await authValidation.validatePreAuthToken(mockTempToken, mockIp, mockServer);

      expect(result).toEqual({
        userId: 'user-123',
        accessId: undefined,
      });
    });
  });
});
