import { describe, expect, it } from 'vitest';

import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import * as UserInvitationSchema from '#src/modules/user/schemas/user-invitation.schema.js';

const { USER_INVITATION_STATUSES } = UserConstant;
const { REQ_PARAM_UUID } = CoreSchema;

describe('UserInvitationSchema', () => {
  describe('index schema', () => {
    it('should define querystring with filters and sorting', () => {
      const { querystring } = UserInvitationSchema.index;

      expect(querystring).toHaveProperty('type', 'object');
      expect(querystring.properties).toHaveProperty('filter_status_eq');
      expect(querystring.properties.filter_status_eq.enum).toEqual(
        Object.values(USER_INVITATION_STATUSES),
      );
      expect(querystring.properties).toHaveProperty('filter_id_eq');
      expect(querystring.properties.filter_id_eq.format).toBe('uuid');
      expect(querystring.properties).toHaveProperty('sortBy');
    });

    it('should define response schema with 200 and meta pagination', () => {
      expect(UserInvitationSchema.index.response).toHaveProperty('200');
      expect(UserInvitationSchema.index.response[200].properties).toHaveProperty('data');
      expect(UserInvitationSchema.index.response[200].properties).toHaveProperty('meta');
    });
  });

  describe('create schema', () => {
    it('should require username, roleId, and currencyIds', () => {
      const { body } = UserInvitationSchema.create;

      expect(body).toHaveProperty('required');
      expect(body.required).toEqual(expect.arrayContaining(['username', 'roleId', 'currencyIds']));
      expect(body.properties.username.pattern).toBe('^[A-Za-z0-9]+$');
      expect(body.properties.currencyIds.items.format).toBe('uuid');
    });

    it('should define proper response schema', () => {
      expect(UserInvitationSchema.create.response).toHaveProperty('201');
      expect(UserInvitationSchema.create.response[201].properties).toHaveProperty('message');
      expect(UserInvitationSchema.create.response[201].properties).toHaveProperty('data');
    });
  });

  describe('updateStatus schema', () => {
    it('should require valid status in body', () => {
      const { body } = UserInvitationSchema.updateStatus;

      expect(body.properties.status.enum).toEqual(Object.values(USER_INVITATION_STATUSES));
    });

    it('should include UUID param schema', () => {
      expect(UserInvitationSchema.updateStatus.params).toEqual(REQ_PARAM_UUID);
    });

    it('should define proper update response', () => {
      expect(UserInvitationSchema.updateStatus.response).toHaveProperty('200');
    });
  });
});
