import { describe, expect, it } from 'vitest';

import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import { login } from '#src/modules/user/schemas/auth.schema.js';

describe('User Schema', () => {
  describe('login schema', () => {
    it('should have the correct tags', () => {
      expect(login.tags).toEqual(['BO / Authentication']);
    });

    it('should have the correct summary and description', () => {
      expect(login.summary).toBe('User authentication');
      expect(login.description).toBe('Authenticate a user and return a JWT token');
    });

    describe('request body', () => {
      it('should require username, password, and h-captcha-response', () => {
        expect(login.body.required).toEqual(['username', 'password', 'h-captcha-response']);
      });

      it('should define the correct properties', () => {
        const { properties } = login.body;

        expect(properties.username).toEqual({ type: 'string' });
        expect(properties.password).toEqual({ type: 'string' });
        expect(properties.accessId).toEqual({ type: 'string' });
        expect(properties['h-captcha-response']).toEqual({ type: 'string' });
      });
    });

    describe('response schema', () => {
      it('should include the standard error responses', () => {
        expect(login.response).toEqual(expect.objectContaining(CoreSchema.ERROR_RESPONSE));
      });

      it('should define a 200 success response with the correct structure', () => {
        const successResponse = login.response[200];

        expect(successResponse.description).toBe('Success response');
        expect(successResponse.type).toBe('object');

        const { properties } = successResponse;

        expect(properties.message).toEqual({
          type: 'string',
          description: 'Success message',
        });

        expect(properties.data.type).toBe('object');
      });

      it('should define the token property in the response data', () => {
        const { properties } = login.response[200].properties.data;

        expect(properties.token).toEqual({
          type: 'string',
          description: 'JWT authentication token',
        });
      });

      it('should define the user object with the correct properties', () => {
        const userProperties = login.response[200].properties.data.properties.user.properties;

        expect(userProperties.id).toEqual({ type: 'string', format: 'uuid' });
        expect(userProperties.email).toEqual({ type: 'string', format: 'email' });
        expect(userProperties.username).toEqual({ type: 'string' });
        expect(userProperties.status).toEqual({
          type: 'string',
          enum: Object.values(UserConstant.USER_STATUSES),
        });
        expect(userProperties.entityId).toEqual({ type: 'string', format: 'uuid' });
      });

      it('should define the accessId property in the response data', () => {
        const { properties } = login.response[200].properties.data;

        expect(properties.accessId).toEqual({ type: 'string' });
      });
    });
  });
});
