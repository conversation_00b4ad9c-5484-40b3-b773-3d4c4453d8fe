import { describe, expect, it } from 'vitest';

import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import * as userSSOAccountSchema from '#src/modules/user/schemas/user-sso-account.schema.js';

const { USER_SSO_STATUSES } = UserConstant;
const { USER } = MODULE_NAMES;
const { REQ_PARAM_UUID, UPDATE_RESPONSE } = CoreSchema;

describe('UserSSO Account Schema', () => {
  describe('index schema', () => {
    const { index } = userSSOAccountSchema;

    it('should have correct base structure', () => {
      expect(index.tags).toContain('BO / User Management / Account / SSO Onboarding');
      expect(index.summary).toBe(`Get a list of ${USER} SSO Onboarding`);
      expect(index.querystring).toBeDefined();
      expect(index.response[200]).toBeDefined();
    });

    it('should include valid status filter enum', () => {
      expect(index.querystring.properties.filter_status_eq.enum).toEqual(
        Object.values(USER_SSO_STATUSES),
      );
    });

    it('should have response data as an array of user objects', () => {
      expect(index.response[200].properties.data.type).toBe('array');
      expect(index.response[200].properties.data.items.type).toBe('object');
    });
  });

  describe('updateStatus schema', () => {
    const { updateStatus } = userSSOAccountSchema;

    it('should have correct summary and param schema', () => {
      expect(updateStatus.summary).toBe(`Update ${USER} user SSO`);
      expect(updateStatus.params).toEqual(REQ_PARAM_UUID);
    });

    it('should include valid status enum in body', () => {
      expect(updateStatus.body.properties.status.enum).toEqual(Object.values(USER_SSO_STATUSES));
    });

    it('should return standard update response', () => {
      expect(updateStatus.response).toBe(UPDATE_RESPONSE);
    });
  });

  describe('assign schema', () => {
    const { assign } = userSSOAccountSchema;

    it('should define assign structure correctly', () => {
      expect(assign.summary).toBe(`Assign ${USER} SSO`);
      expect(assign.params).toEqual(REQ_PARAM_UUID);
      expect(assign.body.properties.username.type).toBe('string');
      expect(assign.response).toBe(UPDATE_RESPONSE);
    });
  });

  describe('onboard schema', () => {
    const { onboard } = userSSOAccountSchema;

    it('should define onboard structure correctly', () => {
      expect(onboard.summary).toBe(`Onboard ${USER} SSO`);
      expect(onboard.params).toEqual(REQ_PARAM_UUID);
      expect(onboard.body.properties.username.type).toBe('string');
      expect(onboard.response).toBe(UPDATE_RESPONSE);
    });
  });
});
