import { describe, expect, it } from 'vitest';

import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import * as userSchema from '#src/modules/user/schemas/user.schema.js';

const { USER } = MODULE_NAMES;
describe('User Schema', () => {
  describe('index schema', () => {
    it('should have correct tags and summary', () => {
      expect(userSchema.index.tags).toEqual(['BO / User Management / Account / User ']);
      expect(userSchema.index.summary).toBe(`Get a list of ${USER}`);
    });

    it('should have correct querystring properties', () => {
      const { properties } = userSchema.index.querystring;
      expect(properties).toHaveProperty('filter_status_in');
      expect(properties).toHaveProperty('filter_id_eq');
      expect(properties).toHaveProperty('filter_username_iLike');
      expect(properties).toHaveProperty('filter_name_iLike');
      expect(properties).toHaveProperty('filter_ua.uac.currencyId_in');
      expect(properties).toHaveProperty('sortBy');
    });

    it('should have correct response schema', () => {
      const { properties } = userSchema.index.response[200];
      expect(properties).toHaveProperty('message');
      expect(properties).toHaveProperty('data');
      expect(properties).toHaveProperty('meta');
    });
  });

  describe('view schema', () => {
    it('should have correct tags and summary', () => {
      expect(userSchema.view.tags).toEqual(['BO / User Management / Account / User ']);
      expect(userSchema.view.summary).toBe(`View a ${USER}`);
    });

    it('should have params', () => {
      expect(userSchema.view).toHaveProperty('params');
    });

    it('should have response schema', () => {
      expect(userSchema.view).toHaveProperty('response');
    });
  });

  describe('create schema', () => {
    it('should have correct tags and summary', () => {
      expect(userSchema.create.tags).toEqual(['BO / User Management / Account / User ']);
      expect(userSchema.create.summary).toBe(`Create a ${USER}`);
    });

    it('should have required properties', () => {
      const { required } = userSchema.create.body;
      expect(required).toContain('username');
      expect(required).toContain('password');
      expect(required).toContain('type');
    });

    it('should have response schema', () => {
      expect(userSchema.create).toHaveProperty('response');
    });
  });
  describe('options schema', () => {
    it('should have correct tags and summary', () => {
      expect(userSchema.options.tags).toEqual(['BO / User Management / Account / User ']);
      expect(userSchema.options.summary).toBe('Get available user options');
    });
    it('should have response schema', () => {
      expect(userSchema.options).toHaveProperty('response');
      expect(userSchema.options.response).toHaveProperty('200');
    });
  });

  describe('updateBasicInformation schema', () => {
    it('should have correct tags and summary', () => {
      expect(userSchema.create.tags).toEqual(['BO / User Management / Account / User ']);
      expect(userSchema.updateBasicInformation.tags).toEqual([
        'BO / User Management / Account / User ',
      ]);
      expect(userSchema.updateBasicInformation.summary).toBe('Update users basic information');
    });

    it('should validate body properties', () => {
      expect(userSchema.updateBasicInformation.body.properties).toHaveProperty('name');
      expect(userSchema.updateBasicInformation.body.properties.name.type).toBe('string');
      expect(userSchema.updateBasicInformation.body.properties.name.minLength).toBe(3);
      expect(userSchema.updateBasicInformation.body.properties.name.maxLength).toBe(100);
      expect(userSchema.updateBasicInformation.body.properties.name.pattern).toBeDefined();
      expect(userSchema.updateBasicInformation.body.properties).toHaveProperty('email');
      expect(userSchema.updateBasicInformation.body.properties.email.type).toBe('string');
      expect(userSchema.updateBasicInformation.body.properties.email.format).toBe('email');
    });

    it('should have a response schema', () => {
      expect(userSchema.updateBasicInformation).toHaveProperty('response');
      expect(userSchema.updateBasicInformation.response).toHaveProperty('200');
    });
  });

  describe('updateOrganisation schema', () => {
    it('should have correct tags and summary', () => {
      expect(userSchema.updateOrganisation.tags).toEqual([
        'BO / User Management / Account / User ',
      ]);
      expect(userSchema.updateOrganisation.summary).toBe('Update users organisation');
    });
    it('should validate body properties', () => {
      expect(userSchema.updateOrganisation.body.properties).toHaveProperty('roleId');
      expect(userSchema.updateOrganisation.body.properties.roleId.type).toBe('string');
      expect(userSchema.updateOrganisation.body.properties.roleId.format).toBe('uuid');

      expect(userSchema.updateOrganisation.body.properties).toHaveProperty('currencyIds');
      expect(userSchema.updateOrganisation.body.properties.currencyIds.type).toBe('array');
      expect(userSchema.updateOrganisation.body.properties.currencyIds.items.type).toBe('string');
      expect(userSchema.updateOrganisation.body.properties.currencyIds.items.format).toBe('uuid');
      expect(userSchema.updateOrganisation.body.properties.currencyIds.uniqueItems).toBe(true);
    });

    it('should have a response schema', () => {
      expect(userSchema.updateOrganisation).toHaveProperty('response');
      expect(userSchema.updateOrganisation.response).toHaveProperty('200');
    });
  });

  describe('updateLoginAccess schema', () => {
    it('should have correct tags and summary', () => {
      expect(userSchema.updateLoginAccess.tags).toEqual(['BO / User Management / Account / User ']);
      expect(userSchema.updateLoginAccess.summary).toBe('Update users login access');
    });
    it('should validate body properties', () => {
      expect(userSchema.updateLoginAccess.body.properties).toHaveProperty('twoFactorAuth');
      expect(Array.isArray(userSchema.updateLoginAccess.body.properties.twoFactorAuth.enum)).toBe(
        true,
      );

      expect(userSchema.updateLoginAccess.body.properties).toHaveProperty('validityDate');
      expect(userSchema.updateLoginAccess.body.properties.validityDate.type).toBe('string');
      expect(userSchema.updateLoginAccess.body.properties.validityDate.format).toBe('date-time');
    });

    it('should have a response schema', () => {
      expect(userSchema.updateLoginAccess).toHaveProperty('response');
      expect(userSchema.updateLoginAccess.response).toHaveProperty('200');
    });
  });

  describe('updateStatus schema', () => {
    it('should have correct tags and summary', () => {
      expect(userSchema.updateStatus.tags).toEqual(['BO / User Management / Account / User ']);
      expect(userSchema.updateStatus.summary).toBe('Update users status');
    });

    it('should validate body properties', () => {
      expect(userSchema.updateStatus.body.properties).toHaveProperty('status');
      expect(Array.isArray(userSchema.updateStatus.body.properties.status.enum)).toBe(true);
    });

    it('should have a response schema', () => {
      expect(userSchema.updateStatus).toHaveProperty('response');
      expect(userSchema.updateStatus.response).toHaveProperty('200');
    });
  });
  it('should have response schema', () => {
    expect(userSchema.options).toHaveProperty('response');
    expect(userSchema.options.response).toHaveProperty('200');
  });
});
