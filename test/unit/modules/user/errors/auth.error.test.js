import { beforeEach, describe, expect, it, vi } from 'vitest';

import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { authError } from '#src/modules/user/errors/auth.error.js';

vi.mock('#src/utils/error.util.js', () => ({
  createModuleErrors: vi.fn((moduleName, errorDef) => {
    const errors = {};
    Object.entries(errorDef).forEach(([key, [code, message, status]]) => {
      errors[key] = {
        code: `${moduleName}-${code}`,
        message,
        status,
      };
    });
    return errors;
  }),
}));

describe('User Error Definitions', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    vi.resetModules();
  });
  it('should define invalidCredentials error with correct properties', () => {
    expect(authError.invalidCredentials).toEqual({
      code: `${MODULE_NAMES.USER}-20001`,
      message: 'error.auth.sentence.invalidCredentials',
      status: 401,
    });
  });

  it('should define accountInactive error with correct properties', () => {
    expect(authError.accountInactive).toEqual({
      code: `${MODULE_NAMES.USER}-20002`,
      message: 'error.auth.sentence.accountInactive',
      status: 403,
    });
  });

  it('should define accountSuspended error with correct properties', () => {
    expect(authError.accountSuspended).toEqual({
      code: `${MODULE_NAMES.USER}-20003`,
      message: 'error.auth.sentence.accountSuspended',
      status: 423,
    });
  });

  it('should define accountPending error with correct properties', () => {
    expect(authError.accountPending).toEqual({
      code: `${MODULE_NAMES.USER}-20004`,
      message: 'error.auth.sentence.accountPending',
      status: 403,
    });
  });

  it('should define forbidden error with correct properties', () => {
    expect(authError.forbidden).toEqual({
      code: `${MODULE_NAMES.USER}-20005`,
      message: 'error.auth.sentence.forbidden',
      status: 403,
    });
  });

  it('should define tooManyRequests error with correct properties', () => {
    expect(authError.tooManyRequests).toEqual({
      code: `${MODULE_NAMES.USER}-20006`,
      message: 'error.auth.sentence.tooManyRequests',
      status: 429,
    });
  });

  it('should define maintenanceMode error with correct properties', () => {
    expect(authError.maintenanceMode).toEqual({
      code: `${MODULE_NAMES.USER}-20007`,
      message: 'error.auth.sentence.maintenanceMode',
      status: 503,
    });
  });

  it('should have all expected error definitions', () => {
    const expectedErrorKeys = [
      'accountInactive',
      'accountPending',
      'accountSuspended',
      'forbidden',
      'googleAuthError',
      'invalid2FAToken',
      'invalidAccessId',
      'invalidCredentials',
      'invalidPreAuthToken',
      'maintenanceMode',
      'ssoAccountPending',
      'tooManyRequests',
      'twoFactorAlreadyEnabled',
      'twoFactorAlreadySetup',
      'twoFactorNotSetup',
      'unauthorisedDomain',
    ];

    expectedErrorKeys.forEach((key) => {
      expect(authError).toHaveProperty(key);
    });

    expect(Object.keys(authError).length).toBe(expectedErrorKeys.length);
  });
});
