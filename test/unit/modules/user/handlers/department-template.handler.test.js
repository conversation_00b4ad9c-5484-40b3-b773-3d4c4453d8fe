import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import * as handler from '#src/modules/user/handlers/department-template.handler.js';
import { DepartmentService } from '#src/modules/user/services/index.js';
import { initializeAuditMeta } from '#src/utils/audit-trail.util.js';
import * as cacheUtil from '#src/utils/cache.util.js';
import * as responseUtil from '#src/utils/response.util.js';

vi.mock('#src/utils/cache.util.js');
vi.mock('#src/modules/user/services/index.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/utils/audit-trail.util.js', () => ({
  initializeAuditMeta: vi.fn(),
}));

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { DEPARTMENT_TEMPLATE },
  MODULE_METHODS: {
    CREATE,
    DELETE,
    INDEX,
    OPTION,
    UPDATE_POLICY,
    UPDATE_STATUS,
    UPDATE_BASIC_INFORMATION,
    VIEW,
  },
} = CoreConstant;

describe('Department Template Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      server: {
        redis: {},
        withAuditLogging: vi.fn().mockResolvedValue(undefined),
      },
      params: { id: 'department1' },
    };
    mockReply = {};

    // Reset mocks
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should call handleServiceResponse with cached service function', async () => {
      const mockCacheKey = 'mock_cache_key';
      cacheUtil.generateCacheKey.mockReturnValue(mockCacheKey);

      await handler.index(mockRequest, mockReply);

      expect(cacheUtil.generateCacheKey).toHaveBeenCalledWith(
        `${DEPARTMENT_TEMPLATE}_${INDEX}`,
        mockRequest,
      );

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: expect.any(Function),
          module: DEPARTMENT_TEMPLATE,
          method: INDEX,
        }),
      );

      // Extract the serviceFn and call it
      const serviceFn = responseUtil.handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      // Verify that fetchFromCache was called with the correct arguments
      expect(cacheUtil.fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        SHORT,
      );

      // Extract the callback function passed to fetchFromCache and call it
      const fetchFromCacheCallback = cacheUtil.fetchFromCache.mock.calls[0][2];
      await fetchFromCacheCallback();

      // Verify that DepartmentService.index was called with the correct arguments
      expect(DepartmentService.index).toHaveBeenCalledWith(mockRequest, true);
    });
  });

  describe('view', () => {
    it('should call handleServiceResponse with cached service function', async () => {
      const mockCacheKey = 'mock_cache_key';
      cacheUtil.generateCacheKey.mockReturnValue(mockCacheKey);

      const mockResult = 'mockResult';
      DepartmentService.view.mockResolvedValue(mockResult);
      cacheUtil.fetchFromCache.mockResolvedValue(mockResult);

      await handler.view(mockRequest, mockReply);
      expect(initializeAuditMeta).toHaveBeenCalledOnce();

      expect(cacheUtil.generateCacheKey).toHaveBeenCalledWith(
        `${DEPARTMENT_TEMPLATE}_${VIEW}`,
        mockRequest,
      );

      // Extract the serviceFn and call it
      const serviceFn = responseUtil.handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();

      expect(result).toBe(mockResult);

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: expect.any(Function),
          module: DEPARTMENT_TEMPLATE,
          method: VIEW,
        }),
      );

      // Now verify that fetchFromCache was called
      expect(cacheUtil.fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        SHORT,
      );
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with create service function', async () => {
      const mockResult = { result: 'created', auditModelMapping: 'auditMapping' };
      DepartmentService.create.mockResolvedValue(mockResult);

      await handler.create(mockRequest, mockReply);
      expect(initializeAuditMeta).toHaveBeenCalledOnce();

      // Extract the serviceFn and call it
      const serviceFn = responseUtil.handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('created');

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn,
          module: DEPARTMENT_TEMPLATE,
          method: CREATE,
          audit: {
            modelMapping: 'auditMapping',
          },
        }),
      );

      // Verify that DepartmentService.create was called with the correct arguments
      expect(DepartmentService.create).toHaveBeenCalledWith(mockRequest, true);
    });
  });

  describe('updateBasicInformation', () => {
    it('should call handleServiceResponse with updateBasicInformation service function', async () => {
      const mockResult = { result: 'updated', auditModelMapping: 'auditMapping' };
      DepartmentService.updateBasicInformation.mockResolvedValue(mockResult);

      await handler.updateBasicInformation(mockRequest, mockReply);
      expect(initializeAuditMeta).toHaveBeenCalledOnce();

      // Extract the serviceFn and call it
      const serviceFn = responseUtil.handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('updated');

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn,
          module: DEPARTMENT_TEMPLATE,
          method: UPDATE_BASIC_INFORMATION,
          audit: {
            modelMapping: 'auditMapping',
          },
        }),
      );
    });
  });

  describe('updatePolicy', () => {
    it('should call handleServiceResponse with updatePolicy service function', async () => {
      const mockResult = { result: 'updated', auditModelMapping: 'auditMapping' };
      DepartmentService.updatePolicy.mockResolvedValue(mockResult);

      await handler.updatePolicy(mockRequest, mockReply);
      expect(initializeAuditMeta).toHaveBeenCalledOnce();

      // Extract the serviceFn and call it
      const serviceFn = responseUtil.handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('updated');

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn,
          module: DEPARTMENT_TEMPLATE,
          method: UPDATE_POLICY,
          audit: {
            modelMapping: 'auditMapping',
          },
        }),
      );
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with updateStatus service function', async () => {
      const mockResult = { result: 'updated', auditModelMapping: 'auditMapping' };
      DepartmentService.updateStatus.mockResolvedValue(mockResult);

      await handler.updateStatus(mockRequest, mockReply);
      expect(initializeAuditMeta).toHaveBeenCalledOnce();

      // Extract the serviceFn and call it
      const serviceFn = responseUtil.handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('updated');

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn,
          module: DEPARTMENT_TEMPLATE,
          method: UPDATE_STATUS,
          audit: {
            modelMapping: 'auditMapping',
          },
        }),
      );
    });
  });

  describe('remove', () => {
    it('should call handleServiceResponse with remove service function', async () => {
      const mockResult = { result: 'removed', auditModelMapping: 'auditMapping' };
      DepartmentService.remove.mockResolvedValue(mockResult);

      await handler.remove(mockRequest, mockReply);
      expect(initializeAuditMeta).toHaveBeenCalledOnce();

      // Extract the serviceFn and call it
      const serviceFn = responseUtil.handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('removed');

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn,
          module: DEPARTMENT_TEMPLATE,
          method: DELETE,
          audit: {
            modelMapping: 'auditMapping',
          },
        }),
      );
    });
  });

  describe('options', () => {
    it('should call handleServiceResponse with options service function', async () => {
      await handler.options(mockRequest, mockReply);

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: expect.any(Function),
          module: DEPARTMENT_TEMPLATE,
          method: OPTION,
        }),
      );
    });
  });
});
