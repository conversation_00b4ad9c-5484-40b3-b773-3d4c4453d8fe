import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  google,
  login,
  setup2fa,
  verify2faLogin,
} from '#src/modules/user/handlers/auth.handler.js';
import { AuthService } from '#src/modules/user/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

vi.mock('#src/modules/user/services/index.js', () => ({
  AuthService: {
    login: vi.fn(),
    google: vi.fn(),
    verify2faLogin: vi.fn(),
    reset2fa: vi.fn(),
  },
}));

vi.mock('#src/utils/response.util.js', () => ({
  handleServiceResponse: vi.fn(),
}));

const {
  MODULE_NAMES: { USER },
  MODULE_METHODS: {
    LOGIN,
    REGISTER_SSO,
    REQUEST_2FA_LOGIN,
    REQUEST_2FA_SETUP,
    REVOKE_TRUSTED_DEVICE,
    RESET_2FA,
  },
} = CoreConstant;

describe('User Handler', () => {
  let mockRequest;
  let mockReply;
  let mockResponse;

  beforeEach(() => {
    vi.clearAllMocks();

    mockRequest = {
      body: {
        username: 'testuser',
        password: 'password123',
        'h-captcha-response': 'captcha-token',
      },
      headers: {
        'user-agent': 'test-agent',
      },
      ip: '127.0.0.1',
    };

    mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
      setCookie: vi.fn().mockReturnThis(),
    };

    mockResponse = {
      success: true,
      message: 'Login successful',
      data: {
        token: 'jwt-token',
        user: {
          id: 'user-id',
          username: 'testuser',
        },
      },
    };

    handleServiceResponse.mockResolvedValue(mockResponse);
  });

  describe('login', () => {
    it('should call handleServiceResponse with the correct parameters', async () => {
      await login(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AuthService.login,
        module: USER,
        method: expect.any(Function),
      });
    });

    it('should return LOGIN method when 2FA is not required', async () => {
      await login(mockRequest, mockReply);

      const methodFn = handleServiceResponse.mock.calls[0][0].method;
      const regularLoginResponse = {
        success: true,
        data: {
          token: 'jwt-token',
        },
      };

      expect(methodFn(regularLoginResponse)).toBe(LOGIN);
    });

    it('should return VERIFY_2FA_LOGIN method when 2FA is required', async () => {
      const twoFactorResponse = {
        success: true,
        requiresTwoFactor: true,
        data: {
          userId: 'user-id',
          qrCode: 'qr-code-data',
        },
      };
      handleServiceResponse.mockResolvedValue(twoFactorResponse);

      await login(mockRequest, mockReply);

      const methodFn = handleServiceResponse.mock.calls[0][0].method;
      expect(methodFn(twoFactorResponse)).toBe(REQUEST_2FA_LOGIN);
    });

    it('should return the response from handleServiceResponse', async () => {
      const result = await login(mockRequest, mockReply);

      expect(result).toBe(mockResponse);
    });

    it('should propagate errors from handleServiceResponse', async () => {
      const error = new Error('Service error');
      handleServiceResponse.mockRejectedValueOnce(error);

      await expect(login(mockRequest, mockReply)).rejects.toThrow('Service error');
    });
  });

  describe('google', () => {
    beforeEach(() => {
      mockRequest = {
        body: {
          googleToken: 'google-token-123',
          entityId: 'entity-123',
        },
        headers: {
          'user-agent': 'test-agent',
        },
        ip: '127.0.0.1',
      };
    });

    it('should call handleServiceResponse with the correct parameters for successful login', async () => {
      const loginResponse = {
        success: true,
        message: 'Google login successful',
        data: {
          token: 'jwt-token',
          user: {
            id: 'user-id',
            email: '<EMAIL>',
          },
        },
      };
      handleServiceResponse.mockResolvedValue(loginResponse);

      await google(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AuthService.google,
        module: USER,
        method: expect.any(Function),
      });

      const methodFn = handleServiceResponse.mock.calls[0][0].method;
      expect(methodFn(loginResponse)).toBe(LOGIN);
    });

    it('should use REGISTER_SSO method when status is pending', async () => {
      const pendingResponse = {
        success: true,
        message: 'SSO registration pending',
        status: 'pending',
        data: {
          ssoAccountId: 'sso-123',
        },
      };
      handleServiceResponse.mockResolvedValue(pendingResponse);

      await google(mockRequest, mockReply);

      const methodFn = handleServiceResponse.mock.calls[0][0].method;

      expect(methodFn(pendingResponse)).toBe(REGISTER_SSO);
    });

    it('should return the response from handleServiceResponse', async () => {
      const result = await google(mockRequest, mockReply);

      expect(result).toBe(mockResponse);
    });

    it('should propagate errors from handleServiceResponse', async () => {
      const error = new Error('Google auth error');
      handleServiceResponse.mockRejectedValueOnce(error);

      await expect(google(mockRequest, mockReply)).rejects.toThrow('Google auth error');
    });
  });

  describe('setup2fa', () => {
    it('should call handleServiceResponse with the correct parameters', async () => {
      await setup2fa(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AuthService.setup2fa,
        module: USER,
        method: REQUEST_2FA_SETUP,
      });
    });

    it('should return the response from handleServiceResponse', async () => {
      const result = await setup2fa(mockRequest, mockReply);

      expect(result).toBe(mockResponse);
    });

    it('should propagate errors from handleServiceResponse', async () => {
      const error = new Error('Service error');
      handleServiceResponse.mockRejectedValueOnce(error);

      await expect(login(mockRequest, mockReply)).rejects.toThrow('Service error');
    });
  });

  describe('verify2faLogin', () => {
    it('should call handleServiceResponse with the correct parameters', async () => {
      AuthService.verify2faLogin.mockResolvedValue(mockResponse);

      await verify2faLogin(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: USER,
        method: LOGIN,
      });

      expect(AuthService.verify2faLogin).toHaveBeenCalledWith(mockRequest);
    });

    it('should set cookie on reply when setCookie is in the result', async () => {
      const responseWithCookie = {
        ...mockResponse,
        setCookie: {
          name: 'trusted_device',
          value: 'cookie-value-123',
          options: {
            httpOnly: true,
            maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
          },
        },
      };

      AuthService.verify2faLogin.mockResolvedValue(responseWithCookie);

      mockReply.setCookie = vi.fn();

      await verify2faLogin(mockRequest, mockReply);

      expect(mockReply.setCookie).toHaveBeenCalledWith('trusted_device', 'cookie-value-123', {
        httpOnly: true,
        maxAge: 30 * 24 * 60 * 60 * 1000,
      });
    });

    it('should return the response from handleServiceResponse', async () => {
      AuthService.verify2faLogin.mockResolvedValue(mockResponse);

      const result = await verify2faLogin(mockRequest, mockReply);

      expect(result).toBe(mockResponse);
    });

    it('should propagate errors from the service', async () => {
      const error = new Error('Service error');
      AuthService.verify2faLogin.mockRejectedValueOnce(error);

      await expect(verify2faLogin(mockRequest, mockReply)).rejects.toThrow('Service error');
    });
  });

  describe('revokeTrustedDevices', () => {
    beforeEach(() => {
      AuthService.revokeTrustedDevices = vi.fn();
      mockReply.setCookie = vi.fn();
      AuthService.revokeTrustedDevices.mockResolvedValue({
        success: true,
        message: 'Trusted devices revoked',
      });
    });

    it('should call handleServiceResponse with the correct parameters', async () => {
      const { revokeTrustedDevices } = await import('#src/modules/user/handlers/auth.handler.js');

      await revokeTrustedDevices(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: USER,
        method: REVOKE_TRUSTED_DEVICE,
      });
    });

    it('should return the response from handleServiceResponse', async () => {
      const { revokeTrustedDevices } = await import('#src/modules/user/handlers/auth.handler.js');

      const result = await revokeTrustedDevices(mockRequest, mockReply);

      expect(result).toBe(mockResponse);
    });

    it('should propagate errors from handleServiceResponse', async () => {
      const { revokeTrustedDevices } = await import('#src/modules/user/handlers/auth.handler.js');

      const error = new Error('Service error');
      handleServiceResponse.mockRejectedValueOnce(error);

      await expect(revokeTrustedDevices(mockRequest, mockReply)).rejects.toThrow('Service error');
    });

    it('should not call setCookie when result.setCookie is undefined', async () => {
      const responseWithoutCookie = {
        ...mockResponse,
        setCookie: undefined,
      };

      AuthService.revokeTrustedDevices.mockResolvedValue(responseWithoutCookie);

      mockReply.setCookie = vi.fn();

      const { revokeTrustedDevices } = await import('#src/modules/user/handlers/auth.handler.js');

      await revokeTrustedDevices(mockRequest, mockReply);

      expect(mockReply.setCookie).not.toHaveBeenCalled();
    });

    it('should handle serviceFn that returns null result', async () => {
      const nullResult = null;

      AuthService.revokeTrustedDevices.mockResolvedValue(nullResult);

      const { revokeTrustedDevices } = await import('#src/modules/user/handlers/auth.handler.js');

      await revokeTrustedDevices(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: USER,
        method: REVOKE_TRUSTED_DEVICE,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();

      expect(result).toBeNull();
      expect(mockReply.setCookie).not.toHaveBeenCalled();
    });

    it('should handle setCookie when value is null', async () => {
      const responseWithNullCookie = {
        ...mockResponse,
        setCookie: {
          name: 'trusted_device',
          value: null,
          options: {
            httpOnly: true,
            maxAge: 0,
          },
        },
      };

      AuthService.revokeTrustedDevices.mockResolvedValue(responseWithNullCookie);

      mockReply.setCookie = vi.fn();

      const { revokeTrustedDevices } = await import('#src/modules/user/handlers/auth.handler.js');

      await revokeTrustedDevices(mockRequest, mockReply);

      expect(mockReply.setCookie).toHaveBeenCalledWith('trusted_device', null, {
        httpOnly: true,
        maxAge: 0,
      });
    });
  });

  describe('reset2fa', () => {
    beforeEach(() => {
      AuthService.reset2fa = vi.fn();
      mockReply.setCookie = vi.fn().mockReturnThis();
    });

    it('should call AuthService.reset2fa and handleServiceResponse with the correct parameters', async () => {
      const mockUserMfa = {
        id: 'mfa-123',
        userId: 'user-123',
        setupStatus: 'inactive',
      };

      const serviceResponse = {
        userMfa: mockUserMfa,
        setCookie: {
          name: 'trusted_device',
          value: '',
          options: {
            httpOnly: true,
            secure: true,
            sameSite: 'none',
            expires: new Date(0),
            maxAge: 0,
          },
        },
      };

      AuthService.reset2fa.mockResolvedValue(serviceResponse);

      const { reset2fa } = await import('#src/modules/user/handlers/auth.handler.js');

      await reset2fa(mockRequest, mockReply);

      expect(AuthService.reset2fa).toHaveBeenCalledWith(mockRequest);
      expect(mockReply.setCookie).toHaveBeenCalledWith('trusted_device', '', {
        httpOnly: true,
        secure: true,
        sameSite: 'none',
        expires: new Date(0),
        maxAge: 0,
      });
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: USER,
        method: RESET_2FA,
      });
    });

    it('should not call setCookie when setCookie is not in the result', async () => {
      const mockUserMfa = {
        id: 'mfa-123',
        userId: 'user-123',
        setupStatus: 'inactive',
      };

      const serviceResponse = {
        userMfa: mockUserMfa,
      };

      AuthService.reset2fa.mockResolvedValue(serviceResponse);

      const { reset2fa } = await import('#src/modules/user/handlers/auth.handler.js');

      await reset2fa(mockRequest, mockReply);

      expect(mockReply.setCookie).not.toHaveBeenCalled();
    });

    it('should return the response from handleServiceResponse', async () => {
      const mockUserMfa = {
        id: 'mfa-123',
        userId: 'user-123',
        setupStatus: 'inactive',
      };

      const serviceResponse = {
        userMfa: mockUserMfa,
      };

      AuthService.reset2fa.mockResolvedValue(serviceResponse);

      const { reset2fa } = await import('#src/modules/user/handlers/auth.handler.js');

      const result = await reset2fa(mockRequest, mockReply);

      expect(result).toBe(mockResponse);
    });

    it('should propagate errors from AuthService.reset2fa', async () => {
      const { reset2fa } = await import('#src/modules/user/handlers/auth.handler.js');

      const error = new Error('Service error');
      AuthService.reset2fa.mockRejectedValueOnce(error);

      await expect(reset2fa(mockRequest, mockReply)).rejects.toThrow('Service error');
    });

    it('should handle serviceFn correctly in handleServiceResponse', async () => {
      const mockUserMfa = {
        id: 'mfa-123',
        userId: 'user-123',
        setupStatus: 'inactive',
      };

      const serviceResponse = {
        userMfa: mockUserMfa,
        setCookie: {
          name: 'trusted_device',
          value: '',
          options: {
            httpOnly: true,
            maxAge: 0,
          },
        },
      };

      AuthService.reset2fa.mockResolvedValue(serviceResponse);

      const { reset2fa } = await import('#src/modules/user/handlers/auth.handler.js');

      await reset2fa(mockRequest, mockReply);

      // Get the serviceFn that was passed to handleServiceResponse
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();

      // The serviceFn should return only userMfa, not the full result
      expect(result).toEqual(mockUserMfa);
    });
  });
});
