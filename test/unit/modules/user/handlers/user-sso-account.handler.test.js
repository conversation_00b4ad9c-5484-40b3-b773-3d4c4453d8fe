import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  assign,
  index,
  onboard,
  updateStatus,
} from '#src/modules/user/handlers/user-sso-account.handler.js';
import { UserSSOAccountService } from '#src/modules/user/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

vi.mock('#src/modules/user/services/index.js');
vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');

describe('UserSSOHandler', () => {
  let mockRequest;
  let mockReply;

  const MODULE = CoreConstant.MODULE_NAMES.USER;

  beforeEach(() => {
    vi.resetAllMocks();

    mockRequest = {
      query: {},
      params: {},
      body: {},
      server: {
        redis: {},
      },
    };

    mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };
  });

  describe('index', () => {
    it('should call handleServiceResponse with cached UserSSOAccountService.index and INDEX method', async () => {
      const mockCacheKey = 'user_sso_index_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);
      fetchFromCache.mockImplementation((redis, key, fn) => fn());

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${MODULE}_sso_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with UserSSOAccountService.updateStatus and UPDATE_STATUS method', async () => {
      await updateStatus(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserSSOAccountService.updateStatus,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.UPDATE_STATUS,
      });
    });
  });

  describe('assign', () => {
    it('should call handleServiceResponse with UserSSOAccountService.assign and ASSIGN_USER method', async () => {
      await assign(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserSSOAccountService.assign,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.ASSIGN_USER,
      });
    });
  });

  describe('onboard', () => {
    it('should call handleServiceResponse with UserSSOAccountService.onboard and ONBOARD_USER method', async () => {
      await onboard(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserSSOAccountService.onboard,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.ONBOARD_USER,
      });
    });
  });
});
