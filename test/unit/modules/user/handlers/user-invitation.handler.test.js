import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { create, index, updateStatus } from '#src/modules/user/handlers/user-invitation.handler.js';
import { UserInvitationService } from '#src/modules/user/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

vi.mock('#src/modules/user/services/index.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/utils/cache.util.js');

describe('UserInvitationHandler', () => {
  let mockRequest;
  let mockReply;

  const MODULE = CoreConstant.MODULE_NAMES.USER;

  beforeEach(() => {
    vi.resetAllMocks();

    mockRequest = {
      query: {},
      params: {},
      body: {},
      server: {
        redis: {},
      },
    };

    mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };
  });

  describe('index', () => {
    it('should call handleServiceResponse with UserInvitationService.index and INDEX method', async () => {
      const mockCacheKey = 'users_invitation_index';
      generateCacheKey.mockReturnValue(mockCacheKey);
      fetchFromCache.mockImplementation((redis, key, fn) => fn());

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${MODULE}_invitation_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      // Test cache layer call
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();
      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with UserInvitationService.create and CREATE method', async () => {
      await create(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserInvitationService.create,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.CREATE,
      });
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with UserInvitationService.updateStatus and UPDATE_STATUS method', async () => {
      await updateStatus(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserInvitationService.updateStatus,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.UPDATE_STATUS,
      });
    });
  });
});
