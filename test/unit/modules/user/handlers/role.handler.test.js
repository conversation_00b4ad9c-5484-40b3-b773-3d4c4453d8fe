import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  create,
  index,
  navigations,
  options,
  update,
  updateStatus,
  view,
} from '#src/modules/user/handlers/role.handler.js';
import { RoleService } from '#src/modules/user/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/user/services/index.js');

describe('Role Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      server: {
        redis: {},
      },
      params: { id: '123' },
      query: { page: 1, limit: 10 },
      body: { name: 'Admin', permissions: ['read', 'write'] },
      authInfo: { id: '123' },
    };
    mockReply = {};

    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ROLE}_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.ROLE,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        10,
      );
    });

    it('should call RoleService.index if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await index(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(RoleService.index).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('view', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await view(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ROLE}_${CoreConstant.MODULE_METHODS.VIEW}_${mockRequest.params.id}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.ROLE,
        method: CoreConstant.MODULE_METHODS.VIEW,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        10,
      );
    });

    it('should call RoleService.view if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await view(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(RoleService.view).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await create(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: RoleService.create,
        module: CoreConstant.MODULE_NAMES.ROLE,
        method: CoreConstant.MODULE_METHODS.CREATE,
      });
    });
  });

  describe('update', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await update(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: RoleService.update,
        module: CoreConstant.MODULE_NAMES.ROLE,
        method: CoreConstant.MODULE_METHODS.UPDATE,
      });
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateStatus(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: RoleService.updateStatus,
        module: CoreConstant.MODULE_NAMES.ROLE,
        method: CoreConstant.MODULE_METHODS.UPDATE_STATUS,
      });
    });
  });

  describe('options', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await options(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.ROLE,
        method: CoreConstant.MODULE_METHODS.OPTION,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();
      expect(RoleService.options).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('navigations', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await navigations(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ROLE}_${CoreConstant.MODULE_METHODS.NAVIGATION}_${mockRequest.authInfo.roleId}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.ROLE,
        method: CoreConstant.MODULE_METHODS.NAVIGATION,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        86400, // DAILY cache duration
      );
    });

    it('should call RoleService.navigations if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await navigations(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(RoleService.navigations).toHaveBeenCalledWith(mockRequest);
    });
  });
});
