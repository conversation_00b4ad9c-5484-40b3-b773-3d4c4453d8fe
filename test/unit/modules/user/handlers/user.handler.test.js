import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import {
  checkAvailability,
  create,
  index,
  options,
  subAccountIndex,
  updateBasicInformation,
  updateLoginAccess,
  updateOrganisation,
  updateStatus,
  view,
} from '#src/modules/user/handlers/user.handler.js';
import { UserService } from '#src/modules/user/services/index.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

vi.mock('#src/modules/user/services/index.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/utils/cache.util.js');
describe('User Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      query: {},
      params: {},
      body: {},
      server: {
        redis: {},
      },
    };
    mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    vi.resetAllMocks();
  });

  const MODULE = CoreConstant.MODULE_NAMES.USER;

  describe('index', () => {
    it('should call handleServiceResponse with UserService.index and INDEX method', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.USER}_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });
  });

  describe('sub account index', () => {
    it('should call handleServiceResponse with UserService.index and INDEX method', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await subAccountIndex(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.USER}_subaccount_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with UserService.create and CREATE method', async () => {
      await create(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserService.create,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.CREATE,
      });
    });
  });

  describe('view', () => {
    it('should call handleServiceResponse with UserService.view and VIEW method', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await view(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.USER}_${CoreConstant.MODULE_METHODS.VIEW}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.VIEW,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });
  });
  describe('updateBasicInformation', () => {
    it('should call handleServiceResponse with UserService.updateBasicInformation and UPDATE method', async () => {
      await updateBasicInformation(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserService.updateBasicInformation,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.UPDATE_BASIC_INFORMATION,
      });
    });
  });

  describe('updateOrganisation', () => {
    it('should call handleServiceResponse with UserService.updateOrganisation and UPDATE method', async () => {
      await updateOrganisation(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserService.updateOrganisation,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.UPDATE_ORGANISATION,
      });
    });
  });

  describe('updateLoginAccess', () => {
    it('should call handleServiceResponse with UserService.updateLoginAccess and UPDATE method', async () => {
      await updateLoginAccess(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserService.updateLoginAccess,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.UPDATE_LOGIN_ACCESS,
      });
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with UserService.updateStatus and UPDATE method', async () => {
      await updateStatus(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserService.updateStatus,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.UPDATE_STATUS,
      });
    });
  });
  describe('options', () => {
    it('should call handleServiceResponse with UserService.option  and OPTION method', async () => {
      await options(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.OPTION,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn(mockRequest);

      expect(UserService.option).toHaveBeenCalledWith(mockRequest);
    });
  });
  describe('checkAvailability', () => {
    it('should call handleServiceResponse with UserService.checkAvailability and CHECK_AVAILABILITY method', async () => {
      await checkAvailability(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: UserService.checkAvailability,
        module: MODULE,
        method: CoreConstant.MODULE_METHODS.CHECK_AVAILABILITY,
      });
    });
  });
});
