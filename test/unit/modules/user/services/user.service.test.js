import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreError } from '#src/modules/core/errors/index.js';
import { UserValidation } from '#src/modules/core/validations/index.js';
import { LocalisationService } from '#src/modules/setting/services/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import { UserError } from '#src/modules/user/errors/index.js';
import {
  UserAssociationCurrencyRepository,
  UserAssociationRepository,
  UserMfaSettingRepository,
  UserRepository,
} from '#src/modules/user/repository/index.js';
import * as userService from '#src/modules/user/services/user.service.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';
import { hashPassword } from '#src/utils/hash.util.js';
import { generate2FASecret } from '#src/utils/twofa.util.js';

const { USER_STATUSES, USER_TYPES, USER_ORIGINS, USER_MFA_STATUSES } = UserConstant;

vi.mock('#src/utils/twofa.util.js', () => ({
  generate2FASecret: vi.fn(),
}));
vi.mock('#src/utils/hash.util.js', () => ({
  hashPassword: vi.fn(),
}));
vi.mock('#src/utils/cache.util.js');
vi.mock('#src/modules/setting/services/index.js', () => ({
  LocalisationService: {
    generateDropdown: vi.fn(),
  },
}));

vi.mock('#src/utils/db-transaction.util.js', () => ({
  withTransaction: vi.fn(),
}));
vi.mock('#src/modules/core/validations/index.js', () => ({
  UserValidation: {
    validateEmail: vi.fn(),
    validateUsername: vi.fn(),
  },
}));
vi.mock('#src/modules/user/repository/index.js', () => ({
  UserRepository: {
    findAll: vi.fn(),
    findAllSubAccount: vi.fn(),
    findById: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    getSubAccounts: vi.fn(),
  },
  UserMfaSettingRepository: {
    findByUserId: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
  },
  UserAssociationRepository: {
    create: vi.fn(),
    findByUserId: vi.fn(),
    update: vi.fn(),
  },
  UserAssociationCurrencyRepository: {
    deleteByUserAssociationId: vi.fn(),
    create: vi.fn(),
  },
}));

describe('userService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  describe('index', () => {
    it('should return formatted user list with pagination', async () => {
      const mockUserData = {
        rows: [
          {
            id: 1,
            name: 'Test User',
            username: 'testuser',
            type: USER_TYPES.NORMAL,
            status: 'active',
            parentId: null,
            validityDate: null,
            mfa: {
              status: 'enabled',
            },
            userAssociation: [
              {
                currencyCodes: ['USD', 'MYR'],
                roleId: 10,
                role: {
                  name: 'Admin',
                  department: {
                    name: 'root',
                  },
                },
                entityId: 'E1',
                origin: 'internal',
                entity: {
                  hierarchy: 'merchant',
                },
                ui: {
                  status: '',
                },
              },
            ],
            createdAt: '2024-01-01',
            createdBy: 'admin',
            updatedAt: '2024-01-02',
            updatedBy: 'admin',
          },
        ],
        pagination: { total: 1 },
      };

      UserRepository.findAll.mockResolvedValue(mockUserData);

      const request = {
        server: {},
        entity: { id: 'ent-123' },
        query: { someQueryParam: 'value' },
      };

      const result = await userService.index(request);

      expect(UserRepository.findAll).toHaveBeenCalledWith(request.server, {
        ...request.query,
        filter_type_eq: USER_TYPES.NORMAL,
        'filter_ua.entityId_eq': request.entity.id,
      });

      expect(result).toEqual({
        rows: [
          {
            id: 1,
            name: 'Test User',
            username: 'testuser',
            type: USER_TYPES.NORMAL,
            status: 'active',
            parentId: '',
            validityDate: '',
            mfaStatus: 'enabled',
            userInvitationStatus: '',
            currencyCodes: ['USD', 'MYR'],
            roleId: 10,
            roleName: 'Admin',
            departmentName: 'root',
            entityId: 'E1',
            origin: 'internal',
            hierarchy: 'merchant',
            createdAt: '2024-01-01',
            createdBy: 'admin',
            updatedAt: '2024-01-02',
            updatedBy: 'admin',
          },
        ],
        pagination: { total: 1 },
      });
    });
    it('should use defaults when optional fields are missing', async () => {
      const mockUserData = {
        rows: [
          {
            id: 2,
            name: 'No Assoc User',
            username: 'noassoc',
            type: USER_TYPES.NORMAL,
            status: 'inactive',
            // parentId missing
            // validityDate missing
            // mfa missing
            userAssociation: [], // empty array triggers ua = {}
            // createdAt missing
            // createdBy missing
            // updatedAt missing
            // updatedBy missing
          },
        ],
        pagination: { total: 1 },
      };

      UserRepository.findAll.mockResolvedValue(mockUserData);

      const request = {
        server: {},
        entity: { id: 'ent-456' },
        query: {},
      };

      const result = await userService.index(request);

      expect(result).toEqual({
        rows: [
          {
            id: 2,
            name: 'No Assoc User',
            username: 'noassoc',
            type: USER_TYPES.NORMAL,
            status: 'inactive',
            parentId: '',
            validityDate: '',
            mfaStatus: undefined,
            currencyCodes: [],
            roleId: '',
            roleName: '',
            entityId: '',
            userInvitationStatus: '',
            departmentName: '',
            origin: undefined,
            createdAt: undefined,
            createdBy: undefined,
            updatedAt: undefined,
            updatedBy: undefined,
          },
        ],
        pagination: { total: 1 },
      });
    });
  });
  describe('subAccountIndex', () => {
    it('should return sub-account list as is', async () => {
      const mockSubAccounts = {
        rows: [
          {
            id: 1,
            name: 'Test User',
            username: 'testuser',
            type: USER_TYPES.SUB_ACCOUNT,
            status: 'active',
            parentId: 'user-123',
            validityDate: null,
            mfa: {
              status: 'enabled',
            },
            userAssociation: [
              {
                currencyCodes: ['USD', 'MYR'],
                roleId: 10,
                role: {
                  name: 'Admin',
                  department: {
                    name: 'root',
                  },
                },
                entityId: 'E1',
                origin: 'internal',
                entity: {
                  hierarchy: 'merchant',
                },
                ui: {
                  status: '',
                },
              },
            ],
            createdAt: '2024-01-01',
            createdBy: 'admin',
            updatedAt: '2024-01-02',
            updatedBy: 'admin',
          },
        ],
        pagination: { total: 1 },
      };

      UserRepository.findAllSubAccount.mockResolvedValue(mockSubAccounts);

      const request = {
        server: {},
        entity: { id: 'ent-456' },
        authInfo: { id: 'user-123' },
        query: { search: 'sub' },
      };

      const result = await userService.subAccountIndex(request);

      expect(UserRepository.findAllSubAccount).toHaveBeenCalledWith(request.server, {
        ...request.query,
        filter_type_eq: USER_TYPES.SUB_ACCOUNT,
        filter_parentId_eq: 'user-123',
        'filter_ua.entityId_eq': 'ent-456',
      });

      expect(result).toEqual({
        rows: [
          {
            id: 1,
            name: 'Test User',
            username: 'testuser',
            type: USER_TYPES.SUB_ACCOUNT,
            status: 'active',
            parentId: 'user-123',
            validityDate: '',
            mfaStatus: 'enabled',
            currencyCodes: ['USD', 'MYR'],
            roleId: 10,
            roleName: 'Admin',
            departmentName: 'root',
            userInvitationStatus: '',
            entityId: 'E1',
            origin: 'internal',
            hierarchy: 'merchant',
            createdAt: '2024-01-01',
            createdBy: 'admin',
            updatedAt: '2024-01-02',
            updatedBy: 'admin',
          },
        ],
        pagination: { total: 1 },
      });
    });

    it('should handle missing optional fields with defaults', async () => {
      const mockSubAccounts = {
        rows: [
          {
            id: 2,
            name: 'Missing Fields User',
            username: 'nofields',
            type: USER_TYPES.SUB_ACCOUNT,
            status: 'inactive',
            // parentId missing
            // validityDate missing
            // mfa missing
            userAssociation: [], // triggers ua = {}
            // createdAt missing
            // createdBy missing
            // updatedAt missing
            // updatedBy missing
          },
        ],
        pagination: { total: 1 },
      };

      UserRepository.findAllSubAccount.mockResolvedValue(mockSubAccounts);

      const request = {
        server: {},
        entity: { id: 'ent-789' },
        authInfo: { id: 'parent-001' },
        query: {},
      };

      const result = await userService.subAccountIndex(request);

      expect(result).toEqual({
        rows: [
          {
            id: 2,
            name: 'Missing Fields User',
            username: 'nofields',
            type: USER_TYPES.SUB_ACCOUNT,
            status: 'inactive',
            parentId: '',
            validityDate: '',
            mfaStatus: undefined,
            currencyCodes: [],
            roleId: '',
            roleName: '',
            departmentName: '',
            entityId: '',
            userInvitationStatus: '',
            origin: undefined,
            createdAt: undefined,
            createdBy: undefined,
            updatedAt: undefined,
            updatedBy: undefined,
          },
        ],
        pagination: { total: 1 },
      });
    });
  });

  describe('view', () => {
    const mockId = '123';
    let request;

    beforeEach(() => {
      vi.clearAllMocks();

      request = {
        params: { id: mockId },
        server: { psql: {} },
      };
    });
    it('should return user if found', async () => {
      const mockUser = {
        id: 1,
        name: 'Test User',
        username: 'testuser',
        type: USER_TYPES.NORMAL,
        status: 'active',
        parentId: null,
        validityDate: null,
        mfa: { status: 'enabled' },
        email: '',
        userAssociation: [
          {
            currencyCodes: ['USD', 'MYR'],
            roleId: 10,
            role: {
              name: 'Admin',
              department: {
                name: 'root',
              },
            },
            entityId: 'E1',
            origin: 'internal',
            entity: {
              hierarchy: 'merchant',
            },
            ui: {
              status: '',
            },
          },
        ],
        createdAt: '2024-01-01',
        createdBy: 'admin',
        updatedAt: '2024-01-02',
        updatedBy: 'admin',
      };

      vi.spyOn(UserRepository, 'findById').mockResolvedValue(mockUser);
      const result = await userService.view(request);

      expect(UserRepository.findById).toHaveBeenCalledWith(request.server, mockId);
      expect(result).toEqual({
        id: 1,
        name: 'Test User',
        username: 'testuser',
        type: USER_TYPES.NORMAL,
        status: 'active',
        parentId: '',
        validityDate: '',
        mfaStatus: 'enabled',
        email: '',
        roleId: 10,
        roleName: 'Admin',
        entityId: 'E1',
        origin: 'internal',
        hierarchy: 'merchant',
        departmentName: 'root',
        userInvitationStatus: '',
        currencyCodes: ['USD', 'MYR'],
        createdAt: '2024-01-01',
        createdBy: 'admin',
        updatedAt: '2024-01-02',
        updatedBy: 'admin',
      });
    });

    it('should handle missing optional fields with defaults', async () => {
      const mockUser = {
        id: 2,
        name: undefined,
        username: 'no-fields',
        type: USER_TYPES.NORMAL,
        email: undefined,
        status: undefined,
        // parentId missing
        // validityDate missing
        // mfa missing
        userAssociation: [], // triggers ua = {}
        // createdAt missing
        // createdBy missing
        // updatedAt missing
        // updatedBy missing
      };

      vi.spyOn(UserRepository, 'findById').mockResolvedValue(mockUser);

      const result = await userService.view(request);

      expect(result).toEqual({
        id: 2,
        name: undefined,
        username: 'no-fields',
        type: USER_TYPES.NORMAL,
        email: undefined,
        status: undefined,
        parentId: '',
        validityDate: '',
        mfaStatus: undefined,
        currencyCodes: [],
        roleId: '',
        roleName: '',
        entityId: '',
        userInvitationStatus: '',
        departmentName: '',
        origin: undefined,
        createdAt: undefined,
        createdBy: undefined,
        updatedAt: undefined,
        updatedBy: undefined,
      });
    });
    it('should throw notFound error if user is not found', async () => {
      vi.spyOn(UserRepository, 'findById').mockResolvedValue(null);
      const notFoundError = new Error('User not found');
      vi.spyOn(CoreError, 'dataNotFound').mockReturnValue(notFoundError);

      await expect(userService.view(request)).rejects.toThrow(notFoundError);
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.user',
        attribute: 'ID',
        value: mockId,
      });
    });
  });

  describe('create', () => {
    it('should create a normal user successfully', async () => {
      const request = {
        server: {},
        authInfo: { id: 'admin-1' },
        entity: { id: 'ent-1', hierarchy: 'merchant' },
        body: {
          name: 'Alice',
          username: 'alice123',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'securepass',
          email: '<EMAIL>',
          currencyIds: ['cur-1', 'cur-2'],
          roleId: 'role-1',
          validityDate: '2025-12-31',
        },
      };

      const createdUser = { id: 'user-1' };

      UserValidation.validateEmail.mockResolvedValue();
      UserValidation.validateUsername.mockResolvedValue('alice123');
      hashPassword.mockResolvedValue('hashed-pass');
      UserRepository.create.mockResolvedValue(createdUser);
      generate2FASecret.mockResolvedValue({ base32secret: 'secret-key' });
      UserMfaSettingRepository.create.mockResolvedValue();
      UserAssociationRepository.create.mockResolvedValue({ id: 'ua-1' });
      UserAssociationCurrencyRepository.create.mockResolvedValue();

      withTransaction.mockImplementation(async (server, options, callback) => {
        return await callback({}); // mock transaction
      });

      const result = await userService.create(request);

      expect(UserRepository.create).toHaveBeenCalledWith(
        request.server,
        expect.objectContaining({
          name: 'Alice',
          username: 'alice123',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'hashed-pass',
          email: '<EMAIL>',
          currencyIds: ['cur-1', 'cur-2'],
          type: USER_TYPES.NORMAL,
          validityDate: '2025-12-31',
          status: USER_STATUSES.ACTIVE,
        }),
        expect.anything(),
      );

      expect(UserAssociationCurrencyRepository.create).toHaveBeenCalled();
      expect(result).toEqual(createdUser);
    });

    it('should throw if roleId is missing for normal user', async () => {
      const request = {
        server: {},
        authInfo: { id: 'admin-1' },
        entity: { id: 'ent-1', hierarchy: 'MERCHANT' },
        body: {
          name: 'Alice',
          username: 'alice123',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'securepass',
          currencyIds: ['cur-1'],
        },
      };

      await expect(userService.create(request)).rejects.toThrow(
        CoreError.requiredData({ attribute: 'common.label.role' }),
      );
    });

    it('should throw if currencyIds missing for merchant', async () => {
      const request = {
        server: {},
        authInfo: { id: 'admin-1' },
        entity: { id: 'ent-1', hierarchy: 'merchant' },
        body: {
          name: 'Alice',
          username: 'alice123',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'securepass',
          roleId: 'role-1',
          currencyIds: [],
        },
      };

      await expect(userService.create(request)).rejects.toThrow(
        CoreError.requiredData({ attribute: 'common.label.currency' }),
      );
    });

    it('should throw if parentId missing for sub-account', async () => {
      const request = {
        server: {},
        authInfo: { id: 'admin-1' },
        entity: { id: 'ent-1', hierarchy: 'merchant' },
        body: {
          name: 'Bob',
          username: 'bob123',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'securepass',
          type: USER_TYPES.SUB_ACCOUNT,
        },
      };

      await expect(userService.create(request)).rejects.toThrow(
        CoreError.requiredData({
          attribute: 'common.label.parentUser',
        }),
      );
    });

    it('should throw if parent not found for sub-account', async () => {
      const request = {
        server: {},
        authInfo: { id: 'admin-1' },
        entity: { id: 'ent-1', hierarchy: 'merchant' },
        body: {
          name: 'Bob',
          username: 'bob123',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'securepass',
          type: USER_TYPES.SUB_ACCOUNT,
          parentId: 'nonexistent-parent',
        },
      };

      UserRepository.findById.mockResolvedValue(null);
      await expect(userService.create(request)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.parentUser',
          attribute: 'ID',
          value: 'nonexistent-parent',
        }),
      );
    });
  });

  describe('updateBasicInformation', () => {
    it('should update user if found and email is valid', async () => {
      const user = { id: 'user-1' };
      const updated = { isDirty: true };

      UserRepository.findById.mockResolvedValue(user);
      UserValidation.validateEmail.mockResolvedValue();
      UserRepository.update.mockResolvedValue(updated);

      const request = {
        server: {},
        params: { id: 'user-1' },
        authInfo: { id: 'admin-1' },
        body: {
          name: 'Updated Name',
          email: '<EMAIL>',
        },
      };

      await expect(userService.updateBasicInformation(request)).resolves.toBeUndefined();

      expect(UserRepository.findById).toHaveBeenCalledWith(request.server, 'user-1');
      expect(UserValidation.validateEmail).toHaveBeenCalledWith(
        request.server,
        '<EMAIL>',
        'user-1',
      );
      expect(UserRepository.update).toHaveBeenCalledWith(user, request.body, {
        authInfoId: 'admin-1',
      });
    });

    it('should throw notFound error if user not found', async () => {
      UserRepository.findById.mockResolvedValue(null);

      const request = {
        server: {},
        params: { id: 'missing-user' },
        authInfo: { id: 'admin-1' },
        body: {},
      };

      await expect(userService.updateBasicInformation(request)).rejects.toThrowError(
        CoreError.dataNotFound({
          data: 'common.label.user',
          attribute: 'ID',
          value: 'missing-user',
        }),
      );
    });

    it('should throw unprocessable error if update is not dirty', async () => {
      const user = { id: 'user-1' };

      UserRepository.findById.mockResolvedValue(user);
      UserValidation.validateEmail.mockResolvedValue();
      UserRepository.update.mockResolvedValue({ isDirty: false });

      const request = {
        server: {},
        params: { id: 'user-1' },
        authInfo: { id: 'admin-1' },
        body: { name: 'Same Name', email: '<EMAIL>' },
      };

      await expect(userService.updateBasicInformation(request)).rejects.toThrowError(
        CoreError.unprocessable(),
      );
    });
  });

  describe('updateOrganisation', () => {
    const baseRequest = {
      server: {},
      entity: { id: 'entity-1' },
      authInfo: { id: 'admin-1' },
      params: { id: 'user-1' },
    };

    it('should throw not found if user does not exist', async () => {
      UserRepository.findById.mockResolvedValue(null);

      await expect(
        userService.updateOrganisation({ ...baseRequest, body: {} }),
      ).rejects.toThrowError(
        CoreError.dataNotFound({
          data: 'common.label.user',
          attribute: 'ID',
          value: 'user-1',
        }),
      );
    });

    it('should throw unprocessable if no change detected', async () => {
      UserRepository.findById.mockResolvedValue({ id: 'user-1', type: USER_TYPES.NORMAL });
      UserAssociationRepository.findByUserId.mockResolvedValue({
        id: 'ua-1',
        uac: [{ currencyId: 'cur-1' }],
      });
      UserAssociationRepository.update.mockResolvedValue({ isDirty: false });

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(
        userService.updateOrganisation({
          ...baseRequest,
          body: { roleId: undefined, currencyIds: ['cur-1'] },
        }),
      ).rejects.toThrowError(CoreError.unprocessable());
    });

    it('should update role only', async () => {
      UserRepository.findById.mockResolvedValue({ id: 'user-1', type: USER_TYPES.NORMAL });
      UserAssociationRepository.findByUserId.mockResolvedValue({
        id: 'ua-1',
        uac: [{ currencyId: 'cur-1' }],
      });
      UserAssociationRepository.update.mockResolvedValue({ isDirty: true });

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(
        userService.updateOrganisation({
          ...baseRequest,
          body: { roleId: 'role-2', currencyIds: ['cur-1'] },
        }),
      ).resolves.toBeUndefined();

      expect(UserAssociationRepository.update).toHaveBeenCalledWith(
        expect.anything(),
        { roleId: 'role-2' },
        expect.anything(),
      );
    });

    it('should update currency only', async () => {
      UserRepository.findById.mockResolvedValue({ id: 'user-1', type: USER_TYPES.NORMAL });
      UserAssociationRepository.findByUserId.mockResolvedValue({
        id: 'ua-1',
        uac: [{ currencyId: 'cur-1' }],
      });
      UserAssociationRepository.update.mockResolvedValue({ isDirty: false });

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(
        userService.updateOrganisation({
          ...baseRequest,
          body: { currencyIds: ['cur-2'] },
        }),
      ).resolves.toBeUndefined();

      expect(UserAssociationCurrencyRepository.deleteByUserAssociationId).toHaveBeenCalledWith(
        baseRequest.server,
        'ua-1',
        expect.anything(),
      );

      expect(UserAssociationCurrencyRepository.create).toHaveBeenCalledWith(
        baseRequest.server,
        [{ userAssociationId: 'ua-1', currencyId: 'cur-2' }],
        expect.anything(),
      );
    });

    it('should update both role and currency', async () => {
      UserRepository.findById.mockResolvedValue({ id: 'user-1', type: USER_TYPES.NORMAL });
      UserAssociationRepository.findByUserId.mockResolvedValue({
        id: 'ua-1',
        uac: [{ currencyId: 'cur-1' }],
      });
      UserAssociationRepository.update.mockResolvedValue({ isDirty: true });

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(
        userService.updateOrganisation({
          ...baseRequest,
          body: { roleId: 'role-2', currencyIds: ['cur-2'] },
        }),
      ).resolves.toBeUndefined();

      expect(UserAssociationRepository.update).toHaveBeenCalled();
      expect(UserAssociationCurrencyRepository.create).toHaveBeenCalled();
    });
  });

  describe('updateLoginAccess', () => {
    const baseRequest = {
      server: {},
      authInfo: { id: 'admin-1' },
      params: { id: 'user-1' },
      body: { twoFactorAuth: 'enabled', status: 'active' },
    };

    it('should throw not found if user does not exist', async () => {
      UserRepository.findById.mockResolvedValue(null);

      await expect(userService.updateLoginAccess(baseRequest)).rejects.toThrowError(
        CoreError.dataNotFound({
          data: 'common.label.user',
          attribute: 'ID',
          value: 'user-1',
        }),
      );
    });

    it('should throw unprocessable if nothing changed', async () => {
      UserRepository.findById.mockResolvedValue({ id: 'user-1' });
      UserMfaSettingRepository.findByUserId.mockResolvedValue({ id: 'mfa-1' });
      UserMfaSettingRepository.update.mockResolvedValue({ isDirty: false });
      UserRepository.update.mockResolvedValue({ isDirty: false });

      withTransaction.mockImplementation(async (_server, _opts, cb) => cb({}));

      await expect(userService.updateLoginAccess(baseRequest)).rejects.toThrowError(
        CoreError.unprocessable(),
      );
    });

    it('should update MFA and user info successfully', async () => {
      UserRepository.findById.mockResolvedValue({ id: 'user-1' });
      UserMfaSettingRepository.findByUserId.mockResolvedValue({ id: 'mfa-1' });

      UserMfaSettingRepository.update.mockResolvedValue({ isDirty: true });
      UserRepository.update.mockResolvedValue({ isDirty: true });

      withTransaction.mockImplementation(async (_server, _opts, cb) => cb({}));

      await expect(userService.updateLoginAccess(baseRequest)).resolves.toBeUndefined();

      expect(UserMfaSettingRepository.update).toHaveBeenCalledWith(
        { id: 'mfa-1' },
        { status: 'enabled' },
        expect.objectContaining({ transaction: expect.anything(), authInfoId: 'admin-1' }),
      );

      expect(UserRepository.update).toHaveBeenCalledWith(
        { id: 'user-1' },
        baseRequest.body,
        expect.objectContaining({ transaction: expect.anything(), authInfoId: 'admin-1' }),
      );
    });
  });

  describe('updateStatus', () => {
    it('should throw notFound error when the user does not exist in updateStatus', async () => {
      const request = {
        body: { status: USER_STATUSES.ACTIVE },
        params: { id: 'nonexistent-user' },
        server: {},
        authInfo: { id: 'admin-1' },
      };

      UserRepository.findById.mockResolvedValue(null);

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(userService.updateStatus(request)).rejects.toThrowError(
        CoreError.dataNotFound({
          data: 'common.label.user',
          attribute: 'ID',
          value: 'nonexistent-user',
        }),
      );
      expect(UserRepository.findById).toHaveBeenCalledWith(
        request.server,
        'nonexistent-user',
        expect.anything(),
      );
    });

    it('should throw unprocessable error when trying to activate a sub-account without a parentId', async () => {
      const request = {
        body: { status: USER_STATUSES.ACTIVE },
        params: { id: 'sub-account-1' },
        server: {},
        authInfo: { id: 'admin-1' },
      };

      UserRepository.findById.mockResolvedValue({
        id: 'sub-account-1',
        type: USER_TYPES.SUB_ACCOUNT,
        parentId: null,
      });

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(userService.updateStatus(request)).rejects.toThrowError(
        CoreError.unprocessable(),
      );
      expect(UserRepository.findById).toHaveBeenCalledWith(
        request.server,
        'sub-account-1',
        expect.anything(),
      );
    });

    it('should throw notFound error when the parent user does not exist for a sub-account activation', async () => {
      const request = {
        body: { status: USER_STATUSES.ACTIVE },
        params: { id: 'sub-account-1' },
        server: {},
        authInfo: { id: 'admin-1' },
      };

      UserRepository.findById
        .mockResolvedValueOnce({
          id: 'sub-account-1',
          type: USER_TYPES.SUB_ACCOUNT,
          parentId: 'parent-1',
        })
        .mockResolvedValueOnce(null); // Simulate parent user not found

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(userService.updateStatus(request)).rejects.toThrowError(
        CoreError.dataNotFound({
          data: 'common.label.parentUser',
          attribute: 'ID',
          value: 'parent-1',
        }),
      );
      expect(UserRepository.findById).toHaveBeenCalledWith(
        request.server,
        'sub-account-1',
        expect.anything(),
      );
      expect(UserRepository.findById).toHaveBeenCalledWith(
        request.server,
        'parent-1',
        expect.anything(),
      );
    });

    it('should throw "parentInactive" error when trying to activate a sub-account with an inactive parent', async () => {
      const request = {
        body: { status: USER_STATUSES.ACTIVE },
        params: { id: 'sub-account-1' },
        server: {},
        authInfo: { id: 'admin-1' },
      };

      UserRepository.findById
        .mockResolvedValueOnce({
          id: 'sub-account-1',
          type: USER_TYPES.SUB_ACCOUNT,
          parentId: 'parent-1',
        })
        .mockResolvedValueOnce({
          id: 'parent-1',
          status: USER_STATUSES.INACTIVE,
        });

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(userService.updateStatus(request)).rejects.toThrow(UserError.parentInactive());
      expect(UserRepository.findById).toHaveBeenCalledWith(
        request.server,
        'sub-account-1',
        expect.anything(),
      );
      expect(UserRepository.findById).toHaveBeenCalledWith(
        request.server,
        'parent-1',
        expect.anything(),
      );
    });

    it('should throw unprocessable error when the user status is unchanged', async () => {
      const request = {
        body: { status: USER_STATUSES.ACTIVE },
        params: { id: 'user-1' },
        server: {},
        authInfo: { id: 'admin-1' },
      };

      UserRepository.findById.mockResolvedValue({
        id: 'user-1',
        status: USER_STATUSES.ACTIVE,
      });

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(userService.updateStatus(request)).rejects.toThrowError(
        CoreError.unprocessable(),
      );
      expect(UserRepository.findById).toHaveBeenCalledWith(
        request.server,
        'user-1',
        expect.anything(),
      );
    });

    it('should throw unprocessable error when the user update does not mark as dirty', async () => {
      const request = {
        body: { status: USER_STATUSES.ACTIVE },
        params: { id: 'user-1' },
        server: {},
        authInfo: { id: 'admin-1' },
      };

      UserRepository.findById.mockResolvedValue({
        id: 'user-1',
        status: USER_STATUSES.INACTIVE,
      });

      UserRepository.update.mockResolvedValue({ isDirty: false });
      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(userService.updateStatus(request)).rejects.toThrowError(
        CoreError.unprocessable(),
      );

      expect(UserRepository.findById).toHaveBeenCalledWith(
        request.server,
        'user-1',
        expect.anything(),
      );

      expect(UserRepository.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        expect.anything(),
      );
    });

    it('should deactivate all sub-accounts when a main account is deactivated', async () => {
      const request = {
        body: { status: USER_STATUSES.INACTIVE },
        params: { id: 'main-user-1' },
        server: {},
        authInfo: { id: 'admin-1' },
      };

      const mainUser = { id: 'main-user-1', type: USER_TYPES.NORMAL, status: USER_STATUSES.ACTIVE };
      const subAccounts = [
        { id: 'sub-user-1', status: USER_STATUSES.ACTIVE },
        { id: 'sub-user-2', status: USER_STATUSES.ACTIVE },
      ];

      UserRepository.findById.mockResolvedValue(mainUser);
      UserRepository.update.mockResolvedValue({ isDirty: true });
      UserRepository.getSubAccounts.mockResolvedValue(subAccounts);

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(userService.updateStatus(request)).resolves.toBeUndefined();

      expect(UserRepository.update).toHaveBeenCalledWith(
        mainUser,
        { status: USER_STATUSES.INACTIVE },
        expect.objectContaining({ transaction: expect.anything(), authInfoId: 'admin-1' }),
      );

      subAccounts.forEach((subAccount) => {
        expect(UserRepository.update).toHaveBeenCalledWith(
          subAccount,
          { status: USER_STATUSES.INACTIVE },
          expect.objectContaining({ transaction: expect.anything(), authInfoId: 'admin-1' }),
        );
      });
    });

    it('should not deactivate sub-accounts if there are no active sub-accounts when deactivating a main account', async () => {
      const request = {
        body: { status: USER_STATUSES.INACTIVE },
        params: { id: 'main-user-1' },
        server: {},
        authInfo: { id: 'admin-1' },
      };

      const mainUser = { id: 'main-user-1', type: USER_TYPES.NORMAL, status: USER_STATUSES.ACTIVE };
      const subAccounts = []; // No active sub-accounts

      UserRepository.findById.mockResolvedValue(mainUser);
      UserRepository.update.mockResolvedValue({ isDirty: true });
      UserRepository.getSubAccounts.mockResolvedValue(subAccounts);

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(userService.updateStatus(request)).resolves.toBeUndefined();

      expect(UserRepository.update).toHaveBeenCalledWith(
        mainUser,
        { status: USER_STATUSES.INACTIVE },
        expect.objectContaining({ transaction: expect.anything(), authInfoId: 'admin-1' }),
      );

      // Ensure no sub-account updates are attempted
      expect(UserRepository.update).toHaveBeenCalledTimes(1);
    });

    it('should update user status successfully when all conditions are met', async () => {
      const request = {
        body: { status: USER_STATUSES.INACTIVE },
        params: { id: 'user-1' },
        server: {},
        authInfo: { id: 'admin-1' },
      };

      const user = { id: 'user-1', type: USER_TYPES.NORMAL, status: USER_STATUSES.ACTIVE };
      const subAccounts = [
        { id: 'sub-user-1', status: USER_STATUSES.ACTIVE },
        { id: 'sub-user-2', status: USER_STATUSES.ACTIVE },
      ];

      UserRepository.findById.mockResolvedValue(user);
      UserRepository.update.mockResolvedValue({ isDirty: true });
      UserRepository.getSubAccounts.mockResolvedValue(subAccounts);

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(userService.updateStatus(request)).resolves.toBeUndefined();

      expect(UserRepository.update).toHaveBeenCalledWith(
        user,
        { status: USER_STATUSES.INACTIVE },
        expect.objectContaining({ transaction: expect.anything(), authInfoId: 'admin-1' }),
      );

      subAccounts.forEach((subAccount) => {
        expect(UserRepository.update).toHaveBeenCalledWith(
          subAccount,
          { status: USER_STATUSES.INACTIVE },
          expect.objectContaining({ transaction: expect.anything(), authInfoId: 'admin-1' }),
        );
      });
    });

    it('should handle concurrent updates of sub-accounts when deactivating a main account', async () => {
      const request = {
        body: { status: USER_STATUSES.INACTIVE },
        params: { id: 'main-user-1' },
        server: {},
        authInfo: { id: 'admin-1' },
      };

      const mainUser = { id: 'main-user-1', type: USER_TYPES.NORMAL, status: USER_STATUSES.ACTIVE };
      const subAccounts = [
        { id: 'sub-user-1', status: USER_STATUSES.ACTIVE },
        { id: 'sub-user-2', status: USER_STATUSES.ACTIVE },
      ];

      UserRepository.findById.mockResolvedValue(mainUser);
      UserRepository.update.mockResolvedValue({ isDirty: true });
      UserRepository.getSubAccounts.mockResolvedValue(subAccounts);

      withTransaction.mockImplementation(async (server, options, cb) => cb({}));

      await expect(userService.updateStatus(request)).resolves.toBeUndefined();

      expect(UserRepository.update).toHaveBeenCalledWith(
        mainUser,
        { status: USER_STATUSES.INACTIVE },
        expect.objectContaining({ transaction: expect.anything(), authInfoId: 'admin-1' }),
      );

      subAccounts.forEach((subAccount) => {
        expect(UserRepository.update).toHaveBeenCalledWith(
          subAccount,
          { status: USER_STATUSES.INACTIVE },
          expect.objectContaining({ transaction: expect.anything(), authInfoId: 'admin-1' }),
        );
      });
    });
  });

  describe('option', () => {
    it('should return currency, role, and department options', async () => {
      const fakeCurrency = [
        { id: 'USD', name: 'US Dollar' },
        { id: 'MYR', name: 'Malaysian Ringgit' },
      ];

      LocalisationService.generateDropdown.mockResolvedValue(fakeCurrency);

      const request = {
        query: {},
        server: {},
      };

      const result = await userService.option(request);

      expect(LocalisationService.generateDropdown).toHaveBeenCalledWith({
        ...request,
        query: { 'filter_localisation.category_eq': 'currency' },
        server: request.server,
      });

      expect(result).toEqual({
        currency: fakeCurrency,
        role: expect.arrayContaining([
          expect.objectContaining({ name: 'Admin' }),
          expect.objectContaining({ name: 'Manager' }),
          expect.objectContaining({ name: 'User' }),
        ]),
        department: expect.arrayContaining([
          expect.objectContaining({ name: 'Dev Team' }),
          expect.objectContaining({ name: 'Finance' }),
          expect.objectContaining({ name: 'Operation' }),
        ]),
      });
    });
  });
});

describe('userService.checkAvailability', () => {
  const mockRequest = {
    query: { username: 'john' },
    server: {},
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return the validated username if not taken', async () => {
    UserValidation.validateUsername.mockResolvedValue('john');

    const result = await userService.checkAvailability(mockRequest);

    expect(UserValidation.validateUsername).toHaveBeenCalledWith(
      mockRequest.server,
      'john',
      'normal',
    );
    expect(result).toBe('john');
  });

  it('should throw if username is already taken', async () => {
    const error = new Error('Username "john" already exist');
    UserValidation.validateUsername.mockRejectedValue(error);

    await expect(userService.checkAvailability(mockRequest)).rejects.toThrow(
      'Username "john" already exist',
    );
  });
});
