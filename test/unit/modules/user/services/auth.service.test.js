import { OAuth2Client } from 'google-auth-library';
import { toDataURL } from 'qrcode';
import speakeasy from 'speakeasy';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { EntityRepository } from '#src/modules/core/repository/index.js';
import { SettingRepository } from '#src/modules/setting/repository/index.js';
import { AuthConstant, UserConstant } from '#src/modules/user/constants/index.js';
import { AuthError } from '#src/modules/user/errors/index.js';
import {
  UserMfaSettingRepository,
  UserRepository,
  UserSSOAccountRepository,
} from '#src/modules/user/repository/index.js';
import * as authService from '#src/modules/user/services/auth.service.js';
import { AuthValidations } from '#src/modules/user/validations/index.js';
import { fetchFromCache, generateCacheKey, clearCacheWithPrefix } from '#src/utils/cache.util.js';
import { signJWT } from '#src/utils/jwt.util.js';

vi.mock('#src/modules/user/constants/index.js', () => ({
  AuthConstant: {
    EXPIRY_TIMES: {
      TOKEN: 24 * 60 * 60, // 24 hours in seconds
    },
    SECURITY: {
      VALID_ISSUERS: ['accounts.google.com', 'https://accounts.google.com'],
      TWO_FACTOR_AUTHENTICATION_WINDOW_DURATION: 2,
    },
    KEYS: {
      TRUSTED_DEVICE_COOKIE_NAME: 'trusted_device',
    },
    SECONDS: {
      DAY: 24 * 60 * 60,
    },
  },
  UserConstant: {
    USER_ORIGINS: {
      INTERNAL: 'internal',
    },
    USER_SSO_STATUSES: {
      ACTIVE: 'active',
      COMPLETED: 'completed',
      PENDING: 'pending',
    },
    USER_MFA_STATUSES: {
      ENABLED: 'enabled',
      DISABLED: 'disabled',
    },
    USER_MFA_SETUP_STATUSES: {
      ACTIVE: 'active',
      INACTIVE: 'inactive',
    },
  },
}));

vi.mock('#src/modules/user/validations/index.js', () => ({
  AuthValidations: {
    checkIpRateLimiting: vi.fn(),
    checkSystemStatus: vi.fn(),
    validateAccessAndGetEntityData: vi.fn(),
    validateUserStatus: vi.fn(),
    validatePassword: vi.fn(),
    validateEmailDomain: vi.fn(),
    check2FARequirement: vi.fn(),
    validate2FA: vi.fn(),
    validatePreAuthToken: vi.fn(),
  },
}));

vi.mock('#src/utils/jwt.util.js', () => ({
  signJWT: vi.fn(),
}));

vi.mock('google-auth-library', () => ({
  OAuth2Client: vi.fn(() => ({
    verifyIdToken: vi.fn(),
  })),
}));

vi.mock('#src/modules/user/repository/index.js', () => ({
  UserRepository: {
    findUser: vi.fn(),
    findById: vi.fn(),
  },
  UserSSOAccountRepository: {
    findOne: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
  },
  UserMfaSettingRepository: {
    findByUserId: vi.fn(),
    update: vi.fn(),
  },
}));

vi.mock('#src/modules/core/repository/index.js', () => ({
  EntityRepository: {
    getEntity: vi.fn(),
  },
}));

vi.mock('#src/modules/user/errors/index.js', () => ({
  AuthError: {
    invalidCredentials: vi.fn(() => new Error('Invalid credentials')),
    invalidAccessId: vi.fn(() => new Error('Invalid access ID')),
    forbidden: vi.fn(() => new Error('Forbidden')),
    ssoAccountPending: vi.fn(() => new Error('SSO account pending')),
    googleAuthError: vi.fn(() => new Error('Google authentication error')),
    invalid2FAToken: vi.fn(() => new Error('Invalid 2FA token')),
    twoFactorNotSetup: vi.fn(() => new Error('Two factor authentication not enabled')),
  },
}));

vi.mock('#src/modules/core/errors/index.js', () => ({
  CoreError: {
    dataNotFound: vi.fn(() => new Error('Invalid credentials')),
  },
}));

vi.mock('speakeasy', () => ({
  default: {
    generateSecret: vi.fn(),
    otpauthURL: vi.fn(),
    totp: {
      verify: vi.fn(),
    },
  },
}));

vi.mock('qrcode', () => ({
  toDataURL: vi.fn(),
}));

vi.mock('#src/utils/cache.util.js', () => ({
  generateCacheKey: vi.fn(),
  fetchFromCache: vi.fn(),
  clearCacheWithPrefix: vi.fn(),
  setCacheWithTTL: vi.fn(),
}));

vi.mock('#src/modules/user/repository/index.js', () => ({
  UserRepository: {
    findUser: vi.fn(),
    findById: vi.fn(),
  },
  UserSSOAccountRepository: {
    findOne: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
  },
  UserMfaSettingRepository: {
    findByUserId: vi.fn(),
    update: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/repository/index.js', () => ({
  SettingRepository: {
    getSingleSetting: vi.fn(),
  },
}));

describe('Auth Service', () => {
  let mockRequest;
  let mockUser;
  let mockServer;
  let mockEntityData;

  beforeEach(() => {
    vi.clearAllMocks();

    mockUser = {
      id: '123',
      username: 'testuser',
      email: '<EMAIL>',
      ua: [
        {
          id: 'assoc-456',
          userId: '123',
          roleId: '789',
          entity: {
            id: '456',
            hierarchy: 'organisation',
            code: 'PRJ001',
            prefix: 'PRJ',
            name: 'User Project',
          },
        },
      ],
      toJSON: vi.fn(() => ({
        id: '123',
        username: 'testuser',
        email: '<EMAIL>',
      })),
    };

    mockEntityData = {
      hasAccess: true,
      userAssociation: {
        id: 'assoc-456',
        userId: '123',
        roleId: '789',
        entity: {
          id: '456',
          hierarchy: 'main',
          code: 'PRJ001',
          prefix: 'PRJ',
          name: 'User Project',
        },
      },
      targetEntity: {
        id: '789',
        hierarchy: 'target',
        code: 'TGT001',
        prefix: 'TGT',
        name: 'Target Project',
        accessId: 'target-access-123',
        parent: {
          id: '999',
          hierarchy: 'parent',
          code: 'PRJ000',
          prefix: 'PRJ',
          name: 'Parent Project',
        },
      },
      parentEntity: {
        id: '999',
        hierarchy: 'parent',
        code: 'PRJ000',
        prefix: 'PRJ',
        name: 'Parent Project',
      },
    };

    mockServer = {
      jwt: {
        sign: vi.fn(() => 'mock-jwt-token'),
      },
      psql: {
        Entity: {
          findOne: vi.fn(),
          findAll: vi.fn(),
          create: vi.fn(),
          count: vi.fn(),
        },
      },
      redis: {
        get: vi.fn(),
        setex: vi.fn(),
        db3: {
          setex: vi.fn(),
          get: vi.fn(),
          del: vi.fn(),
        },
      },
    };
    mockRequest = {
      body: {
        username: 'testuser',
        password: 'password123',
        accessId: 'access123',
      },
      server: mockServer,
      ip: '127.0.0.1',
      headers: {
        fingerprint: 'fingerprint123',
      },
      riskLevel: CoreConstant.HCAPTCHA_RISK_LEVELS.LOW,
      headers: {
        fingerprint: 'fingerprint123',
      },
    };

    UserRepository.findUser.mockResolvedValue(mockUser);
    AuthValidations.checkIpRateLimiting.mockResolvedValue(undefined);
    AuthValidations.checkSystemStatus.mockResolvedValue(undefined);
    AuthValidations.validateAccessAndGetEntityData.mockResolvedValue(mockEntityData);
    AuthValidations.validateUserStatus.mockReturnValue(undefined);
    AuthValidations.validatePassword.mockResolvedValue(undefined);
    signJWT.mockReturnValue('mock-jwt-token');

    EntityRepository.getEntity.mockImplementation((server, criteria) => {
      if (criteria?.accessId) {
        return Promise.resolve({
          id: 'entity-123',
          parentId: 'parent-456',
        });
      }
      if (criteria?.id === 'parent-456') {
        return Promise.resolve({
          id: 'parent-456',
          parentId: null,
        });
      }
      return Promise.resolve(null);
    });
  });

  describe('login', () => {
    it('should successfully authenticate a user and return token and user data', async () => {
      const result = await authService.login(mockRequest);

      expect(AuthValidations.checkIpRateLimiting).toHaveBeenCalledWith(mockServer, mockRequest.ip);
      expect(AuthValidations.checkSystemStatus).toHaveBeenCalledWith(mockServer, mockUser);

      expect(UserRepository.findUser).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          filter_username_eq: 'testuser',
          'filter_ua.origin_eq': 'internal',
        }),
        true,
      );

      expect(AuthValidations.validatePassword).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        mockRequest.body.password,
        mockRequest.ip,
      );
      expect(AuthValidations.validateUserStatus).toHaveBeenCalledWith(mockUser);
      expect(AuthValidations.validateAccessAndGetEntityData).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        mockRequest.body.accessId,
      );

      expect(mockUser.toJSON).toHaveBeenCalled();

      expect(result).toEqual({
        token: 'mock-jwt-token',
        user: expect.any(Object),
        accessId: 'target-access-123',
      });
    });

    it('should handle login without accessId (internal user)', async () => {
      delete mockRequest.body.accessId;

      const result = await authService.login(mockRequest);

      expect(EntityRepository.getEntity).not.toHaveBeenCalled();

      expect(UserRepository.findUser).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          filter_username_eq: 'testuser',
          'filter_ua.origin_eq': expect.any(String),
        }),
        true,
      );

      expect(result).toEqual({
        token: 'mock-jwt-token',
        user: expect.any(Object),
        accessId: 'target-access-123',
      });
    });

    it('should handle single entity hierarchy (no parent)', async () => {
      EntityRepository.getEntity.mockImplementation((server, criteria) => {
        if (criteria?.accessId) {
          return Promise.resolve({
            id: 'single-entity',
            parentId: null,
          });
        }
        return Promise.resolve(null);
      });

      await authService.login(mockRequest);

      expect(UserRepository.findUser).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          filter_username_eq: 'testuser',
          'filter_ua.origin_eq': 'internal',
        }),
        true,
      );
    });

    it('should handle complex entity hierarchy', async () => {
      EntityRepository.getEntity.mockImplementation((server, criteria) => {
        if (criteria?.accessId) {
          return Promise.resolve({ id: 'child', parentId: 'parent' });
        }
        if (criteria?.id === 'parent') {
          return Promise.resolve({ id: 'parent', parentId: 'grandparent' });
        }
        if (criteria?.id === 'grandparent') {
          return Promise.resolve({ id: 'grandparent', parentId: null });
        }
        return Promise.resolve(null);
      });

      await authService.login(mockRequest);

      expect(UserRepository.findUser).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          filter_username_eq: 'testuser',
          'filter_ua.origin_eq': 'internal',
        }),
        true,
      );
    });

    it('should throw an error when user is not found without accessId', async () => {
      delete mockRequest.body.accessId;
      UserRepository.findUser.mockResolvedValue(null);

      await expect(authService.login(mockRequest)).rejects.toThrow();
      expect(AuthError.invalidCredentials).toHaveBeenCalled();
    });

    it('should throw an error when risk level is high', async () => {
      mockRequest.riskLevel = CoreConstant.HCAPTCHA_RISK_LEVELS.HIGH;
      await expect(authService.login(mockRequest)).rejects.toThrow();
      expect(AuthError.forbidden).toHaveBeenCalled();
    });

    it('should throw an error when IP rate limiting fails', async () => {
      const error = new Error('Rate limit exceeded');
      AuthValidations.checkIpRateLimiting.mockRejectedValue(error);

      await expect(authService.login(mockRequest)).rejects.toThrow(error);
    });

    it('should throw an error when system status check fails', async () => {
      const error = new Error('System maintenance');
      AuthValidations.checkSystemStatus.mockRejectedValue(error);

      await expect(authService.login(mockRequest)).rejects.toThrow(error);
    });

    it('should throw an error when entity access validation fails', async () => {
      const error = new Error('No access to entity');
      AuthValidations.validateAccessAndGetEntityData.mockRejectedValue(error);

      await expect(authService.login(mockRequest)).rejects.toThrow(error);
    });

    it('should throw an error when user status validation fails', async () => {
      const error = new Error('User account inactive');
      AuthValidations.validateUserStatus.mockImplementation(() => {
        throw error;
      });

      await expect(authService.login(mockRequest)).rejects.toThrow(error);
    });

    it('should throw an error when password validation fails', async () => {
      const error = new Error('Invalid password');
      AuthValidations.validatePassword.mockRejectedValue(error);

      await expect(authService.login(mockRequest)).rejects.toThrow(error);
    });

    it('should return early with 2FA setup when 2FA is required but not enabled', async () => {
      const mockUserMFA = {
        secretKey: 'JBSWY3DPEHPK3PXP',
        status: UserConstant.USER_MFA_STATUSES.DISABLED,
      };

      const mock2FAResponse = {
        requiresTwoFactor: true,
        userId: mockUser.id,
        qrCode: 'data:image/png;base64,mockQRCode...',
      };

      AuthValidations.validate2FA.mockResolvedValue(mock2FAResponse);
      UserMfaSettingRepository.findByUserId.mockResolvedValue(mockUserMFA);
      AuthValidations.check2FARequirement.mockResolvedValue(mock2FAResponse);

      const result = await authService.login(mockRequest);

      expect(AuthValidations.checkIpRateLimiting).toHaveBeenCalledWith(mockServer, mockRequest.ip);
      expect(UserRepository.findUser).toHaveBeenCalled();
      expect(AuthValidations.checkSystemStatus).toHaveBeenCalledWith(mockServer, mockUser);
      expect(AuthValidations.validatePassword).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        mockRequest.body.password,
        mockRequest.ip,
      );
      expect(AuthValidations.validate2FA).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        mockRequest,
        mockRequest.headers,
      );

      expect(AuthValidations.validateAccessAndGetEntityData).not.toHaveBeenCalled();

      expect(result).toEqual({
        ...mock2FAResponse,
        preAuthToken: 'mock-jwt-token',
      });
    });

    it('should return early with 2FA verification requirement when 2FA is enabled', async () => {
      const mockUserMFA = {
        secretKey: 'JBSWY3DPEHPK3PXP',
        status: UserConstant.USER_MFA_STATUSES.ENABLED,
      };

      const mock2FAResponse = {
        requiresTwoFactor: true,
        userId: mockUser.id,
      };

      AuthValidations.validate2FA.mockResolvedValue(mock2FAResponse);
      UserMfaSettingRepository.findByUserId.mockResolvedValue(mockUserMFA);
      AuthValidations.check2FARequirement.mockResolvedValue(mock2FAResponse);

      const result = await authService.login(mockRequest);

      expect(AuthValidations.checkIpRateLimiting).toHaveBeenCalledWith(mockServer, mockRequest.ip);
      expect(UserRepository.findUser).toHaveBeenCalled();
      expect(AuthValidations.checkSystemStatus).toHaveBeenCalledWith(mockServer, mockUser);
      expect(AuthValidations.validatePassword).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        mockRequest.body.password,
        mockRequest.ip,
      );
      expect(AuthValidations.validateUserStatus).toHaveBeenCalledWith(mockUser);
      expect(AuthValidations.validate2FA).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        mockRequest,
        mockRequest.headers,
      );

      expect(result).toEqual({
        ...mock2FAResponse,
        preAuthToken: 'mock-jwt-token',
      });
    });

    it('should continue normal login flow when 2FA is not required', async () => {
      const mockUserMFA = {
        secretKey: null,
        status: UserConstant.USER_MFA_STATUSES.DISABLED,
      };

      AuthValidations.validate2FA.mockResolvedValue(null);
      UserMfaSettingRepository.findByUserId.mockResolvedValue(mockUserMFA);
      AuthValidations.check2FARequirement.mockResolvedValue(null);

      const result = await authService.login(mockRequest);

      expect(AuthValidations.checkIpRateLimiting).toHaveBeenCalledWith(mockServer, mockRequest.ip);
      expect(UserRepository.findUser).toHaveBeenCalled();
      expect(AuthValidations.checkSystemStatus).toHaveBeenCalledWith(mockServer, mockUser);
      expect(AuthValidations.validatePassword).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        mockRequest.body.password,
        mockRequest.ip,
      );
      expect(AuthValidations.validateUserStatus).toHaveBeenCalledWith(mockUser);
      expect(AuthValidations.validate2FA).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        mockRequest,
        mockRequest.headers,
      );
      expect(AuthValidations.validateAccessAndGetEntityData).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        mockRequest.body.accessId,
      );

      expect(result).toEqual({
        token: 'mock-jwt-token',
        user: expect.any(Object),
        accessId: 'target-access-123',
      });
    });
  });

  describe('google', () => {
    let mockGoogleRequest;
    let mockGoogleUserInfo;
    let mockTicket;
    let mockOAuthClient;

    beforeEach(() => {
      vi.clearAllMocks();

      mockGoogleUserInfo = {
        email: '<EMAIL>',
        sub: 'google-sub-123',
        email_verified: true,
        iss: 'accounts.google.com',
        exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour in the future
        iat: Math.floor(Date.now() / 1000) - 60, // 1 minute in the past
        hd: 'example.com', // Email domain
      };

      mockTicket = {
        getPayload: vi.fn(() => mockGoogleUserInfo),
      };

      mockOAuthClient = {
        verifyIdToken: vi.fn().mockResolvedValue(mockTicket),
      };

      OAuth2Client.mockImplementation(() => mockOAuthClient);

      mockGoogleRequest = {
        body: {
          googleToken: 'mock-google-token',
        },
        server: {
          ...mockServer,
          log: {
            error: vi.fn(),
          },
        },
        ip: '127.0.0.1',
        headers: {
          fingerprint: 'fingerprint123',
        },
      };

      UserRepository.findUser.mockResolvedValue(mockUser);
      UserSSOAccountRepository.findOne.mockResolvedValue(null);
      UserSSOAccountRepository.create.mockImplementation((server, data) => ({
        id: 'new-sso-account-id',
        ...data,
      }));
      UserSSOAccountRepository.update.mockResolvedValue(true);
    });

    it('should successfully authenticate a user with Google and return token and user data', async () => {
      const result = await authService.google(mockGoogleRequest);

      expect(AuthValidations.checkIpRateLimiting).toHaveBeenCalledWith(
        mockGoogleRequest.server,
        mockGoogleRequest.ip,
      );

      expect(OAuth2Client).toHaveBeenCalledWith('************.apps.googleusercontent.com');
      expect(mockOAuthClient.verifyIdToken).toHaveBeenCalledWith({
        idToken: mockGoogleRequest.body.googleToken,
        audience: '************.apps.googleusercontent.com',
      });

      expect(AuthValidations.checkSystemStatus).toHaveBeenCalledWith(
        mockGoogleRequest.server,
        mockUser,
      );

      expect(AuthValidations.validateUserStatus).toHaveBeenCalledWith(mockUser);

      expect(UserSSOAccountRepository.create).toHaveBeenCalledWith(
        mockGoogleRequest.server,
        expect.objectContaining({
          userId: mockUser.id,
          sub: mockGoogleUserInfo.sub,
          email: mockGoogleUserInfo.email,
          status: UserConstant.USER_SSO_STATUSES.COMPLETED,
        }),
      );

      expect(result).toEqual({
        token: 'mock-jwt-token',
        user: expect.any(Object),
        accessId: 'target-access-123',
      });
      expect(mockUser.toJSON).toHaveBeenCalled();
    });

    it('should handle new user registration when user does not exist', async () => {
      UserRepository.findUser.mockResolvedValue(null);

      const result = await authService.google(mockGoogleRequest);

      expect(UserSSOAccountRepository.create).toHaveBeenCalledWith(
        mockGoogleRequest.server,
        expect.objectContaining({
          sub: mockGoogleUserInfo.sub,
          email: mockGoogleUserInfo.email,
          status: UserConstant.USER_SSO_STATUSES.PENDING,
        }),
      );

      expect(result).toEqual({
        status: UserConstant.USER_SSO_STATUSES.PENDING,
        ssoAccountId: 'new-sso-account-id',
      });
    });

    it('should throw an error when user does not exist but SSO account is pending', async () => {
      UserRepository.findUser.mockResolvedValue(null);
      UserSSOAccountRepository.findOne.mockResolvedValue({
        id: 'existing-sso-id',
        provider: 'google',
        sub: mockGoogleUserInfo.sub,
        status: UserConstant.USER_SSO_STATUSES.PENDING,
      });

      await expect(authService.google(mockGoogleRequest)).rejects.toThrow();
      expect(AuthError.ssoAccountPending).toHaveBeenCalled();
    });

    it('should link existing SSO account to user if not already linked', async () => {
      const existingSSOAccount = {
        id: 'existing-sso-id',
        provider: 'google',
        sub: mockGoogleUserInfo.sub,
        user_id: null,
        status: UserConstant.USER_SSO_STATUSES.PENDING,
      };

      const now = new Date();

      // Mock date to ensure resolved date is same in actual function during test
      vi.useFakeTimers();
      vi.setSystemTime(now);

      UserSSOAccountRepository.findOne.mockResolvedValue(existingSSOAccount);

      await authService.google(mockGoogleRequest);

      expect(UserSSOAccountRepository.update).toHaveBeenCalledWith(
        existingSSOAccount,
        expect.objectContaining({
          userId: mockUser.id,
          status: UserConstant.USER_SSO_STATUSES.COMPLETED,
        }),
      );

      // Reset back to original
      vi.useRealTimers();
    });

    it('should not update SSO account if already linked to user', async () => {
      const existingSSOAccount = {
        id: 'existing-sso-id',
        provider: 'google',
        sub: mockGoogleUserInfo.sub,
        user_id: mockUser.id,
        status: UserConstant.USER_SSO_STATUSES.COMPLETED,
      };

      UserSSOAccountRepository.findOne.mockResolvedValue(existingSSOAccount);

      await authService.google(mockGoogleRequest);

      expect(UserSSOAccountRepository.update).not.toHaveBeenCalled();
    });

    it('should throw an error when Google token payload is missing email', async () => {
      mockTicket.getPayload.mockReturnValue({
        sub: 'google-sub-123',
        iss: 'accounts.google.com',
        email_verified: true,
        exp: Math.floor(Date.now() / 1000) + 3600,
        iat: Math.floor(Date.now() / 1000) - 60,
      });

      await expect(authService.google(mockGoogleRequest)).rejects.toThrow();
      expect(AuthError.googleAuthError).toHaveBeenCalled();
    });

    it('should throw an error when Google token payload is missing sub', async () => {
      mockTicket.getPayload.mockReturnValue({
        email: '<EMAIL>',
        iss: 'accounts.google.com',
        email_verified: true,
        exp: Math.floor(Date.now() / 1000) + 3600,
        iat: Math.floor(Date.now() / 1000) - 60,
      });

      await expect(authService.google(mockGoogleRequest)).rejects.toThrow();
      expect(AuthError.googleAuthError).toHaveBeenCalled();
    });

    it('should throw an error when Google token has invalid issuer', async () => {
      mockTicket.getPayload.mockReturnValue({
        ...mockGoogleUserInfo,
        iss: 'invalid-issuer.com',
      });

      await expect(authService.google(mockGoogleRequest)).rejects.toThrow();
      expect(AuthError.googleAuthError).toHaveBeenCalled();
    });

    it('should throw an error when Google token email is not verified', async () => {
      mockTicket.getPayload.mockReturnValue({
        ...mockGoogleUserInfo,
        email_verified: false,
      });

      await expect(authService.google(mockGoogleRequest)).rejects.toThrow();
      expect(AuthError.googleAuthError).toHaveBeenCalled();
    });

    it('should throw an error when IP rate limiting fails', async () => {
      const error = new Error('Rate limit exceeded');
      AuthValidations.checkIpRateLimiting.mockRejectedValue(error);

      await expect(authService.google(mockGoogleRequest)).rejects.toThrow(error);
    });

    it('should throw an error when system status check fails', async () => {
      const error = new Error('System maintenance');
      AuthValidations.checkSystemStatus.mockRejectedValue(error);

      await expect(authService.google(mockGoogleRequest)).rejects.toThrow(error);
    });

    it('should throw an error when user status validation fails', async () => {
      const error = new Error('User account inactive');
      AuthValidations.validateUserStatus.mockImplementation(() => {
        throw error;
      });

      await expect(authService.google(mockGoogleRequest)).rejects.toThrow(error);
    });

    it('should include email domain in SSO account creation when available', async () => {
      UserRepository.findUser.mockResolvedValue(null);

      await authService.google(mockGoogleRequest);

      expect(UserSSOAccountRepository.create).toHaveBeenCalledWith(
        mockGoogleRequest.server,
        expect.objectContaining({
          sub: mockGoogleUserInfo.sub,
          email: mockGoogleUserInfo.email,
          status: UserConstant.USER_SSO_STATUSES.PENDING,
        }),
      );
    });

    it('should throw googleAuthError when token verification fails', async () => {
      const verificationError = new Error('Invalid token format');
      mockOAuthClient.verifyIdToken.mockRejectedValue(verificationError);

      await expect(authService.google(mockGoogleRequest)).rejects.toThrow();

      expect(mockOAuthClient.verifyIdToken).toHaveBeenCalledWith({
        idToken: mockGoogleRequest.body.googleToken,
        audience: '************.apps.googleusercontent.com',
      });

      expect(AuthError.googleAuthError).toHaveBeenCalledWith({
        message: verificationError.message,
      });

      expect(UserRepository.findUser).not.toHaveBeenCalled();
      expect(UserSSOAccountRepository.findOne).not.toHaveBeenCalled();
    });
  });

  describe('generateAuthToken', () => {
    it('should generate a token with correct payload structure', async () => {
      await authService.login(mockRequest);

      expect(signJWT).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          sub: mockUser.id,
          iat: expect.any(Number),
          exp: expect.any(Number),
          basicInformation: expect.objectContaining({
            authInfo: expect.objectContaining({
              id: mockUser.id,
              authAccess: 'user',
              username: mockUser.username,
              roleId: mockEntityData.userAssociation.roleId,
              fingerprintId: mockRequest.headers.fingerprint,
            }),
            userEntity: expect.objectContaining({
              id: mockEntityData.userAssociation.entity.id,
              hierarchy: mockEntityData.userAssociation.entity.hierarchy,
              code: mockEntityData.userAssociation.entity.code,
              prefix: mockEntityData.userAssociation.entity.prefix,
              name: mockEntityData.userAssociation.entity.name,
            }),
            parentEntity: expect.objectContaining({
              id: mockEntityData.targetEntity.parent.id,
              hierarchy: mockEntityData.targetEntity.parent.hierarchy,
              code: mockEntityData.targetEntity.parent.code,
              prefix: mockEntityData.targetEntity.parent.prefix,
              name: mockEntityData.targetEntity.parent.name,
            }),
            entity: expect.objectContaining({
              id: mockEntityData.targetEntity.id,
              hierarchy: mockEntityData.targetEntity.hierarchy,
              code: mockEntityData.targetEntity.code,
              prefix: mockEntityData.targetEntity.prefix,
              name: mockEntityData.targetEntity.name,
            }),
          }),
        }),
      );
    });

    it('should handle null parent entity', async () => {
      mockEntityData.targetEntity.parent = null;
      mockEntityData.parentEntity = null;

      await authService.login(mockRequest);

      expect(signJWT).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          basicInformation: expect.objectContaining({
            parentEntity: null,
          }),
        }),
      );
    });

    it('should handle null targetEntity', async () => {
      mockEntityData.userAssociation.entity = null;

      await authService.login(mockRequest);

      expect(signJWT).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          basicInformation: expect.objectContaining({
            userEntity: null,
          }),
        }),
      );
    });

    it('should handle null targetEntity', async () => {
      mockEntityData.targetEntity = null;

      await authService.login(mockRequest);

      expect(signJWT).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          basicInformation: expect.objectContaining({
            entity: null,
            parentEntity: null,
          }),
        }),
      );
    });

    it('should handle null userEntity', async () => {
      mockEntityData.userAssociation.entity = null;

      await authService.login(mockRequest);

      expect(signJWT).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          basicInformation: expect.objectContaining({
            userEntity: null,
          }),
        }),
      );
    });

    it('should include correct expiration time using AuthConstant.TOKEN_EXPIRY_TIME', async () => {
      const now = 1609459200000; // Fixed timestamp
      const realDateNow = Date.now;
      global.Date.now = vi.fn(() => now);

      await authService.login(mockRequest);

      global.Date.now = realDateNow;

      const expectedIat = Math.floor(now / 1000);
      const expectedExp = expectedIat + AuthConstant.EXPIRY_TIMES.TOKEN;

      expect(signJWT).toHaveBeenCalledWith(
        mockServer,
        expect.objectContaining({
          iat: expectedIat,
          exp: expectedExp,
        }),
      );
    });
  });

  describe('setup2fa', () => {
    let mockRequest;
    let mockUser;

    beforeEach(() => {
      vi.clearAllMocks();

      mockUser = {
        id: '123',
        username: 'testuser',
        mfa: {
          secretKey: 'JBSWY3DPEHPK3PXP',
          status: UserConstant.USER_MFA_STATUSES.DISABLED,
        },
        ua: [
          {
            entity: {
              id: 'entity-1',
              name: 'YourApp',
              parent: { id: 'parent-1' },
            },
          },
        ],
      };

      mockRequest = {
        server: mockServer,
        authInfo: { id: '123' },
      };

      UserRepository.findById.mockResolvedValue(mockUser);
      speakeasy.otpauthURL.mockReturnValue(
        'otpauth://totp/YourApp%20(testuser)?secret=JBSWY3DPEHPK3PXP&issuer=YourApp',
      );
      toDataURL.mockResolvedValue('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...');
    });

    it('should successfully generate QR code for 2FA setup', async () => {
      const result = await authService.setup2fa(mockRequest);

      expect(UserRepository.findById).toHaveBeenCalledWith(mockServer, '123');
      expect(speakeasy.otpauthURL).toHaveBeenCalledWith({
        secret: 'JBSWY3DPEHPK3PXP',
        label: 'testuser',
        issuer: 'QPLY2.0 YourApp',
        encoding: 'base32',
      });
      expect(toDataURL).toHaveBeenCalledWith(
        'otpauth://totp/YourApp%20(testuser)?secret=JBSWY3DPEHPK3PXP&issuer=YourApp',
      );

      expect(result).toEqual({ qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...' });
    });

    it('should throw error when user is not found', async () => {
      UserRepository.findById.mockResolvedValue(null);

      await expect(authService.setup2fa(mockRequest)).rejects.toThrow();
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.user',
        attribute: 'ID',
        value: '123',
      });
    });

    it('should throw error when 2FA is already setup', async () => {
      mockUser.mfa.setupStatus = UserConstant.USER_MFA_SETUP_STATUSES.ACTIVE;

      if (!AuthError.twoFactorAlreadySetup) {
        AuthError.twoFactorAlreadySetup = vi.fn(() => new Error('2FA already setup'));
      }

      await expect(authService.setup2fa(mockRequest)).rejects.toThrow();
      expect(AuthError.twoFactorAlreadySetup).toHaveBeenCalled();
    });
  });

  describe('verify2faLogin', () => {
    let mockRequest;
    let mockUser;
    let mockUserMFA;

    beforeEach(() => {
      vi.clearAllMocks();

      mockUser = {
        id: '123',
        mfa: {
          secretKey: 'JBSWY3DPEHPK3PXP',
          isEnabled: false,
        },
        toJSON: vi.fn(() => ({ id: '123' })),
      };
      mockUserMFA = { secretKey: 'JBSWY3DPEHPK3PXP', isEnabled: false };

      mockRequest = {
        body: { userId: '123', token: '123456', accessId: 'access123' },
        server: mockServer,
        headers: { fingerprint: 'fingerprint123' },
      };

      UserRepository.findById.mockResolvedValue(mockUser);
      UserMfaSettingRepository.findByUserId.mockResolvedValue(mockUserMFA);
      UserMfaSettingRepository.update.mockResolvedValue(true);
      AuthValidations.validateAccessAndGetEntityData.mockResolvedValue(mockEntityData);
      AuthValidations.validatePreAuthToken.mockResolvedValue({
        userId: '123',
        accessId: 'access123',
      });
    });

    it('should verify 2FA token successfully', async () => {
      speakeasy.totp.verify.mockReturnValue(true);

      await authService.verify2faLogin(mockRequest);

      expect(UserRepository.findById).toHaveBeenCalledWith(mockServer, '123');
      expect(speakeasy.totp.verify).toHaveBeenCalledWith({
        secret: 'JBSWY3DPEHPK3PXP',
        encoding: 'base32',
        token: '123456',
        window: 2,
      });
      expect(UserMfaSettingRepository.update).toHaveBeenCalledWith(mockUser.mfa, {
        setupStatus: 'active',
      });
      expect(AuthValidations.validateAccessAndGetEntityData).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        'access123',
      );
    });

    it('should not update MFA when already enabled', async () => {
      mockUser.mfa.isEnabled = true;
      speakeasy.totp.verify.mockReturnValue(true);

      await authService.verify2faLogin(mockRequest);

      expect(UserMfaSettingRepository.update).not.toHaveBeenCalled();
    });

    it('should throw error when user not found', async () => {
      UserRepository.findById.mockResolvedValue(null);

      await expect(authService.verify2faLogin(mockRequest)).rejects.toThrow();
      expect(CoreError.dataNotFound).toHaveBeenCalled();
    });

    it('should throw error when 2FA token is invalid', async () => {
      speakeasy.totp.verify.mockReturnValue(false);

      await expect(authService.verify2faLogin(mockRequest)).rejects.toThrow();
      expect(AuthError.invalid2FAToken).toHaveBeenCalled();
    });

    it('should handle totp.verify returning undefined', async () => {
      speakeasy.totp.verify.mockReturnValue(undefined);

      await expect(authService.verify2faLogin(mockRequest)).rejects.toThrow();
      expect(AuthError.invalid2FAToken).toHaveBeenCalled();
    });

    it('should include setCookie in response when trustDevice is true and fingerprint exists', async () => {
      speakeasy.totp.verify.mockReturnValue(true);
      mockRequest.body.trustDevice = true;

      // Mock the actual createTrustedDeviceCookie to return a value
      // You'll need to ensure fetchFromCache and other dependencies are mocked
      fetchFromCache.mockResolvedValue({ customSettings: { value: 7 } });

      const result = await authService.verify2faLogin(mockRequest);

      // Test the result, not the call
      expect(result.setCookie).toBeDefined();
      expect(result.setCookie.name).toBe('trusted_device');
      expect(result.setCookie.value).toBeDefined();
    });

    it('should not include setCookie when trustDevice is false', async () => {
      speakeasy.totp.verify.mockReturnValue(true);
      mockRequest.body.trustDevice = false;

      const result = await authService.verify2faLogin(mockRequest);

      expect(result.setCookie).toBeUndefined();
    });

    it('should not include setCookie when fingerprint is missing', async () => {
      speakeasy.totp.verify.mockReturnValue(true);
      mockRequest.body.trustDevice = true;
      delete mockRequest.headers.fingerprint;

      const result = await authService.verify2faLogin(mockRequest);

      expect(result.setCookie).toBeUndefined();
    });
  });

  describe('reset2fa', () => {
    let mockRequest;
    let mockUserMfa;

    beforeEach(() => {
      vi.clearAllMocks();

      mockUser = {
        id: 'user-123',
        ua: [
          {
            entityId: 'beb9aa2c-09fd-11f0-ba09-df67e743a9a2',
          },
        ],
      };

      mockUserMfa = {
        id: 'userMfa-123',
        userId: 'user-123',
        setupStatus: UserConstant.USER_MFA_SETUP_STATUSES.ACTIVE,
        secretKey: 'JBSWY3DPEHPK3PXP',
      };

      mockRequest = {
        body: {
          userId: 'user-123',
        },
        server: mockServer,
        authInfo: {
          id: 'user-123',
        },
        entity: {
          id: 'beb9aa2c-09fd-11f0-ba09-df67e743a9a2',
        },
      };

      UserRepository.findById.mockResolvedValue(mockUser);
      UserMfaSettingRepository.findByUserId.mockResolvedValue(mockUserMfa);
      UserMfaSettingRepository.update.mockResolvedValue(true);
      speakeasy.generateSecret.mockReturnValue({ base32: 'JBSWY3DPEHPK3PXP' });

      vi.spyOn(authService, 'revokeTrustedDevices').mockResolvedValue({
        success: true,
        message: 'All trusted devices revoked successfully',
      });
    });

    afterEach(() => {
      // This will restore all spies and mocks
      vi.restoreAllMocks();
    });

    it('should throw CoreError.dataNotFound when user MFA setting is not found', async () => {
      UserMfaSettingRepository.findByUserId.mockResolvedValue(null);

      await expect(authService.reset2fa(mockRequest)).rejects.toThrow();

      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.userMfaSetting',
        attribute: 'User ID',
        value: 'user-123',
      });

      expect(UserMfaSettingRepository.update).not.toHaveBeenCalled();
      expect(authService.revokeTrustedDevices).not.toHaveBeenCalled();
    });

    it('should throw CoreError.dataNotFound when user does not have access to entity', async () => {
      // Mock user with different entity access
      const userWithDifferentEntity = {
        id: 'user-123',
        ua: [
          {
            entityId: 'different-entity-id',
          },
        ],
      };

      UserRepository.findById.mockResolvedValue(userWithDifferentEntity);

      await expect(authService.reset2fa(mockRequest)).rejects.toThrow();

      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.user',
        attribute: 'User ID',
        value: 'user-123',
      });

      expect(UserMfaSettingRepository.update).not.toHaveBeenCalled();
      expect(authService.revokeTrustedDevices).not.toHaveBeenCalled();
    });

    it('should throw AuthError.twoFactorNotSetup when userMfa has no setupStatus property', () => {
      const userMfa = {};

      expect(() => {
        if (userMfa?.setupStatus !== USER_MFA_SETUP_STATUSES.ACTIVE) {
          throw AuthError.twoFactorNotSetup();
        }
      }).toThrow();
    });
  });

  describe('revokeTrustedDevices', () => {
    let mockRequest;
    let mockRedis;

    beforeEach(() => {
      vi.clearAllMocks();

      mockRedis = {
        db3: {
          setex: vi.fn(),
          get: vi.fn(),
          del: vi.fn(),
        },
      };

      mockRequest = {
        server: {
          ...mockServer,
          redis: mockRedis,
        },
        authInfo: {
          id: 'user-123',
        },
      };

      // Mock clearCacheWithPrefix
      clearCacheWithPrefix.mockResolvedValue();
    });

    it('should successfully revoke all trusted devices', async () => {
      const mockClearResult = {
        setCookie: {
          name: 'trusted_device',
          options: {
            expires: new Date(0),
            httpOnly: true,
            maxAge: 0,
            sameSite: 'none',
            secure: true,
          },
          value: '',
        },
      };
      const result = await authService.revokeTrustedDevices(mockRequest);
      expect(clearCacheWithPrefix).toHaveBeenCalledWith(mockRedis.db3, 'trusted_device:user-123');
      expect(result).toEqual(mockClearResult);
    });

    it('should handle different user IDs correctly', async () => {
      mockRequest.authInfo.id = 'different-user-456';

      await authService.revokeTrustedDevices(mockRequest);

      expect(clearCacheWithPrefix).toHaveBeenCalledWith(
        mockRedis.db3,
        'trusted_device:different-user-456',
      );
    });

    it('should handle clearCacheWithPrefix errors gracefully', async () => {
      const cacheError = new Error('Cache clear failed');
      clearCacheWithPrefix.mockRejectedValue(cacheError);

      await expect(authService.revokeTrustedDevices(mockRequest)).rejects.toThrow(
        'Cache clear failed',
      );

      expect(clearCacheWithPrefix).toHaveBeenCalledWith(mockRedis.db3, 'trusted_device:user-123');
    });

    it('should use correct cache prefix format', async () => {
      const testUserId = 'test-user-789';
      mockRequest.authInfo.id = testUserId;

      await authService.revokeTrustedDevices(mockRequest);

      expect(clearCacheWithPrefix).toHaveBeenCalledWith(
        mockRedis.db3,
        `trusted_device:${testUserId}`,
      );
    });

    it('should call clearCacheWithPrefix with redis.db3 instance', async () => {
      await authService.revokeTrustedDevices(mockRequest);

      expect(clearCacheWithPrefix).toHaveBeenCalledWith(mockRedis.db3, expect.any(String));
    });

    it('should return the result from clearCacheWithPrefix', async () => {
      const mockClearResult = {
        setCookie: {
          name: 'trusted_device',
          options: {
            expires: new Date(0),
            httpOnly: true,
            maxAge: 0,
            sameSite: 'none',
            secure: true,
          },
          value: '',
        },
      };
      clearCacheWithPrefix.mockResolvedValue(mockClearResult);

      const result = await authService.revokeTrustedDevices(mockRequest);

      expect(result).toEqual(mockClearResult);
    });
  });

  describe('createTrustedDeviceCookie', () => {
    let mockServer;
    let mockRedis;
    let mockSettingRepository;

    beforeEach(() => {
      vi.clearAllMocks();

      mockRedis = {
        get: vi.fn(),
        setex: vi.fn(),
        db3: {
          setex: vi.fn(),
        },
      };

      mockServer = {
        redis: mockRedis,
        psql: {
          Setting: { findOne: vi.fn() },
        },
      };

      mockSettingRepository = {
        getSingleSetting: vi.fn(),
      };

      SettingRepository.getSingleSetting = mockSettingRepository.getSingleSetting;

      generateCacheKey.mockReturnValue('trusted_device_settings:global');
      signJWT.mockReturnValue('mock-trusted-device-token');
      const fixedTimestamp = 1609459200000;
      vi.spyOn(Date, 'now').mockReturnValue(fixedTimestamp);
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    it('should create trusted device cookie and store token in cache', async () => {
      const mockSettings = {
        customSettings: {
          value: 7,
        },
      };

      fetchFromCache.mockResolvedValue(mockSettings);

      const result = await authService.createTrustedDeviceCookie(
        mockServer,
        'user-123',
        'device-fingerprint-abc',
      );

      expect(generateCacheKey).toHaveBeenCalledWith(
        'trusted_device_settings',
        {
          raw: { url: '/settings/safety/twoFactorSessionTimeoutDays' },
          query: {},
          entity: { id: 'global' },
        },
        { excludeKey: true },
      );

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRedis,
        'trusted_device_settings:global',
        expect.any(Function),
      );

      expect(result).toEqual({
        name: 'trusted_device',
        value: 'mock-trusted-device-token',
        options: {
          httpOnly: true,
          secure: true,
          sameSite: 'none',
          maxAge: 7 * 24 * 60 * 60,
        },
      });
    });

    it('should use AuthConstant.DEFAULT_TRUST_DAYS when setting is not available', async () => {
      fetchFromCache.mockResolvedValue(null);

      const result = await authService.createTrustedDeviceCookie(
        mockServer,
        'user-456',
        'device-fingerprint-xyz',
      );

      expect(result).toEqual({
        name: 'trusted_device',
        value: 'mock-trusted-device-token',
        options: {
          httpOnly: true,
          secure: true,
          sameSite: 'none',
          maxAge: AuthConstant.DEFAULT_TRUST_DAYS * 24 * 60 * 60 * 1000,
        },
      });
    });

    it('should use default trust days when customSettings value is missing', async () => {
      const mockSettings = {
        customSettings: {},
      };

      fetchFromCache.mockResolvedValue(mockSettings);

      const result = await authService.createTrustedDeviceCookie(
        mockServer,
        'user-789',
        'device-fingerprint-123',
      );

      expect(result.options.maxAge).toBe(AuthConstant.DEFAULT_TRUST_DAYS * 24 * 60 * 60 * 1000);
    });

    it('should handle fetchFromCache errors', async () => {
      const cacheError = new Error('Cache fetch failed');
      fetchFromCache.mockRejectedValue(cacheError);

      await expect(
        authService.createTrustedDeviceCookie(mockServer, 'user-123', 'device-fingerprint-abc'),
      ).rejects.toThrow('Cache fetch failed');

      expect(fetchFromCache).toHaveBeenCalled();
    });

    it('should call SettingRepository.getSingleSetting with correct parameters in cache callback', async () => {
      fetchFromCache.mockImplementation(async (redis, key, callback) => {
        return await callback();
      });

      mockSettingRepository.getSingleSetting.mockResolvedValue({
        customSettings: { value: 10 },
      });

      await authService.createTrustedDeviceCookie(mockServer, 'user-123', 'device-fingerprint-abc');

      expect(mockSettingRepository.getSingleSetting).toHaveBeenCalledWith(mockServer, {
        category: 'safety',
        field: 'twoFactorSessionTimeoutDays',
      });
    });
  });

  describe('entity data integration', () => {
    it('should pass correct user and accessId to validateAccessAndGetEntityData', async () => {
      await authService.login(mockRequest);

      expect(AuthValidations.validateAccessAndGetEntityData).toHaveBeenCalledWith(
        mockServer,
        mockUser,
        mockRequest.body.accessId,
      );
    });
  });
});
