import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreError } from '#src/modules/core/errors/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import { UserError } from '#src/modules/user/errors/index.js';
import { UserInvitationRepository } from '#src/modules/user/repository/index.js';
import * as UserInvitationService from '#src/modules/user/services/user-invitation.service.js';
import * as withTxn from '#src/utils/db-transaction.util.js';

const { USER_ORIGINS, USER_INVITATION_STATUSES } = UserConstant;

vi.mock('#src/modules/user/repository/index.js', () => ({
  UserRepository: { findUser: vi.fn() },
  UserInvitationRepository: {
    findById: vi.fn(),
    findAll: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
  },
  UserAssociationRepository: { create: vi.fn() },
  UserAssociationCurrencyRepository: { create: vi.fn() },
}));

vi.mock('#src/utils/db-transaction.util.js', () => ({
  withTransaction: vi.fn(),
}));

describe('UserInvitationService', () => {
  let request;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('index', () => {
    it('should call findAll', async () => {
      const mockResult = { data: [], meta: {} };

      UserInvitationRepository.findAll.mockResolvedValue(mockResult);

      const result = await UserInvitationService.index(request);

      expect(UserInvitationRepository.findAll).toHaveBeenCalledWith(request.server, {
        ...request.query,
        'filter_ua.userId_eq': 'auth-id',
      });

      expect(result).toEqual(mockResult);
    });
  });

  describe('create', () => {
    let findUser, createInvitation, createUA, createCurrencies;

    beforeEach(async () => {
      // Import the functions inside an async beforeEach
      const repository = await import('#src/modules/user/repository/index.js');
      findUser = repository.UserRepository.findUser;
      createInvitation = repository.UserInvitationRepository.create;
      createUA = repository.UserAssociationRepository.create;
      createCurrencies = repository.UserAssociationCurrencyRepository.create;
    });

    request = {
      server: {},
      authInfo: { id: 'auth-id' },
      entity: { id: 'entity-id' },
      query: {},
      body: {
        username: 'testuser',
        currencyIds: ['cur1', 'cur2'],
        roleId: 'role-id',
      },
      params: {
        id: 'inv-id',
      },
    };
    it('should throw CoreError.notFound if user not found', async () => {
      findUser.mockResolvedValue(null);

      await expect(UserInvitationService.create(request)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.user',
          attribute: 'common.label.username',
          value: 'testuser',
        }),
      );
    });

    it('should throw UserError.userEntityConflict if user already associated', async () => {
      findUser.mockResolvedValue({
        id: 'user-id',
        userAssociation: [{ entityId: 'entity-id' }],
      });

      await expect(UserInvitationService.create(request)).rejects.toThrow(
        UserError.userEntityConflict({ username: 'testuser' }),
      );
    });

    it('should create user invitation, association, and currencies in a transaction', async () => {
      findUser.mockResolvedValue({
        id: 'user-id',
        userAssociation: [],
      });

      withTxn.withTransaction.mockImplementation(async (srv, opts, cb) => cb('mockTx'));

      createUA.mockResolvedValue({ id: 'ua-id' });
      createCurrencies.mockResolvedValue(undefined);
      createInvitation.mockResolvedValue({ id: 'inv-id' });

      const result = await UserInvitationService.create(request);

      expect(createUA).toHaveBeenCalledWith(
        request.server,
        expect.objectContaining({
          userId: 'user-id',
          entityId: 'entity-id',
          origin: UserConstant.USER_ORIGINS.EXTERNAL,
          roleId: 'role-id',
        }),
        expect.objectContaining({ transaction: 'mockTx' }),
      );

      expect(createCurrencies).toHaveBeenCalledWith(
        request.server,
        [
          { userAssociationId: 'ua-id', currencyId: 'cur1' },
          { userAssociationId: 'ua-id', currencyId: 'cur2' },
        ],
        expect.objectContaining({ transaction: 'mockTx' }),
      );

      expect(createInvitation).toHaveBeenCalledWith(
        request.server,
        {
          userAssociationId: 'ua-id',
          invitedBy: 'auth-id',
          status: UserConstant.USER_INVITATION_STATUSES.PENDING,
        },
        expect.objectContaining({ transaction: 'mockTx' }),
      );

      expect(result).toEqual({ id: 'inv-id' });
    });
  });

  describe('updateStatus', () => {
    const mockServer = {};
    const mockRequest = {
      server: mockServer,
      params: { id: 'inv-id' },
      body: { status: USER_INVITATION_STATUSES.APPROVED },
      authInfo: { id: 'auth-user-id' },
    };

    it('should update status with acceptedDate if approved', async () => {
      const now = new Date();
      vi.setSystemTime(now);
      const invitation = { id: 'inv-id', status: USER_INVITATION_STATUSES.PENDING };

      UserInvitationRepository.findById.mockResolvedValue(invitation);
      UserInvitationRepository.update.mockResolvedValue({});

      await UserInvitationService.updateStatus(mockRequest);

      expect(UserInvitationRepository.update).toHaveBeenCalledWith(
        invitation,
        expect.objectContaining({
          status: USER_INVITATION_STATUSES.APPROVED,
          acceptedDate: now,
        }),
        expect.objectContaining({ authInfoId: 'auth-user-id' }),
      );
    });
    it('should throw CoreError.notFound if user invitation is not found', async () => {
      const request = {
        body: { status: 'APPROVED' },
        params: { id: 'non-existent-id' },
        server: {},
        authInfo: { id: 'auth-user-id' },
      };

      UserInvitationRepository.findById.mockResolvedValue(null);

      await expect(UserInvitationService.updateStatus(request)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.userInvitation',
          attribute: 'ID',
          value: 'non-existent-id',
        }),
      );

      expect(UserInvitationRepository.findById).toHaveBeenCalledWith(
        request.server,
        request.params.id,
      );
    });
    it('should update status with cancelledDate if cancelled', async () => {
      const now = new Date();
      vi.setSystemTime(now);
      const invitation = { id: 'inv-1', status: USER_INVITATION_STATUSES.PENDING };

      UserInvitationRepository.findById.mockResolvedValue(invitation);
      await UserInvitationService.updateStatus({
        ...request,
        body: { status: 'cancelled' },
      });

      expect(UserInvitationRepository.update).toHaveBeenCalledWith(
        invitation,
        expect.objectContaining({
          status: USER_INVITATION_STATUSES.CANCELLED,
          cancelledDate: now,
        }),
        expect.objectContaining({ authInfoId: 'auth-id' }),
      );
    });

    it('should throw CoreError.unprocessable if status is the same as existing', async () => {
      const invitation = { id: 'inv-1', status: USER_INVITATION_STATUSES.APPROVED };

      UserInvitationRepository.findById.mockResolvedValue(invitation);

      await expect(
        UserInvitationService.updateStatus({
          ...request,
          params: { id: invitation.id },
          body: { status: USER_INVITATION_STATUSES.APPROVED },
        }),
      ).rejects.toThrow(CoreError.unprocessable());

      expect(UserInvitationRepository.findById).toHaveBeenCalledWith(request.server, invitation.id);
      expect(UserInvitationRepository.update).not.toHaveBeenCalled();
    });
  });
});
