import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { UserValidation } from '#src/modules/core/validations/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import {
  UserAssociationRepository,
  UserRepository,
  UserSSOAccountRepository,
} from '#src/modules/user/repository/index.js';
import * as UserSSOAccountService from '#src/modules/user/services/user-sso-account.service.js';
import * as withTxn from '#src/utils/db-transaction.util.js';
import { hashPassword } from '#src/utils/hash.util.js';
import { generate2FASecret } from '#src/utils/twofa.util.js';
vi.mock('#src/modules/user/repository/index.js');
vi.mock('#src/utils/db-transaction.util.js', () => ({
  withTransaction: vi.fn(),
}));

vi.mock('#src/utils/hash.util.js', () => ({
  hashPassword: vi.fn(),
}));

vi.mock('#src/utils/twofa.util.js', () => ({
  generate2FASecret: vi.fn(),
}));
vi.mock('#src/modules/core/validations/index.js', () => ({
  UserValidation: {
    validateEmail: vi.fn(),
    validateUsername: vi.fn(),
  },
}));

const { USER_SSO_STATUSES } = UserConstant;

describe('UserSSOAccountService', () => {
  let request;

  beforeEach(() => {
    vi.clearAllMocks();

    request = {
      server: {},
      authInfo: { id: 'auth-id' },
      params: { id: 'sso-id' },
      body: {},
      query: {},
    };
  });

  describe('index', () => {
    it('should call UserSSOAccountRepository.findAll', async () => {
      const mockResult = { data: [], meta: {} };
      UserSSOAccountRepository.findAll.mockResolvedValue(mockResult);

      const result = await UserSSOAccountService.index(request);
      expect(UserSSOAccountRepository.findAll).toHaveBeenCalledWith(request.server, request.query);
      expect(result).toEqual(mockResult);
    });
  });

  describe('updateStatus', () => {
    const request = {
      body: { status: USER_SSO_STATUSES.COMPLETED },
      params: { id: 'sso-id' },
      server: {},
      authInfo: { id: 'auth-id' },
    };

    afterEach(() => {
      vi.clearAllMocks();
      vi.useRealTimers();
    });

    it('should update status with correct completedDate', async () => {
      const now = new Date('2025-01-01T00:00:00Z');
      vi.useFakeTimers().setSystemTime(now);

      const existingSSO = { id: 'sso-id', status: 'PENDING' };
      UserSSOAccountRepository.findById.mockResolvedValue(existingSSO);
      UserSSOAccountRepository.update.mockResolvedValue();

      await UserSSOAccountService.updateStatus(request);

      expect(UserSSOAccountRepository.update).toHaveBeenCalledWith(
        existingSSO,
        expect.objectContaining({
          status: USER_SSO_STATUSES.COMPLETED,
          completedDate: now,
        }),
        { authInfoId: 'auth-id' },
      );
    });

    it('should update status with correct cancelledDate', async () => {
      const now = new Date('2025-01-01T00:00:00Z');
      vi.useFakeTimers().setSystemTime(now);

      const cancelledRequest = {
        ...request,
        body: { status: USER_SSO_STATUSES.CANCELLED },
      };

      const existingSSO = { id: 'sso-id', status: 'PENDING' };
      UserSSOAccountRepository.findById.mockResolvedValue(existingSSO);
      UserSSOAccountRepository.update.mockResolvedValue();

      await UserSSOAccountService.updateStatus(cancelledRequest);

      expect(UserSSOAccountRepository.update).toHaveBeenCalledWith(
        existingSSO,
        expect.objectContaining({
          status: USER_SSO_STATUSES.CANCELLED,
          cancelledDate: now,
        }),
        { authInfoId: 'auth-id' },
      );
    });

    it('should throw if UserSSO not found', async () => {
      UserSSOAccountRepository.findById.mockResolvedValue(null);

      await expect(UserSSOAccountService.updateStatus(request)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.userSSO',
          attribute: 'ID',
          value: 'sso-id',
        }),
      );
    });

    it('should throw if status is unchanged', async () => {
      const existingSSO = { id: 'sso-id', status: USER_SSO_STATUSES.COMPLETED };
      UserSSOAccountRepository.findById.mockResolvedValue(existingSSO);

      await expect(UserSSOAccountService.updateStatus(request)).rejects.toThrow(
        CoreError.unprocessable(),
      );
    });
  });

  describe('assign', () => {
    it('should assign user to SSO and update user email', async () => {
      const user = { id: 'user-id' };
      const sso = { id: 'sso-id', email: '<EMAIL>' };
      request.body.username = 'testuser';

      UserRepository.findUser.mockResolvedValue(user);
      UserSSOAccountRepository.findById.mockResolvedValue(sso);
      withTxn.withTransaction.mockImplementation(async (srv, opts, cb) => cb('tx'));

      await UserSSOAccountService.assign(request);

      expect(UserSSOAccountRepository.update).toHaveBeenCalledWith(
        sso,
        expect.objectContaining({
          userId: user.id,
          status: USER_SSO_STATUSES.COMPLETED,
        }),
        expect.objectContaining({ transaction: 'tx', authInfoId: 'auth-id' }),
      );

      expect(UserRepository.update).toHaveBeenCalledWith(
        user,
        expect.objectContaining({
          email: '<EMAIL>',
        }),
        expect.objectContaining({ transaction: 'tx', authInfoId: 'auth-id' }),
      );
    });
    it('should throw an error if the user is not found', async () => {
      UserRepository.findUser.mockResolvedValue(null);

      await expect(UserSSOAccountService.assign(request)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.user',
          attribute: 'common.label.username',
          value: 'testuser',
        }),
      );
    });

    it('should throw an error if the SSO record is not found', async () => {
      const user = { id: 'user-id' };
      UserRepository.findUser.mockResolvedValue(user);
      UserSSOAccountRepository.findById.mockResolvedValue(null);

      await expect(UserSSOAccountService.assign(request)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.userSSO',
          attribute: 'ID',
          value: 'sso-id',
        }),
      );
    });
  });

  describe('onboard', () => {
    const mockRequest = {
      body: {
        name: 'John Doe',
        username: 'johndoe',
        // eslint-disable-next-line sonarjs/no-hardcoded-passwords
        password: 'password123',
        currencyIds: ['USD'],
        hierarchy: 'MERCHANT',
        roleId: 'role-id',
        entityId: 'entity-id',
        type: 'normal',
        twoFactorAuth: 'DISABLED',
        validityDate: '2025-12-31',
      },
      params: { id: 'sso-id' },
      server: {},
      authInfo: { id: 'auth-user-id' },
    };

    beforeEach(() => {
      vi.clearAllMocks();
    });

    it('should onboard a user and update the SSO record', async () => {
      const mockSSO = { email: '<EMAIL>' };
      const mockUser = { id: 'user-id' };
      const mockUserAssoc = { id: 'user-assoc-id' };

      UserSSOAccountRepository.findById.mockResolvedValue(mockSSO);
      UserValidation.validateEmail.mockResolvedValue();
      UserValidation.validateUsername.mockResolvedValue('johndoe');
      hashPassword.mockResolvedValue('hashedPassword');
      generate2FASecret.mockResolvedValue({ base32secret: 'base32' });
      UserRepository.create.mockResolvedValue(mockUser);
      UserAssociationRepository.create.mockResolvedValue(mockUserAssoc);
      withTxn.withTransaction.mockImplementation(async (_server, _options, fn) => fn({}));

      await expect(UserSSOAccountService.onboard(mockRequest)).resolves.toBeUndefined();

      expect(UserSSOAccountRepository.findById).toHaveBeenCalledWith(mockRequest.server, {
        filter_id_eq: 'sso-id',
        filter_status_eq: 'pending',
      });
      expect(UserValidation.validateEmail).toHaveBeenCalledWith(mockRequest.server, mockSSO.email);
      expect(UserValidation.validateUsername).toHaveBeenCalledWith(
        mockRequest.server,
        'johndoe',
        'normal',
        null,
      );
      expect(hashPassword).toHaveBeenCalledWith('password123');
      expect(UserRepository.create).toHaveBeenCalledWith(
        mockRequest.server,
        expect.objectContaining({
          name: 'John Doe',
          username: 'johndoe',
          email: mockSSO.email,
        }),
        expect.any(Object),
      );

      expect(UserSSOAccountRepository.update).toHaveBeenCalledWith(
        mockSSO,
        expect.objectContaining({
          userId: 'user-id',
          status: 'completed',
        }),
        expect.objectContaining({ authInfoId: 'auth-user-id' }),
      );
    });

    it('should throw an error if the SSO record is not found', async () => {
      vi.mocked(UserSSOAccountRepository.findById).mockResolvedValue(null);

      await expect(UserSSOAccountService.onboard(mockRequest)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.userSSO',
          attribute: 'ID',
          value: 'sso-id',
        }),
      );
    });

    it('should throw an error if currency is required but not provided', async () => {
      const requestWithoutCurrency = {
        ...mockRequest,
        body: {
          ...mockRequest.body,
          currencyIds: [],
          hierarchy: CoreConstant.HIERARCHY.MERCHANT,
        },
      };
      const mockSSO = { email: '<EMAIL>' };

      UserSSOAccountRepository.findById.mockResolvedValue(mockSSO);

      await expect(UserSSOAccountService.onboard(requestWithoutCurrency)).rejects.toThrow(
        CoreError.requiredData({ attribute: 'common.label.currency' }),
      );
    });
  });
});
