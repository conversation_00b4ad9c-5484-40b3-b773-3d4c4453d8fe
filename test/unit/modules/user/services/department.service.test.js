import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { ModuleRepository } from '#src/modules/core/repository/index.js';
import { DepartmentRepository } from '#src/modules/user/repository/index.js';
import * as departmentService from '#src/modules/user/services/department.service.js';
import { fetchFromCache } from '#src/utils/cache.util.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';
import { populateToJSON } from '#src/utils/test-helper.util.js';

const { HIERARCHY } = CoreConstant;

vi.mock('#src/modules/user/repository/index.js');
vi.mock('#src/modules/core/repository/index.js');
vi.mock('#src/utils/db-transaction.util.js', () => ({
  withTransaction: vi.fn((_, __, callback) => callback('tx')),
}));
vi.mock('#src/utils/cache.util.js', () => ({
  fetchFromCache: vi.fn((redis, key, fallback) => fallback()),
  generateCacheKey: vi.fn(),
}));

const fakeServer = { redis: {} };
const fakeEntity = { id: 'entity-1', hierarchy: 'root' };
const fakeAuthInfo = { id: 'auth-1' };
const fakeDepartment = populateToJSON({
  id: 'dep-1',
  modulePolicies: [],
});
const fakeModule = {
  id: 'mod-1',
  name: 'Module',
  hierarchy: 'merchant',
  parentId: null,
  translationKey: 'translation.key',
  level: 1,
  policy: populateToJSON({ canView: true, canEdit: false }),
};
let mockRequest = {};

describe('departmentService', () => {
  beforeEach(() => {
    mockRequest = {
      server: fakeServer,
      params: { id: 'dep-1' },
      authInfo: { id: 'auth-123' },
      body: [
        {
          moduleId: 'mod-1',
          policy: ['canView', 'canEdit'],
        },
      ],
    };

    vi.restoreAllMocks();
  });

  describe('index', () => {
    it('returns departments for entity', async () => {
      DepartmentRepository.findAll.mockResolvedValue(['dep1']);
      const result = await departmentService.index(
        {
          entity: fakeEntity,
          query: {},
          server: fakeServer,
        },
        true,
      );
      expect(result).toEqual(['dep1']);
    });
  });

  describe('view', () => {
    it('returns department and formats modules', async () => {
      const department = {
        ...fakeDepartment,
        modulePolicies: [
          {
            module: fakeModule,
            policy: populateToJSON({ canView: true, canEdit: false }),
          },
        ],
      };
      DepartmentRepository.findById.mockResolvedValue(department);
      const result = await departmentService.view({
        params: { id: 'dep-1' },
        server: fakeServer,
      });
      expect(result.modules).toHaveProperty('merchant');
    });

    it('throws if not found', async () => {
      DepartmentRepository.findById.mockResolvedValue(null);
      await expect(
        departmentService.view({
          params: { id: 'missing' },
          server: fakeServer,
        }),
      ).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.department',
          attribute: 'ID',
          value: 'missing',
        }),
      );
    });
  });

  describe('create', () => {
    it('creates a department and attaches modules', async () => {
      const mockPolicy = populateToJSON({
        canView: true,
      });
      const mockModule = populateToJSON({
        id: 'mod-1',
        hierarchy: 'merchant',
        policy: mockPolicy,
      });

      const mockDepartment = populateToJSON({
        id: 'dep-1',
        modulePolicies: [
          {
            module: populateToJSON(mockModule),
            policy: mockPolicy,
          },
        ],
      });

      ModuleRepository.findAll.mockResolvedValue([mockModule]);
      DepartmentRepository.createDepartment.mockResolvedValue(mockDepartment);
      DepartmentRepository.createDepartmentModule.mockResolvedValue(populateToJSON({ id: 'dm-1' }));
      DepartmentRepository.upsertPolicy.mockResolvedValue(
        populateToJSON({
          policy: {},
        }),
      );
      DepartmentRepository.findDepartmentModule.mockResolvedValue(null);

      DepartmentRepository.findById.mockResolvedValue(mockDepartment);

      withTransaction.mockImplementation(async (_s, _opt, callback) => {
        return await callback({});
      });

      const request = {
        body: {
          name: 'Finance',
          description: 'Handles finances',
          hierarchy: 'merchant',
          status: 'active',
          modules: [{ moduleId: 'mod-1', policies: ['canView'] }],
        },
        entity: { id: 'ent-1', hierarchy: 'merchant' },
        authInfo: { id: 'admin-user' },
        server: fakeServer,
      };

      const { result, auditModelMapping } = await departmentService.create(request, false);

      expect(DepartmentRepository.createDepartment).toHaveBeenCalled();
      expect(DepartmentRepository.createDepartmentModule).toHaveBeenCalled();
      expect(DepartmentRepository.upsertPolicy).toHaveBeenCalled();
      expect(result.id).toBe('dep-1');
      expect(auditModelMapping.Department).toBeDefined();
      expect(auditModelMapping.DepartmentModule).toBeDefined();
      expect(auditModelMapping.Policy).toBeDefined();
    });

    it('skips module if moduleId is not found', async () => {
      const fakeDepartment = populateToJSON({ id: 'dep-1' });
      const fakeDepartmentWithModules = populateToJSON({
        id: 'dep-1',
        modulePolicies: [],
      });

      ModuleRepository.findAll.mockResolvedValue([]);
      DepartmentRepository.createDepartment.mockResolvedValue(fakeDepartment);
      DepartmentRepository.findById.mockResolvedValue(fakeDepartmentWithModules);

      const request = {
        body: {
          name: 'Unknown Module Dept',
          description: 'Test department',
          hierarchy: 'merchant',
          status: 'active',
          modules: [
            {
              moduleId: 'non-existent-module-id',
              policies: ['canView', 'canEdit'],
            },
          ],
        },
        server: fakeServer,
        entity: { id: 'ent-1', hierarchy: 'merchant' },
        authInfo: { id: 'user-1' },
      };

      const { result, auditModelMapping } = await departmentService.create(request, false);

      expect(ModuleRepository.findAll).toHaveBeenCalled();
      expect(DepartmentRepository.createDepartmentModule).not.toHaveBeenCalled();
      expect(DepartmentRepository.upsertPolicy).not.toHaveBeenCalled();
      expect(result.id).toBe(fakeDepartment.id);
      expect(auditModelMapping.Department).toBeDefined();
      expect(auditModelMapping.DepartmentModule).toEqual([]);
      expect(auditModelMapping.Policy).toEqual([]);
    });

    it('should return correct auditModelMapping if changes are valid', async () => {
      const fakeDepartment = populateToJSON({ id: 'dep-1' });
      const fakeDepartmentWithModules = populateToJSON({
        id: 'dep-1',
        modulePolicies: [],
      });
      const fakeDepartmentModule = populateToJSON({
        id: 'dm-1',
      });

      ModuleRepository.findAll.mockResolvedValue([
        {
          id: 'mod-1',
          hierarchy: 'merchant',
          policy: populateToJSON({ canView: true, canEdit: false }),
        },
        {
          id: 'mod-2',
        },
      ]);
      DepartmentRepository.createDepartment.mockResolvedValue(fakeDepartment);
      DepartmentRepository.findById.mockResolvedValue(fakeDepartmentWithModules);
      DepartmentRepository.createDepartmentModule.mockResolvedValue(fakeDepartmentModule);
      DepartmentRepository.findDepartmentModule.mockResolvedValue(null);
      DepartmentRepository.upsertPolicy.mockResolvedValue({
        afterState: {
          id: 'policy-setting-changed',
        },
      });

      const request = {
        body: {
          name: 'Unknown Module Dept',
          description: 'Test department',
          hierarchy: 'merchant',
          status: 'active',
          modules: [
            {
              moduleId: 'mod-1',
              policies: ['canView', 'canEdit'],
            },
            {
              moduleId: 'non-existent-module-id',
              policies: ['canView', 'canEdit'],
            },
          ],
        },
        server: fakeServer,
        entity: { id: 'ent-1', hierarchy: 'merchant' },
        authInfo: { id: 'user-1' },
      };

      const { auditModelMapping } = await departmentService.create(request, false);

      expect(auditModelMapping.Policy[0]).toEqual({
        afterState: {
          id: 'policy-setting-changed',
        },
      });
      expect(auditModelMapping.DepartmentModule[0]).toEqual({
        afterState: { id: 'dm-1' },
      });
    });

    it('should not allow merchant entity to create root department', async () => {
      const request = {
        body: {
          name: 'Root Department',
          description: 'Test root department',
          hierarchy: 'root',
          status: 'active',
          modules: [],
        },
        server: fakeServer,
        entity: { id: 'ent-1', hierarchy: 'merchant' },
        authInfo: { id: 'user-1' },
      };

      await expect(departmentService.create(request, false)).rejects.toThrow();
    });

    it('should allow root entity to create root department only', async () => {
      const mockDepartment = populateToJSON({
        id: 'dep-1',
        modulePolicies: [],
      });

      ModuleRepository.findAll.mockResolvedValue([]);
      DepartmentRepository.createDepartment.mockResolvedValue(mockDepartment);
      DepartmentRepository.findById.mockResolvedValue(mockDepartment);

      withTransaction.mockImplementation(async (_s, _opt, callback) => {
        return await callback({});
      });

      const request = {
        body: {
          name: 'Root Department',
          description: 'Test root department',
          hierarchy: 'root', // Same as entity hierarchy
          status: 'active',
          modules: [],
        },
        server: fakeServer,
        entity: { id: 'ent-1', hierarchy: 'root' }, // Root entity
        authInfo: { id: 'user-1' },
      };

      const { result } = await departmentService.create(request, false);
      expect(result.id).toBe('dep-1');
    });
  });

  describe('validateDepartmentHierarchy', () => {
    it('validates department hierarchy correctly', () => {
      // Entity can only create departments with matching hierarchy
      expect(departmentService.validateDepartmentHierarchy('root', 'root')).toBe(true);
      expect(departmentService.validateDepartmentHierarchy('organisation', 'organisation')).toBe(
        true,
      );
      expect(departmentService.validateDepartmentHierarchy('merchant', 'merchant')).toBe(true);

      // Cannot create departments with different hierarchy
      expect(departmentService.validateDepartmentHierarchy('root', 'organisation')).toBe(false);
      expect(departmentService.validateDepartmentHierarchy('root', 'merchant')).toBe(false);
      expect(departmentService.validateDepartmentHierarchy('organisation', 'root')).toBe(false);
      expect(departmentService.validateDepartmentHierarchy('organisation', 'merchant')).toBe(false);
      expect(departmentService.validateDepartmentHierarchy('merchant', 'root')).toBe(false);
      expect(departmentService.validateDepartmentHierarchy('merchant', 'organisation')).toBe(false);

      // Test with invalid/null/undefined values
      expect(departmentService.validateDepartmentHierarchy('unknown', 'merchant')).toBe(false);
      expect(departmentService.validateDepartmentHierarchy('root', 'invalid')).toBe(false);
      expect(departmentService.validateDepartmentHierarchy('', 'organisation')).toBe(false);
      expect(departmentService.validateDepartmentHierarchy(null, 'merchant')).toBe(false);
      expect(departmentService.validateDepartmentHierarchy(undefined, 'root')).toBe(false);
    });
  });

  describe('updateBasicInformation', () => {
    it('updates basic info when changes are made', async () => {
      const departmentId = 'dep-1';
      const updateData = { name: 'New Name' };
      const authInfoId = 'auth-1';

      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      DepartmentRepository.update.mockResolvedValue({ isDirty: true });

      const { auditModelMapping } = await departmentService.updateBasicInformation({
        params: { id: departmentId },
        body: updateData,
        authInfo: { id: authInfoId },
        server: fakeServer,
      });

      expect(auditModelMapping.Department).toBeDefined();

      expect(DepartmentRepository.findById).toHaveBeenCalledWith(fakeServer, departmentId);
      expect(DepartmentRepository.update).toHaveBeenCalledWith(fakeDepartment, updateData, {
        authInfoId,
      });
    });

    it('throws CoreError.dataNotFound if department is not found', async () => {
      const departmentId = 'non-existent-dep';

      DepartmentRepository.findById.mockResolvedValue(null);

      await expect(
        departmentService.updateBasicInformation({
          params: { id: departmentId },
          body: { name: 'New Name' },
          authInfo: { id: 'auth-1' },
          server: fakeServer,
        }),
      ).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.department',
          attribute: 'ID',
          value: departmentId,
        }),
      );
    });

    it('throws CoreError.unprocessable if no changes are made', async () => {
      const departmentId = 'dep-1';
      const updateData = { name: 'Existing Name' };

      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      DepartmentRepository.update.mockResolvedValue({ isDirty: false });

      await expect(
        departmentService.updateBasicInformation({
          params: { id: departmentId },
          body: updateData,
          authInfo: { id: 'auth-1' },
          server: fakeServer,
        }),
      ).rejects.toThrow(CoreError.unprocessable().message);
    });
  });

  describe('updateStatus', () => {
    it('updates department status when changes are made', async () => {
      const departmentId = 'dep-1';
      const newStatus = 'inactive';
      const authInfoId = 'auth-1';

      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      DepartmentRepository.update.mockResolvedValue({ isDirty: true });

      const { auditModelMapping } = await departmentService.updateStatus({
        params: { id: departmentId },
        body: { status: newStatus },
        authInfo: { id: authInfoId },
        server: fakeServer,
      });

      expect(auditModelMapping.Department).toBeDefined();

      expect(DepartmentRepository.findById).toHaveBeenCalledWith(fakeServer, departmentId, false);
      expect(DepartmentRepository.update).toHaveBeenCalledWith(
        fakeDepartment,
        { status: newStatus },
        { authInfoId },
      );
    });

    it('throws CoreError.dataNotFound if department is not found', async () => {
      const departmentId = 'non-existent-dep';

      DepartmentRepository.findById.mockResolvedValue(null);

      await expect(
        departmentService.updateStatus({
          params: { id: departmentId },
          body: { status: 'inactive' },
          authInfo: { id: 'auth-1' },
          server: fakeServer,
        }),
      ).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.department',
          attribute: 'ID',
          value: departmentId,
        }),
      );
    });

    it('throws CoreError.unprocessable if no changes are made', async () => {
      const departmentId = 'dep-1';
      const currentStatus = 'active';

      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      DepartmentRepository.update.mockResolvedValue({ isDirty: false });

      await expect(
        departmentService.updateStatus({
          params: { id: departmentId },
          body: { status: currentStatus },
          authInfo: { id: 'auth-1' },
          server: fakeServer,
        }),
      ).rejects.toThrow(CoreError.unprocessable().message);
    });
  });

  describe('remove', () => {
    it('remove soft-deletes and removes department', async () => {
      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      DepartmentRepository.update.mockResolvedValue();
      DepartmentRepository.remove.mockResolvedValue('removed');

      withTransaction.mockImplementation(async (_, __, cb) => await cb('tx'));

      const { result, auditModelMapping } = await departmentService.remove({
        params: { id: 'dep-1' },
        authInfo: fakeAuthInfo,
        server: fakeServer,
      });

      expect(result).toBe('removed');
      expect(auditModelMapping.Department.afterState).toBeDefined();
    });

    it('remove throws if department not found', async () => {
      const departmentId = 'dep-404';

      DepartmentRepository.findById.mockResolvedValue(null);

      const request = {
        params: { id: departmentId },
        authInfo: { id: 'auth-1' },
        server: fakeServer,
      };

      await expect(departmentService.remove(request)).rejects.toThrowError(
        CoreError.dataNotFound({
          data: 'common.label.department',
          attribute: 'ID',
          value: departmentId,
        }),
      );

      expect(DepartmentRepository.findById).toHaveBeenCalledWith(fakeServer, departmentId, false);
    });
  });

  describe('getModulePolicyOptions', () => {
    it('getModulePolicyOptions fetches from cache and filters', async () => {
      ModuleRepository.findModulePolicies.mockResolvedValue([fakeModule]);
      fetchFromCache.mockImplementation(async (_, __, cb) => await cb());
      const result = await departmentService.getModulePolicyOptions({
        server: fakeServer,
        entity: fakeEntity,
      });
      expect(result).toHaveProperty('merchant');
    });

    it('getModulePolicyOptions returns structured modules', async () => {
      const fakeModules = [
        /* fake modules */
      ];

      ModuleRepository.findModulePolicies = vi.fn().mockResolvedValue(fakeModules);
      fetchFromCache.mockResolvedValue({ ROOT: [], ORGANISATION: [], MERCHANT: [] });

      const result = await departmentService.getModulePolicyOptions({
        server: fakeServer,
        entity: { id: 'ent-1', hierarchy: 'merchant' },
      });

      expect(result).toBeDefined();
      expect(fetchFromCache).toHaveBeenCalled();
    });

    it('throws an error for invalid hierarchy', async () => {
      const invalidRequest = {
        server: { redis: {} },
        entity: {
          id: 'entity-xyz',
          hierarchy: 'INVALID_ACCESS_LEVEL',
        },
      };

      await expect(departmentService.getModulePolicyOptions(invalidRequest)).rejects.toThrow(
        'Invalid hierarchy',
      );
    });

    it('returns structured modules for ORGANISATION hierarchy', async () => {
      const mockModules = [
        {
          id: 'mod-1',
          name: 'Organisation Module',
          hierarchy: HIERARCHY.ORGANISATION,
          navigationPosition: 1,
          navigationType: 'menu',
          translationKey: 'module.org',
          level: 1,
          parentId: null,
          policy: {
            toJSON: () => ({
              canView: true,
              canEdit: false,
            }),
          },
          children: [],
        },
      ];

      const mockServer = { redis: {} };
      const mockRequest = {
        server: mockServer,
        entity: {
          id: 'entity-001',
          hierarchy: HIERARCHY.ORGANISATION,
        },
      };

      ModuleRepository.findModulePolicies.mockResolvedValue(mockModules);

      const result = await departmentService.getModulePolicyOptions(mockRequest);

      expect(ModuleRepository.findModulePolicies).toHaveBeenCalledWith(mockServer, [
        HIERARCHY.ORGANISATION,
        HIERARCHY.MERCHANT,
      ]);

      expect(result[HIERARCHY.ORGANISATION]).toEqual([
        expect.objectContaining({
          id: 'mod-1',
          name: 'Organisation Module',
        }),
      ]);
    });
  });

  describe('updatePolicy', () => {
    const fakeServer = { db: {} };
    const authInfoId = 'auth-123';
    const departmentId = 'dep-1';
    const fakeDepartment = { id: departmentId, hierarchy: 'merchant' };
    const fakeModule = {
      id: 'mod-1',
      hierarchy: 'merchant',
      policy: populateToJSON({
        canView: true,
        canEdit: true,
        canCreate: false,
      }),
    };
    const fakeModules = [
      populateToJSON({
        id: 'mod-1',
        hierarchy: 'merchant',
        policy: populateToJSON({
          canView: true,
          canEdit: true,
          canCreate: false,
        }),
      }),
      populateToJSON({
        id: 'mod-3',
        hierarchy: 'merchant',
        policy: populateToJSON({
          canView: true,
          canEdit: true,
          canCreate: false,
        }),
      }),
    ];

    const fakeDeptModule = populateToJSON({
      moduleId: 'mod-1',
      id: 'deptmod-1',
    });
    const fakeDeptModule2 = populateToJSON({
      moduleId: 'mod-2',
      id: 'deptmod-2',
    });

    const mockRequest = {
      server: fakeServer,
      params: { id: 'dep-1' },
      authInfo: { id: authInfoId },
      body: [
        {
          moduleId: 'mod-1',
          policies: ['canView', 'canEdit'],
        },
        {
          moduleId: 'mod-9',
          policies: [],
        },
        {
          moduleId: 'mod-10',
          policies: ['canManage'],
        },
      ],
    };

    beforeEach(() => {
      vi.clearAllMocks();

      DepartmentRepository.findById = vi.fn().mockResolvedValue(fakeDepartment);
      DepartmentRepository.findAllModulePolicies = vi
        .fn()
        .mockResolvedValue([fakeDeptModule, fakeDeptModule2]);
      DepartmentRepository.removeDepartmentModule = vi.fn().mockResolvedValue(true);

      ModuleRepository.findAll = vi.fn().mockResolvedValue(fakeModules);

      withTransaction.mockImplementation(async (server, options, cb) => await cb({}));
    });

    it('skips upsert if module not found', async () => {
      ModuleRepository.findAll.mockResolvedValue([]);

      const spy = vi.spyOn(departmentService, 'upsertDepartmentModule');

      await departmentService.updatePolicy(mockRequest);

      expect(spy).not.toHaveBeenCalled();
    });

    it('removes department module when no valid policies', async () => {
      const removeSpy = vi.spyOn(DepartmentRepository, 'removeDepartmentModule');

      await departmentService.updatePolicy({
        ...mockRequest,
        body: [
          {
            moduleId: 'mod-1',
            policies: [],
          },
        ],
      });

      expect(removeSpy).toBeCalledTimes(3);

      expect(removeSpy).toHaveBeenCalledWith(
        fakeServer,
        fakeDeptModule.id,
        expect.objectContaining({
          transaction: expect.anything(),
          authInfoId,
        }),
      );
    });

    it('throws CoreError.dataNotFound if department not found', async () => {
      DepartmentRepository.findById.mockResolvedValue(null);

      await expect(departmentService.updatePolicy(mockRequest)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.department',
          attribute: 'ID',
          value: departmentId,
        }),
      );
    });

    it('throws CoreError.unprocessable if no changes made', async () => {
      const emptyReq = {
        ...mockRequest,
        body: [],
      };

      DepartmentRepository.findAllModulePolicies.mockResolvedValue([]);

      await expect(departmentService.updatePolicy(emptyReq)).rejects.toThrow(
        CoreError.unprocessable().message,
      );
    });

    it('upserts department module when valid policies exist', async () => {
      const departmentId = 'dep-1';
      const moduleId = 'mod-1';

      const fakeDepartmentModule = populateToJSON({ id: 'dm-1' });

      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      ModuleRepository.findAll.mockResolvedValue([fakeModule]);
      DepartmentRepository.findAllModulePolicies.mockResolvedValue([]);
      DepartmentRepository.findDepartmentModule.mockResolvedValue(null);
      DepartmentRepository.createDepartmentModule.mockResolvedValue(fakeDepartmentModule);
      DepartmentRepository.removeDepartmentModule = vi.fn();
      DepartmentRepository.upsertPolicy.mockResolvedValue({ isDirty: true });

      await departmentService.updatePolicy(mockRequest);

      expect(DepartmentRepository.findDepartmentModule).toHaveBeenCalled();

      expect(DepartmentRepository.createDepartmentModule).toHaveBeenCalledWith(
        fakeServer,
        { departmentId, moduleId },
        expect.objectContaining({ transaction: expect.anything(), authInfoId: 'auth-123' }),
      );

      expect(DepartmentRepository.upsertPolicy).toHaveBeenCalledWith(
        fakeServer,
        expect.objectContaining({
          parentId: fakeDepartmentModule.id,
          canView: true,
          canEdit: true,
          canCreate: false,
          canImport: false,
          canExport: false,
          canManage: false,
          canMasking: false,
          canOverwrite: false,
          canVerify: false,
        }),
        expect.objectContaining({
          transaction: expect.anything(),
          authInfoId: 'auth-123',
        }),
      );
    });
  });

  it('validateAndFilterPolicies returns valid policies or null', () => {
    const module = {
      hierarchy: 'merchant',
      policy: populateToJSON({ canView: true }),
    };
    expect(
      departmentService.validateAndFilterPolicies(module, 'merchant', ['canView', 'canEdit']),
    ).toEqual(['canView']);
    expect(departmentService.validateAndFilterPolicies(module, 'root', ['canView'])).toEqual([
      'canView',
    ]);
    expect(departmentService.validateAndFilterPolicies(module, 'merchant', ['canView'])).toEqual([
      'canView',
    ]);
    expect(departmentService.validateAndFilterPolicies(module, 'merchant', ['canEdit'])).toEqual(
      [],
    );
    module.hierarchy = 'root';
    expect(departmentService.validateAndFilterPolicies(module, 'merchant', ['canView'])).toBe(null);
  });

  it('formatModule returns cleaned policy structure', () => {
    const formatted = departmentService.formatModule(fakeModule);
    expect(formatted).toHaveProperty('policies');
    expect(formatted).toHaveProperty('id', 'mod-1');
  });

  it('includes parentId when it is not null', () => {
    const inputModule = {
      id: 'mod-1',
      name: 'Test Module',
      hierarchy: 'merchant',
      navigationPosition: 1,
      navigationType: 'menu',
      translationKey: 'test.module',
      level: 1,
      parentId: 'parent-1',
      policy: populateToJSON({
        canView: true,
        canCreate: false,
      }),
    };

    const result = departmentService.formatModule(inputModule);

    expect(result).toMatchObject({
      id: 'mod-1',
      parentId: 'parent-1',
    });
  });

  it('filterAndStructureModules organizes modules by hierarchy', () => {
    const result = departmentService.filterAndStructureModules([fakeModule], ['merchant']);
    expect(result.merchant.length).toBeGreaterThan(0);
  });

  it('should filter out modules with disallowed hierarchies', () => {
    const modules = [
      { id: '1', name: 'Root Module', hierarchy: HIERARCHY.ROOT, parentId: null },
      { id: '2', name: 'Org Module', hierarchy: HIERARCHY.ORGANISATION, parentId: null },
      { id: '3', name: 'Merchant Module', hierarchy: HIERARCHY.MERCHANT, parentId: null },
    ];

    const allowedHierarchies = [HIERARCHY.ORGANISATION, HIERARCHY.MERCHANT];

    const result = departmentService.filterAndStructureModules(modules, allowedHierarchies);

    expect(result[HIERARCHY.ROOT]).toHaveLength(0);
    expect(result[HIERARCHY.ORGANISATION]).toHaveLength(1);
    expect(result[HIERARCHY.MERCHANT]).toHaveLength(1);
    expect(result[HIERARCHY.ORGANISATION][0].name).toBe('Org Module');
    expect(result[HIERARCHY.MERCHANT][0].name).toBe('Merchant Module');
  });

  it('structures modules with children recursively', () => {
    const modules = [
      {
        id: 'mod-root-1',
        name: 'Root Module',
        hierarchy: HIERARCHY.ROOT,
        navigationPosition: 1,
        navigationType: 'menu',
        translationKey: 'module.root',
        level: 1,
        parentId: null,
        policy: populateToJSON({
          canView: true,
          canEdit: true,
        }),
        children: [
          {
            id: 'mod-root-child-1',
            name: 'Child Module',
            hierarchy: HIERARCHY.ROOT,
            navigationPosition: 2,
            navigationType: 'menu',
            translationKey: 'module.child',
            level: 2,
            parentId: 'mod-root-1',
            policy: populateToJSON({
              canView: true,
            }),
            children: [],
          },
        ],
      },
    ];

    const result = departmentService.filterAndStructureModules(modules, [HIERARCHY.ROOT]);

    expect(result[HIERARCHY.ROOT]).toHaveLength(1);

    const parent = result[HIERARCHY.ROOT][0];
    expect(parent.children).toHaveLength(1);

    expect(parent.children[0]).toMatchObject({
      id: 'mod-root-child-1',
      name: 'Child Module',
      parentId: 'mod-root-1',
    });
  });
});
