import { beforeEach, describe, expect, it, vi } from 'vitest';

import { DepartmentHandler } from '#src/modules/user/handlers/index.js';
import DepartmentRoute from '#src/modules/user/routes/department.route.js';
import { DepartmentSchema } from '#src/modules/user/schemas/index.js';

describe('DepartmentRoute', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      get: vi.fn(),
      post: vi.fn(),
      patch: vi.fn(),
      delete: vi.fn(),
    };
  });

  it('should register all routes with correct configurations', async () => {
    await DepartmentRoute(mockFastify);

    const commonAccessConfig = {
      user: true,
      member: false,
      webhook: false,
      public: false,
      ipWhitelist: ['127.0.0.1'],
    };

    const expectedRoutes = [
      {
        method: 'get',
        url: '/',
        schema: DepartmentSchema.index,
        config: { name: 'department.index', access: commonAccessConfig },
        handler: DepartmentHandler.index,
      },
      {
        method: 'get',
        url: '/:id',
        schema: DepartmentSchema.view,
        config: { name: 'department.view', access: commonAccessConfig },
        handler: DepartmentHandler.view,
      },
      {
        method: 'post',
        url: '/',
        schema: DepartmentSchema.create,
        config: { name: 'department.create', access: commonAccessConfig },
        handler: DepartmentHandler.create,
      },
      {
        method: 'patch',
        url: '/:id/basic-information',
        schema: DepartmentSchema.updateBasicInformation,
        config: { name: 'department.update', access: commonAccessConfig },
        handler: DepartmentHandler.updateBasicInformation,
      },
      {
        method: 'patch',
        url: '/:id/policy',
        schema: DepartmentSchema.updatePolicy,
        config: { name: 'department.update', access: commonAccessConfig },
        handler: DepartmentHandler.updatePolicy,
      },
      {
        method: 'patch',
        url: '/:id/status',
        schema: DepartmentSchema.updateStatus,
        config: { name: 'department.updateStatus', access: commonAccessConfig },
        handler: DepartmentHandler.updateStatus,
      },
      {
        method: 'delete',
        url: '/:id',
        schema: DepartmentSchema.remove,
        config: { name: 'department.remove', access: commonAccessConfig },
        handler: DepartmentHandler.remove,
      },
      {
        method: 'get',
        url: '/options',
        schema: DepartmentSchema.options,
        config: { name: 'department.options', access: commonAccessConfig },
        handler: DepartmentHandler.options,
      },
    ];

    expectedRoutes.forEach((route) => {
      expect(mockFastify[route.method]).toHaveBeenCalledWith(
        route.url,
        expect.objectContaining({
          schema: route.schema,
          config: route.config,
          handler: route.handler,
        }),
      );
    });

    expect(mockFastify.get).toHaveBeenCalledTimes(3);
    expect(mockFastify.post).toHaveBeenCalledTimes(1);
    expect(mockFastify.patch).toHaveBeenCalledTimes(3);
    expect(mockFastify.delete).toHaveBeenCalledTimes(1);
  });
});
