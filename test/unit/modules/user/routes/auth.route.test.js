import { beforeEach, describe, expect, it, vi } from 'vitest';

import { AuthHand<PERSON> } from '#src/modules/user/handlers/index.js';
import AuthRoute from '#src/modules/user/routes/auth.route.js';
import { AuthSchema } from '#src/modules/user/schemas/index.js';

describe('User Route Module', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      post: vi.fn(),
      get: vi.fn(),
      hcaptcha: 'hcaptchaMiddleware',
    };
  });

  it('should register the login route with correct configuration', async () => {
    await AuthRoute(fastifyMock, {});

    expect(fastifyMock.post).toHaveBeenCalledTimes(5);
    expect(fastifyMock.get).toHaveBeenCalledTimes(1);

    expect(fastifyMock.post.mock.calls[0][0]).toBe('/login');
    expect(fastifyMock.post.mock.calls[1][0]).toBe('/google');

    const routeConfig = fastifyMock.post.mock.calls[0][1];

    expect(routeConfig.preHandler).toBe(fastifyMock.hcaptcha);

    expect(routeConfig.schema).toBe(AuthSchema.login);

    expect(routeConfig.handler).toBe(AuthHandler.login);

    expect(routeConfig.config.name).toBe('user.login');
    expect(routeConfig.config.access).toEqual({
      user: false,
      member: false,
      webhook: false,
      public: true,
      ipWhitelist: [],
    });
  });

  it('should export a function', () => {
    expect(typeof AuthRoute).toBe('function');
  });

  it('should be an async function', () => {
    expect(AuthRoute.constructor.name).toBe('AsyncFunction');
  });
});
