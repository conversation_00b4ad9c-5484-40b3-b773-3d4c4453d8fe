import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  UserIn<PERSON><PERSON><PERSON><PERSON>,
  UserSSOAccountHandler,
} from '#src/modules/user/handlers/index.js';
import UserRoute from '#src/modules/user/routes/user.route.js';
import {
  UserInvitationSchema,
  UserSSOAccountSchema,
  UserSchema,
} from '#src/modules/user/schemas/index.js';

describe('User Route', () => {
  let fastifyMock;

  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: true,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  beforeEach(() => {
    fastifyMock = {
      get: vi.fn(),
      post: vi.fn(),
      patch: vi.fn(),
      put: vi.fn(),
    };
  });

  // --- USER CRUD ---

  it('should register GET / route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.get).toHaveBeenCalledWith('/', {
      schema: UserSchema.index,
      config: { name: 'user.list', access: commonAccessConfig },
      handler: UserHandler.index,
    });
  });

  it('should register the list route correctly', async () => {
    await UserRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith('/sub-account', {
      schema: UserSchema.subAccountIndex,
      config: { name: 'user.subaccount.list', access: commonAccessConfig },
      handler: UserHandler.subAccountIndex,
    });
  });

  it('should register the create route correctly', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.post).toHaveBeenCalledWith('/', {
      schema: UserSchema.create,
      config: { name: 'user.create', access: commonAccessConfig },
      handler: UserHandler.create,
    });
  });

  it('should register GET /:id route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.get).toHaveBeenCalledWith('/:id', {
      schema: UserSchema.view,
      config: { name: 'user.view', access: commonAccessConfig },
      handler: UserHandler.view,
    });
  });

  it('should register PATCH /:id/basic-information route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/basic-information', {
      schema: UserSchema.updateBasicInformation,
      config: { name: 'user.updateBasicInformation', access: commonAccessConfig },
      handler: UserHandler.updateBasicInformation,
    });
  });

  it('should register PATCH /:id/organisation route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/organisation', {
      schema: UserSchema.updateOrganisation,
      config: { name: 'user.updateOrganisation', access: commonAccessConfig },
      handler: UserHandler.updateOrganisation,
    });
  });

  it('should register PATCH /:id/login-access route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/login-access', {
      schema: UserSchema.updateLoginAccess,
      config: { name: 'user.updateLoginAccess', access: commonAccessConfig },
      handler: UserHandler.updateLoginAccess,
    });
  });

  it('should register PATCH /:id/status route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/status', {
      schema: UserSchema.updateStatus,
      config: { name: 'user.updateStatus', access: commonAccessConfig },
      handler: UserHandler.updateStatus,
    });
  });

  it('should register GET /options route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.get).toHaveBeenCalledWith('/options', {
      schema: UserSchema.options,
      config: { name: 'user.options', access: commonAccessConfig },
      handler: UserHandler.options,
    });
  });

  it('should register GET /check-username route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.get).toHaveBeenCalledWith('/check-username', {
      schema: UserSchema.checkAvailability,
      config: { name: 'user.checkAvailability', access: commonAccessConfig },
      handler: UserHandler.checkAvailability,
    });
  });

  // --- USER INVITATION ---

  it('should register GET /invitations route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.get).toHaveBeenCalledWith('/invitations', {
      schema: UserInvitationSchema.index,
      config: { name: 'user.invitation.list', access: commonAccessConfig },
      handler: UserInvitationHandler.index,
    });
  });

  it('should register POST /invitations route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.post).toHaveBeenCalledWith('/invitations', {
      schema: UserInvitationSchema.create,
      config: { name: 'user.invitation.create', access: commonAccessConfig },
      handler: UserInvitationHandler.create,
    });
  });

  it('should register PATCH /invitations/:id/status route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.patch).toHaveBeenCalledWith('/invitations/:id/status', {
      schema: UserInvitationSchema.updateStatus,
      config: { name: 'user.invitation.updateStatus', access: commonAccessConfig },
      handler: UserInvitationHandler.updateStatus,
    });
  });

  // --- USER SSO ---

  it('should register GET /sso route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.get).toHaveBeenCalledWith('/sso', {
      schema: UserSSOAccountSchema.index,
      config: { name: 'user.sso.list', access: commonAccessConfig },
      handler: UserSSOAccountHandler.index,
    });
  });

  it('should register PATCH /sso/:id/status route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.patch).toHaveBeenCalledWith('/sso/:id/status', {
      schema: UserSSOAccountSchema.updateStatus,
      config: { name: 'user.sso.updateStatus', access: commonAccessConfig },
      handler: UserSSOAccountHandler.updateStatus,
    });
  });

  it('should register PUT /sso/:id/assign route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.put).toHaveBeenCalledWith('/sso/:id/assign', {
      schema: UserSSOAccountSchema.assign,
      config: { name: 'user.sso.assign', access: commonAccessConfig },
      handler: UserSSOAccountHandler.assign,
    });
  });

  it('should register PUT /sso/:id/onboard route', async () => {
    await UserRoute(fastifyMock);
    expect(fastifyMock.put).toHaveBeenCalledWith('/sso/:id/onboard', {
      schema: UserSSOAccountSchema.onboard,
      config: { name: 'user.sso.onboard', access: commonAccessConfig },
      handler: UserSSOAccountHandler.onboard,
    });
  });
});
