import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { create } from '#src/modules/media/handlers/media.handler.js';
import { MediaService } from '#src/modules/media/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

vi.mock('#src/modules/media/services/index.js', () => ({
  MediaService: {
    create: vi.fn(),
  },
}));

vi.mock('#src/utils/response.util.js', () => ({
  handleServiceResponse: vi.fn(),
}));

describe('Media Handler', () => {
  const {
    MODULE_NAMES: { MEDIA },
    MODULE_METHODS: { CREATE },
  } = CoreConstant;

  let mockRequest;
  let mockReply;

  beforeEach(() => {
    vi.resetAllMocks();

    mockRequest = {
      body: {
        file: {
          buffer: Buffer.from('test file content'),
          filename: 'test-file.jpg',
          mimetype: 'image/jpeg',
        },
      },
      authInfo: {
        id: 'user-123',
      },
    };

    mockReply = {
      code: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    handleServiceResponse.mockResolvedValue({
      statusCode: 201,
      payload: {
        id: 'media-123',
        file_path: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
      },
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await create(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: MediaService.create,
        module: MEDIA,
        method: CREATE,
      });
    });

    it('should return the result from handleServiceResponse', async () => {
      const expectedResponse = {
        statusCode: 201,
        payload: {
          id: 'media-123',
          file_path: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
        },
      };

      handleServiceResponse.mockResolvedValue(expectedResponse);

      const result = await create(mockRequest, mockReply);

      expect(result).toEqual(expectedResponse);
    });

    it('should propagate errors from handleServiceResponse', async () => {
      const expectedError = new Error('Service error');
      handleServiceResponse.mockRejectedValue(expectedError);

      await expect(create(mockRequest, mockReply)).rejects.toThrow(expectedError);
    });

    it('should handle different types of requests', async () => {
      const alternativeMockRequest = {
        body: {
          file: {
            toBuffer: vi.fn().mockResolvedValue(Buffer.from('alternative content')),
            filename: 'alternative.pdf',
            mimetype: 'application/pdf',
          },
          visibility: 'private',
        },
        authInfo: {
          id: 'admin-456',
        },
      };

      await create(alternativeMockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: alternativeMockRequest,
        reply: mockReply,
        serviceFn: MediaService.create,
        module: MEDIA,
        method: CREATE,
      });
    });

    it('should handle requests without authentication', async () => {
      const unauthenticatedRequest = {
        body: {
          file: {
            buffer: Buffer.from('test file content'),
            filename: 'test-file.jpg',
            mimetype: 'image/jpeg',
          },
        },
      };

      await create(unauthenticatedRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: unauthenticatedRequest,
        reply: mockReply,
        serviceFn: MediaService.create,
        module: MEDIA,
        method: CREATE,
      });
    });
  });
});
