import { beforeEach, describe, expect, it, vi } from 'vitest';

import * as MediaAssetRepository from '#src/modules/media/repository/media-asset.repository.js';

describe('Media Asset Repository', () => {
  let mockFastify;
  let mockMediaAssetInstance;
  let mockMediaAssetData;

  beforeEach(() => {
    vi.resetAllMocks();

    mockMediaAssetInstance = {
      toJSON: vi.fn().mockReturnValue({
        id: 'media-123',
        entity_id: 'entity-456',
        file_path: 's3://bucket/path/to/file.jpg',
        file_type: 'IMAGE',
        mime_type: 'image/jpeg',
        file_size: 12345,
        alt_text: 'Test image',
        visibility: 'public',
        metadata: { originalFilename: 'file.jpg' },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      }),
    };

    mockMediaAssetData = {
      entity_id: 'entity-456',
      file_path: 's3://bucket/path/to/file.jpg',
      file_type: 'IMAGE',
      mime_type: 'image/jpeg',
      file_size: 12345,
      alt_text: 'Test image',
      visibility: 'public',
      metadata: { originalFilename: 'file.jpg' },
    };

    mockFastify = {
      psql: {
        MediaAsset: {
          create: vi.fn().mockResolvedValue(mockMediaAssetInstance),
          findByPk: vi.fn().mockResolvedValue(mockMediaAssetInstance),
        },
      },
    };
  });

  describe('create', () => {
    it('should create a new media asset and return it as a plain object', async () => {
      const result = await MediaAssetRepository.create(mockFastify, mockMediaAssetData);

      expect(mockFastify.psql.MediaAsset.create).toHaveBeenCalledWith(mockMediaAssetData, {});

      expect(mockMediaAssetInstance.toJSON).toHaveBeenCalled();

      expect(result).toEqual({
        id: 'media-123',
        entity_id: 'entity-456',
        file_path: 's3://bucket/path/to/file.jpg',
        file_type: 'IMAGE',
        mime_type: 'image/jpeg',
        file_size: 12345,
        alt_text: 'Test image',
        visibility: 'public',
        metadata: { originalFilename: 'file.jpg' },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      });
    });

    it('should pass additional options to the create method', async () => {
      const options = {
        transaction: 'mock-transaction',
        authInfoId: 'user-789',
      };

      await MediaAssetRepository.create(mockFastify, mockMediaAssetData, options);

      expect(mockFastify.psql.MediaAsset.create).toHaveBeenCalledWith(mockMediaAssetData, options);
    });
  });

  describe('findByPk', () => {
    it('should find a media asset by primary key and return it as a plain object', async () => {
      const result = await MediaAssetRepository.findByPk(mockFastify, 'media-123');

      expect(mockFastify.psql.MediaAsset.findByPk).toHaveBeenCalledWith('media-123', {});

      expect(mockMediaAssetInstance.toJSON).toHaveBeenCalled();

      expect(result).toEqual({
        id: 'media-123',
        entity_id: 'entity-456',
        file_path: 's3://bucket/path/to/file.jpg',
        file_type: 'IMAGE',
        mime_type: 'image/jpeg',
        file_size: 12345,
        alt_text: 'Test image',
        visibility: 'public',
        metadata: { originalFilename: 'file.jpg' },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      });
    });

    it('should return null when no media asset is found', async () => {
      mockFastify.psql.MediaAsset.findByPk.mockResolvedValue(null);

      const result = await MediaAssetRepository.findByPk(mockFastify, 'non-existent-id');

      expect(mockFastify.psql.MediaAsset.findByPk).toHaveBeenCalledWith('non-existent-id', {});

      expect(result).toBeNull();
    });

    it('should pass additional options to the findByPk method', async () => {
      const options = {
        include: ['related-model'],
        attributes: ['id', 'file_path'],
      };

      await MediaAssetRepository.findByPk(mockFastify, 'media-123', options);

      expect(mockFastify.psql.MediaAsset.findByPk).toHaveBeenCalledWith('media-123', options);
    });
  });
});
