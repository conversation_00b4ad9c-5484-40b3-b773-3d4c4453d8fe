import { beforeEach, describe, expect, it, vi } from 'vitest';

import { MediaHandler } from '#src/modules/media/handlers/index.js';
import mediaRoutes from '#src/modules/media/routes/media.route.js';
import * as MediaSchema from '#src/modules/media/schemas/media.schema.js';

describe('Media Routes', () => {
  let mockFastify;
  let mockOptions;

  beforeEach(() => {
    mockFastify = {
      post: vi.fn(),
    };

    mockOptions = {};
  });

  it('should register the POST route for media creation', async () => {
    await mediaRoutes(mockFastify, mockOptions);

    expect(mockFastify.post).toHaveBeenCalledWith('/', {
      schema: MediaSchema.create,
      config: {
        name: 'media.create',
        access: {
          user: true,
          member: true,
          webhook: false,
          public: false,
          ipWhitelist: [],
        },
      },
      handler: MediaHandler.create,
      attachValidation: true,
    });
  });

  it('should register the correct number of routes', async () => {
    await mediaRoutes(mockFastify, mockOptions);

    expect(mockFastify.post).toHaveBeenCalledTimes(1);
  });

  it('should use the correct schema for the POST route', async () => {
    await mediaRoutes(mockFastify, mockOptions);

    const postCallArgs = mockFastify.post.mock.calls[0];
    const routeOptions = postCallArgs[1];

    expect(routeOptions.schema).toBe(MediaSchema.create);
  });

  it('should use the correct handler for the POST route', async () => {
    await mediaRoutes(mockFastify, mockOptions);

    const postCallArgs = mockFastify.post.mock.calls[0];
    const routeOptions = postCallArgs[1];

    expect(routeOptions.handler).toBe(MediaHandler.create);
  });

  it('should set attachValidation to true for the POST route', async () => {
    await mediaRoutes(mockFastify, mockOptions);

    const postCallArgs = mockFastify.post.mock.calls[0];
    const routeOptions = postCallArgs[1];

    expect(routeOptions.attachValidation).toBe(true);
  });

  it('should configure the correct access permissions', async () => {
    await mediaRoutes(mockFastify, mockOptions);

    const postCallArgs = mockFastify.post.mock.calls[0];
    const routeOptions = postCallArgs[1];
    const accessConfig = routeOptions.config.access;

    expect(accessConfig).toEqual({
      user: true,
      member: true,
      webhook: false,
      public: false,
      ipWhitelist: [],
    });
  });

  it('should set the correct route name', async () => {
    await mediaRoutes(mockFastify, mockOptions);

    const postCallArgs = mockFastify.post.mock.calls[0];
    const routeOptions = postCallArgs[1];

    expect(routeOptions.config.name).toBe('media.create');
  });
});
