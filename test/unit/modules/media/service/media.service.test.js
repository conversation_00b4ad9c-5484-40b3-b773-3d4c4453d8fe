import { beforeEach, describe, expect, it, vi } from 'vitest';

import { MediaAssetConstant } from '#src/modules/media/constants/index.js';
import { MediaError } from '#src/modules/media/errors/index.js';
import { MediaAssetRepository } from '#src/modules/media/repository/index.js';
import { create } from '#src/modules/media/services/media.service.js';
import { uploadToS3 } from '#src/utils/upload.util.js';

vi.mock('#src/utils/upload.util.js', () => ({
  uploadToS3: vi.fn(),
}));

vi.mock('#src/modules/media/repository/index.js', () => ({
  MediaAssetRepository: {
    create: vi.fn(),
  },
}));
vi.mock('#src/modules/media/errors/index.js', () => ({
  MediaError: {
    uploadFailed: vi.fn((message) => new Error(`Upload failed: ${message}`)),
  },
}));

describe('Media Service', () => {
  let mockRequest;

  beforeEach(() => {
    vi.resetAllMocks();

    mockRequest = {
      body: {
        file: {
          buffer: Buffer.from('test file content'),
          filename: 'test-file.jpg',
          mimetype: 'image/jpeg',
        },
        visibility: 'public',
        altText: 'Test image',
        metadata: {
          description: 'Test description',
        },
      },
      authInfo: {
        id: 'user-123',
      },
      server: {
        config: {
          S3_BUCKET: 'test-bucket',
        },
      },
    };

    uploadToS3.mockResolvedValue('https://test-bucket.s3.amazonaws.com/test-file.jpg');

    MediaAssetRepository.create.mockResolvedValue({
      id: 'media-123',
      filePath: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
      fileType: 'IMAGE',
      mimeType: 'image/jpeg',
      fileSize: 17,
      altText: 'Test image',
      visibility: 'public',
      metadata: {
        originalFilename: 'test-file.jpg',
        description: 'Test description',
      },
    });
  });

  describe('create', () => {
    it('should successfully upload a file and create a media asset record', async () => {
      const result = await create(mockRequest);

      expect(uploadToS3).toHaveBeenCalledWith(
        mockRequest.body.file.buffer,
        mockRequest.body.file.filename,
        mockRequest.body.file.mimetype,
        mockRequest.server,
      );

      expect(MediaAssetRepository.create).toHaveBeenCalledWith(
        mockRequest.server,
        {
          fileType: MediaAssetConstant.FILE_TYPE.IMAGE,
          filePath: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
          mimeType: 'image/jpeg',
          fileSize: 17,
          altText: 'Test image',
          visibility: 'public',
          metadata: {
            originalFilename: 'test-file.jpg',
            description: 'Test description',
          },
        },
        {
          authInfoId: 'user-123',
        },
      );
      expect(result).toEqual({
        id: 'media-123',
        filePath: 'https://test-bucket.s3.amazonaws.com/test-file.jpg',
        fileType: 'IMAGE',
        mimeType: 'image/jpeg',
        fileSize: 17,
        altText: 'Test image',
        visibility: 'public',
        metadata: {
          originalFilename: 'test-file.jpg',
          description: 'Test description',
        },
      });
    });

    it('should handle files with toBuffer method instead of buffer property', async () => {
      mockRequest.body.file = {
        toBuffer: vi.fn().mockResolvedValue(Buffer.from('test file content')),
        filename: 'test-file.jpg',
        mimetype: 'image/jpeg',
      };

      await create(mockRequest);

      expect(mockRequest.body.file.toBuffer).toHaveBeenCalled();

      expect(uploadToS3).toHaveBeenCalledWith(
        Buffer.from('test file content'),
        'test-file.jpg',
        'image/jpeg',
        mockRequest.server,
      );
    });

    it('should correctly identify image file types', async () => {
      mockRequest.body.file.mimetype = 'image/png';

      await create(mockRequest);

      expect(MediaAssetRepository.create).toHaveBeenCalledWith(
        mockRequest.server,
        expect.objectContaining({
          fileType: MediaAssetConstant.FILE_TYPE.IMAGE,
          mimeType: 'image/png',
        }),
        expect.objectContaining({
          authInfoId: 'user-123',
        }),
      );
    });

    it('should correctly identify audio file types', async () => {
      mockRequest.body.file.mimetype = 'audio/mp3';

      await create(mockRequest);

      expect(MediaAssetRepository.create).toHaveBeenCalledWith(
        mockRequest.server,
        expect.objectContaining({
          fileType: MediaAssetConstant.FILE_TYPE.AUDIO,
          mimeType: 'audio/mp3',
        }),
        expect.objectContaining({
          authInfoId: 'user-123',
        }),
      );
    });

    it('should default to document file type for other mime types', async () => {
      mockRequest.body.file.mimetype = 'application/pdf';

      await create(mockRequest);

      expect(MediaAssetRepository.create).toHaveBeenCalledWith(
        mockRequest.server,
        expect.objectContaining({
          fileType: MediaAssetConstant.FILE_TYPE.DOCUMENT,
          mimeType: 'application/pdf',
        }),
        expect.objectContaining({
          authInfoId: 'user-123',
        }),
      );
    });

    it('should use default values when optional parameters are not provided', async () => {
      delete mockRequest.body.visibility;
      delete mockRequest.body.altText;
      delete mockRequest.body.metadata;

      await create(mockRequest);

      expect(MediaAssetRepository.create).toHaveBeenCalledWith(
        mockRequest.server,
        expect.objectContaining({
          visibility: 'public',
          altText: null,
          metadata: {
            originalFilename: 'test-file.jpg',
          },
        }),
        expect.objectContaining({
          authInfoId: 'user-123',
        }),
      );
    });

    it('should throw an error when S3 upload fails', async () => {
      const errorMessage = 'S3 connection error';
      uploadToS3.mockRejectedValue(new Error(errorMessage));

      await expect(create(mockRequest)).rejects.toThrow(`Upload failed: ${errorMessage}`);

      expect(MediaError.uploadFailed).toHaveBeenCalledWith(errorMessage);

      expect(MediaAssetRepository.create).not.toHaveBeenCalled();
    });

    it('should throw an error when media asset creation fails', async () => {
      const errorMessage = 'Database error';
      MediaAssetRepository.create.mockRejectedValue(new Error(errorMessage));

      await expect(create(mockRequest)).rejects.toThrow(errorMessage);

      expect(uploadToS3).toHaveBeenCalled();
    });

    it('should throw an error when request body is missing', async () => {
      MediaError.missingFile = vi.fn(() => new Error('File is missing'));

      mockRequest.body = undefined;

      await expect(create(mockRequest)).rejects.toThrow('File is missing');

      expect(MediaError.missingFile).toHaveBeenCalled();
      expect(uploadToS3).not.toHaveBeenCalled();
      expect(MediaAssetRepository.create).not.toHaveBeenCalled();
    });

    it('should throw an error when file is missing in request body', async () => {
      MediaError.missingFile = vi.fn(() => new Error('File is missing'));

      delete mockRequest.body.file;

      await expect(create(mockRequest)).rejects.toThrow('File is missing');

      expect(MediaError.missingFile).toHaveBeenCalled();
      expect(uploadToS3).not.toHaveBeenCalled();
      expect(MediaAssetRepository.create).not.toHaveBeenCalled();
    });

    it('should pass entity ID from request to repository', async () => {
      mockRequest.entity = {
        id: 'entity-456',
      };

      await create(mockRequest);

      expect(MediaAssetRepository.create).toHaveBeenCalledWith(
        mockRequest.server,
        expect.objectContaining({
          entityId: 'entity-456',
        }),
        expect.objectContaining({
          authInfoId: 'user-123',
        }),
      );
    });
  });
});
