import { describe, expect, it, vi } from 'vitest';

import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { create } from '#src/modules/media/schemas/media.schema.js';

vi.mock('#src/modules/core/schemas/index.js', () => ({
  CoreSchema: {
    ERROR_RESPONSE: {
      400: {
        description: 'Bad Request',
        type: 'object',
        properties: {
          statusCode: { type: 'number' },
          error: { type: 'string' },
          message: { type: 'string' },
        },
      },
      401: {
        description: 'Unauthorised',
        type: 'object',
        properties: {
          statusCode: { type: 'number' },
          error: { type: 'string' },
          message: { type: 'string' },
        },
      },
      500: {
        description: 'Internal Server Error',
        type: 'object',
        properties: {
          statusCode: { type: 'number' },
          error: { type: 'string' },
          message: { type: 'string' },
        },
      },
    },
  },
}));

describe('Media Schema', () => {
  describe('create schema', () => {
    it('should have the correct tags', () => {
      expect(create.tags).toEqual(['General / Media Management']);
    });

    it('should have the correct summary and description', () => {
      expect(create.summary).toBe('Upload media file');
      expect(create.description).toBe('Upload a media file (image, video, audio, document)');
    });

    it('should consume multipart/form-data', () => {
      expect(create.consumes).toEqual(['multipart/form-data']);
    });

    it('should have the correct body schema', () => {
      expect(create.body.type).toBe('object');
      expect(create.body.properties).toHaveProperty('file');
      expect(create.body.properties.file.type).toBe('string');
      expect(create.body.properties.file.format).toBe('binary');
      expect(create.body.properties.file.description).toBe('Media file to upload');
      expect(create.body.required).toEqual(['file']);
    });

    it('should have the correct success response schema', () => {
      const successResponse = create.response[200];

      expect(successResponse.description).toBe('Success response');
      expect(successResponse.type).toBe('object');

      expect(successResponse.properties).toHaveProperty('message');
      expect(successResponse.properties.message.type).toBe('string');
      expect(successResponse.properties.message.description).toBe('Success message');

      expect(successResponse.properties).toHaveProperty('data');
      expect(successResponse.properties.data.type).toBe('object');

      expect(successResponse.properties.data.properties).toHaveProperty('id');
      expect(successResponse.properties.data.properties.id.type).toBe('string');
      expect(successResponse.properties.data.properties.id.format).toBe('uuid');

      expect(successResponse.properties.data.properties).toHaveProperty('filePath');
      expect(successResponse.properties.data.properties.filePath.type).toBe('string');
    });

    it('should include error responses from CoreSchema', () => {
      expect(create.response).toHaveProperty('400');
      expect(create.response).toHaveProperty('401');
      expect(create.response).toHaveProperty('500');

      expect(create.response[400]).toEqual(CoreSchema.ERROR_RESPONSE[400]);
      expect(create.response[401]).toEqual(CoreSchema.ERROR_RESPONSE[401]);
      expect(create.response[500]).toEqual(CoreSchema.ERROR_RESPONSE[500]);
    });

    it('should not have any additional properties in the schema', () => {
      const schemaKeys = Object.keys(create);
      const expectedKeys = ['tags', 'summary', 'description', 'consumes', 'body', 'response'];

      schemaKeys.forEach((key) => {
        expect(expectedKeys).toContain(key);
      });

      expectedKeys.forEach((key) => {
        expect(schemaKeys).toContain(key);
      });
    });
  });
});
