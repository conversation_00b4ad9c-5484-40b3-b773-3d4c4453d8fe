import Fastify from 'fastify';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import onRegisterHook from '#src/hooks/onRegister.hook.js';

// Mock the Kafka worker
vi.mock('#src/workers/retry-kafka-worker.js', () => ({
  default: vi.fn().mockResolvedValue(), // Mock the Kafka worker method
}));

describe('Test case for onRegister hook', () => {
  let fastify;
  let onRegisterFunction;

  beforeEach(async () => {
    fastify = Fastify();
    fastify.config = { KAFKA: false };

    // Mock the listen method to avoid network-related errors
    vi.spyOn(fastify, 'listen').mockResolvedValue();

    // Mocking the server and address calls to prevent the 'family' error
    vi.spyOn(fastify, 'server', 'get').mockReturnValue({
      address: () => ({ family: 'IPv4' }), // Mocking 'family' directly
    });

    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(onRegisterHook, {});

    // Extract the function
    onRegisterFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onRegister')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onRegister hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onRegister', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the onRegister hook
    await onRegisterFunction(fastify, {});

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onRegister hook');
  });
});
