import Fastify from 'fastify';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import onSendHook from '#src/hooks/onSend.hook.js';
import { modifyPayload } from '#src/utils/response-modifier.util.js';

vi.mock('#src/utils/response-modifier.util.js', () => ({
  modifyPayload: vi.fn(),
}));

describe('Test case for onSend hook', () => {
  let fastify;
  let onSendFunction;

  // Mock the data and function
  let mockRequest = {};
  const mockReply = {
    statusCode: 200,
  };
  const mockPayload = JSON.stringify({ data: 'test' });

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Reset mock request object
    mockRequest = {
      id: 'test-request-id',
      responsePayload: {},
    };

    // Register the hook
    await fastify.register(onSendHook, {});

    // Extract the function
    onSendFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onSend')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onSend hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onSend', expect.any(Function));
  });

  it('should have logged debug', async () => {
    // Trigger the onSend hook
    await onSendFunction(mockRequest, mockReply, mockPayload);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onSend hook');
  });

  it('should have updated responsePayload when modified payload does not equal to payload', async () => {
    // Mock modifyPayload returned value
    const mockModifiedPayload = JSON.stringify({ data: 'modified' });
    modifyPayload.mockReturnValue(mockModifiedPayload);

    // Trigger the onSend hook
    await onSendFunction(mockRequest, mockReply, mockPayload);

    // Check if responsePayload was updated
    expect(mockRequest.responsePayload).toEqual(JSON.parse(mockModifiedPayload));
  });

  it('should not update responsePayload when modified payload equal to payload', async () => {
    // Mock modifyPayload returned value
    modifyPayload.mockReturnValue(mockPayload);

    // Save before value
    const beforeChange = mockRequest.responsePayload;

    // Trigger the onSend hook
    await onSendFunction(mockRequest, mockReply, mockPayload);

    // Check if responsePayload was not updated
    expect(mockRequest.responsePayload).toEqual(beforeChange);
  });

  it('should log error when failed to parse modified payload', async () => {
    // Mock modifyPayload returned value as function throwing error
    const mockError = new Error('Test error');
    const mockModifiedPayload = vi.fn().mockImplementation(() => {
      throw mockError;
    });
    modifyPayload.mockReturnValue(mockModifiedPayload);

    // Trigger the onSend hook
    await onSendFunction(mockRequest, mockReply, mockPayload);

    // Check if the error was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalled(
      mockError,
      'Failed to parse modified payload for logging',
    );
  });
});
