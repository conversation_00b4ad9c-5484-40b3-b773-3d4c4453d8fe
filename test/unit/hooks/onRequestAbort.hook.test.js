import Fastify from 'fastify';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import onRequestAbortHook from '#src/hooks/onRequestAbort.hook.js';

describe('Test case for onRequestAbort hook', () => {
  let fastify;
  let onRequestAbortFunction;

  const mockRequest = {};

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(onRequestAbortHook);

    // Extract the function
    onRequestAbortFunction = fastify.addHook.mock.calls.find(
      (call) => call[0] === 'onRequestAbort',
    )[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onRequestAbort hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onRequestAbort', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the onRequestAbort hook
    await onRequestAbortFunction(mockRequest);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onRequestAbort hook');
  });
});
