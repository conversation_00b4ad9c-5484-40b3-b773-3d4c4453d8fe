import Fastify from 'fastify';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import onTimeoutHook from '#src/hooks/onTimeout.hook.js';

describe('Test case for onTimeout hook', () => {
  let fastify;
  let onTimeoutFunction;

  const mockRequest = {};
  const mockReply = {};

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(onTimeoutHook, {});

    // Extract the function
    onTimeoutFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onTimeout')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onTimeout hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onTimeout', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the onTimeout hook
    await onTimeoutFunction(mockRequest, mockReply);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onTimeout hook');
  });
});
