import Fastify from 'fastify';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import onCloseHook from '#src/hooks/onClose.hook.js';

describe('Test case for onClose hook', () => {
  let fastify;
  let onCloseFunction;

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(onCloseHook);

    // Extract the function
    onCloseFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onClose')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onClose hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onClose', expect.any(Function));
  });

  it('should handle error correctly', async () => {
    // Simulate an error in the hook
    const mockError = new Error('Test error');

    // Mock a function that throws an error inside the hook
    fastify.mockErrorForUnitTest = vi.fn().mockImplementation(() => {
      throw mockError;
    });

    // Call the onClose hook function
    await expect(onCloseFunction(fastify)).rejects.toThrow(mockError);

    // Check if the error was called with correct arguments
    expect(fastify.log.error).toHaveBeenCalledWith(mockError, 'Error during shutdown');
  });

  it('should handle clean exits', async () => {
    // Call the onClose hook function
    await onCloseFunction(fastify);

    // Verify that no errors were logged
    expect(fastify.log.error).not.toHaveBeenCalled();
  });
});
