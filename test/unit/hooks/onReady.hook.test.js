import { spawn } from 'child_process';

import Fastify from 'fastify';
import geoip from 'geoip-lite';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import onReadyHook from '#src/hooks/onReady.hook.js';
import { retryKafka } from '#src/hooks/onReady.hook.js';
import * as cacheUtil from '#src/utils/cache.util.js';

vi.mock('geoip-lite');
vi.mock('child_process', () => ({
  spawn: vi.fn().mockImplementation(() => {
    return {
      stdout: {
        on: vi.fn(),
      },
      stderr: {
        on: vi.fn(),
      },
      on: vi.fn(),
    };
  }),
}));
vi.mock('#src/utils/cache.util.js');

describe('Test case for onReady hook', () => {
  let fastify;
  let onReadyFunction;

  beforeEach(async () => {
    vi.resetAllMocks();
    vi.useFakeTimers();

    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
    };

    // Mock the kafka
    fastify.decorate('config', {
      KAFKA: true,
      KAFKA_BROKERS: 'localhost:9092',
      GEOIP_LICENSE_KEY: 'dummy-key',
    });

    fastify.kafka = {
      producer: {
        connect: vi.fn().mockResolvedValue(),
        send: vi.fn().mockResolvedValue(),
      },
      admin: {
        listTopics: vi.fn().mockResolvedValue(['existing-topic']),
        createTopics: vi.fn().mockResolvedValue(),
      },
    };

    // Mock the redis
    fastify.redis = {};

    // Define and configure the mock ChildProcess instance for `spawn`
    const mockStdout = { on: vi.fn() };
    const mockStderr = { on: vi.fn() };
    const mockChildProcessInstance = {
      stdout: mockStdout,
      stderr: mockStderr,
      on: vi.fn((event, cb) => {
        // This mock ensures that when `onReadyHook` calls `updatedb.on('close', ...)`,
        // the callback `cb(0)` is immediately executed, triggering `geoip.reloadData`.
        if (event === 'close') {
          cb(0); // Simulate successful close
        }
      }),
    };

    // Make `spawn` return our configured mock instance for every test
    spawn.mockReturnValue(mockChildProcessInstance);

    // Also, configure `geoip.reloadData` for every test
    geoip.reloadData.mockImplementation((cb) => cb());

    // Register the hook
    await fastify.register(onReadyHook);

    // Extract the function
    onReadyFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onReady')[1];
  });

  it('should register the onReady hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onReady', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the onReady hook
    await onReadyFunction();

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onReady hook');
  });

  it('should create audit-trails topic if not already present', async () => {
    fastify.kafka.admin.listTopics.mockResolvedValueOnce([]);
    fastify.kafka.admin.createTopics.mockResolvedValueOnce();

    await onReadyFunction();

    // Check if the admin client tried to create the audit-trails topic
    expect(fastify.kafka.admin.createTopics).toHaveBeenCalledWith({
      topics: [
        {
          topic: 'audit-trails',
          numPartitions: 50,
          replicationFactor: 1,
        },
      ],
    });
  });

  it('should not create the audit-trails topic if it already exists', async () => {
    fastify.kafka.admin.listTopics.mockResolvedValueOnce(['audit-trails']);

    await onReadyFunction();

    // Ensure the createTopics method was never called, as the topic already exists
    expect(fastify.kafka.admin.createTopics).not.toHaveBeenCalled();
  });

  it('should skip retry if no failed Kafka messages found', async () => {
    cacheUtil.getCacheKeysWithPrefix.mockResolvedValue([]);
    await onReadyFunction();
    vi.advanceTimersByTime(10000);
    expect(cacheUtil.getCacheKeysWithPrefix).toHaveBeenCalled();
  });

  it('should handle errors when creating a new Kafka topic fails', async () => {
    const error = new Error('Kafka topic creation failed');

    // Simulate no existing topic, and error on createTopics
    fastify.kafka.admin.listTopics.mockResolvedValueOnce([]);
    fastify.kafka.admin.createTopics.mockRejectedValueOnce(error);

    await onReadyFunction();

    expect(fastify.log.error).toHaveBeenCalledWith(error, 'Error during Kafka topic setup');
  });

  it('should log an error if sending a Kafka message fails and the message is not removed from Redis', async () => {
    const failedMessageKey = 'failed-kafka-message:123';
    const failedMessage = [{ value: 'some-failed-data' }];
    const sendError = new Error('Kafka send failure');

    cacheUtil.getCacheKeysWithPrefix.mockResolvedValueOnce([failedMessageKey]);
    cacheUtil.fetchFromCache.mockResolvedValueOnce(failedMessage);
    fastify.kafka.producer.send.mockRejectedValueOnce(sendError);

    // Call retryKafka directly
    await retryKafka(fastify, { topicName: 'audit-trails' });

    expect(cacheUtil.getCacheKeysWithPrefix).toHaveBeenCalledWith(
      fastify.redis,
      'failed_kafka_message*',
    );
    expect(cacheUtil.fetchFromCache).toHaveBeenCalledWith(fastify.redis, failedMessageKey);
    expect(fastify.kafka.producer.send).toHaveBeenCalledWith({
      topic: 'audit-trails',
      messages: failedMessage,
    });
    expect(fastify.log.debug).toHaveBeenCalledWith(sendError, 'Kafka send failed from worker');
    expect(cacheUtil.clearCache).not.toHaveBeenCalled();
  });

  it('should log an info message when a Kafka message is successfully resent and removed from Redis', async () => {
    const successMessageKey = 'failed-kafka-message:456';
    const successMessage = [{ value: 'some-successful-data' }];

    cacheUtil.getCacheKeysWithPrefix.mockResolvedValueOnce([successMessageKey]);
    cacheUtil.fetchFromCache.mockResolvedValueOnce(successMessage);
    fastify.kafka.producer.send.mockResolvedValueOnce();

    // Call retryKafka directly
    await retryKafka(fastify, { topicName: 'audit-trails' });

    expect(cacheUtil.fetchFromCache).toHaveBeenCalledWith(fastify.redis, successMessageKey);
    expect(fastify.kafka.producer.send).toHaveBeenCalledWith({
      topic: 'audit-trails',
      messages: successMessage,
    });
    expect(cacheUtil.clearCache).toHaveBeenCalledWith(fastify.redis, successMessageKey);
    expect(fastify.log.info).toHaveBeenCalledWith('Kafka resend from worker');
  });

  it('should skip processing if no message is found for a failed Kafka key', async () => {
    const missingMessageKey = 'failed-kafka-message:789';

    cacheUtil.getCacheKeysWithPrefix.mockResolvedValueOnce([missingMessageKey]);
    cacheUtil.fetchFromCache.mockResolvedValueOnce(null);

    // Call retryKafka directly
    await retryKafka(fastify, { topicName: 'audit-trails' });

    expect(cacheUtil.getCacheKeysWithPrefix).toHaveBeenCalledWith(
      fastify.redis,
      'failed_kafka_message*',
    );
    expect(cacheUtil.fetchFromCache).toHaveBeenCalledWith(fastify.redis, missingMessageKey);

    expect(fastify.kafka.producer.send).not.toHaveBeenCalled();
  });

  it('should update and reload GeoIP data', async () => {
    const mockStdout = { on: vi.fn() };
    const mockStderr = { on: vi.fn() };
    const mockChild = {
      stdout: mockStdout,
      stderr: mockStderr,
      on: vi.fn((event, cb) => {
        if (event === 'close') {
          cb(0);
        }
      }),
    };

    spawn.mockReturnValue(mockChild);
    geoip.reloadData = vi.fn((cb) => cb());

    await onReadyFunction();

    expect(spawn).toHaveBeenCalledWith(expect.stringContaining('node'), [
      './node_modules/geoip-lite/scripts/updatedb.js',
      'license_key=dummy-key',
    ]);
    expect(geoip.reloadData).toHaveBeenCalled();
    expect(fastify.log.info).toHaveBeenCalledWith('GeoIP data reloaded into memory.');
  });

  it('should log error if GeoIP update fails', async () => {
    const mockChild = {
      stdout: { on: vi.fn() },
      stderr: { on: vi.fn((_, cb) => cb('GeoIP error')) },
      on: vi.fn(),
    };
    spawn.mockReturnValue(mockChild);

    await onReadyFunction();

    expect(fastify.log.error).toHaveBeenCalledWith('GeoIP update failed: GeoIP error');
  });

  it('should log error if GeoIP license key missing', async () => {
    fastify.config.GEOIP_LICENSE_KEY = undefined;

    await onReadyFunction();

    expect(fastify.log.warn).toHaveBeenCalledWith('GeoIP license key missing. Skipping update.');
    expect(spawn).not.toHaveBeenCalled();
    expect(geoip.reloadData).not.toHaveBeenCalled();
  });

  it('should log info when GeoIP database update output is received', async () => {
    const mockStdout = {
      on: vi.fn((event, callback) => {
        if (event === 'data') {
          callback('GeoIP update output line'); // Simulate stdout data
        }
      }),
    };

    const mockStderr = { on: vi.fn() };
    const mockChild = {
      stdout: mockStdout,
      stderr: mockStderr,
      on: vi.fn((event, cb) => {
        if (event === 'close') {
          cb(0); // Simulate process ending successfully
        }
      }),
    };

    spawn.mockReturnValue(mockChild);

    await onReadyFunction(); // Triggers updateGeoIPDatabase via hook

    expect(fastify.log.info).toHaveBeenCalledWith(
      'GeoIP database updated: GeoIP update output line',
    );
  });
});
