# Ignore artifacts:
build
coverage

# Ignore all markdown files:
**/*.md

# Ignore all yaml files:
**/*.yml
**/*.yaml

# Ignore Dockerfile
Dockerfile
Dockerfile.filebeat

# Ignore Node specific files
node_modules

# Ignore environment files
.env
.env.*

# Ignore changelog template file
changelog-template.hbs

# Ignore all other files
.editorconfig
.gitignore
.husky
.nvmrc
.prettierignore
.sequelizerc
sonar-project.properties

# REST Client
*.http

# Configuration files
**/*.conf
