'use strict';

const path = require('path');
const { Op } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));
const { uuidExtension, trigramExtension } = require(
  path.join(__dirname, '..', 'helpers', 'extensions.cjs'),
);

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createExtensions(queryInterface);

    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_localisations_category" AS ENUM('currency', 'language', 'region');`,
    );

    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);

    await addTrigramSupport(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('localisations');

    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_localisations_category";');

    // 3. Optionally, drop the UUID & Trigram extension if it’s no longer needed
    // await queryInterface.sequelize.query('DROP EXTENSION IF EXISTS uuid-ossp');
    // await queryInterface.sequelize.query('DROP EXTENSION IF EXISTS pg_trgm');
  },
};

const createExtensions = async (queryInterface) => {
  await uuidExtension(queryInterface);
  await trigramExtension(queryInterface);
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'localisations',
    addBaseColumns(
      {
        category: {
          allowNull: false,
          type: 'enum_localisations_category',
          defaultValue: 'currency',
          comment: 'The category of the localisation',
        },
        name: {
          allowNull: false,
          type: Sequelize.STRING(100),
          comment: 'The full name of the localisation item',
        },
        code: {
          allowNull: false,
          type: Sequelize.STRING(10),
          comment: 'The unique code for the localisation item (e.g., USD for US Dollar)',
        },
        metadata: {
          allowNull: true,
          type: Sequelize.JSONB,
          defaultValue: {},
          comment: 'Additional metadata for the localisation item in JSON format',
        },
      },
      { withParanoid: true },
    ),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('localisations', {
    fields: ['category', 'code'],
    type: 'check',
    name: 'chk_localisations_code_format',
    where: Sequelize.literal(`
      (category = 'currency' AND code ~ '^[A-Z]{3}$')
      OR (category = 'language' AND code ~ '^[A-Z]{2,3}(-[A-Z]{2})?$')
      OR (category = 'region' AND code ~ '^[A-Z]{2}$')
    `),
    comment: 'Validate code pattern based on category',
  });

  await queryInterface.addConstraint('localisations', {
    fields: ['category', 'code'],
    type: 'unique',
    name: 'unq_localisations_category_code',
    comment: 'Ensures each category-code combination is unique',
  });
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('localisations', ['category'], {
    name: 'idx_localisations_category',
    comment: 'Improves query performance when filtering by category',
  });

  await queryInterface.addIndex('localisations', ['deleted_at'], {
    name: 'idx_localisations_deleted_at',
    where: {
      deleted_at: { [Op.ne]: null },
    },
    comment: 'Optimizes queries filtering out non-deleted records',
  });

  await queryInterface.addIndex('localisations', ['metadata'], {
    name: 'idx_localisations_metadata_gin',
    using: 'gin',
    comment: 'Improves performance of JSONB metadata queries',
  });
};

const addTrigramSupport = async (queryInterface) => {
  await queryInterface.sequelize.query(`
    CREATE INDEX idx_localisations_name_trgm ON localisations
    USING gin (name gin_trgm_ops)
  `);

  await queryInterface.sequelize.query(`
    CREATE INDEX idx_localisations_code_trgm ON localisations
    USING gin (code gin_trgm_ops)
  `);
};
