# This pipeline allows you to build and push your docker image to AWS Elastic Container Registry (ECR).
# The workflow allows running tests, code linting and security scans on feature branches.
# The docker image will be pushed to the private registry after the code is merged to develop.

# Prerequisites: $AWS_ACCESS_KEY_ID, $AWS_SECRET_ACCESS_KEY, $AWS_DEFAULT_REGION, $ECR_REPO_URL and $IMAGE_NAME setup as deployment variables.

image: node:20 # Use the official Node.js 20 image

definitions:
  steps:
    - step: &installation-step
        name: Install dependencies
        caches:
          - node
        script:
          - node -v # Verify Node.js version
          - npm install # Use npm install to generate lockfile
        artifacts:
          - node_modules/**
    - step: &test-step
        name: Run tests and coverage
        script:
          - npm test
        artifacts:
          - coverage/**
    - step: &lint-step
        name: Run code linter
        script:
          - npm run lint
    - step: &analysis-step
        name: Perform SonarQube analysis
        image: sonarsource/sonar-scanner-cli:10
        caches:
          - sonar
        clone:
          depth: full
        script:
          - sonar-scanner -Dsonar.branch.name=$BITBUCKET_BRANCH -Dproject.settings=config/sonar-project.properties
    - step: &build-and-push-image-step
        name: Build and push Docker image
        image: docker:27-dind
        services:
          - docker
        caches:
          - docker
        script:
          - export PATH=/usr/bin:$PATH
          - export DOCKER_BUILDKIT=0

          # - docker-compose -f compose.staging.yaml pull
          # - docker-compose -f compose.staging.yaml build
          - docker build -t $IMAGE_NAME:$DEPLOYMENT_TAG -f Dockerfile .
          - docker images

          # - docker tag $IMAGE_NAME:latest $IMAGE_NAME:$DEPLOYMENT_TAG
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
              AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
              AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
              IMAGE_NAME: $IMAGE_NAME
              TAGS: $DEPLOYMENT_TAG

          - apk add --no-cache curl
          - curl --version # Verify curl availability
          # trigger watchtower webhook
          - curl -X GET "$WATCHTOWER_WEBHOOK_URL" -H "Authorization:Bearer $WATCHTOWER_API_TOKEN"

  caches:
    sonar: ~/.sonar
    node: node_modules

clone:
  depth: full

pipelines:
  branches:
    'main':
      - step: *installation-step
      - step: *test-step
      - step: *lint-step
      - step: *analysis-step
      # - step:
      #     deployment: production
      #     <<: *build-and-push-image-step

    'develop':
      - step: *installation-step
      - step: *test-step
      - step: *lint-step
      - step: *analysis-step
      # - step:
      #     deployment: production
      #     <<: *build-and-push-image-step

    ## Intended for testing purposes only
    # 'feature/*':
      # - step: *installation-step
      # - step: *test-step
      # - step: *lint-step
      # - step: *analysis-step
      # - step: *build-and-push-image-step

    ## For QPLY-610 testing only
    'feature/QPLY-610-Staging-Docker-Setup':
      - step: *installation-step
      - step:
          deployment: staging
          <<: *build-and-push-image-step

  pull-requests:
    '**':
      - step: *installation-step
      - step: *test-step
      - step: *lint-step
      - step: *analysis-step
