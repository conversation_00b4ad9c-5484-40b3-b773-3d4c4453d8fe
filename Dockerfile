# Use an official Node.js runtime as a parent image
FROM node:20

# Set the working directory in the container
WORKDIR /app

# Create a non-root user
RUN groupadd -r appgroup && useradd -r -g appgroup appuser

# Copy package.json and package-lock.json into the working directory
COPY package*.json ./

# Install the dependencies
RUN npm install --ignore-scripts

# Copy the current directory contents into the container
COPY . .

# Change ownership of the app directory to the non-root user
RUN chown -R appuser:appgroup /app

# Switch to the non-root user
USER appuser

# Set Fastify address to 0.0.0.0
# to allow incoming connections to reach Fastify inside the container
ENV DOCKER_CONTAINER=true
ENV FASTIFY_ADDRESS=0.0.0.0
ENV FASTIFY_PORT=3000

# Expose port 3000
EXPOSE 3000

# Command to run the app
CMD ["npm", "run", "dev"]
