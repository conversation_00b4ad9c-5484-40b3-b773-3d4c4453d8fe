'use strict';

/**
 * Adds the UUID extension to the database if it doesn't already exist.
 * This allows the use of UUID data types in the database.
 * @param {import('sequelize').QueryInterface} queryInterface - The Sequelize QueryInterface instance
 * @returns {Promise<void>}
 */
const uuidExtension = async (queryInterface) => {
  await queryInterface.sequelize.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
};

/**
 * Adds the pg_trgm (trigram) extension to the database if it doesn't already exist.
 * This extension provides functions and operators for determining the similarity of alphanumeric text based on trigram matching.
 * @param {import('sequelize').QueryInterface} queryInterface - The Sequelize QueryInterface instance
 * @returns {Promise<void>} A promise that resolves when the extension has been added or if it already exists
 */
const trigramExtension = async (queryInterface) => {
  await queryInterface.sequelize.query('CREATE EXTENSION IF NOT EXISTS pg_trgm');
};

const ltreeExtension = async (queryInterface) => {
  await queryInterface.sequelize.query('CREATE EXTENSION IF NOT EXISTS ltree;');
};

module.exports = {
  ltreeExtension,
  trigramExtension,
  uuidExtension,
};
