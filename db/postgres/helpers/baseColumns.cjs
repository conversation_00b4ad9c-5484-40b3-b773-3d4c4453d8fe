'use strict';

const { DataTypes } = require('sequelize');
const { v1: uuidv1 } = require('uuid');

/**
 * Custom UUID generator configuration
 * Uses a static node ID instead of MAC address for security
 * @constant {Array<number>} NODE_ID - Static node identifier bytes
 */
const NODE_ID = [0x01, 0x23, 0x45, 0x67, 0x89, 0xab];
/**
 * @constant {number} CLOCK_SEQ - Static clock sequence for UUIDv1
 */
const CLOCK_SEQ = 0x1234;

/**
 * Generates a UUIDv1 with fixed node ID and clock sequence
 * @returns {string} Generated UUID string
 */
const generateUUID = () => uuidv1({ node: NODE_ID, clockseq: CLOCK_SEQ });

/**
 * Default options for base columns
 * @constant {Object} DEFAULT_OPTIONS
 */
const DEFAULT_OPTIONS = {
  withTimestamps: true,
  withParanoid: false,
  withUserTracking: true,
  withVersion: false,
};

/**
 * @typedef {Object} BaseColumnOptions
 * @property {boolean} [withTimestamps=true] - Include created_at and updated_at
 * @property {boolean} [withParanoid=false] - Include deleted_at and deleted_by
 * @property {boolean} [withUserTracking=true] - Include created_by and updated_by
 * @property {boolean} [withVersion=false] - Include version column
 */

/**
 * Adds standardized base columns to a Sequelize model definition
 * @param {Object} columns - Additional model columns to merge with base columns
 * @param {BaseColumnOptions} [opts] - Configuration options for base columns
 * @returns {Object} Complete column definition including base columns
 */
const addBaseColumns = (columns, opts = {}) => {
  const OPTIONS = { ...DEFAULT_OPTIONS, ...opts };
  const now = DataTypes.NOW;

  /**
   * Core base columns: id and optional version
   */
  const BASE = {
    id: {
      type: DataTypes.UUID,
      defaultValue: generateUUID,
      primaryKey: true,
      allowNull: false,
    },
    ...(OPTIONS.withVersion && {
      version: {
        type: DataTypes.BIGINT,
        defaultValue: 1,
        allowNull: false,
        comment: 'Row version for optimistic locking',
      },
    }),
  };

  // timestamps
  if (OPTIONS.withTimestamps) {
    Object.assign(BASE, {
      created_at: { type: DataTypes.DATE, allowNull: false, defaultValue: now },
      updated_at: { type: DataTypes.DATE, allowNull: false, defaultValue: now },
    });
  }

  // paranoid (soft deletes)
  if (OPTIONS.withParanoid) {
    Object.assign(BASE, {
      deleted_at: { type: DataTypes.DATE, allowNull: true },
      deleted_by: { type: DataTypes.UUID, allowNull: true },
    });
  }

  // user tracking
  if (OPTIONS.withUserTracking) {
    Object.assign(BASE, {
      created_by: { type: DataTypes.UUID, allowNull: true },
      updated_by: { type: DataTypes.UUID, allowNull: true },
    });
  }

  return { ...BASE, ...columns };
};

/**
 * Adds default values for seeding database records
 * @param {Object} data - Initial seed data
 * @returns {Object} Seed data with default values populated
 */
const addDefaultValues = (data) => ({
  ...data,
  id: data.id || generateUUID(),
});

module.exports = {
  generateUUID,
  addBaseColumns,
  addDefaultValues,
};
