/* eslint-disable no-console */
import { readdirSync } from 'node:fs';
import { join } from 'node:path';

import { SequelizeAdapter, factory } from 'factory-girl';
import { Sequelize } from 'sequelize';

import sequelizeConfig from '#config/sequelize.config.js';
import { getFactoriesPath, getModelPath } from '#src/utils/file.util.js';

// Initialise Sequelize connection
const sequelize = new Sequelize(sequelizeConfig);
const initialiseDatabase = async () => {
  try {
    await sequelize.authenticate();
    console.log('[factory] Database connection established for factories');
  } catch (err) {
    console.error('[factory] Sequelize connection error:', err);
    process.exit(1);
  }
};

// Load Sequelize models dynamically from the models directory
const loadModels = async () => {
  const models = {};
  const modelsDir = join(getModelPath(), 'postgres');
  const files = readdirSync(modelsDir);

  for (const file of files) {
    if (!file.endsWith('.model.js')) {
      continue;
    }

    const { default: modelFn } = await import(join(modelsDir, file));
    const model = modelFn({
      psql: { connection: sequelize },
    });

    models[model.name] = model;
  }

  // Setup associations (after all models are defined)
  Object.values(models).forEach((model) => {
    if (typeof model.associate === 'function') {
      model.associate(models);
    }
  });

  return models;
};

// Create and set Sequelize adapter for factory-girl
const initialiseFactory = async () => {
  const adapter = new SequelizeAdapter();
  factory.setAdapter(adapter);
};

// Dynamically register all factories in the factories directory
export const registerFactories = async () => {
  const factoriesDir = getFactoriesPath();

  const files = readdirSync(factoriesDir);
  for (const file of files) {
    if (file.endsWith('.factory.js')) {
      await import(join(factoriesDir, file));
    }
  }
};

await initialiseDatabase();
const models = await loadModels();
await initialiseFactory();

export { factory, models, sequelize };
