/* eslint-disable no-console */
import minimist from 'minimist';

import { factory, registerFactories } from './index.js';

const args = minimist(process.argv.slice(2));

// Define command line arguments here
// For example:
// npm run factory entity
// npm run factory -- entity --count=5
// npm run factory -- entity --count=10 --build
const {
  _: [factoryName],
  count = 1,
  build: buildOnly = false,
  ...attrs
} = args;

/**
 * This function runs a factory to create or build data for specified factoryName.
 * If count is not provided, it will create 1 record.
 * If build is set to true, it will build the data but not save it to the database.
 * Otherwise, it will create the data and save it to the database.
 *
 * @param {string} factoryName - The name of the factory to run.
 * @param {number} count - The number of records to create. Defaults to 1.
 * @param {boolean} build - If true, the data will be built but not saved to the database. Defaults to false.
 * @param {any} attrs - Additional attributes to pass to the factory.
 * @returns {Promise<void>}
 */
const runFactory = async () => {
  if (!factoryName) {
    console.error(
      '[factory] Usage: npm run factory -- <factoryName> [--count=N] [--build] [--attr1=value1] [--attr2=value2]',
    );
    process.exit(1);
  }

  await registerFactories();

  if (!factory.factories[factoryName]) {
    console.error(`[factory] Factory "${factoryName}" is not registered.`);
    process.exit(1);
  }

  console.log(`[factory] ${buildOnly ? 'Building' : 'Creating'} ${count} "${factoryName}"…`);

  const results = buildOnly
    ? await factory.buildMany(factoryName, count, attrs).then((results) => {
        console.log(
          `[factory] Results:`,
          results.map((r) => r.toJSON()),
        );
        return results;
      })
    : await factory.createMany(factoryName, count, attrs);

  console.log(`[factory] Done. ${results.length} record(s) ${buildOnly ? 'built' : 'created'}.`);
};

runFactory()
  .then(() => process.exit(0))
  .catch((err) => {
    console.error('[factory] Error:', err.message);
    console.error(err.stack);
    process.exit(1);
  });
