import { faker } from '@faker-js/faker';
import { Op } from 'sequelize';

import { factory, models } from './index.js';

const { Department, Entity, Module, Policy, DepartmentModule } = models;
const NAME_MAX = 50;
const pad = (n) => String(n).padStart(4, '0');
/**
 * Copy selected policy fields from a Module-level Policy
 * to a DepartmentModule-level Policy payload.
 */
const toDeptPolicyDefaults = (modulePolicy, audit = {}) => {
  // If module has a policy, clone it; otherwise default all to false.
  const base = modulePolicy || {};
  const bool = (v) => v === true; // ensure booleans

  return {
    canView: bool(base.canView),
    canCreate: bool(base.canCreate),
    canEdit: bool(base.canEdit),
    canImport: bool(base.canImport),
    canExport: bool(base.canExport),
    canManage: bool(base.canManage),
    canMasking: bool(base.canMasking),
    canOverwrite: bool(base.canOverwrite),
    canVerify: bool(base.canVerify),
  };
};

/**
 * Ensure department has an entity and hierarchy
 * - If no entityId is provided, create a new Entity
 * - Always attach hierarchy from the entity
 *
 * @param {Department} department
 * @returns {Promise<Department>}
 */
const withHierarchy = async (department) => {
  let entity;

  if (!department.entityId) {
    // No entityId → create one
    entity = await factory.create('entity');
    department.entityId = entity.id;
  } else {
    // Entity already exists
    entity = await Entity.findByPk(department.entityId);
  }

  department.hierarchy = entity.hierarchy;
  return department;
};

/**
 * Ensure every DepartmentModule has a Policy.
 * - Load modules (same hierarchy, navigation_url not null) incl. their policy.
 * - Link to department.
 * - Bulk insert Policy for ALL DepartmentModules (skip dupes).
 */
const withModules = async (department, opts = {}) => {
  const { transaction: t, createdBy = null, updatedBy = null } = opts;

  // STEP 1: Find modules in the same hierarchy with non-null navigation_url
  const modules = await Module.findAll({
    where: {
      hierarchy: department.hierarchy,
      navigation_url: {
        [Op.ne]: null, // not equal to null
      },
    },
    include: [
      {
        model: Policy,
        as: 'policy',
        required: false, // a module may not have a policy yet
      },
    ],
    transaction: t,
  });

  if (modules.length === 0) {
    return; // nothing to do
  }

  // STEP 2: Link modules to the department (create DepartmentModule rows)
  await department.addModules(modules, { transaction: t });

  // Grab the freshly (or previously) linked join rows for just these modules
  const moduleIds = modules.map((m) => m.id);
  const deptModules = await DepartmentModule.findAll({
    where: { departmentId: department.id, moduleId: moduleIds },
    transaction: t,
  });

  // Quick lookup: moduleId -> Module (with its policy)
  const modById = new Map(modules.map((m) => [m.id, m]));

  // 4) Build payloads for ALL deptModules
  const rows = [];
  for (const dm of deptModules) {
    const mod = modById.get(dm.moduleId);
    const defaults = toDeptPolicyDefaults(mod?.policy);

    rows.push({
      parentId: dm.id, // DepartmentModule.hasOne(Policy) via parentId
      ...defaults,
      createdBy,
      updatedBy,
    });
  }

  // 5) Bulk insert for all; rely on UNIQUE(parentId) to skip existing
  // Postgres/MySQL: `ignoreDuplicates` → ON CONFLICT/IGNORE
  await Policy.bulkCreate(rows, {
    transaction: t,
    ignoreDuplicates: true, // requires UNIQUE on parentId
  });
};

/**
 * Department Factory
 *
 * Generates a Department record for development/testing purposes.
 * - Automatically creates a new Entity if no entityId is passed.
 * - Adds hierarchy based on the Entity.
 * - Associates with a random set of Modules matching hierarchy.
 *
 * CLI Usage Examples:
 *   1. Create 1 department with a new Entity:
 *        npm run factory -- department
 *
 *   2. Create 3 departments with new Entities:
 *        npm run factory -- department --count=3
 *
 *   3. Create 2 departments under an existing Entity:
 *        npm run factory -- department --count=2 --entityId=<existingEntityId>
 *
 *   4. Build (but do not save) 3 departments under an existing Entity:
 *        npm run factory -- department --count=3 --build --entityId=<existingEntityId>
 */
factory.define(
  'department',
  Department,
  {
    name: factory.sequence((n) => {
      const base = faker.person.jobArea();
      const suffix = `-A${pad(n)}`;
      return `${base.slice(0, NAME_MAX - suffix.length)}${suffix}`;
    }),
    description: () => faker.lorem.sentence({ min: 5, max: 10 }),
  },
  {
    afterBuild: withHierarchy,
    afterCreate: withModules,
  },
);

// Run "npm run factory -- departmentTemplate" to create a department template
factory.extend(
  'department',
  'departmentTemplate',
  {
    template: true,
  },
  {
    afterBuild: withHierarchy,
    afterCreate: withModules,
  },
);
