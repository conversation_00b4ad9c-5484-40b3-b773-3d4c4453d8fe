import { factory, models } from './index.js';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { UserConstant } from '#src/modules/user/constants/index.js';
import { hashPassword } from '#src/utils/hash.util.js';
import { generate2FASecret } from '#src/utils/twofa.util.js';

const { User, Entity, Role, UserAssociation, UserMfaSetting } = models;
const { USER_TYPES, USER_STATUSES, USER_ORIGINS } = UserConstant;
const { HIERARCHY } = CoreConstant;

/**
 * Root User Factory
 *
 * This factory creates root-level users with all necessary associations (Entity, Role, UserAssociation, MFA).
 *
 * @module RootUserFactory
 *
 * @example
 * // Create a default root user
 * npm run factory -- rootUser
 *
 * @example
 * // Create a root user with MFA enabled
 * npm run factory -- rootUserWithMfa
 *
 * @example
 * // Create a root user with expiry date
 * npm run factory -- rootUserWithExpiry
 *
 * @example
 * // Create multiple root users
 * npm run factory -- rootUser --count=3
 *
 * @example
 * // Create root user with custom attributes
 * npm run factory -- rootUser --name="Custom Admin" --username="customroot"
 *
 * @example
 * // Programmatic usage in code
 * import { factory } from '#db/postgres/factories/index.js';
 *
 * // Create single root user
 * const rootUser = await factory.create('rootUser');
 *
 * // Create with custom attributes
 * const customRoot = await factory.create('rootUser', {
 *   name: 'Custom Admin',
 *   username: 'customadmin',
 *   email: '<EMAIL>'
 * });
 *
 * // Create multiple root users
 * const rootUsers = await factory.createMany('rootUser', 3);
 *
 * // Build without saving to database
 * const builtUser = await factory.build('rootUser');
 *
 * @example
 * // Using the helper function for complete control
 * import { createCompleteRootUser } from '#db/postgres/factories/root-user.factory.js';
 *
 * const result = await createCompleteRootUser({
 *   name: 'Super Admin',
 *   username: 'superadmin',
 *   email: '<EMAIL>',
 *   password: 'SuperSecure@123',
 *   mfaEnabled: true,
 * });
 *
 * console.log('Username:', result.user.username);
 * console.log('Password:', result.plainPassword);
 * console.log('MFA Secret:', result.mfaSetting.secret);
 */

/**
 * Find or create the root entity.
 *
 * @private
 * @async
 * @returns {Promise<Entity>} The root entity instance
 * @throws {Error} If entity creation fails
 */
const findOrCreateRootEntity = async () => {
  let rootEntity = await Entity.findOne({
    where: { hierarchy: HIERARCHY.ROOT },
  });

  if (!rootEntity) {
    rootEntity = await Entity.create({
      hierarchy: HIERARCHY.ROOT,
      accessId: '000000000000',
      code: 'ROOT',
      prefix: 'ROOT',
      name: 'Root Entity',
      description: 'System Root Entity',
      phone: '60100000000',
      email: '<EMAIL>',
      parentId: null,
    });
  }

  return rootEntity;
};

/**
 * Find or create the root role.
 *
 * @private
 * @async
 * @returns {Promise<Role>} The root role instance
 * @throws {Error} If role creation fails
 */
const findOrCreateRootRole = async () => {
  const rootEntity = await findOrCreateRootEntity();

  // Find role by entityId and name (since hierarchy column doesn't exist)
  let rootRole = await Role.findOne({
    where: {
      entityId: rootEntity.id,
      name: 'Super Admin',
    },
  });

  if (!rootRole) {
    rootRole = await Role.create({
      entityId: rootEntity.id,
      name: 'Super Admin',
      description: 'Root Super Administrator Role',
      status: 'ACTIVE',
      template: false,
    });
  }

  return rootRole;
};

/**
 * Check if a root user already exists in the system.
 *
 * @private
 * @async
 * @returns {Promise<boolean>} True if a root user exists, false otherwise
 */
const rootUserExists = async () => {
  const rootEntity = await Entity.findOne({
    where: { hierarchy: HIERARCHY.ROOT },
  });

  if (!rootEntity) {
    return false;
  }

  const rootUserAssociation = await UserAssociation.findOne({
    where: { entityId: rootEntity.id },
    include: [
      {
        model: User,
        as: 'user',
        where: { type: USER_TYPES.NORMAL },
      },
    ],
  });

  return !!rootUserAssociation;
};

/**
 * Generate a unique root username.
 * Starts with 'root' and appends a number if the username already exists.
 *
 * @private
 * @async
 * @returns {Promise<string>} A unique username (e.g., 'root', 'root1', 'root2')
 *
 * @example
 * const username = await generateRootUsername();
 * // Returns: 'root' or 'root1' or 'root2' etc.
 */
const generateRootUsername = async () => {
  const baseUsername = 'root';
  let username = baseUsername;
  let counter = 1;

  while (await User.findOne({ where: { username } })) {
    username = `${baseUsername}${counter}`;
    counter++;
  }

  return username;
};

/**
 * Generate a unique root email address.
 * Starts with '<EMAIL>' and appends a number if the email already exists.
 *
 * @private
 * @async
 * @returns {Promise<string>} A unique email address (e.g., '<EMAIL>', '<EMAIL>')
 *
 * @example
 * const email = await generateRootEmail();
 * // Returns: '<EMAIL>' or '<EMAIL>' etc.
 */
const generateRootEmail = async () => {
  const baseDomain = 'system.root';
  let email = `admin@${baseDomain}`;
  let counter = 1;

  while (await User.findOne({ where: { email } })) {
    email = `admin${counter}@${baseDomain}`;
    counter++;
  }

  return email;
};

/**
 * Create a complete root user with all necessary associations.
 * This is a helper function that creates everything needed for a root user in one transaction.
 *
 * **Important:** This function will throw an error if a root user already exists.
 * Use `factory.create('rootUser')` to create additional root users.
 *
 * @public
 * @async
 * @param {Object} [overrides={}] - Optional overrides for user attributes
 * @param {string} [overrides.name='Root Administrator'] - The name of the root user
 * @param {string} [overrides.username] - Custom username (auto-generated if not provided)
 * @param {string} [overrides.email] - Custom email (auto-generated if not provided)
 * @param {string} [overrides.password='Root@123456'] - Custom password
 * @param {boolean} [overrides.mfaEnabled=false] - Whether to enable MFA for this user
 *
 * @returns {Promise<Object>} Object containing all created entities
 * @returns {User} returns.user - The created user instance
 * @returns {UserAssociation} returns.userAssociation - The user association instance
 * @returns {UserMfaSetting} returns.mfaSetting - The MFA setting instance
 * @returns {Entity} returns.rootEntity - The root entity instance
 * @returns {Role} returns.rootRole - The root role instance
 * @returns {string} returns.plainPassword - The plain text password (for reference only)
 *
 * @throws {Error} If a root user already exists
 * @throws {Error} If user creation fails
 *
 * @example
 * // Create default root user
 * const result = await createCompleteRootUser();
 * console.log('Username:', result.user.username); // 'root'
 * console.log('Password:', result.plainPassword); // 'Root@123456'
 *
 * @example
 * // Create root user with custom attributes
 * const result = await createCompleteRootUser({
 *   name: 'Super Admin',
 *   username: 'superadmin',
 *   email: '<EMAIL>',
 *   password: 'SuperSecure@123',
 *   mfaEnabled: true,
 * });
 *
 * @example
 * // Access all created entities
 * const result = await createCompleteRootUser();
 * console.log('User ID:', result.user.id);
 * console.log('Entity ID:', result.rootEntity.id);
 * console.log('Role ID:', result.rootRole.id);
 * console.log('MFA Secret:', result.mfaSetting.secret);
 */
export const createCompleteRootUser = async (overrides = {}) => {
  // Check if root user already exists
  if (await rootUserExists()) {
    throw new Error(
      'Root user already exists. Use factory.create("rootUser") for additional root users.',
    );
  }

  const rootEntity = await findOrCreateRootEntity();
  const rootRole = await findOrCreateRootRole();

  // Create the root user
  const username = overrides.username || (await generateRootUsername());
  const email = overrides.email || (await generateRootEmail());
  const password = overrides.password || 'Root@123456';

  const user = await User.create({
    name: overrides.name || 'Root Administrator',
    username,
    password: await hashPassword(password),
    email,
    type: USER_TYPES.NORMAL,
    status: USER_STATUSES.ACTIVE,
    validityDate: null, // Root users don't expire
    parentId: null,
  });

  // Create user association
  const userAssociation = await UserAssociation.create({
    userId: user.id,
    entityId: rootEntity.id,
    roleId: rootRole.id,
    origin: USER_ORIGINS.INTERNAL,
  });

  // Create MFA setting
  const { base32secret } = generate2FASecret();
  const mfaStatus = overrides.mfaEnabled
    ? UserConstant.USER_MFA_STATUSES.ENABLED
    : UserConstant.USER_MFA_STATUSES.INACTIVE;
  const mfaSetting = await UserMfaSetting.create({
    userId: user.id,
    secretKey: base32secret,
    status: mfaStatus,
  });

  return {
    user,
    userAssociation,
    mfaSetting,
    rootEntity,
    rootRole,
    plainPassword: password, // Return plain password for reference
  };
};

/**
 * Factory definition for creating a default root user.
 *
 * Creates a root user with:
 * - Auto-generated unique username (root, root1, root2, etc.)
 * - Auto-generated unique email (<EMAIL>, <EMAIL>, etc.)
 * - Default password: 'Root@123456'
 * - MFA disabled by default
 * - No expiry date
 * - Automatically creates UserAssociation and UserMfaSetting
 *
 * @factory rootUser
 * @type {User}
 *
 * @example
 * // CLI usage
 * npm run factory -- rootUser
 *
 * @example
 * // Programmatic usage
 * const user = await factory.create('rootUser');
 *
 * @example
 * // With custom attributes
 * const user = await factory.create('rootUser', {
 *   name: 'Custom Admin',
 *   username: 'customroot'
 * });
 */
factory.define(
  'rootUser',
  User,
  {
    name: () => 'Root Administrator',
    username: async () => await generateRootUsername(),
    password: async () => {
      // Default root password: 'Root@123456'
      return await hashPassword('Root@123456');
    },
    email: async () => await generateRootEmail(),
    type: USER_TYPES.NORMAL,
    status: USER_STATUSES.ACTIVE,
    validityDate: null, // Root users don't expire
    parentId: null,
  },
  {
    afterCreate: async (user) => {
      const rootEntity = await findOrCreateRootEntity();
      const rootRole = await findOrCreateRootRole();

      // Create user association
      await UserAssociation.create({
        userId: user.id,
        entityId: rootEntity.id,
        roleId: rootRole.id,
        origin: USER_ORIGINS.INTERNAL,
      });

      // Create MFA setting
      const { base32secret } = generate2FASecret();
      await UserMfaSetting.create({
        userId: user.id,
        secretKey: base32secret,
        status: UserConstant.USER_MFA_STATUSES.INACTIVE,
      });

      return user;
    },
  },
);

/**
 * Factory definition for creating a root user with MFA enabled.
 *
 * Creates a root user with:
 * - Auto-generated unique username
 * - Auto-generated unique email
 * - Default password: 'Root@123456'
 * - MFA enabled (ACTIVE status)
 * - No expiry date
 * - Automatically creates UserAssociation and UserMfaSetting with active MFA
 *
 * @factory rootUserWithMfa
 * @type {User}
 *
 * @example
 * // CLI usage
 * npm run factory -- rootUserWithMfa
 *
 * @example
 * // Programmatic usage
 * const user = await factory.create('rootUserWithMfa');
 */
factory.define(
  'rootUserWithMfa',
  User,
  {
    name: () => 'Root Administrator MFA',
    username: async () => await generateRootUsername(),
    password: async () => {
      return await hashPassword('Root@123456');
    },
    email: async () => await generateRootEmail(),
    type: USER_TYPES.NORMAL,
    status: USER_STATUSES.ACTIVE,
    validityDate: null,
    parentId: null,
  },
  {
    afterCreate: async (user) => {
      const rootEntity = await findOrCreateRootEntity();
      const rootRole = await findOrCreateRootRole();

      // Create user association
      await UserAssociation.create({
        userId: user.id,
        entityId: rootEntity.id,
        roleId: rootRole.id,
        origin: USER_ORIGINS.INTERNAL,
      });

      // Create MFA setting with ACTIVE status
      const { base32secret } = generate2FASecret();
      await UserMfaSetting.create({
        userId: user.id,
        secretKey: base32secret,
        status: UserConstant.USER_MFA_STATUSES.ENABLED,
      });

      return user;
    },
  },
);

/**
 * Factory definition for creating a root user with an expiry date.
 *
 * Creates a root user with:
 * - Auto-generated unique username
 * - Auto-generated unique email
 * - Default password: 'Root@123456'
 * - MFA disabled by default
 * - Validity date set to 1 year from creation
 * - Automatically creates UserAssociation and UserMfaSetting
 *
 * @factory rootUserWithExpiry
 * @type {User}
 *
 * @example
 * // CLI usage
 * npm run factory -- rootUserWithExpiry
 *
 * @example
 * // Programmatic usage
 * const user = await factory.create('rootUserWithExpiry');
 *
 * @example
 * // With custom validity date
 * const customDate = new Date();
 * customDate.setMonth(customDate.getMonth() + 6); // 6 months from now
 * const user = await factory.create('rootUserWithExpiry', {
 *   validityDate: customDate
 * });
 */
factory.define(
  'rootUserWithExpiry',
  User,
  {
    name: () => 'Root Administrator Temporary',
    username: async () => await generateRootUsername(),
    password: async () => {
      return await hashPassword('Root@123456');
    },
    email: async () => await generateRootEmail(),
    type: USER_TYPES.NORMAL,
    status: USER_STATUSES.ACTIVE,
    validityDate: () => {
      // Set expiry to 1 year from now
      const date = new Date();
      date.setFullYear(date.getFullYear() + 1);
      return date;
    },
    parentId: null,
  },
  {
    afterCreate: async (user) => {
      const rootEntity = await findOrCreateRootEntity();
      const rootRole = await findOrCreateRootRole();

      // Create user association
      await UserAssociation.create({
        userId: user.id,
        entityId: rootEntity.id,
        roleId: rootRole.id,
        origin: USER_ORIGINS.INTERNAL,
      });

      // Create MFA setting
      const { base32secret } = generate2FASecret();
      await UserMfaSetting.create({
        userId: user.id,
        secretKey: base32secret,
        status: UserConstant.USER_MFA_STATUSES.INACTIVE,
      });

      return user;
    },
  },
);
