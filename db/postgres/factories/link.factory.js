import { faker } from '@faker-js/faker';

import { factory, models } from './index.js';

const { Link, Entity } = models;

/**
 * Ensure link has an entity
 * - If no entityId is provided, create a new Entity
 * - Validate that the entity exists if entityId is provided
 *
 * @param {Link} link
 * @returns {Promise<Link>}
 */
const withEntity = async (link) => {
  let entity;

  if (!link.entity_id) {
    // No entity_id → create one
    entity = await factory.create('entity');
    link.entity_id = entity.id;
  } else {
    // Validate entity exists
    entity = await Entity.findByPk(link.entity_id);
    if (!entity) {
      throw new Error(`Entity with id ${link.entity_id} not found`);
    }
  }

  return link;
};

/**
 * Generate appropriate URL based on link type
 * @param {Link} link
 * @returns {Promise<Link>}
 */
const withUrl = async (link) => {
  // If URL is already set, don't override it
  if (link.url) {
    return link;
  }

  // Generate URL based on type
  if (link.type === 'email_domain') {
    link.url = faker.internet.email();
  } else {
    link.url = faker.internet.url();
  }

  return link;
};

/**
 * Set default type if not provided
 * @param {Link} link
 * @returns {Promise<Link>}
 */
const withType = async (link) => {
  // If type is already set (from command line args), don't override it
  if (!link.type) {
    const types = ['email_domain', 'signin'];
    link.type = faker.helpers.arrayElement(types);
  }
  return link;
};

/**
 * Link Factory
 *
 * Generates a Link record for development/testing purposes.
 * - Automatically creates a new Entity if no entityId is passed.
 * - Generates appropriate URL based on link type.
 * - Supports both 'email_domain' and 'signin' link types.
 *
 * CLI Usage Examples:
 *   1. Create 1 link with a new Entity:
 *        npm run factory -- link
 *
 *   2. Create 3 links with new Entities:
 *        npm run factory -- link --count=3
 *
 *   3. Create 2 links under an existing Entity:
 *        npm run factory -- link --count=2 --entityId=<existingEntityId>
 *
 *   4. Create an email domain link for an existing Entity:
 *        npm run factory -- link --entityId=<existingEntityId> --type=email_domain
 *
 *   5. Build (but do not save) 3 links under an existing Entity:
 *        npm run factory -- link --count=3 --build --entityId=<existingEntityId>
 *
 *   6. Create signin links with custom URLs:
 *        npm run factory -- link --type=signin --url=https://custom-domain.com
 */
factory.define(
  'link',
  Link,
  {
    // Remove static definitions - let afterBuild handle everything
  },
  {
    afterBuild: async (link) => {
      await withType(link); // Set type if not provided
      await withEntity(link); // Set entity if not provided
      await withUrl(link); // Set URL based on type if not provided
      return link;
    },
  },
);

// Create specialized factories for specific link types
factory.extend(
  'link',
  'emailDomainLink',
  {
    type: 'email_domain',
  },
  {
    afterBuild: async (link) => {
      await withEntity(link);
      await withUrl(link);
      return link;
    },
  },
);

factory.extend(
  'link',
  'signinLink',
  {
    type: 'signin',
  },
  {
    afterBuild: async (link) => {
      await withEntity(link);
      await withUrl(link);
      return link;
    },
  },
);
