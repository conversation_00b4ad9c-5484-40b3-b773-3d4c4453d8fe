'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const now = new Date();

    await queryInterface.bulkInsert('entities', [
      {
        id: 'beb9aa2c-09fd-11f0-ba09-df67e743a9a1',
        parent_id: null,
        access_id: 'ROOT',
        hierarchy: 'root',
        code: 'ROOT',
        prefix: 'ROOT',
        name: 'Root',
        description: 'The root entity of the system',
        status: 'active',
        phone: null,
        email: null,
        logo_desktop: null,
        logo_mobile: null,
        favicon: null,
        created_at: now,
        updated_at: now,
        created_by: null,
        updated_by: null,
        version: 1,
      },
    ]);
  },

  async down(queryInterface, Sequelize) {},
};
