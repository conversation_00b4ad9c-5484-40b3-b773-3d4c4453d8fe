'use strict';

const currencies = require('../../data/currency.data.json');
const languages = require('../../data/language.data.json');
const regions = require('../../data/region.data.json');

const rootEntityId = 'beb9aa2c-09fd-11f0-ba09-df67e743a9a1';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('Starting Localisation Seeder...');

      const { default: LocalisationModel } = await import(
        '#src/modules/core/models/postgres/localisation.model.js'
      );
      const { default: CustomLocalisationModel } = await import(
        '#src/modules/core/models/postgres/custom-localisation.model.js'
      );

      const localisationModel = LocalisationModel({
        psql: { connection: queryInterface.sequelize },
      });
      const customLocalisationModel = CustomLocalisationModel({
        psql: { connection: queryInterface.sequelize },
      });

      // Process currencies
      await this.processCategory(
        'currency',
        currencies,
        localisationModel,
        customLocalisationModel,
        transaction,
      );

      // Process languages
      await this.processCategory(
        'language',
        languages,
        localisationModel,
        customLocalisationModel,
        transaction,
      );

      // Process regions
      await this.processCategory(
        'region',
        regions,
        localisationModel,
        customLocalisationModel,
        transaction,
      );

      await transaction.commit();
      console.log('Localisation Seeder completed successfully.');
    } catch (error) {
      await transaction.rollback();
      console.error('Error in Localisation Seeder:', error);
      throw error;
    }
  },

  async down(queryInterface) {},

  async processCategory(category, data, localisationModel, customLocalisationModel, transaction) {
    console.log(`Processing ${category} data...`);

    const seederData = data.map((item) => ({
      category,
      ...item,
    }));

    const createdSettings = await localisationModel.bulkCreate(seederData, {
      updateOnDuplicate: ['name', 'metadata'],
      conflictAttributes: ['category', 'code'],
      transaction,
    });

    const customSettingData = createdSettings.map((setting) => ({
      parentId: setting.id,
      entityId: rootEntityId,
      exchangeRate: category === 'currency' ? 4.41 : null,
    }));

    await customLocalisationModel.bulkCreate(customSettingData, {
      updateOnDuplicate: ['value', 'status', 'exchangeRate'],
      conflictAttributes: ['parentId', 'entityId'],
      transaction,
    });

    console.log(`Completed processing ${category} with ${createdSettings.length} records.`);
  },
};
