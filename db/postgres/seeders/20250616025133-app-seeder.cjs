'use strict';

const appData = require('../../data/app.data.json');

// temporary hardcode root user id
const rootUserId = '9524c60a-3ab7-11f0-9fe2-0242ac120002';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('Starting App Seeder...');

      const { default: AppModel } = await import('#src/modules/core/models/postgres/app.model.js');

      const appModel = AppModel({ psql: { connection: queryInterface.sequelize } });

      for (const app of appData) {
        const existingApp = await appModel.findOne({
          where: { category: app.category, name: app.name },
          transaction,
        });

        const now = new Date();
        const appPayload = {
          description: app.description,
          apiUrl: app.apiUrl,
          testFeature: app.testFeature,
          updatedBy: rootUserId,
          updatedAt: now,
        };

        if (existingApp) {
          await existingApp.update(appPayload, { transaction });
        } else {
          await appModel.create(
            {
              ...appPayload,
              category: app.category,
              name: app.name,
              createdBy: rootUserId,
              createdAt: now,
            },
            { transaction },
          );
        }
      }

      await transaction.commit();
      console.log('App Seeder completed successfully.');
    } catch (error) {
      await transaction.rollback();
      console.error('Error in App Seeder:', error);
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      console.log('App Seeder rollback completed.');
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      console.error('Error in App Seeder rollback:', error);
      throw error;
    }
  },
};
