'use strict';

const rootEntityId = 'beb9aa2c-09fd-11f0-ba09-df67e743a9a1';
const departmentId = 'beb9aa2c-09fd-11f0-ba09-df67e743a9a2';
module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('Starting Super Department Seeder...');

      const { default: DepartmentModel } = await import(
        '#src/modules/core/models/postgres/department.model.js'
      );
      const { default: DepartmentModuleModel } = await import(
        '#src/modules/core/models/postgres/department-module.model.js'
      );
      const { default: PolicyModel } = await import(
        '#src/modules/core/models/postgres/policy.model.js'
      );
      const { default: ModuleModel } = await import(
        '#src/modules/core/models/postgres/module.model.js'
      );
      const { RoleConstant } = await import('#src/modules/user/constants/index.js');
      const { CoreConstant } = await import('#src/modules/core/constants/index.js');

      const { Op } = await import('sequelize');

      const departmentModel = DepartmentModel({ psql: { connection: queryInterface.sequelize } });
      const departmentModuleModel = DepartmentModuleModel({
        psql: { connection: queryInterface.sequelize },
      });
      const policyModel = PolicyModel({
        psql: { connection: queryInterface.sequelize },
      });
      const moduleModel = ModuleModel({ psql: { connection: queryInterface.sequelize } });

      const POLICIES = RoleConstant.POLICIES;
      const HIERARCHY = CoreConstant.HIERARCHY;

      const now = new Date();
      const [superDepartment] = await departmentModel.findOrCreate({
        where: { name: 'Super Department', entityId: rootEntityId },
        defaults: {
          id: departmentId,
          name: 'Super Department',
          entityId: rootEntityId,
          hierarchy: HIERARCHY.ROOT,
          template: false,
          description: 'Department with all system policies',
          status: CoreConstant.COMMON_STATUSES.ACTIVE,
          createdAt: now,
          updatedAt: now,
          createdBy: null,
          updatedBy: null,
        },
        transaction,
      });

      const modules = await moduleModel.findAll({
        where: {
          navigationUrl: {
            [Op.ne]: null,
          },
        },
        transaction,
      });

      for (const module of modules) {
        const [departmentModule] = await departmentModuleModel.findOrCreate({
          where: { departmentId: superDepartment.id, moduleId: module.id },
          defaults: {
            departmentId: superDepartment.id,
            moduleId: module.id,
            createdAt: now,
            updatedAt: now,
            createdBy: null,
            updatedBy: null,
          },
          transaction,
        });

        const policyDefaults = {};
        POLICIES.forEach((policy) => {
          policyDefaults[policy] = true;
        });

        await policyModel.findOrCreate({
          where: { parentId: departmentModule.id },
          defaults: {
            parentId: departmentModule.id,
            ...policyDefaults,
            createdAt: now,
            updatedAt: now,
            createdBy: null,
            updatedBy: null,
          },
          transaction,
        });
      }

      await transaction.commit();
      console.log('Super Department Seeder completed successfully.');
    } catch (error) {
      await transaction.rollback();
      console.error('Error in Super Department Seeder:', error);
      throw error;
    }
  },

  async down(queryInterface) {},
};
