'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('credit_limits');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'credit_limits',
    addBaseColumns(
      {
        entity_id: {
          allowNull: false,
          type: DataTypes.UUID,
          references: {
            model: 'entities',
            key: 'id',
          },
          onDelete: 'RESTRICT',
          onUpdate: 'CASCADE',
          comment: 'Reference to the merchant in entities table',
        },
        credit: {
          allowNull: false,
          type: DataTypes.DECIMAL(30, 8),
          defaultValue: 0,
          comment: 'The credit limit amount',
        },
      },
      { withVersion: true },
    ),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('credit_limits', ['entity_id'], {
    name: 'uniq_credit_limits_entity_id',
    unique: true,
    comment: 'Enforces unique credit limit per entity',
  });
};
