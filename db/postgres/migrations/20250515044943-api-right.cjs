'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('api_rights');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'api_rights',
    addBaseColumns({
      parent_id: {
        allowNull: false,
        type: DataTypes.UUID,
        comment: 'UUID of the parent entity to which these API rights belong',
      },
      module_id: {
        allowNull: false,
        type: DataTypes.UUID,
        comment: 'UUID of the module for which these API rights are defined',
      },
      can_read: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Indicates whether the entity has read access to the module',
      },
      can_create: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Indicates whether the entity has create access to the module',
      },
      can_edit: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Indicates whether the entity has edit access to the module',
      },
      can_manage: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Indicates whether the entity has manage access to the module',
      },
      can_import: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Indicates whether the entity has import access to the module',
      },
      can_export: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Indicates whether the entity has export access to the module',
      },
    }),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('api_rights', {
    fields: ['parent_id', 'module_id'],
    type: 'unique',
    name: 'unq_api_rights_parent_module',
    comment: 'Ensures unique combination of parent_id and module_id',
  });

  await queryInterface.addConstraint('api_rights', {
    fields: ['can_read', 'can_create', 'can_edit', 'can_manage', 'can_import', 'can_export'],
    type: 'check',
    name: 'chk_api_rights_at_least_one',
    where: Sequelize.literal(
      '(can_read OR can_create OR can_edit OR can_manage OR can_import OR can_export)',
    ),
    comment: 'Ensures at least one permission is granted',
  });
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('api_rights', ['parent_id'], {
    name: 'idx_api_rights_parent',
    comment: 'Improves query performance when filtering or joining by parent_id',
  });
};
