'use strict';

const path = require('path');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));
const { uuidExtension } = require(path.join(__dirname, '..', 'helpers', 'extensions.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createExtensions(queryInterface);
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('addresses');
  },
};

const createExtensions = async (queryInterface) => await uuidExtension(queryInterface);

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'addresses',
    addBaseColumns({
      entity_id: {
        type: Sequelize.UUID,
        allowNull: false,
        comment: 'The entity ID this address belongs to',
      },
      street: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'The street address',
      },
      city: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: 'The city name',
      },
      state: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: 'The state or province name',
      },
      postal_code: {
        type: Sequelize.STRING(20),
        allowNull: false,
        comment: 'The postal or zip code',
      },
      country: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: 'The country name',
      },
    }),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('addresses', ['entity_id'], {
    name: 'idx_addresses_entity',
    comment: 'Improves query performance when filtering addresses by entity',
  });
};
