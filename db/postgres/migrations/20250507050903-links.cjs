'use strict';

const path = require('path');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));
const { uuidExtension } = require(path.join(__dirname, '..', 'helpers', 'extensions.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createExtensions(queryInterface);
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_links_type" AS ENUM('email_domain', 'signin');`,
    );
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('links');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_links_type";');
  },
};

const createExtensions = async (queryInterface) => await uuidExtension(queryInterface);

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'links',
    addBaseColumns({
      entity_id: {
        type: Sequelize.UUID,
        allowNull: false,
        comment: 'The entity ID this link belongs to',
      },
      type: {
        type: 'enum_links_type',
        allowNull: false,
        comment: 'The type of the link',
      },
      url: {
        type: Sequelize.STRING(2083),
        allowNull: false,
        comment: 'The URL of the link',
      },
    }),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('links', ['entity_id', 'type'], {
    name: 'idx_links_entity_type',
    comment:
      'Improves query performance when filtering links by both entity and type simultaneously',
  });
};
