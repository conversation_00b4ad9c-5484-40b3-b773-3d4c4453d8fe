'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_entity_app_status" AS ENUM('active', 'inactive', 'installed', 'uninstalled');`,
    );
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface) {
    await queryInterface.dropTable('entity_apps');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_entity_app_status";');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'entity_apps',
    addBaseColumns(
      {
        entity_id: {
          allowNull: false,
          type: DataTypes.UUID,
          references: {
            model: 'entities',
            key: 'id',
          },
          onDelete: 'RESTRICT',
          onUpdate: 'CASCADE',
          comment: 'Foreign key referencing the entity table',
        },
        app_id: {
          allowNull: false,
          type: DataTypes.UUID,
          references: {
            model: 'apps',
            key: 'id',
          },
          onDelete: 'RESTRICT',
          onUpdate: 'CASCADE',
          comment: 'Foreign key referencing the app table',
        },
        status: {
          allowNull: false,
          type: 'enum_entity_app_status',
          defaultValue: 'uninstalled',
          comment: 'Status of the app for the entity',
        },
      },
      {
        withParanoid: true,
      },
    ),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('entity_apps', ['entity_id', 'app_id'], {
    name: 'unq_entity_apps_entity_id_app_id',
    unique: true,
    where: {
      deleted_at: null,
    },
    comment: 'unique index on entity_id and app_id',
  });
};
