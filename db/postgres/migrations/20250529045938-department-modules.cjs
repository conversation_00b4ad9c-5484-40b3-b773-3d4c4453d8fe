'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('department_modules');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'department_modules',
    addBaseColumns({
      department_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'departments',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        comment: 'Reference to the departments',
      },
      module_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'modules',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        comment: 'Reference to the modules',
      },
    }),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('department_modules', {
    fields: ['department_id', 'module_id'],
    type: 'unique',
    name: 'unq_department_modules_department_module',
    comment: 'Ensures each department-module combination is unique',
  });
};
