'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_user_mfa_setting_status" AS ENUM('enabled', 'disabled');`,
    );

    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_user_mfa_setting_setup_status" AS ENUM('active', 'inactive');`,
    );

    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('user_mfa_settings');

    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_user_mfa_setting_status";');
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_user_mfa_setting_setup_status";',
    );
  },
};

// This function creates the table with the specified columns
const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'user_mfa_settings',
    addBaseColumns({
      user_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        unique: true,
        comment: 'Identifier for the mfa this user belongs to',
      },
      secret_key: {
        allowNull: false,
        type: Sequelize.STRING(100),
        unique: true,
        comment: 'The secret key of the user',
      },
      status: {
        allowNull: false,
        type: 'enum_user_mfa_setting_status',
        defaultValue: 'disabled',
        comment: 'The status of the mfa',
      },
      setup_status: {
        allowNull: false,
        type: 'enum_user_mfa_setting_setup_status',
        defaultValue: 'inactive',
        comment: 'The setup status of the mfa',
      },
    }),
  );
};

// This function adds indexes to the table
const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('user_mfa_settings', ['user_id', 'status'], {
    name: 'idx_user_mfa_setting_user_id_status',
    comment: 'Improves query performance when filtering by user and status',
  });
};
