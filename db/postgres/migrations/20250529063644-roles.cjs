'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));
const { ltreeExtension } = require(path.join(__dirname, '..', 'helpers', 'extensions.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createExtensions(queryInterface);
    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('roles');
    await queryInterface.sequelize.query('DROP EXTENSION IF EXISTS ltree;');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'roles',
    addBaseColumns(
      {
        parent_id: {
          allowNull: true,
          type: DataTypes.UUID,
          references: {
            model: 'roles',
            key: 'id',
          },
          onDelete: 'RESTRICT',
          onUpdate: 'CASCADE',
          comment: 'Reference to the parent role (null for root roles)',
        },
        entity_id: {
          allowNull: false,
          type: DataTypes.UUID,
          references: {
            model: 'entities',
            key: 'id',
          },
          onDelete: 'RESTRICT',
          onUpdate: 'CASCADE',
          comment: 'Identifier for the entity this role belongs to',
        },
        department_id: {
          allowNull: false,
          type: DataTypes.UUID,
          references: {
            model: 'departments',
            key: 'id',
          },
          onDelete: 'RESTRICT',
          onUpdate: 'CASCADE',
          comment: 'Identifier for the department this role belongs to',
        },
        name: {
          allowNull: false,
          type: DataTypes.STRING(50),
          comment: 'The name of the role',
        },
        description: {
          allowNull: true,
          type: DataTypes.TEXT,
          comment: 'A short description of the role',
        },
        status: {
          type: 'enum_common_status',
          allowNull: false,
          defaultValue: 'active',
          comment: 'The status of the role',
        },
        path: {
          allowNull: false,
          type: 'ltree',
          defaultValue: Sequelize.literal("'root'::ltree"),
          comment: 'The hierarchical path of the role using Ltree',
        },
      },
      { withVersion: true },
    ),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('roles', {
    fields: ['entity_id', 'name'],
    type: 'unique',
    name: 'unq_roles_entity_name',
    comment: 'Ensures each entity-name combination is unique',
  });
};

const addIndexes = async (queryInterface) => {
  await queryInterface.sequelize.query(
    'CREATE INDEX idx_roles_path_gist ON roles USING GIST (path);',
  );

  await queryInterface.sequelize.query('CREATE INDEX idx_roles_path_btree ON roles (path);');

  await queryInterface.addIndex('roles', {
    fields: ['entity_id', 'parent_id'],
    name: 'idx_roles_entity_parent',
  });

  await queryInterface.addIndex('roles', {
    fields: ['entity_id', 'name'],
    name: 'idx_roles_entity_name',
  });

  await queryInterface.addIndex('roles', {
    fields: ['entity_id'],
    name: 'idx_roles_entity',
  });
};

const createExtensions = async (queryInterface) => {
  await ltreeExtension(queryInterface);
};
