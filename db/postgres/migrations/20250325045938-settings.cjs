'use strict';

const path = require('path');
const { Op } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_settings_category" AS ENUM('general', 'personal', 'safety', 'themes');`,
    );

    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('settings');

    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_settings_category";');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'settings',
    addBaseColumns(
      {
        category: {
          allowNull: false,
          type: 'enum_settings_category',
          comment: 'The category of the setting',
        },
        field: {
          allowNull: false,
          type: Sequelize.STRING(50),
          comment: 'Identifier for the setting field',
        },
        access_levels: {
          allowNull: false,
          type: 'enum_entities_hierarchy[]',
          comment: 'Array of access levels that can modify this setting',
        },
      },
      { withParanoid: true },
    ),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('settings', {
    fields: ['category', 'field'],
    type: 'unique',
    name: 'unq_settings_category_field',
    comment: 'Ensures each combination of category and field is unique',
  });
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('settings', ['category'], {
    name: 'idx_settings_category',
    comment: 'Speeds filtering by category',
  });

  await queryInterface.addIndex('settings', ['deleted_at'], {
    name: 'idx_settings_deleted_at',
    where: {
      deleted_at: { [Op.ne]: null },
    },
    comment: 'Optimizes queries filtering out non-deleted records',
  });
};
