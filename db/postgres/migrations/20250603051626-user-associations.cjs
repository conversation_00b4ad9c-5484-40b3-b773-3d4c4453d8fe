'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_user_assoc_origin" AS ENUM('internal', 'external');`,
    );

    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('user_associations');

    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_user_assoc_origin";');
  },
};

// This function creates the table with the specified columns
const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'user_associations',
    addBaseColumns({
      entity_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'entities',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        comment: 'Identifier for the entity this user association belongs to',
      },
      user_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        comment: 'Identifier for the user this user association belongs to',
      },
      origin: {
        allowNull: false,
        type: 'enum_user_assoc_origin',
        defaultValue: 'internal',
        comment: 'The origin of the user',
      },
      role_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'roles',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        comment: 'Identifier for the role this user association belongs to',
      },
    }),
  );
};

// This function adds constraints to the table
const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('user_associations', {
    fields: ['entity_id', 'user_id'],
    type: 'unique',
    name: 'unq_user_associations_entity_user',
    comment:
      'Ensures a user cannot have multiple roles under both internal and external associations for the same entity',
  });
};

// This function adds indexes to the table
const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('user_associations', ['user_id'], {
    name: 'idx_user_assoc_user_id',
    comment: 'Improves query performance when filtering by user',
  });
};
