'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('role_modules');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'role_modules',
    addBaseColumns({
      module_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'modules',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        comment: 'Reference to the parents (e.g., modules, department_module_policies)',
      },
      role_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'roles',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        comment: 'Reference to the roles',
      },
    }),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('role_modules', {
    fields: ['role_id', 'module_id'],
    type: 'unique',
    name: 'unq_role_modules_role_module',
    comment: 'Ensures uniqueness of role-module combinations',
  });
};
