'use strict';

const path = require('path');
const { DataTypes, Op } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query('CREATE EXTENSION IF NOT EXISTS citext;');

    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('developer_hubs');

    await queryInterface.sequelize.query('DROP EXTENSION IF EXISTS citext;');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'developer_hubs',
    addBaseColumns(
      {
        entity_id: {
          allowNull: false,
          type: DataTypes.UUID,
          comment:
            'UUID of the entity (e.g., organisation or merchant) associated with this developer hub entry',
        },
        application: {
          allowNull: false,
          type: Sequelize.STRING(100),
          comment: 'Name or identifier of the application using this developer hub entry',
        },
        api_key: {
          allowNull: false,
          type: Sequelize.CITEXT || 'CITEXT',
          comment: 'API key for authentication and access control',
        },
        expiry_date: {
          allowNull: true,
          type: Sequelize.DATE,
          comment: 'Optional expiration date for the API key. If null, the key does not expire',
        },
        status: {
          allowNull: false,
          type: 'enum_common_status',
          defaultValue: 'active',
          comment: 'Current status of the developer hub entry: active or inactive',
        },
      },
      { withParanoid: true, withVersion: true },
    ),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('developer_hubs', ['api_key'], {
    name: 'uniq_developer_hubs_api_key',
    unique: true,
    comment: 'Enforces unique api key per developer hub',
  });

  await queryInterface.addIndex('developer_hubs', ['entity_id'], {
    name: 'idx_developer_hubs_entity',
    comment: 'Improves query performance when filtering or joining by entity_id',
  });

  await queryInterface.addIndex('developer_hubs', ['status'], {
    name: 'idx_developer_hubs_status',
    comment: 'Enhances performance for queries that filter by status',
  });

  await queryInterface.addIndex('developer_hubs', ['expiry_date'], {
    name: 'idx_developer_hubs_expiry_date',
    comment: 'Filters by expiry_date',
  });

  await queryInterface.addIndex('developer_hubs', ['deleted_at'], {
    name: 'idx_developer_hubs_deleted_at',
    where: {
      deleted_at: { [Op.ne]: null },
    },
    comment: 'Optimizes queries filtering out non-deleted records',
  });
};
