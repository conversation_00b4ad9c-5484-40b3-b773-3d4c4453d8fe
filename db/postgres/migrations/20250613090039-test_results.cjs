'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_test_result_status" AS ENUM('success', 'failed');`,
    );
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('test_results');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_test_result_status";');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'test_results',
    addBaseColumns({
      testable_id: {
        allowNull: false,
        type: DataTypes.UUID,
        comment: 'The ID of the associated model instance',
      },
      testable_type: {
        allowNull: false,
        type: DataTypes.STRING,
        comment: 'The associated model name (e.g., EntityApp, User, etc.)',
      },
      request_header: {
        allowNull: false,
        type: DataTypes.JSONB,
        comment: 'The request headers for the app test',
      },
      request_body: {
        allowNull: false,
        type: DataTypes.JSONB,
        comment: 'The request body for the app test',
      },
      response: {
        allowNull: false,
        type: DataTypes.JSONB,
        comment: 'The response from the app test',
      },
      status: {
        allowNull: false,
        type: 'enum_test_result_status',
        defaultValue: 'failed',
        comment: 'Status of the app for the test',
      },
    }),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('test_results', ['testable_type', 'testable_id'], {
    name: 'idx_test_results_polymorphic',
    comment: 'Index for polymorphic relation on testable_type and testable_id',
  });
};
