'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('custom_settings');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'custom_settings',
    addBaseColumns(
      {
        parent_id: {
          allowNull: false,
          type: DataTypes.UUID,
          references: {
            model: 'settings',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
          comment: 'Foreign key referencing the parent setting in the settings table',
        },
        entity_id: {
          allowNull: false,
          type: DataTypes.UUID,
          comment: 'UUID of the entity associated with this custom setting',
        },
        value: {
          allowNull: false,
          type: Sequelize.STRING(100),
          comment: 'Value of the custom setting, limited to 100 characters',
        },
      },
      {
        withVersion: true,
      },
    ),
  );
};

const addConstraints = async (queryInterface) => {
  await queryInterface.addConstraint('custom_settings', {
    fields: ['parent_id', 'entity_id'],
    type: 'unique',
    name: 'unq_custom_settings_parent_entity',
    comment: 'Ensures each entity has only one custom setting per parent setting',
  });
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('custom_settings', ['entity_id'], {
    name: 'idx_custom_settings_entity',
    comment: 'Speeds queries by entity_id',
  });
};
