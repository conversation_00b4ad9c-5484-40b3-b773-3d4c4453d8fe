'use strict';

const path = require('path');
const { DataTypes, Op } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_remarks_remarkable_type" AS ENUM('ip_access_control');`,
    );

    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_remarks_type" AS ENUM('audit', 'note', 'security', 'system', 'warning');`,
    );

    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface, Sequelize);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('remarks');
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_remarks_remarkable_type", "enum_remarks_type";',
    );
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'remarks',
    addBaseColumns(
      {
        remarkable_id: {
          allowNull: false,
          type: DataTypes.UUID,
          comment: 'UUID of the entity this remark is associated with',
        },
        remarkable_type: {
          allowNull: false,
          type: 'enum_remarks_remarkable_type',
          comment: 'Type of entity this remark is associated with',
        },
        type: {
          allowNull: false,
          type: 'enum_remarks_type',
          defaultValue: 'note',
          comment: 'Type of remark',
        },
        content: {
          allowNull: false,
          type: DataTypes.TEXT,
          comment: 'The actual content of the remark',
        },
        status: {
          allowNull: false,
          type: 'enum_common_status',
          defaultValue: 'active',
          comment: 'Current status of the remark',
        },
      },
      {
        withParanoid: true,
      },
    ),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('remarks', {
    fields: ['content'],
    type: 'check',
    name: 'chk_remarks_content_not_empty',
    where: Sequelize.literal('char_length(content) > 0'),
    comment: 'Ensures remark content is present and not empty',
  });
};

const addIndexes = async (queryInterface, Sequelize) => {
  await queryInterface.addIndex('remarks', ['remarkable_id', 'remarkable_type', 'status'], {
    name: 'idx_remarks_poly_status',
    comment: 'Improves query performance when filtering remarks by associated entity and status',
  });

  await queryInterface.addIndex('remarks', ['type', 'status'], {
    name: 'idx_remarks_type_status',
    comment: 'Enhances performance for queries that filter remarks by type and status',
  });

  await queryInterface.addIndex('remarks', ['deleted_at'], {
    name: 'idx_remarks_deleted_at',
    where: {
      deleted_at: { [Op.ne]: null },
    },
    comment: 'Optimizes queries filtering out non-deleted records',
  });

  /**
   * SELECT *
    FROM remarks
    WHERE to_tsvector('english', content)
      @@ plainto_tsquery('english', 'error handling');
   */
  await queryInterface.addIndex('remarks', [Sequelize.literal("to_tsvector('simple', content)")], {
    name: 'idx_remarks_content_fts',
    using: 'gin',
    comment: 'Full-text search index on remark content',
  });
};
