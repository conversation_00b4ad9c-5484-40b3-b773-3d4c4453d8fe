'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_module_navigation_type" AS ENUM('side', 'top', 'personal', 'none');`,
    );

    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('modules');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'modules',
    addBaseColumns({
      parent_id: {
        allowNull: true,
        type: DataTypes.UUID,
        references: {
          model: 'modules',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        comment: 'Reference to the parent modules',
      },
      name: {
        allowNull: false,
        type: Sequelize.STRING(100),
        comment: 'The full name of the module item',
      },
      hierarchy: {
        allowNull: false,
        type: 'enum_entities_hierarchy',
        comment: 'The hierarchy of the module',
      },
      navigation_type: {
        allowNull: false,
        type: 'enum_module_navigation_type[]',
        defaultValue: '{side}',
        comment: 'The navigation type of the module',
      },
      navigation_position: {
        allowNull: false,
        type: Sequelize.INTEGER(),
        comment: 'The navigation position of the module',
      },
      navigation_url: {
        allowNull: true,
        type: Sequelize.STRING(100),
        comment: 'The navigation url of the module',
      },
      translation_key: {
        allowNull: false,
        type: Sequelize.STRING(150),
        comment: 'i18n translation key, e.g. module.user_management',
      },
      level: {
        allowNull: false,
        type: Sequelize.INTEGER(),
        defaultValue: 0,
        comment: 'The level of the module in the hierarchy. Root is 0.',
      },
    }),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('modules', {
    fields: ['hierarchy', 'name', 'level'],
    type: 'unique',
    name: 'unq_modules_hierarchy_name',
    comment: 'Ensures each hierarchy-name combination is unique',
  });
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('modules', ['parent_id'], {
    name: 'idx_modules_parent',
    comment: 'Improves query performance when filtering by parent_id',
  });

  await queryInterface.addIndex('modules', ['hierarchy'], {
    name: 'idx_modules_hierarchy',
    comment: 'Improves query performance when filtering by hierarchy',
  });

  await queryInterface.addIndex('modules', ['level'], {
    name: 'idx_modules_level',
    comment: 'Improves query performance when filtering by level',
  });
};
