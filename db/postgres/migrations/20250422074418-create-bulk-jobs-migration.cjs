'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', '/helpers', 'baseColumns.cjs'));
const tableName = 'bulk_jobs';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createEnum(queryInterface);
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface) {
    await queryInterface.removeIndex(tableName, `idx_${tableName}_entity_type_status`);
    await queryInterface.dropTable(tableName);
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS "enum_bulk_job_status";
    `);
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS "enum_bulk_job_type";
    `);
  },
};

const createEnum = async (queryInterface) => {
  await queryInterface.sequelize.query(`
  CREATE TYPE "enum_bulk_job_status" AS ENUM('pending', 'running', 'completed', 'failed');
`);
  await queryInterface.sequelize.query(`
  CREATE TYPE "enum_bulk_job_type" AS ENUM('import', 'export', 'bulk_create');
`);
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    tableName,
    addBaseColumns({
      entity_id: {
        allowNull: false,
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        comment: 'UUID of the entity (e.g., organisation or merchant) associated with this job',
      },
      type: {
        allowNull: false,
        type: 'enum_bulk_job_type',
        defaultValue: 'export',
        comment: 'The type of the job (e.g., import or export)',
      },
      dry_run: {
        allowNull: false,
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: 'Indicates if this job should run without making changes',
      },
      title: {
        allowNull: false,
        type: Sequelize.STRING(100),
        comment: 'The title of the job',
      },
      parameters: {
        allowNull: true,
        type: Sequelize.JSONB,
        comment: 'The parameters required for the job',
      },
      model: {
        allowNull: false,
        type: Sequelize.STRING(50),
        comment: 'The model associated with the job',
      },
      file_name: {
        allowNull: true,
        type: Sequelize.STRING(50),
        comment: 'Name of the imported or exported file',
      },
      file_url: {
        allowNull: true,
        type: Sequelize.TEXT,
        comment: 'URL of the imported file',
      },
      result_url: {
        allowNull: true,
        type: Sequelize.TEXT,
        comment: 'URL of the exported result (if generated) or imported result',
      },
      error_message: {
        allowNull: true,
        type: Sequelize.TEXT,
        comment: 'Error message if the job failed',
      },
      success_count: {
        allowNull: true,
        type: Sequelize.INTEGER,
        comment: 'Total successfully imported records count',
      },
      total_count: {
        allowNull: true,
        type: Sequelize.INTEGER,
        comment: 'Total imported records count',
      },
      status: {
        allowNull: false,
        type: 'enum_bulk_job_status',
        defaultValue: 'pending',
        comment: 'The status of the job',
      },
      started_at: {
        allowNull: true,
        type: Sequelize.DATE,
        comment: 'The date and time when the job was started',
      },
      completed_at: {
        allowNull: true,
        type: Sequelize.DATE,
        comment: 'The date and time when the job was completed',
      },
    }),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex(tableName, ['entity_id', 'type', 'status'], {
    name: `idx_${tableName}_entity_type_status`,
  });

  await queryInterface.addIndex(tableName, ['status', 'started_at'], {
    name: `idx_${tableName}_status_started_at`,
  });
};
