'use strict';

const path = require('path');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));
const { uuidExtension } = require(path.join(__dirname, '..', 'helpers', 'extensions.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createExtensions(queryInterface);
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_entities_hierarchy" AS ENUM('root', 'organisation', 'merchant', 'user');`,
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_entities_status" AS ENUM('active', 'inactive', 'suspended', 'archived');`,
    );
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('entities');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_entities_hierarchy";');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_entities_status";');
  },
};

const createExtensions = async (queryInterface) => await uuidExtension(queryInterface);

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'entities',
    addBaseColumns(
      {
        parent_id: {
          type: Sequelize.UUID,
          allowNull: true,
          comment: 'The parent entity ID',
        },
        access_id: {
          type: Sequelize.STRING(12),
          allowNull: false,
          comment: 'The access ID for the entity',
        },
        hierarchy: {
          type: 'enum_entities_hierarchy',
          allowNull: false,
          comment: 'The hierarchy level of the entity',
        },
        code: {
          type: Sequelize.STRING(50),
          allowNull: false,
          comment: 'The unique code for the entity',
        },
        prefix: {
          type: Sequelize.STRING(10),
          allowNull: false,
          comment: 'The prefix for the entity',
        },
        name: {
          type: Sequelize.STRING(50),
          allowNull: false,
          comment: 'The name of the entity',
        },
        description: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'The description of the entity',
        },
        status: {
          type: 'enum_entities_status',
          allowNull: false,
          defaultValue: 'active',
          comment: 'The status of the entity',
        },
        phone: {
          type: Sequelize.STRING(20),
          allowNull: true,
          comment: 'The phone number of the entity',
        },
        email: {
          type: Sequelize.STRING(50),
          allowNull: true,
          comment: 'The email address of the entity',
        },
        logo_desktop: {
          type: Sequelize.UUID,
          allowNull: true,
          comment: 'The desktop logo UUID',
        },
        logo_mobile: {
          type: Sequelize.UUID,
          allowNull: true,
          comment: 'The mobile logo UUID',
        },
        favicon: {
          type: Sequelize.UUID,
          allowNull: true,
          comment: 'The favicon UUID',
        },
      },
      { withVersion: true },
    ),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('entities', ['code'], {
    unique: true,
    name: 'uniq_entities_code',
    comment: 'Ensures code is unique',
  });

  await queryInterface.addIndex('entities', ['access_id'], {
    unique: true,
    name: 'uniq_entities_accessid',
    comment: 'Ensures access_id is unique',
  });

  await queryInterface.addIndex('entities', ['name'], {
    unique: true,
    name: 'uniq_entities_name',
    comment: 'Ensures name is unique',
  });

  await queryInterface.addIndex('entities', ['hierarchy'], {
    name: 'idx_entities_hierarchy',
    comment: 'Improves query performance when filtering by hierarchy',
  });

  await queryInterface.addIndex('entities', ['status'], {
    name: 'idx_entities_status',
    comment: 'Improves query performance when filtering by status',
  });

  await queryInterface.addIndex('entities', ['name'], {
    name: 'idx_entities_name',
    comment: 'Improves query performance when filtering by name',
  });

  await queryInterface.addIndex('entities', ['hierarchy', 'status'], {
    name: 'idx_entities_hierarchy_status',
    comment: 'Improves filtering by hierarchy and status together',
  });

  await queryInterface.addIndex('entities', ['access_id', 'status'], {
    name: 'idx_entities_accessid_status',
    comment: 'Improves filtering by access_id and status together',
  });
};
