'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/**
 * @type {import('sequelize-cli').Migration}
 */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_common_status" AS ENUM('active', 'inactive', 'deleted', 'archived');`,
    );

    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('custom_localisations');

    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_common_status";');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'custom_localisations',
    addBaseColumns(
      {
        parent_id: {
          allowNull: false,
          type: DataTypes.UUID,
          references: {
            model: 'localisations',
            key: 'id',
          },
          onDelete: 'RESTRICT',
          onUpdate: 'CASCADE',
          comment: 'Reference to the parent localisation',
        },
        entity_id: {
          allowNull: false,
          type: DataTypes.UUID,
          comment: 'Identifier for the entity this custom localisation belongs to',
        },
        status: {
          allowNull: false,
          type: 'enum_common_status',
          defaultValue: 'active',
          comment: 'Current status of the custom localisation',
        },
        exchange_rate: {
          allowNull: true,
          type: Sequelize.DECIMAL(15, 6),
          comment: 'Exchange rate against base currency',
        },
      },
      {
        withVersion: true,
      },
    ),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('custom_localisations', {
    fields: ['exchange_rate'],
    type: 'check',
    name: 'chk_custom_localisations_exchange_rate_positive',
    where: Sequelize.literal('exchange_rate IS NULL OR exchange_rate >= 0'),
    comment: 'Ensures the exchange rate is either null or non-negative',
  });

  await queryInterface.addConstraint('custom_localisations', {
    fields: ['parent_id', 'entity_id'],
    type: 'unique',
    name: 'unq_custom_localisations_parent_entity',
    comment: 'Ensures each entity has only one custom localisation per parent localisation',
  });
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('custom_localisations', ['entity_id'], {
    name: 'idx_custom_localisations_entity',
    comment: 'Improves query performance when filtering by entity_id',
  });

  await queryInterface.addIndex('custom_localisations', ['status', 'entity_id'], {
    name: 'idx_custom_localisations_status_entity',
    comment: 'Improves query performance when filtering by status and entity_id',
  });
};
