'use strict';

const { DataTypes } = require('sequelize');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await addColumn(queryInterface);

    await queryInterface.removeConstraint('custom_settings', 'unq_custom_settings_parent_entity');

    await addConstraints(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('custom_settings', 'localisation_id');

    await revertConstraints(queryInterface);
  },
};

const addColumn = async (queryInterface) => {
  await queryInterface.addColumn('custom_settings', 'localisation_id', {
    allowNull: true,
    type: DataTypes.UUID,
    references: {
      model: 'localisations',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    comment: 'Foreign key referencing the currency in localisation table',
  });
};

const addConstraints = async (queryInterface) => {
  // sequalize does not support NULLS NOT DISTINCT, hence using raw query
  await queryInterface.sequelize.query(`
    ALTER TABLE custom_settings
    ADD CONSTRAINT unq_custom_settings_parent_entity_localise
    UNIQUE NULLS NOT DISTINCT (parent_id, entity_id, localisation_id);
  `);
};

const revertConstraints = async (queryInterface) => {
  await queryInterface.addConstraint('custom_settings', {
    fields: ['parent_id', 'entity_id'],
    type: 'unique',
    name: 'unq_custom_settings_parent_entity',
    comment: 'Ensures each entity has only one custom setting per parent setting',
  });
};
