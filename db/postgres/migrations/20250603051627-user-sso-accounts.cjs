'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_user_sso_account_status" AS ENUM('pending', 'completed', 'cancelled');`,
    );

    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('user_sso_accounts');

    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_user_sso_account_status";');
  },
};

// This function creates the table with the specified columns
const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'user_sso_accounts',
    addBaseColumns({
      user_id: {
        allowNull: true,
        type: DataTypes.UUID,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        comment: 'Identifier for the user this user sso belongs to',
      },
      email: {
        allowNull: false,
        type: Sequelize.STRING(100),
        unique: true,
        comment: 'The email of the user sso',
      },
      sub: {
        allowNull: false,
        type: Sequelize.STRING(100),
        unique: true,
        comment: 'The sub id of the user sso',
      },
      registered_date: {
        allowNull: false,
        type: Sequelize.DATE,
        comment: 'The registered date of the user',
      },
      completed_date: {
        allowNull: true,
        type: Sequelize.DATE,
        comment: 'The completed date of the user',
      },
      cancelled_date: {
        allowNull: true,
        type: Sequelize.DATE,
        comment: 'The cancelled date of the user',
      },
      status: {
        allowNull: false,
        type: 'enum_user_sso_account_status',
        defaultValue: 'pending',
        comment: 'The status of the user sso',
      },
    }),
  );
};

// This function adds indexes to the table
const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('user_sso_accounts', ['user_id'], {
    name: 'idx_user_sso_account_user_id',
    comment: 'Improves query performance when join by user',
  });

  await queryInterface.addIndex('user_sso_accounts', ['user_id', 'status'], {
    name: 'idx_user_sso_account_userid_status',
    comment: 'Improves query performance when filtering by user and status',
  });
};
