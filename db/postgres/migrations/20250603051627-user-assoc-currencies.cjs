'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('user_assoc_currencies');
  },
};

// This function creates the table with the specified columns
const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'user_assoc_currencies',
    addBaseColumns({
      user_association_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'user_associations',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        comment: 'Identifier for the user assoc this user assoc currency belongs to',
      },
      currency_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'custom_localisations',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        comment: 'The currency of the user assoc',
      },
    }),
  );
};

// This function adds constraints to the table
const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('user_assoc_currencies', {
    fields: ['user_association_id', 'currency_id'],
    type: 'unique',
    name: 'unq_user_association_currencies_user_association_currency',
    comment: 'Prevents duplicate entries of the same currency for a given user association',
  });
};

// This function adds indexes to the table
const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('user_assoc_currencies', ['user_association_id'], {
    name: 'idx_user_assoc_currencies_user_association_id',
    comment: 'Improves query performance when join by user',
  });
};
