'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('departments');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'departments',
    addBaseColumns(
      {
        entity_id: {
          allowNull: true,
          type: DataTypes.UUID,
          references: {
            model: 'entities',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
          comment: 'Reference to the entities',
        },
        name: {
          allowNull: false,
          type: Sequelize.STRING(50),
          comment: 'The name of the department',
        },
        hierarchy: {
          allowNull: false,
          type: 'enum_entities_hierarchy',
          comment: 'The hierarchy of the department',
        },
        template: {
          allowNull: false,
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          comment: 'The template of the department',
        },
        description: {
          allowNull: true,
          type: Sequelize.STRING(100),
          comment: 'The description of the department',
        },
        status: {
          allowNull: false,
          type: 'enum_common_status',
          defaultValue: 'active',
          comment: 'Current status of the department',
        },
      },
      {
        withParanoid: true,
      },
    ),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('departments', ['entity_id', 'name'], {
    unique: true,
    name: 'unq_departments_entity_name_not_deleted',
    where: {
      deleted_at: null,
    },
    comment: 'Ensures each entity-name combination is unique among non-deleted records',
  });
  await queryInterface.addIndex('departments', ['entity_id'], {
    name: 'idx_departments_entity',
    comment: 'Improves query performance when filtering by entity',
  });
  await queryInterface.addIndex('departments', ['entity_id', 'hierarchy'], {
    name: 'idx_departments_entity_hierarchy',
    comment: 'Improves query performance when filtering by entity and hierarchy',
  });
  await queryInterface.addIndex('departments', ['status'], {
    name: 'idx_departments_status',
    comment: 'Improves query performance when filtering by status',
  });
};
