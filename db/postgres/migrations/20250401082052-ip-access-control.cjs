'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_ipac_status" AS ENUM('active', 'inactive', 'expired');`,
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_ipac_rule_type" AS ENUM('allowlist', 'blocklist');`,
    );

    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ip_access_controls');

    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_ipac_status", "enum_ipac_rule_type";',
    );
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'ip_access_controls',
    addBaseColumns({
      parent_id: {
        allowNull: false,
        type: DataTypes.UUID,
        comment: 'UUID of the parent entity to which this IP access control rule belongs',
      },
      rule_type: {
        allowNull: false,
        type: 'enum_ipac_rule_type',
        defaultValue: 'blocklist',
        comment: 'Type of IP access control rule',
      },
      ip_address: {
        allowNull: false,
        type: DataTypes.INET,
        comment: 'IP address or CIDR range to which this rule applies',
      },
      validity_date: {
        allowNull: true,
        type: Sequelize.DATE,
        comment: 'Optional expiration date for the rule. If null, the rule does not expire',
      },
      status: {
        allowNull: false,
        type: 'enum_ipac_status',
        defaultValue: 'active',
        comment: 'Current status of the rule',
      },
    }),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('ip_access_controls', {
    fields: ['parent_id', 'ip_address'],
    type: 'unique',
    name: 'uk_ipac_parent_ip',
    comment: 'Ensures unique combination of parent_id, and ip_address',
  });
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex(
    'ip_access_controls',
    ['parent_id', 'rule_type', 'status', 'validity_date'],
    {
      name: 'idx_ipac_parent_rule_status_validity',
      comment:
        'Improves query performance when filtering or joining by parent_id, rule_type, status, and validity_date',
    },
  );

  await queryInterface.addIndex('ip_access_controls', ['parent_id'], {
    name: 'idx_ipac_parent',
    comment: 'Improves query performance when filtering or joining by parent_id',
  });

  await queryInterface.addIndex('ip_access_controls', ['rule_type'], {
    name: 'idx_ipac_rule_type',
    comment: 'Enhances performance for queries that filter by rule_type',
  });

  await queryInterface.sequelize.query(`
    CREATE INDEX idx_ipac_ip_gist
    ON ip_access_controls
    USING gist (ip_address inet_ops);
  `);
  await queryInterface.sequelize.query(`
    COMMENT ON INDEX idx_ipac_ip_gist IS 'Optimizes CIDR and IP range queries';
  `);

  await queryInterface.addIndex('ip_access_controls', ['status'], {
    name: 'idx_ipac_status',
    comment: 'Improves performance for queries that filter by status',
  });

  await queryInterface.addIndex('ip_access_controls', ['validity_date'], {
    name: 'idx_ipac_validity_date',
    comment: 'Speeds queries on expiration dates',
  });
};
