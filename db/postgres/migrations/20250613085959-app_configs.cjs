'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface) {
    await queryInterface.dropTable('app_configs');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'app_configs',
    addBaseColumns(
      {
        entity_app_id: {
          allowNull: false,
          type: DataTypes.UUID,
          references: {
            model: 'entity_apps',
            key: 'id',
          },
          onDelete: 'RESTRICT',
          onUpdate: 'CASCADE',
          comment: 'Foreign key referencing the entity_apps table',
        },
        config_key: {
          allowNull: false,
          type: DataTypes.STRING(100),
          comment: 'The key for the configuration setting',
        },
        config_value: {
          allowNull: true,
          type: DataTypes.TEXT,
          comment: 'The value for the configuration setting',
        },
      },
      {
        withParanoid: true,
      },
    ),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('app_configs', ['entity_app_id', 'config_key'], {
    name: 'unq_apps_entity_app_id_config_key',
    unique: true,
    where: {
      deleted_at: null,
    },
    comment: 'unique index on entity_app_id and config_key',
  });
};
