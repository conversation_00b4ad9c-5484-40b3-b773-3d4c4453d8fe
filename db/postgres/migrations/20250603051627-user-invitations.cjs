'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_user_invitation_status" AS ENUM('pending', 'approved', 'rejected', 'revoked', 'cancelled');`,
    );

    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('user_invitations');

    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_user_invitation_status";');
  },
};

// This function creates the table with the specified columns
const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'user_invitations',
    addBaseColumns({
      user_association_id: {
        allowNull: false,
        type: DataTypes.UUID,
        references: {
          model: 'user_associations',
          key: 'id',
        },
        comment: 'Identifier for the user invitation this user belongs to',
      },
      invited_by: {
        allowNull: false,
        type: DataTypes.UUID,
        comment: 'Identifier of the user of the user invitation',
      },
      invited_date: {
        allowNull: false,
        type: DataTypes.DATE,
        default: DataTypes.NOW,
        comment: 'The invited date of the user invitation',
      },
      accepted_date: {
        allowNull: true,
        type: DataTypes.DATE,
        comment: 'The accepted date of the user invitation',
      },
      cancelled_date: {
        allowNull: true,
        type: DataTypes.DATE,
        comment: 'The cancelled date of the user invitation',
      },
      status: {
        allowNull: false,
        type: 'enum_user_invitation_status',
        defaultValue: 'pending',
        comment: 'The status of the user invitation',
      },
    }),
  );
};

// This function adds indexes to the table
const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('user_invitations', ['user_association_id'], {
    name: 'idx_user_invitation_user_association_id',
    comment: 'Improves query performance when filtering by user association id',
  });

  await queryInterface.addIndex('user_invitations', ['user_association_id', 'status'], {
    name: 'idx_inv_user_association_id_status',
    comment: 'Improves query performance when filtering by user association id and status',
  });
};
