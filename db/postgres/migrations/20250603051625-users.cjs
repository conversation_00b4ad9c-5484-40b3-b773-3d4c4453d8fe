'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_user_type" AS ENUM('normal', 'sub_account');`,
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_user_status" AS ENUM('inactive', 'active', 'pending', 'suspended');`,
    );

    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('users');

    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_user_type";');

    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_user_status";');
  },
};

// This function creates the table with the specified columns
const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'users',
    addBaseColumns({
      parent_id: {
        allowNull: true,
        type: DataTypes.UUID,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        comment: 'Identifier for the parent this user belongs to',
      },
      name: {
        allowNull: true,
        type: Sequelize.STRING(100),
        comment: 'The name of the user',
      },
      username: {
        allowNull: false,
        type: Sequelize.STRING(100),
        unique: true,
        comment: 'The full username of the user',
      },
      password: {
        allowNull: false,
        type: Sequelize.STRING(100),
        comment: 'The password of the user',
      },
      email: {
        allowNull: true,
        type: Sequelize.STRING(100),
        unique: true,
        comment: 'The email of the user',
      },
      type: {
        allowNull: false,
        type: 'enum_user_type',
        defaultValue: 'normal',
        comment: 'The type of the user',
      },
      status: {
        allowNull: false,
        type: 'enum_user_status',
        defaultValue: 'active',
        comment: 'The status of the user',
      },
      validity_date: {
        allowNull: true,
        type: Sequelize.DATE,
        comment: 'The validity date of the user',
      },
    }),
  );
};

// This function adds constraints to the table
const addConstraints = async (queryInterface, Sequelize) => {
  const { NAME_REGEX, USERNAME_REGEX, EMAIL_REGEX } = await import(
    '#src/modules/core/constants/regex.constant.js'
  );

  await queryInterface.addConstraint('users', {
    fields: ['validity_date'],
    type: 'check',
    name: 'chk_users_validity_date',
    where: Sequelize.literal('validity_date IS NULL OR validity_date > NOW()'),
    comment: 'Validity date must be null or in the future',
  });

  await queryInterface.addConstraint('users', {
    fields: ['name'],
    type: 'check',
    name: 'chk_users_name_letters_and_spaces_only',
    where: Sequelize.literal(`name ~ '${NAME_REGEX}'`),
    comment: 'Name can only contain letters and spaces, no special characters',
  });

  await queryInterface.addConstraint('users', {
    fields: ['username'],
    type: 'check',
    name: 'chk_users_username_valid_format',
    where: Sequelize.literal(`
    (type = 'normal' AND username ~ '${USERNAME_REGEX.NORMAL}')
    OR
    (type = 'sub_account' AND username ~ '${USERNAME_REGEX.SUB_ACCOUNT}')
  `),
    comment: 'Username format must match based on user type',
  });

  await queryInterface.addConstraint('users', {
    fields: ['email'],
    type: 'check',
    name: 'chk_users_email_code_format',
    where: Sequelize.literal(`email ~* '${EMAIL_REGEX}'`),
    comment: 'Validate email format',
  });
};

// This function adds indexes to the table
const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('users', ['status'], {
    name: 'idx_users_status',
    comment: 'Improves query performance when filtering by status',
  });

  await queryInterface.addIndex('users', ['username', 'status'], {
    name: 'idx_users_username_status',
    comment: 'Improves query performance when filtering by username and status',
  });
};
