'use strict';

const path = require('path');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));
const { uuidExtension } = require(path.join(__dirname, '..', 'helpers', 'extensions.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_media_assets_file_type" AS ENUM('image', 'audio', 'document');`,
    );

    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_media_assets_visibility" AS ENUM('public', 'private');`,
    );

    await createExtensions(queryInterface);
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('media_assets');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS enum_media_assets_file_type;');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS enum_media_assets_visibility;');
  },
};

const createExtensions = async (queryInterface) => await uuidExtension(queryInterface);
const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'media_assets',
    addBaseColumns(
      {
        entity_id: {
          type: Sequelize.UUID,
          allowNull: false,
          comment: 'The entity ID this media belongs to',
        },
        file_type: {
          type: Sequelize.ENUM('image', 'audio', 'document'),
          allowNull: false,
          comment: 'Type of media file (image, audio, or document)',
        },
        file_path: {
          type: Sequelize.STRING(2083),
          allowNull: false,
          comment: 'Path to the stored file on disk or cloud storage',
        },
        mime_type: {
          type: Sequelize.STRING(255),
          allowNull: false,
          comment: 'MIME type of the file (e.g., image/jpeg, audio/mp3)',
        },
        file_size: {
          type: Sequelize.INTEGER,
          allowNull: false,
          comment: 'Size of the file in bytes or formatted size',
        },
        dimension: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: 'Dimensions of the media (e.g., "1920x1080" for images)',
        },
        thumbnail: {
          type: Sequelize.STRING(2083),
          allowNull: true,
          comment: 'Path to thumbnail version of the media if applicable',
        },
        metadata: {
          type: Sequelize.JSONB,
          allowNull: false,
          defaultValue: {},
          comment: 'Additional metadata about the media in JSON format',
        },
        alt_text: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: 'Alternative text for accessibility purposes',
        },
        visibility: {
          type: Sequelize.ENUM('public', 'private'),
          allowNull: false,
          comment: 'Whether the media is publicly accessible or private',
        },
      },
      { withParanoid: true },
    ),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('media_assets', ['entity_id'], {
    name: 'idx_media_assets_entity',
    comment: 'Improves query performance when filtering media by entity',
  });

  await queryInterface.addIndex('media_assets', ['entity_id', 'deleted_at'], {
    name: 'idx_media_assets_entity_deleted',
    comment: 'Optimizes queries filtering by entity that also exclude deleted records',
  });
};
