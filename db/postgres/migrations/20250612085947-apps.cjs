'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_app_category" AS ENUM('cloud_storage', 'communication', 'captcha', 'email', 'currency_exchange_rate', 'payment', 'authentication');`,
    );
    await createTable(queryInterface);
    await addIndexes(queryInterface);
  },

  async down(queryInterface) {
    await queryInterface.dropTable('apps');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_app_category";');
  },
};

const createTable = async (queryInterface) => {
  await queryInterface.createTable(
    'apps',
    addBaseColumns({
      icon: {
        allowNull: true,
        type: DataTypes.UUID,
        // Uncomment the following lines when media_assets table is available
        // references: {
        //   model: 'media_assets',
        //   key: 'id',
        // },
        // onDelete: 'RESTRICT',
        // onUpdate: 'CASCADE',
        comment: 'Foreign key referencing the media_assets table for the app icon',
      },
      category: {
        allowNull: false,
        type: 'enum_app_category',
        comment: 'The category to which the app belongs',
      },
      name: {
        allowNull: false,
        type: DataTypes.STRING(100),
        comment: 'The name of the app',
      },
      description: {
        allowNull: false,
        type: DataTypes.TEXT,
        comment: 'The description of the app',
      },
      api_url: {
        allowNull: true,
        type: DataTypes.STRING(2048),
        comment: 'The API URL of the app',
      },
      test_feature: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'Indicates if the app supports the test feature',
      },
    }),
  );
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('apps', ['name', 'category'], {
    name: 'idx_apps_name_category',
    comment: 'Speeds queries by name and category',
  });
};
