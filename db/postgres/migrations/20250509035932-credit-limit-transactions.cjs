'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_credit_limit_transaction_type" AS ENUM('credit', 'debit');`,
    );
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_transaction_status" AS ENUM('pending', 'completed', 'failed', 'cancelled', 'voided');`,
    );
  },

  async down(queryInterface, Sequelize) {
    // Unable to drop the enums as table is scaffolded
  },
};
