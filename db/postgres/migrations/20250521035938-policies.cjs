'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('policies');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'policies',
    addBaseColumns({
      parent_id: {
        allowNull: false,
        type: DataTypes.UUID,
        unique: true,
        comment: 'Reference to the parents (e.g., modules, department_module_policies)',
      },
      can_view: {
        allowNull: false,
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'The view policy',
      },
      can_create: {
        allowNull: false,
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'The create policy',
      },
      can_edit: {
        allowNull: false,
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'The edit policy',
      },
      can_import: {
        allowNull: false,
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'The import policy',
      },
      can_export: {
        allowNull: false,
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'The export policy',
      },
      can_manage: {
        allowNull: false,
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'The manage policy',
      },
      can_masking: {
        allowNull: false,
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'The masking policy',
      },
      can_overwrite: {
        allowNull: false,
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'The overwrite policy',
      },
      can_verify: {
        allowNull: false,
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'The verify policy',
      },
    }),
  );
};
