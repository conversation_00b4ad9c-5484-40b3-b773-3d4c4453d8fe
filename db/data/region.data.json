[{"name": "America/New_York", "code": "US", "metadata": {"gmt": "-5", "phone": {"countryCode": "+1", "format": {"mobile": "XXX-XXX-XXXX", "landline": "XXX-XXX-XXXX"}, "pattern": "^\\+1[0-9]{10}$", "example": {"mobile": "+12105551234", "landline": "+12105551234"}, "minLength": 10, "maxLength": 10}, "name": {"format": {"western": "firstName lastName", "alternative": "firstName middleName lastName"}, "pattern": "^[A-Za-z\\-'\\s]+$", "example": {"western": "<PERSON>", "alternative": "<PERSON>"}, "fields": ["firstName", "middleName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z\\-'"}}}, {"name": "Asia/Bangkok", "code": "TH", "metadata": {"gmt": "+7", "phone": {"countryCode": "+66", "format": {"mobile": "XX-XXX-XXXX", "landline": "XX-XXX-XXXX"}, "pattern": "^\\+66[0-9]{9}$", "example": {"mobile": "+66812345678", "landline": "+6621234567"}, "minLength": 9, "maxLength": 9}, "name": {"format": {"thai": "firstName lastName", "western": "firstName lastName"}, "pattern": "^[A-Za-z\\u0E00-\\u0E7F\\s]+$", "example": {"thai": "สมชาย ใจดี", "western": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": ["firstName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z\\u0E00-\\u0E7F"}}}, {"name": "Asia/Dhaka", "code": "BD", "metadata": {"gmt": "+6", "phone": {"countryCode": "+880", "format": {"mobile": "XXXX-XXXXXX", "landline": "XX-XXXXXXX"}, "pattern": "^\\+880[0-9]{10}$", "example": {"mobile": "+8801812345678", "landline": "+880212345678"}, "minLength": 10, "maxLength": 10}, "name": {"format": {"bengali": "firstName lastName", "western": "firstName lastName"}, "pattern": "^[A-Za-z\\u0980-\\u09FF\\s]+$", "example": {"bengali": "<PERSON><PERSON><PERSON><PERSON><PERSON>হ<PERSON>", "western": "<PERSON>"}, "fields": ["firstName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z\\u0980-\\u09FF"}}}, {"name": "Asia/Ho_Chi_Minh", "code": "VN", "metadata": {"gmt": "+7", "phone": {"countryCode": "+84", "format": {"mobile": "XXX-XXX-XXXX", "landline": "XX-XXXX-XXXX"}, "pattern": "^\\+84[0-9]{9,10}$", "example": {"mobile": "+84981234567", "landline": "+84281234567"}, "minLength": 9, "maxLength": 10}, "name": {"format": {"vietnamese": "lastName middleName firstName", "western": "firstName lastName"}, "pattern": "^[A-Za-z\\s]+$", "example": {"vietnamese": "<PERSON><PERSON><PERSON><PERSON>", "western": "<PERSON>"}, "fields": ["firstName", "middleName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z"}}}, {"name": "Asia/Hong_Kong", "code": "HK", "metadata": {"gmt": "+8", "phone": {"countryCode": "+852", "format": {"mobile": "XXXX-XXXX", "landline": "XXXX-XXXX"}, "pattern": "^\\+852[0-9]{8}$", "example": {"mobile": "+85291234567", "landline": "+85221234567"}, "minLength": 8, "maxLength": 8}, "name": {"format": {"chinese": "lastName firstName", "western": "firstName lastName"}, "pattern": "^(?:[A-Za-z\\u4e00-\\u9fff][A-Za-z\\u4e00-\\u9fff\\-']*[A-Za-z\\u4e00-\\u9fff]\\s?){1,3}$", "example": {"chinese": "陳大文", "western": "<PERSON>"}, "fields": ["firstName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z\\u4e00-\\u9fff\\-'"}}}, {"name": "Asia/Jakarta", "code": "ID", "metadata": {"gmt": "+7", "phone": {"countryCode": "+62", "format": {"mobile": "XXX-XXXX-XXXX", "landline": "XX-XXXX-XXXX"}, "pattern": "^\\+62[0-9]{9,11}$", "example": {"mobile": "+6281234567890", "landline": "+62211234567"}, "minLength": 9, "maxLength": 11}, "name": {"format": {"indonesian": "firstName lastName", "western": "firstName lastName"}, "pattern": "^[A-Za-z\\s]+$", "example": {"indonesian": "<PERSON><PERSON>", "western": "<PERSON><PERSON>"}, "fields": ["firstName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z"}}}, {"name": "Asia/Karachi", "code": "PK", "metadata": {"gmt": "+5", "phone": {"countryCode": "+92", "format": {"mobile": "XXX-XXXXXXX", "landline": "XX-XXXXXXX"}, "pattern": "^\\+92[0-9]{10}$", "example": {"mobile": "+923001234567", "landline": "+92211234567"}, "minLength": 10, "maxLength": 10}, "name": {"format": {"urdu": "firstName lastName", "western": "firstName lastName"}, "pattern": "^[A-Za-z\\u0600-\\u06FF\\s]+$", "example": {"urdu": "<PERSON><PERSON><PERSON><PERSON>ان", "western": "<PERSON>"}, "fields": ["firstName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z\\u0600-\\u06FF"}}}, {"name": "Asia/Kuala_Lumpur", "code": "MY", "metadata": {"gmt": "+8", "phone": {"countryCode": "+60", "format": {"mobile": "XX-XXXX XXXX", "landline": "X-XXXX XXXX"}, "pattern": "^\\+60[0-9]{9,10}$", "example": {"mobile": "+60123456789", "landline": "+60312345678"}, "minLength": 9, "maxLength": 10}, "name": {"format": {"malay": "firstName bin/binti lastName", "western": "firstName lastName"}, "pattern": "^[A-Za-z\\s]+$", "example": {"malay": "<PERSON>", "western": "<PERSON>"}, "fields": ["firstName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z"}}}, {"name": "Asia/Manila", "code": "PH", "metadata": {"gmt": "+8", "phone": {"countryCode": "+63", "format": {"mobile": "XXX-XXX-XXXX", "landline": "X-XXX-XXXX"}, "pattern": "^\\+63[0-9]{10}$", "example": {"mobile": "+639123456789", "landline": "+6321234567"}, "minLength": 10, "maxLength": 10}, "name": {"format": {"filipino": "firstName middleName lastName", "western": "firstName lastName"}, "pattern": "^[A-Za-z\\s]+$", "example": {"filipino": "Juan <PERSON>", "western": "<PERSON>"}, "fields": ["firstName", "middleName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z"}}}, {"name": "Asia/Singapore", "code": "SG", "metadata": {"gmt": "+8", "phone": {"countryCode": "+65", "format": {"mobile": "XXXX-XXXX", "landline": "XXXX-XXXX"}, "pattern": "^\\+65([36][0-9]|[89]\\d)\\d{6}$", "example": {"mobile": "+6591234567", "landline": "+6563456789"}, "minLength": 8, "maxLength": 8}, "name": {"format": {"western": "firstName lastName", "chinese": "lastName middleName firstName"}, "pattern": "^(?:[A-Za-z\\u4e00-\\u9fff][A-Za-z\\u4e00-\\u9fff\\-']*[A-Za-z\\u4e00-\\u9fff]\\s?){1,3}$", "example": {"western": "<PERSON>", "chinese": "<PERSON>", "hyphenated": "<PERSON><PERSON><PERSON>"}, "fields": ["firstName", "middleName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z\\u4e00-\\u9fff\\-'"}}}, {"name": "Europe/Berlin", "code": "DE", "metadata": {"gmt": "+1", "phone": {"countryCode": "+49", "format": {"mobile": "XXXX-XXXXXXX", "landline": "XXXX-XXXXXXX"}, "pattern": "^\\+49[0-9]{10,11}$", "example": {"mobile": "+4915123456789", "landline": "+493012345678"}, "minLength": 10, "maxLength": 11}, "name": {"format": {"german": "firstName lastName", "western": "firstName lastName"}, "pattern": "^[A-Za-z\\-\\s]+$", "example": {"german": "<PERSON>", "western": "<PERSON>"}, "fields": ["firstName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z\\-"}}}, {"name": "Europe/London", "code": "GB", "metadata": {"gmt": "+0", "phone": {"countryCode": "+44", "format": {"mobile": "XXXX-XXXXXX", "landline": "XXXX-XXXXXX"}, "pattern": "^\\+44[0-9]{10}$", "example": {"mobile": "+447912345678", "landline": "+442012345678"}, "minLength": 10, "maxLength": 10}, "name": {"format": {"british": "firstName middleName lastName", "western": "firstName lastName"}, "pattern": "^[A-Za-z\\-\\s']+$", "example": {"british": "<PERSON>", "western": "<PERSON>"}, "fields": ["firstName", "middleName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z\\-'"}}}, {"name": "Europe/Paris", "code": "FR", "metadata": {"gmt": "+1", "phone": {"countryCode": "+33", "format": {"mobile": "X XX XX XX XX", "landline": "X XX XX XX XX"}, "pattern": "^\\+33[0-9]{9}$", "example": {"mobile": "+33612345678", "landline": "+33123456789"}, "minLength": 9, "maxLength": 9}, "name": {"format": {"french": "firstName lastName", "western": "firstName lastName"}, "pattern": "^[A-Za-z\\-\\s]+$", "example": {"french": "<PERSON>", "western": "<PERSON>"}, "fields": ["firstName", "lastName"], "required": ["firstName", "lastName"], "allowedChars": "A-Za-z\\-"}}}]