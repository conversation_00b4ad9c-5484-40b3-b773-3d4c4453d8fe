[{"icon": null, "category": "cloud_storage", "name": "AWS S3", "description": "Integrating AWS S3 allows applications to efficiently store, manage, and retrieve various types of data, including user uploads and media files.", "apiUrl": null, "testFeature": true}, {"icon": null, "category": "communication", "name": "Telegram", "description": "Integrating Telegram enables applications to send real-time notifications, alerts, and messages to users or teams, improving communication and engagement.", "apiUrl": null, "testFeature": true}, {"icon": null, "category": "communication", "name": "<PERSON><PERSON>ck", "description": "Integrating Slack enables applications to send real-time notifications, alerts, and messages to users or teams, improving communication and engagement.", "apiUrl": null, "testFeature": true}, {"icon": null, "category": "communication", "name": "Discord", "description": "Integrating Discord enables applications to send real-time notifications, alerts, and messages to users or teams, improving communication and engagement.", "apiUrl": null, "testFeature": true}, {"icon": null, "category": "<PERSON><PERSON>a", "name": "hCaptcha", "description": "Protects login and registration processes from automated bot attacks by verifying user interactions.", "apiUrl": null, "testFeature": false}, {"icon": null, "category": "email", "name": "SMTP", "description": "Provides email-sending capabilities emails using SMTP.", "apiUrl": null, "testFeature": true}, {"icon": null, "category": "email", "name": "SendGrid", "description": "Provides email-sending capabilities using SendGrid API.", "apiUrl": null, "testFeature": true}, {"icon": null, "category": "currency_exchange_rate", "name": "Exchangerate Host", "description": "Retrieves the latest currency exchange rates to update system data.", "apiUrl": "https://api.exchangerate.host/latest", "testFeature": true}, {"icon": null, "category": "payment", "name": "Payment System", "description": "Facilitates online transactions by integrating with a secure payment gateway.", "apiUrl": "https://psapi.jk188.net", "testFeature": true}, {"icon": null, "category": "authentication", "name": "Gmail", "description": "Simplifies the login process by allowing users to authenticate using their Google account.", "apiUrl": null, "testFeature": false}]