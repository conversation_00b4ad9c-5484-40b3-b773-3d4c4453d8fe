[{"name": "User management", "translationKey": "common.label.userManagement", "children": [{"name": "Account", "translationKey": "common.label.account", "children": [{"name": "User", "translationKey": "common.label.user", "navigationUrl": "USER_MANAGEMENT.ACCOUNT.USER", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canCreate", "canEdit"]}, {"hierarchy": "organisation", "policies": ["canView", "canCreate", "canEdit", "canManage"]}, {"hierarchy": "merchant", "policies": ["canView", "canCreate", "canEdit"]}]}, {"name": "Sub account", "translationKey": "common.label.subAccount", "navigationUrl": "USER_MANAGEMENT.ACCOUNT.SUB_ACCOUNT", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canCreate", "canEdit", "canManage"]}, {"hierarchy": "organisation", "policies": ["canView", "canCreate", "canEdit", "canManage"]}, {"hierarchy": "merchant", "policies": ["canView", "canCreate", "canEdit", "canManage"]}]}, {"name": "Self onboarding", "translationKey": "common.label.selfOnboarding", "navigationUrl": "USER_MANAGEMENT.ACCOUNT.SELF_ONBOARDING", "hierarchyPolicies": [{"hierarchy": "organisation", "policies": ["canView", "canManage"]}]}]}, {"name": "Access control", "translationKey": "common.label.accessControl", "children": [{"name": "Role", "translationKey": "common.label.role", "navigationUrl": "USER_MANAGEMENT.ACCESS_CONTROL.ROLE", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canCreate", "canEdit", "canManage"]}, {"hierarchy": "organisation", "policies": ["canView", "canCreate", "canEdit", "canManage"]}, {"hierarchy": "merchant", "policies": ["canView", "canCreate", "canEdit", "canManage"]}]}, {"name": "Department", "translationKey": "common.label.department", "navigationUrl": "USER_MANAGEMENT.ACCESS_CONTROL.DEPARTMENT", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canCreate", "canEdit", "canManage"]}, {"hierarchy": "organisation", "policies": ["canView", "canCreate", "canEdit", "canManage"]}, {"hierarchy": "merchant", "policies": ["canView", "canCreate", "canEdit", "canManage"]}]}, {"name": "Department template", "translationKey": "common.label.departmentTemplate", "navigationUrl": "USER_MANAGEMENT.ACCESS_CONTROL.DEPARTMENT_TEMPLATE", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canCreate", "canEdit", "canManage"]}]}]}]}, {"name": "Log & monitoring", "translationKey": "common.label.logNMonitoring", "children": [{"name": "Log", "translationKey": "common.label.log", "children": [{"name": "Audit trail", "translationKey": "common.label.auditTrail", "navigationUrl": "LOG_MONITORING.LOG.AUDIT_TRAIL", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canExport", "canMasking"]}, {"hierarchy": "organisation", "policies": ["canView", "canExport", "canMasking"]}, {"hierarchy": "merchant", "policies": ["canView", "canExport", "canMasking"]}]}]}]}, {"name": "Setting", "translationKey": "common.label.setting", "children": [{"name": "Localisation", "translationKey": "common.label.localisation", "children": [{"name": "Localisation", "translationKey": "common.label.localisation", "navigationType": "none", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canEdit"]}]}]}, {"name": "Theme", "translationKey": "common.label.theme", "navigationUrl": "SETTING.THEME", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canEdit"]}, {"hierarchy": "organisation", "policies": ["canView", "canEdit"]}, {"hierarchy": "merchant", "policies": ["canView", "canEdit"]}]}, {"name": "Security control", "translationKey": "common.label.securityControl", "children": [{"name": "Safety", "translationKey": "common.label.safety", "navigationUrl": "SETTING.SECURITY_CONTROL.SAFETY", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canEdit"]}, {"hierarchy": "organisation", "policies": ["canView", "canEdit"]}, {"hierarchy": "merchant", "policies": ["canView", "canEdit"]}]}, {"name": "Access control", "translationKey": "common.label.accessControl", "navigationUrl": "SETTING.SECURITY_CONTROL.ACCESS_CONTROL", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canEdit"]}, {"hierarchy": "organisation", "policies": ["canView", "canEdit"]}, {"hierarchy": "merchant", "policies": ["canView", "canEdit"]}]}]}, {"name": "Developer hub", "translationKey": "common.label.developerHub", "navigationUrl": "SETTING.DEVELOPER_HUB", "hierarchyPolicies": [{"hierarchy": "merchant", "policies": ["canView", "canCreate", "canEdit", "canManage"]}]}, {"name": "App center", "translationKey": "common.label.appCenter", "navigationUrl": "SETTING.APP_CENTER", "hierarchyPolicies": [{"hierarchy": "root", "policies": ["canView", "canCreate", "canEdit", "canManage"]}, {"hierarchy": "merchant", "policies": ["canView", "canCreate", "canEdit", "canManage"]}]}]}, {"name": "Personal", "translationKey": "common.label.personal", "navigationType": "personal", "children": [{"name": "Personal setting", "translationKey": "common.label.personalSetting", "navigationUrl": "PERSONAL.PERSONAL_SETTING", "navigationType": "personal", "hierarchyPolicies": [{"hierarchy": "user", "policies": []}]}, {"name": "User invitation", "translationKey": "common.label.userInvitation", "navigationUrl": "PERSONAL.USER_INVITATION", "navigationType": "personal", "hierarchyPolicies": [{"hierarchy": "user", "policies": []}]}]}]