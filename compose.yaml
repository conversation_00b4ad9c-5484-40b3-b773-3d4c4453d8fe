services:
  # Runs the Fastify application which is built from the current directory (.)
  wlsapi:
    build: .
    container_name: ${APP_NAME}
    env_file:
      - .env
    ports:
      - "${DOCKER_PORT}:${FASTIFY_PORT}"
    volumes:
      - .:/app  # this line mount the project directory to your local, so you can edit the file directly
      - "/var/lib/docker/containers:/var/lib/docker/containers:ro"
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "/sys/fs/cgroup:/hostfs/sys/fs/cgroup:ro"
      - "/proc:/hostfs/proc:ro"
      - "/:/hostfs:ro"
    develop:
      watch:
        - action: sync
          path: .
          target: /app
          ignore:
            - node_modules/
        - action: rebuild
          path: package.json
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Runs a Filebeat instance
  filebeat:
    build:
      context: .
      dockerfile: ./config/elk/filebeat/Dockerfile.filebeat
      args:
        ELASTIC_STACK_VERSION: ${ELASTIC_STACK_VERSION}
    container_name: ${APP_NAME}-filebeat
    environment:
      - LOGSTASH_HOSTS=${LOGSTASH_HOST}:${LOGSTASH_PORT}
    volumes:
      - certs:/usr/share/filebeat/certs:ro
      - filebeatdata:/usr/share/filebeat/data
      - "./logs:/usr/share/filebeat/logs:ro"
      - "./config/elk/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro"
      - "/var/lib/docker/containers:/var/lib/docker/containers:ro"
      - "/var/run/docker.sock:/var/run/docker.sock:ro"

volumes:
  certs:
    driver: local
  filebeatdata:
    driver: local
