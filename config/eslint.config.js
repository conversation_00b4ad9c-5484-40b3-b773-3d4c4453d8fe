import importPlugin from 'eslint-plugin-import';
import prettier from 'eslint-plugin-prettier';
import sonarjs from 'eslint-plugin-sonarjs';
import globals from 'globals';

import prettierConfig from './prettier.config.js';

export default [
  sonarjs.configs.recommended,
  {
    plugins: {
      prettier,
      import: importPlugin,
    },
    files: ['**/*.js'],
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest,
        Atomics: 'readonly',
        SharedArrayBuffer: 'readonly',
      },
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
    rules: {
      'prettier/prettier': ['error', prettierConfig],
      curly: 'error',
      'no-console': 'warn',
      'sonarjs/todo-tag': 'warn',
      'import/order': [
        'error',
        {
          groups: ['builtin', 'external', 'internal', 'parent', 'sibling', 'index'],
          'newlines-between': 'always',
          alphabetize: {
            order: 'asc',
            caseInsensitive: true,
          },
        },
      ],
    },
    linterOptions: {
      reportUnusedDisableDirectives: 'off',
    },
  },
  {
    files: ['**/*.test.js', '**/*.test.ts'],
    rules: {
      'sonarjs/no-hardcoded-passwords': 'off',
    },
  },
  {
    files: ['**/config/**/*.js'],
    rules: {
      'sonarjs/no-hardcoded-passwords': 'off',
    },
  },
  { languageOptions: { globals: globals.browser } },
];
