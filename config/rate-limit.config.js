import Redis from 'ioredis';

import { getPackageJson } from '#src/utils/file.util.js';

const majorVersion = getPackageJson().version.split('.')[0];

export default {
  rateLimitGroups: {
    // The timeWindow is specified in milliseconds (1 minute = 60000 milliseconds)
    auditTrails: {
      max: 10,
      timeWindow: 60000,
      name: 'auditTrails',
      description: 'Rate limit for audit logs',
    },
    auth: {
      max: 60,
      timeWindow: 60000,
      name: 'auth',
      description: 'Rate limit for authentication',
    },
    default: {
      max: 100,
      timeWindow: 60000,
      name: 'default',
      description: 'Default rate limit for all routes.',
    },
  },
  groupPrefixes: {
    auditTrails: [`/api/v${majorVersion}/audit-trails`],
    auth: [`/api/v${majorVersion}/auth`],
  },
  whitelist: ['127.0.0.1'],
  addHeadersOnExceeding: {
    'X-RateLimit-Limit': true,
    'X-RateLimit-Remaining': true,
    'X-RateLimit-Reset': true,
  },
  addHeaders: {
    'X-RateLimit-Limit': true,
    'X-RateLimit-Remaining': true,
    'X-RateLimit-Reset': true,
    'Retry-After': true,
  },
  errorResponseBuilder: (request, context) => ({
    statusCode: 429,
    error: 'Too Many Requests',
    message: `Rate limit exceeded, retry in ${context.after}`,
  }),
  skipOnError: true,
  nameSpace: `${process.env.APP_NAME || 'default-app'}-rate-limit:`,
  redis: new Redis({
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    connectTimeout: Number(process.env.REDIS_RATE_LIMIT_CONNECT_TIMEOUT || 500),
    maxRetriesPerRequest: 1,
    db: 1, // Using Redis DB 1 for rate limiting.
  }),
  cache: 10000,
  hook: 'onRequest',
};
