export default {
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'Content-Length',
    'Host',
    'User-Agent',
    'Accept',
    'Accept-Encoding',
    'Connection',
    'Accept-Language',
    'X-Forwarded-For',
    'x-access-id',
  ],
  exposedHeaders: [
    'Content-Security-Policy',
    'Cross-Origin-Opener-Policy',
    'Cross-Origin-Resource-Policy',
    'Origin-Agent-Cluster',
    'Referrer-Policy',
    'Strict-Transport-Security',
    'X-Content-Type-Options',
    'X-DNS-Prefetch-Control',
    'X-Download-Options',
    'X-Frame-Options',
    'X-Permitted-Cross-Domain-Policies',
    'X-XSS-Protection',
    'access-control-allow-origin',
    'access-control-allow-credentials',
    'access-control-expose-headers',
    'x-ratelimit-limit',
    'x-ratelimit-remaining',
    'x-ratelimit-reset',
    'retry-after',
    'content-type',
    'content-length',
    'Date',
    'Connection',
    'Keep-Alive',
  ],
  credentials: true,
  maxAge: 86400,
  preflight: true,
  preflightContinue: false,
  optionsSuccessStatus: 204,
  strictPreflight: true,
  cacheControl: 'max-age=86400',
};
