export default {
  contentSecurityPolicy:
    process.env.NODE_ENV === 'development'
      ? false
      : {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ['*', 'data:'],
            scriptSrc: ["'self'"],
          },
        },
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
  frameguard: { action: 'deny' },
  referrerPolicy: { policy: 'same-origin' },
  xssFilter: true,
  noSniff: true,
  dnsPrefetchControl: { allow: false },
  expectCt: { maxAge: 86400, enforce: true },
  permittedCrossDomainPolicies: { permittedPolicies: 'none' },
};
