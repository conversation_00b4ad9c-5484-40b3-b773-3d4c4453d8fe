/**
 * Applies the selected theme to the Swagger UI interface.
 * Enables the relevant theme stylesheet and sets a body attribute.
 * @returns {void}
 */
const applyTheme = () => {
  let savedTheme = localStorage.getItem('swaggerTheme') || window.SWAGGER_CONFIG.defaultTheme;
  const themeNames = window.SWAGGER_CONFIG.availableThemes;

  themeNames.forEach((themeName) => {
    const link = document.querySelector(`link[href*="${themeName}.css"]`);
    if (link) {
      link.disabled = themeName !== savedTheme;
    }
  });

  document.body.setAttribute('data-theme', savedTheme);
};

/**
 * Adds a theme selector dropdown to the Swagger UI interface topbar.
 * Will not add if it already exists or if topbar is missing.
 * @returns {void}
 */
const addThemeSelector = () => {
  if (document.querySelector('#theme-selector-container')) {
    return;
  }

  const swaggerHeader = document.querySelector('.topbar-wrapper');
  if (!swaggerHeader) {
    return;
  } // Don't add if Swagger UI not loaded

  const selectorContainer = document.createElement('div');
  selectorContainer.id = 'theme-selector-container';
  selectorContainer.className = 'theme-selector';

  const select = document.createElement('select');
  select.name = 'theme-select';

  const currentTheme = localStorage.getItem('swaggerTheme') || window.SWAGGER_CONFIG.defaultTheme;

  window.SWAGGER_CONFIG.availableThemes.forEach((themeName) => {
    const option = document.createElement('option');
    option.value = themeName;
    option.textContent = themeName.charAt(0).toUpperCase() + themeName.slice(1);
    if (themeName === currentTheme) {
      option.selected = true;
    }
    select.appendChild(option);
  });

  // Change theme without reload
  select.addEventListener('change', (e) => {
    localStorage.setItem('swaggerTheme', e.target.value);
    applyTheme();
  });

  selectorContainer.appendChild(select);
  swaggerHeader.appendChild(selectorContainer);
};

/**
 * Initializes the theme functionality for the Swagger UI.
 * Ensures selector is added once the DOM and Swagger UI are ready.
 * @returns {void}
 */
const init = () => {
  // Run on load, and also on DOMContentLoaded in case Swagger UI is injected late
  const trySetup = () => {
    applyTheme();
    addThemeSelector();
  };

  window.addEventListener('load', trySetup);
  document.addEventListener('DOMContentLoaded', () => setTimeout(trySetup, 250));
};

// Start
init();
