// vitest.config.js
import path from 'node:path';

import { defineConfig } from 'vitest/config';

export default defineConfig({
  root: path.resolve(__dirname, '..'),
  test: {
    environment: 'node',
    include: ['test/**/**/*.{test,spec}.{js,ts}'],
    coverage: {
      all: true,
      enabled: true,
      include: ['src/**/*.{js,ts,jsx,tsx}'], // Adjust the glob pattern if needed
      exclude: ['src/**/index.js', 'src/app.js', 'src/routes.js'], // Adjust the glob pattern if needed
      provider: 'v8',
      reporter: ['text', 'html', 'lcov'],
      reportsDirectory: './coverage',
      thresholds: {
        perFile: true,
        statements: 80,
        branches: 80,
        functions: 80,
        lines: 80,
      },
    },
    testTimeout: 10000,
    maxThreads: 4,
    minThreads: 1,
    mockReset: true,
  },
});
