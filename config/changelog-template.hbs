## Changelog

All notable changes to this project will be documented in this file.

{{#unless options.hideCredit}}
  Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).
{{/unless}}

{{!--
Introduction
• This template tries to follow conventional commits format https://www.conventionalcommits.org/en/v1.0.0/
• The template uses regex to filter commit types into their own headings (this is more than just fixes and features headings)
• It also uses the replaceText function in package.json to remove the commit type text from the message, because the headers are shown instead.

• The text 'Breaking:' or 'Breaking changes:' can be located anywhere in the commit.
• The types feat:, fix:, chore:, docs:, refactor:, test:, style:, perf: must be at the beginning of the commit subject with an : on end.
   • They can optionally have a scope set to outline the module or component that is affected eg feat(bldAssess):
• There is a short hash on the end of every commit that is currently commented out so that change log did not grow too long (due to some system's file size limitations).  You can uncomment if you wish [`{{shorthash}}`]({{href}})

Example Definitions
• feat: A new feature
• fix: A bug fix
• perf: A code change that improves performance
• refactor: A code change that neither fixes a bug nor adds a feature
• style: Changes that do not affect the meaning of the code (white-space, formatting, spelling mistakes, missing semi-colons, etc)
• test: Adding missing tests or correcting existing tests
• docs: Adding/updating documentation
• chore: Something like updating a library version, or moving files to be in a better location and updating all file refs
--}}

 {{!--
 Regex reminders
 ^ = starts with
 \( = ( character (otherwise it is interpreted as a regex lookup group)
 * = zero or more of the previous character
 \s = whitespace
 . = any character except newline
 | = or
 [aA] = character a or character A
 --}}


{{#each releases}}
  {{#if href}}
    ##{{#unless major}}#{{/unless}} [{{title}}]({{href}}) - {{#if tag}} {{niceDate}} {{/if}}

  {{else}}
    ## {{title}}
  {{/if}}


  {{#if summary}}
    {{summary}}
  {{/if}}

  {{#each merges}}
    - {{#if commit.breaking}}**Breaking change:** {{/if}}{{message}}
  {{/each}}

  {{#each fixes}}
    - {{#if commit.breaking}}**Breaking change:** {{/if}}{{commit.subject}}{{#each fixes}}{{/each}}
  {{/each}}


  {{! List commits with 'breaking:' or 'Breaking change:' anywhere in the message under a heading}}
  {{#commit-list commits heading='#### Breaking Changes :warning:' message='[bB]reaking [cC]hange:|[bB]reaking:' }}
    - {{subject}} {{!-- @{{author}} --}}  [`{{shorthash}}`]({{href}})
  {{/commit-list}}

  {{! List commits organised under a heading, but not those already listed in the breaking section }}
      {{#commit-list commits heading='#### New Features' message='^[fF]eat:|[fF]eat\(' exclude='^[fF]eat:|[bB]reaking [cC]hange:|[bB]reaking:'}}
        - {{subject}} {{!-- @{{author}} --}}  [`{{shorthash}}`]({{href}})
      {{/commit-list}}

      {{#commit-list commits heading='#### Bug Fixes' message='^[fF]ix:|^[fF]ix\(' exclude='^[fF]ix:|[bB]reaking [cC]hange:|[bB]reaking:'}}
        - {{subject}} {{!-- @{{author}} --}}  [`{{shorthash}}`]({{href}})
      {{/commit-list}}

      {{#commit-list commits heading='#### Chores & Housekeeping' message='^[cC]hore:|^[cC]hore\(' exclude='^[cC]hore:|[bB]reaking [cC]hange:|[bB]reaking:'}}
        - {{subject}} {{!-- @{{author}} --}}  [`{{shorthash}}`]({{href}})
      {{/commit-list}}

      {{#commit-list commits heading='#### Documentation Changes' message='^[dD]ocs:|^[dD]ocs\(' exclude='^[dD]ocs:|[bB]reaking [cC]hange:|[bB]reaking:'}}
        - {{subject}} {{!-- @{{author}} --}}  [`{{shorthash}}`]({{href}})
      {{/commit-list}}

      {{#commit-list commits heading='#### Refactoring and Updates' message='^[rR]efactor:|^[rR]efactor\(' exclude='^[rR]efactor:|[bB]reaking [cC]hange:|[bB]reaking:'}}
        - {{subject}} {{!-- @{{author}} --}}  [`{{shorthash}}`]({{href}})
      {{/commit-list}}

      {{#commit-list commits heading='#### Changes to Test Assests' message='^[tT]est:|^[tT]est\(' exclude='^[tT]est:|[bB]reaking [cC]hange:|[bB]reaking:'}}
        - {{subject}} {{!-- @{{author}} --}}  [`{{shorthash}}`]({{href}})
      {{/commit-list}}

      {{#commit-list commits heading='#### Style Changes' message='^[sS]tyle:|^[sS]tyle\(' exclude='^[sS]tyle:|[bB]reaking [cC]hange:|[bB]reaking:'}}
        - {{subject}} {{!-- @{{author}} --}}  [`{{shorthash}}`]({{href}})
      {{/commit-list}}

      {{#commit-list commits heading='#### Performance Improvments' message='^[pP]erf:|^[pP]erf\(' exclude='^[pP]erf:|[bB]reaking [cC]hange:|[bB]reaking:'}}
        - {{subject}} {{!-- @{{author}} --}}  [`{{shorthash}}`]({{href}})
      {{/commit-list}}

      {{!-- {{#commit-list commits heading='#### General Changes' exclude='[bB]reaking [cC]hange:|[bB]reaking:|^[fF]eat:|^[fF]eat\(|^[fF]ix:|^[fF]ix\(|^[cC]hore:|^[cC]hore\(|^[dD]ocs:|^[dD]ocs\(|^[rR]efactor:|^[rR]efactor\(|^[tT]est:|^[tT]est\(|^[sS]tyle:|^[sS]tyle\(|^[pP]erf:|^[pP]erf\('}}
        - {{subject}} @{{author}} [`{{shorthash}}`]({{href}})
      {{/commit-list}} --}}

{{/each}}
