export default {
  arrowParens: 'always',
  bracketSameLine: false,
  bracketSpacing: true,
  endOfLine: 'lf',
  htmlWhitespaceSensitivity: 'css',
  insertPragma: false,
  parser: 'babel',
  printWidth: 100,
  proseWrap: 'preserve',
  quoteProps: 'as-needed',
  requirePragma: false,
  semi: true,
  singleQuote: true,
  tabWidth: 2,
  trailingComma: 'all',
  useTabs: false,
  overrides: [
    {
      files: ['**/*.json', '*.json'],
      options: {
        bracketSpacing: false,
        parser: 'json',
        printWidth: 120,
        semi: false,
        trailingComma: 'none',
        useTabs: true,
      },
    },
    {
      files: '*.css',
      options: {
        parser: 'css',
      },
    },
  ],
};
