filebeat.inputs:
- type: filestream
  id: postgres-logs-filestream
  paths:
    - /usr/share/filebeat/logs/*-postgres.*.log
  fields:
    log_type: postgres_logs
  parsers:
    - ndjson:
        target: ""
        add_error_key: true
  close.on_states_change.inactive: 10m

- type: filestream
  id: application-logs-filestream
  paths:
    - /usr/share/filebeat/logs/*.log
  prospector.scanner.exclude_files: ['.*postgres.*\.log']
  fields:
    log_type: application_logs
  parsers:
    - ndjson:
        target: ""
        add_error_key: true
  close.on_states_change.inactive: 10m


filebeat.autodiscover:
  providers:
    - type: docker
      hints.enabled: true


processors:
  - add_docker_metadata: ~


output.logstash:
  hosts: ${LOGSTASH_HOSTS}
