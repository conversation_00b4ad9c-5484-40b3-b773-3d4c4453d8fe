ARG ELASTIC_STACK_VERSION

FROM docker.elastic.co/beats/filebeat:${ELASTIC_STACK_VERSION}

USER root

# Create filebeat user and group if they don't exist
RUN if ! getent group filebeat > /dev/null 2>&1; then \
  groupadd -g 1000 filebeat; \
  fi && \
  if ! getent passwd filebeat > /dev/null 2>&1; then \
  useradd -u 1000 -g filebeat -s /sbin/nologin -m filebeat; \
  fi

# Copy the configuration file
COPY ./config/elk/filebeat/filebeat.yml /usr/share/filebeat/filebeat.yml

# Set correct ownership and permissions
RUN chown root:filebeat /usr/share/filebeat/filebeat.yml && \
  chmod 644 /usr/share/filebeat/filebeat.yml && \
  chmod go-w /usr/share/filebeat/filebeat.yml

USER filebeat

CMD ["filebeat", "-e", "--strict.perms=false"]
